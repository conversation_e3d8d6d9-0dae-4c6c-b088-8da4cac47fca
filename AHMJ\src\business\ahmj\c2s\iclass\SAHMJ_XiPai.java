package business.ahmj.c2s.iclass;						
						
import cenum.ClassType;						
import jsproto.c2s.cclass.BaseSendMsg;						
						
						
public class SAHMJ_XiPai extends BaseSendMsg {						
    /**						
	 * 						
	 */						
	private static final long serialVersionUID = 1L;						
	public long roomID;						
    public long pid;						
    public ClassType cType;						
    public static SAHMJ_Xi<PERSON>ai make(long roomID, long pid, ClassType cType) {						
    	SAHMJ_XiPai ret = new SAHMJ_XiPai();						
        ret.roomID = roomID;						
        ret.pid = pid;						
        ret.cType = cType;						
        return ret;						
    						
						
    }						
}						
