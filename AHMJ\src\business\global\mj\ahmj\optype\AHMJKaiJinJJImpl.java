package business.global.mj.ahmj.optype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCard;
import business.global.mj.ahmj.AHMJRoom;
import business.global.mj.ahmj.AHMJRoomSet;
import business.global.mj.manage.OpCard;

/**
 * 开金 进金 开到白板，白板金
 *
 * <AUTHOR>
 */
public class AHMJKaiJinJJImpl implements OpCard {

    @Override
    public boolean checkOpCard(AbsMJSetPos mSetPos, int cardID) {
        // 操作开金
        this.opKaiJin(mSetPos);
        return false;
    }

    @Override
    public boolean doOpCard(AbsMJSetPos mSetPos, int cardID) {
        // TODO Auto-generated method stub
        return false;
    }

    /**
     * 开金
     *
     * @return
     */
    public void opKaiJin(AbsMJSetPos mSetPos) {
        // 开金
        MJCard fanCard = this.kaiJinCard(mSetPos);
        // 开金通知。
        MJCard jinJin = jinJin(fanCard);
        mSetPos.getSet().getmJinCardInfo().addJinCard(jinJin);
        mSetPos.getSet().kaiJinNotify(fanCard, jinJin);
        return;
    }

    /**
     * 开金
     *
     * @param mSetPos 玩家信息
     */
    public MJCard kaiJinCard(AbsMJSetPos mSetPos) {
        // 摸牌开金
        MJCard card = ((AHMJRoomSet) mSetPos.getSet()).kaiJin();
        if (((AHMJRoom) mSetPos.getRoom()).isKingsOfSeven()) {
            mSetPos.getSet().getmJinCardInfo().addJinCard(card);
        }
        return card;
    }

    /**
     * 开金补花通知
     *
     * @param mSetPos 玩家信息
     * @param mCard   开出的牌
     */
    private void kaiJinApplique(AbsMJSetPos mSetPos, MJCard mCard) {
        // 添加打出的牌
        mSetPos.addOutCardIDs(mCard.getCardID());
        // 添加花
        mSetPos.getPosOpRecord().addHua(mCard.getCardID());
        // 通知补花,补花位置。
        mSetPos.getSet().MJApplique(mSetPos.getPosID());
    }

    /**
     * 进金
     *
     * @param card
     * @return
     */
    public MJCard jinJin(MJCard card) {
        // 牌类型
        int type = card.type / 10;
        // 牌大小
        int size = (card.cardID % 1000) / 100;
        int tem = size;
        // 如果是 万条筒
        // 牌 >= 9 就是 九 万条筒
        if (size >= 9) {
            // 进金为 一 万条筒
            size = 1;
        }
        if (tem == size) {
            size++;
        }
        int cardId = (type * 10 + size) * 100 + 1;
        return new MJCard(cardId);
    }
}
