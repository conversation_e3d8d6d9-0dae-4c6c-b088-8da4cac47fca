package business.wxmj.c2s.iclass;			
			
import jsproto.c2s.cclass.BaseSendMsg;			
			
/**			
 * 莆田麻将 接收客户端数据 创建房间			
 * 			
 * <AUTHOR>			
 *			
 */			
@SuppressWarnings("serial")			
public class CWXMJ_PiaoFen extends BaseSendMsg {			
			
	public long roomID;//房间ID			
	public int piaoFen; // 5根10  插5台或是10			
			
	public static CWXMJ_PiaoFen make(long roomID, int piaoFen) {			
		CWXMJ_PiaoFen ret = new CWXMJ_PiaoFen();			
		ret.roomID = roomID;			
		ret.piaoFen = piaoFen;			
		return ret;			
	}			
}			
