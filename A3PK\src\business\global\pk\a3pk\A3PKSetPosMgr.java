package business.global.pk.a3pk;	
	
import business.global.pk.AbsPKSetPosMgr;	
import business.global.pk.AbsPKSetRoom;	
import com.ddm.server.common.utils.CommMath;	
import com.ddm.server.common.utils.CommTime;	
import jsproto.c2s.cclass.mj.BaseMJSet_Pos;	
import jsproto.c2s.cclass.pk.base.BasePKSet_Pos;	
import lombok.Data;	
import org.apache.commons.collections.CollectionUtils;	
	
import java.util.ArrayList;	
import java.util.Collections;	
import java.util.List;	
import java.util.stream.Collectors;	
	
@Data	
public class A3PKSetPosMgr extends AbsPKSetPosMgr {	
	
    private A3PKRoomSet a3pkRoomSet;	
	
    public A3PKSetPosMgr(AbsPKSetRoom set) {	
        super(set);	
        this.a3pkRoomSet = (A3PKRoomSet) set;	
    }	
	
    @Override	
    public List<BasePKSet_Pos> getAllPlayBackNotify() {	
        return this.set.getPosDict().values().stream().filter(k -> null != k).map(k -> k.getPlayBackNotify()).collect(Collectors.toList());	
    }	
	
	
    /**	
     * 三人给一人分数	
     */	
    public void calc3V1CompleteWin(int onePosId, int value) {	
        int sumPoint = (this.getSet().getRoom().getPlayerNum() - 1) * value;	
        A3PKSetPos oneSetPos = (A3PKSetPos) this.getSet().getPKSetPos(onePosId);	
        oneSetPos.setDeductPoint(oneSetPos.getDeductPoint() + sumPoint);	
        this.getSet().getPosDict().entrySet().stream().filter(k -> k.getKey() != onePosId).forEach(k -> k.getValue().setDeductPoint(k.getValue().getDeductPoint() - value));	
        this.setRanksTypePosState(oneSetPos.getRanksType(), A3PKRoomEnum.A3PK_POS_STATE.COMPLETE_WIN, A3PKRoomEnum.A3PK_POS_STATE.COMPLETE_LOSE);	
    }	
	
    /**	
     * 三人给一人分数	
     */	
    public void calc3V1Win(int onePosId, int value) {	
        int sumPoint = (this.getSet().getRoom().getPlayerNum() - 1) * value;	
        A3PKSetPos oneSetPos = (A3PKSetPos) this.getSet().getPKSetPos(onePosId);	
        oneSetPos.setDeductPoint(oneSetPos.getDeductPoint() + sumPoint);	
        this.getSet().getPosDict().entrySet().stream().filter(k -> k.getKey() != onePosId).forEach(k -> k.getValue().setDeductPoint(k.getValue().getDeductPoint() - value));	
        this.setRanksTypePosState(oneSetPos.getRanksType(), A3PKRoomEnum.A3PK_POS_STATE.WIN, A3PKRoomEnum.A3PK_POS_STATE.LOSE);	
    }	
	
    /**	
     * 一人给三人分数	
     */	
    public void calc1V3Lose(int onePosId, int value) {	
        int sumPoint = (this.getSet().getRoom().getPlayerNum() - 1) * value;	
        A3PKSetPos oneSetPos = (A3PKSetPos) this.getSet().getPKSetPos(onePosId);	
        oneSetPos.setDeductPoint(oneSetPos.getDeductPoint() - sumPoint);	
        this.getSet().getPosDict().entrySet().stream().filter(k -> k.getKey() != onePosId).forEach(k -> k.getValue().setDeductPoint(k.getValue().getDeductPoint() + value));	
        this.setRanksTypePosState(oneSetPos.getRanksType(), A3PKRoomEnum.A3PK_POS_STATE.LOSE, A3PKRoomEnum.A3PK_POS_STATE.WIN);	
    }	
	
	
    /**	
     * 一人给三人分数	
     */	
    public void calc1V3CompleteLose(int onePosId, int value) {	
        int sumPoint = (this.getSet().getRoom().getPlayerNum() - 1) * value;	
        A3PKSetPos oneSetPos = (A3PKSetPos) this.getSet().getPKSetPos(onePosId);	
        oneSetPos.setDeductPoint(oneSetPos.getDeductPoint() - sumPoint);	
        this.getSet().getPosDict().entrySet().stream().filter(k -> k.getKey() != onePosId).forEach(k -> k.getValue().setDeductPoint(k.getValue().getDeductPoint() + value));	
        this.setRanksTypePosState(oneSetPos.getRanksType(), A3PKRoomEnum.A3PK_POS_STATE.COMPLETE_LOSE, A3PKRoomEnum.A3PK_POS_STATE.COMPLETE_WIN);	
	
    }	
	
	
	
    /**	
     * 计算2v2	
     *	
     * @param onePosId     头游玩家	
     * @param partnerPosId 头游玩家伙伴	
     */	
    public void calc2V2CompleteWin(int onePosId, int partnerPosId, int value) {	
        this.getSet().getPosDict().entrySet().stream().forEach(k -> {	
            if(onePosId == k.getKey() || partnerPosId == k.getKey()) {	
                k.getValue().setDeductPoint(k.getValue().getDeductPoint() + value);	
                ((A3PKSetPos) k.getValue()).setPosState(A3PKRoomEnum.A3PK_POS_STATE.COMPLETE_WIN);	
            } else {	
                k.getValue().setDeductPoint(k.getValue().getDeductPoint() - value);	
                ((A3PKSetPos) k.getValue()).setPosState(A3PKRoomEnum.A3PK_POS_STATE.COMPLETE_LOSE);	
            }	
        });	
    }	
	
	
    /**	
     * 计算2v2	
     *	
     * @param onePosId     头游玩家	
     * @param partnerPosId 头游玩家伙伴	
     */	
    public void calc2V2Win(int onePosId, int partnerPosId, int value) {	
        this.getSet().getPosDict().entrySet().stream().forEach(k -> {	
            if(onePosId == k.getKey() || partnerPosId == k.getKey()) {	
                k.getValue().setDeductPoint(k.getValue().getDeductPoint() + value);	
                ((A3PKSetPos) k.getValue()).setPosState(A3PKRoomEnum.A3PK_POS_STATE.WIN);	
            } else {	
                k.getValue().setDeductPoint(k.getValue().getDeductPoint() - value);	
                ((A3PKSetPos) k.getValue()).setPosState(A3PKRoomEnum.A3PK_POS_STATE.LOSE);	
            }	
        });	
    }	
	
	
	
    /**	
     * 下局时间	
     */	
    public void getAllTimeSec() {	
        this.getSet().getPosDict().values().stream().forEach(k -> k.getRoomPos().setTimeSec(CommTime.nowSecond()));	
    }	
	
    /**	
     * 设置队伍的输赢状态	
     */	
    public void setRanksTypePosState(int ranksType, A3PKRoomEnum.A3PK_POS_STATE ranksTypeState, A3PKRoomEnum.A3PK_POS_STATE notRanksTypeState) {	
        this.getSet().getPosDict().values().stream().map(p -> ((A3PKSetPos) p)).forEach(k -> {	
            if (ranksType == k.getRanksType()) {	
                k.setPosState(ranksTypeState);	
            } else {	
                k.setPosState(notRanksTypeState);	
            }	
        });	
    }	
	
	
	
	
	
	
	
	
}	
