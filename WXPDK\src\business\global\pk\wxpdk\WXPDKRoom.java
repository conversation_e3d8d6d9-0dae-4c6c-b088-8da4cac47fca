package business.global.pk.wxpdk;

import BaseCommon.CommLog;
import business.global.room.RoomRecordMgr;
import business.global.room.base.AbsRoomPos;
import business.global.room.base.AbsRoomPosMgr;
import business.global.room.pk.PockerRoom;
import business.wxpdk.c2s.cclass.CWXPDK_PiaoHua;
import business.wxpdk.c2s.cclass.WXPDKRoomSetInfo;
import business.wxpdk.c2s.cclass.WXPDKRoom_RecordPosInfo;
import business.wxpdk.c2s.cclass.WXPDK_define;
import business.wxpdk.c2s.cclass.WXPDK_define.WXPDK_WANFA;
import business.wxpdk.c2s.iclass.*;
import cenum.ChatType;
import cenum.ClassType;
import cenum.room.*;
import com.ddm.server.common.CommLogD;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;
import core.network.http.proto.SData_Result;
import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.pk.PKRoom_Record;
import jsproto.c2s.cclass.pk.PKRoom_RecordPosInfo;
import jsproto.c2s.cclass.room.BaseRoomConfigure;
import jsproto.c2s.cclass.room.GetRoomInfo;
import jsproto.c2s.cclass.room.RoomPosInfo;
import jsproto.c2s.iclass.S_GetRoomInfo;
import jsproto.c2s.iclass.room.SBase_Dissolve;
import jsproto.c2s.iclass.room.SBase_PosLeave;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 游戏房间
 *
 * <AUTHOR>
 */
public class WXPDKRoom extends PockerRoom {
    // 房间配置
    private CWXPDK_CreateRoom roomCfg = null;
    private WXPDKConfigMgr configMgr = new WXPDKConfigMgr();
    protected int m_lastWinPos = -1;//记录上局赢家
    public boolean isEnd = false;
    //-1发起解散 0默认 1:同意解散 2:超时解散（阿泽说不用发起解散）
    private List<Integer> jieSanTypeList = new ArrayList<>();
    //是否是超时解散
    private boolean overTimeJieSan = false;

    protected WXPDKRoom(BaseRoomConfigure<CWXPDK_CreateRoom> baseRoomConfigure, String roomKey, long ownerID) {
        super(baseRoomConfigure, roomKey, ownerID);
        initShareBaseCreateRoom(CWXPDK_CreateRoom.class, baseRoomConfigure);
        this.roomCfg = (CWXPDK_CreateRoom) baseRoomConfigure.getBaseCreateRoom();
    }

    public int getLastWinPos() {
        return this.m_lastWinPos >= 0 ? this.m_lastWinPos : 0;
    }

    public void setLastWinPos(int lastWinPos) {
        this.m_lastWinPos = lastWinPos;
    }


    /**
     * 清除记录。
     */
    @Override
    public void clearEndRoom() {
        super.clear();
        this.roomCfg = null;
        this.configMgr = null;
    }

    /**
     * 玩法
     */
    public boolean isWanFaByType(WXPDK_WANFA wanfa) {
        return getRoomCfg().getKexuanwanfa().contains(wanfa.value());
    }

    /**
     * 房间内每个位置信息 管理器
     */
    @Override
    public AbsRoomPosMgr initRoomPosMgr() {
        return new WXPDKRoomPosMgr(this);
    }

    /**
     * 获取房间配置
     *
     * @return
     */
    public CWXPDK_CreateRoom getRoomCfg() {
        if (this.roomCfg == null) {
            initShareBaseCreateRoom(CWXPDK_CreateRoom.class, getBaseRoomConfigure());
            return (CWXPDK_CreateRoom) getBaseRoomConfigure().getBaseCreateRoom();
        }
        return this.roomCfg;
    }


    @SuppressWarnings("unchecked")
    @Override
    public <T> T getCfg() {
        return (T) getRoomCfg();
    }

    @Override
    public String dataJsonCfg() {
        // 获取房间配置
        return new Gson().toJson(this.getRoomCfg());
    }

    @SuppressWarnings("rawtypes")
    @Override
    public GetRoomInfo getRoomInfo(long pid) {
        S_GetRoomInfo ret = new S_GetRoomInfo();
        // 设置房间公共信息
        this.getBaseRoomInfo(ret);
        if (null != this.getCurSet()) {
            ret.setSet(this.getCurSet().getNotify_set(pid));
        } else {
            ret.setSet(new WXPDKRoomSetInfo());
        }
        return ret;
    }

    /**
     * 托管
     *
     * @param roomID      房间id
     * @param pid         pid
     * @param pos         pos
     * @param trusteeship 托管
     * @return {@link BaseSendMsg}
     */
    @Override
    public BaseSendMsg Trusteeship(long roomID, long pid, int pos, boolean trusteeship) {
        SWXPDK_Trusteeship make = SWXPDK_Trusteeship.make(roomID, pid, pos, trusteeship);
        make.secTotal = getCurSet() != null ? ((WXPDKRoomSet) getCurSet()).getTime1(pos) : -1;
        return make;
    }


    @Override
    public BaseSendMsg PosLeave(SBase_PosLeave posLeave) {
        return SWXPDK_PosLeave.make(posLeave);
    }

    @Override
    public BaseSendMsg LostConnect(long roomID, long pid, boolean isLostConnect, boolean isShowLeave) {
        return SWXPDK_LostConnect.make(roomID, pid, isLostConnect, isShowLeave);
    }

    @Override
    public BaseSendMsg PosContinueGame(long roomID, int pos) {
        return SWXPDK_PosContinueGame.make(roomID, pos);
    }

    @Override
    public BaseSendMsg PosUpdate(long roomID, int pos, RoomPosInfo posInfo, int custom) {
        return SWXPDK_PosUpdate.make(roomID, pos, posInfo, custom);
    }

    @Override
    public BaseSendMsg PosReadyChg(long roomID, int pos, boolean isReady) {
        return SWXPDK_PosReadyChg.make(roomID, pos, isReady);
    }

    @Override
    public BaseSendMsg Dissolve(SBase_Dissolve dissolve) {
        return SWXPDK_Dissolve.make(dissolve);
    }

    @Override
    public BaseSendMsg StartVoteDissolve(long roomID, int createPos, int endSec) {
        return SWXPDK_StartVoteDissolve.make(roomID, createPos, endSec);
    }

    @Override
    public BaseSendMsg PosDealVote(long roomID, int pos, boolean agreeDissolve, int endSec) {
        return SWXPDK_PosDealVote.make(roomID, pos, agreeDissolve);
    }

    @Override
    public BaseSendMsg Voice(long roomID, int pos, String url) {
        return SWXPDK_Voice.make(roomID, pos, url);
    }

    @Override
    public <T> BaseSendMsg RoomRecord(List<T> records) {
        return SWXPDK_RoomRecord.make(records);
    }

    @Override
    public void setEndRoom() {
        if (null != this.getCurSet()) {
            if (getHistorySet().size() > 0) {
                // 增加房局记录
                RoomRecordMgr.getInstance().add(this);
                this.getRoomPosMgr().notify2All(SWXPDK_RoomEnd.make(this.getPKRoomRecordInfo()));
                refererReceiveList();
            }
        }
    }

    /**
     * 构建房间回放返回给客户端
     *
     * @return 通知结构体
     */
    public PKRoom_Record getPKRoomRecordInfo() {
        PKRoom_Record pkRoom_record = new PKRoom_Record();
        pkRoom_record.setCnt = this.getHistorySetSize();
        pkRoom_record.recordPosInfosList = this.getRecordPosInfoList();
        pkRoom_record.roomID = this.getRoomID();
        pkRoom_record.endSec = this.getGameRoomBO().getEndTime();
        return pkRoom_record;
    }

    @Override
    protected List<PKRoom_RecordPosInfo> getRecordPosInfoList() {
        List<PKRoom_RecordPosInfo> sRecord = new ArrayList<PKRoom_RecordPosInfo>();
        for (int i = 0; i < this.getPlayerNum(); i++) {
            WXPDKRoomPos roomPos = (WXPDKRoomPos) this.getRoomPosMgr().getPosByPosID(i);
            PKRoom_RecordPosInfo posInfo = roomPos.initAndReturnResult(new WXPDKRoom_RecordPosInfo());
            posInfo.flatCount = roomPos.getFlat();
            posInfo.loseCount = roomPos.getLose();
            posInfo.winCount = roomPos.getWin();

            posInfo.point = roomPos.getPoint();
            posInfo.pos = i;
            posInfo.pid = roomPos.getPid();
            posInfo.setMaxPoint = roomPos.maxPoint;
            posInfo.outBombSize = roomPos.outBombNum;
            posInfo.setSportsPoint(roomPos.sportsPoint());
            sRecord.add(posInfo);
        }
        return sRecord;
    }

    @Override
    public void startNewSet() {
        // 更新连续托管局数
        for (int i = 0; i < getPlayerNum(); i++) {
            WXPDKRoomPos WXPDKRoomPos = (WXPDKRoomPos) getRoomPosMgr().getPosByPosID(i);
            if (WXPDKRoomPos.isTrusteeship()) { // 托管
                WXPDKRoomPos.addTuoGuanSetCount();
            }
        }
        this.setCurSetID(this.getCurSetID() + 1);
        this.createSet();
        // 每个位置，清空准备状态
        this.getRoomPosMgr().clearGameReady();
        //清理开始标志
        ((WXPDKRoomPosMgr) this.getRoomPosMgr()).clearBeginFlag();
    }

    //创建set
    public void createSet() {
        if (null != this.getCurSet()) {
            this.getCurSet().clear();
            this.setCurSet(null);
        }
        this.setCurSet(new WXPDKRoomSet_FJ(this));
        this.getRoomTyepImpl().roomSetIDChange();
    }


    @Override
    public int getPlayingCount() {
        return this.getPlayerNum();
    }

    /**
     * @return configMgr
     */
    public WXPDKConfigMgr getConfigMgr() {
        return configMgr;
    }

    @Override
    public void roomTrusteeship(int pos) {
        ((WXPDKRoomSet) this.getCurSet()).roomTrusteeship(pos);
    }

    @Override
    public void RobotDeal(int pos) {
        ((WXPDKRoomSet) this.getCurSet()).roomTrusteeship(pos);
    }


    @Override
    public void cancelTrusteeship(AbsRoomPos pos) {
        ((WXPDKRoomSet) this.getCurSet()).roomTrusteeship(pos.getPosID());
    }

    @Override
    public boolean isGodCard() {
        // TODO 自动生成的方法存根
        return this.getConfigMgr().isGodCard();
    }

    @Override
    public BaseSendMsg XiPai(long roomID, long pid, ClassType cType) {
        return SWXPDK_XiPai.make(roomID, pid, cType);
    }

    @Override
    public BaseSendMsg ChatMessage(long pid, String name, String content, ChatType type, long toCId, int quickID) {
        return SWXPDK_ChatMessage.make(pid, name, content, type, toCId, quickID);
    }

    @Override
    public boolean isCanChangePlayerNum() {
        return this.getBaseRoomConfigure().getBaseCreateRoom().getFangjian().contains(GameRoomConfigEnum.FangJianQieHuanRenShu.ordinal());
    }

    @Override
    public BaseSendMsg ChangePlayerNum(long roomID, int createPos, int endSec, int playerNum) {
        return SWXPDK_ChangePlayerNum.make(roomID, createPos, endSec, playerNum);
    }

    @Override
    public BaseSendMsg ChangePlayerNumAgree(long roomID, int pos, boolean agreeChange) {
        return SWXPDK_ChangePlayerNumAgree.make(roomID, pos, agreeChange);
    }

    @Override
    public BaseSendMsg ChangeRoomNum(long roomID, String roomKey, int createType) {
        return SWXPDK_ChangeRoomNum.make(roomID, roomKey, createType);
    }

    @Override
    public SData_Result playerReady(boolean isReady, long pid) {
        try {
            lock();
            AbsRoomPos roomPos = this.getRoomPosMgr().getPosByPid(pid);
            if (roomPos == null) {
                // 找不到指定的位置信息
                return SData_Result.make(ErrorCode.NotAllow, "not in pos");
            }
            if (!RoomState.Init.equals(this.getRoomState())) {
                // 房间不处于初始阶段
                return SData_Result.make(ErrorCode.NotAllow, "playerReady RoomState Init :{%s}", this.getRoomState());
            }
            roomPos.setReady(isReady);
            if (this.getRoomPosMgr().isAllReady()) {
                atOnceSetStart();
            }
            return SData_Result.make(ErrorCode.Success);
        } finally {
            unlock();
        }
    }

    /**
     * 立即开始游戏
     */
    public void atOnceSetStart() {
        // 设置房间状态
        this.setRoomState(RoomState.Playing);
        // 创建新亲友圈房间
        getRoomTyepImpl().createNewSetRoom();
        // 游戏开始操作
        startGameBase();
    }

//	/**
//	 * 继续功能
//	 */
//	@Override
//	protected void continueRoom() {
//		ContinueRoomInfo continueRoomInfo=new ContinueRoomInfo();
//		continueRoomInfo.setRoomID(this.getRoomID());
//		continueRoomInfo.setBaseRoomConfigure(this.getBaseRoomConfigure().deepClone());
//		continueRoomInfo.setRoomEndTime(this.getGameRoomBO().getEndTime());
//		continueRoomInfo.setPlayerIDList(this.getRoomPidAll());
//		ContinueRoomInfoMgr.getInstance().putContinueRoomInfo(continueRoomInfo);
//	}

    @Override
    public int getTimerTime() {
        return 200;
    }


    /**
     * 房主是否需要准备
     *
     * @return
     */
    @Override
    public boolean ownerNeedReady() {
        return true;
    }

    @Override
    public boolean autoReadyGame() {
        return !isWanFaByType(WXPDK_WANFA.WXPDK_WANFA_SHOUDONGZHUNBEI);
    }

    /**
     * 存在有玩家离开、踢出清空所有玩家准备状态
     *
     * @return T: 清空,F:不清空
     */
    public boolean existLeaveClearAllPosReady() {
        return true;
    }

    /**
     * 30秒未准备自动退出
     *
     * @return
     */
    @Override
    public boolean is30SencondTimeOut() {
        return getRoomCfg().getGaoji().contains(4);
    }

    /**
     * 是否需要解散次数
     *
     * @return
     */
    @Override
    public boolean needDissolveCount() {
        return true;
    }

    /**
     * 获取解散次数
     *
     * @return
     */
    @Override
    public int getJieShanShu() {
        return 3;
    }

    /**
     * 是否小局自动解散
     *
     * @return boolean
     */
    public boolean isSetAutoJieSan() {
        return this.getBaseRoomConfigure().getBaseCreateRoom().getFangjian().contains(WXPDK_define.WXPDKGameRoomConfigEnum.SetAutoJieSan.ordinal());
    }

    /**
     * 是否小局自动解散
     *
     * @return boolean
     */
    public boolean isSetAutoJieSan2() {
        return this.getBaseRoomConfigure().getBaseCreateRoom().getFangjian().contains(WXPDK_define.WXPDKGameRoomConfigEnum.SetAutoJieSan2.ordinal());
    }

    /**
     * 是否是四带n
     */
    public boolean isSiDaiByNum(int daiNum) {
        return getRoomCfg().sidai.contains((daiNum - 1));
    }

    /**
     * 竞技点能否低于零
     *
     * @returnx
     */
    @Override
    public boolean isRulesOfCanNotBelowZero() {
        return this.isWanFaByType(WXPDK_WANFA.WXPDK_WANFA_NOTBELOWZERO);
    }

    /**
     * 只能赢当前身上分
     *
     * @return
     */
    @Override
    public boolean isOnlyWinRightNowPoint() {
        return isWanFaByType(WXPDK_WANFA.WXPDK_WANFA_OnlyWinRightNowPoint);
    }

    @Override
    public boolean isEnd() {
        return isEnd;
    }

    /**
     * 是否是十进制
     *
     * @return
     */
    public boolean isDecimalism() {
        // TODO 自动生成的方法存根
        return this.getConfigMgr().isDecimalism();
    }

    /**
     * 神牌消息
     *
     * @param msg
     * @param pid
     */
    @Override
    public void godCardMsg(String msg, long pid) {
        if (isGodCard() && "x".equals(msg)) {
            getCurSet().endSet();
        }
    }

    /**
     * 是不是三带1
     *
     * @return
     */
    public boolean isSanDaiOne() {
        return getRoomCfg().sandai.contains(WXPDK_define.WXPDK_SanDai.WXPDK_WANFA_3DAI1.value());
    }

    /**
     * 是不是三带2
     *
     * @return
     */
    public boolean isSanDaiTwo() {
        return getRoomCfg().sandai.contains(WXPDK_define.WXPDK_SanDai.WXPDK_WANFA_3DAI2.value());
    }

    /**
     * 是不是三带1对
     *
     * @return
     */
    public boolean isSanDaiDui() {
        return getRoomCfg().sandai.contains(WXPDK_define.WXPDK_SanDai.WXPDK_WANFA_3DAI21.value());
    }

    /**
     * 是不是三带
     *
     * @return
     */
    public boolean isSanBuDai() {
        return getRoomCfg().sandai.contains(WXPDK_define.WXPDK_SanDai.WXPDK_WANFA_3BUDAI.value());
    }

    /**
     * 是不是4带2对
     *
     * @return
     */
    public boolean isSiDaiTwoDui() {
        return getRoomCfg().sidai.contains(3);
    }


    /**
     * 飘花
     */
    public void opPiaoHua(WebSocketRequest request, long pid, CWXPDK_PiaoHua data) {
        try {
            lock();
            if (null == this.getCurSet()) {
                request.error(ErrorCode.NotAllow, "");
                return;
            }
            WXPDKRoomSet roomSet = (WXPDKRoomSet) this.getCurSet();
            roomSet.opPiaoHua(request, pid, data);
        } catch (Exception e) {
            CommLogD.error(e.getMessage());
        } finally {
            unlock();
        }
    }

    public boolean isPiao() {
        return getRoomCfg().piaoHua == WXPDK_define.WXPDKCfg.Piao.value();
    }

    public boolean gdPiao1() {
        return getRoomCfg().piaoHua == WXPDK_define.WXPDKCfg.GDPiao1.value();
    }

    public boolean gdPiao2() {
        return getRoomCfg().piaoHua == WXPDK_define.WXPDKCfg.GDPiao2.value();
    }

    public boolean gdPiao5() {
        return getRoomCfg().piaoHua == WXPDK_define.WXPDKCfg.GDPiao5.value();
    }

    public boolean isEveryPiao() {
        return getRoomCfg().piaohuaxuanze == WXPDK_define.WXPDKPiaoMoShi.Every.value();
    }

    public boolean is1415JiaKou7() {
        return isWanFaByType(WXPDK_WANFA.WXPDK_WANFA_Leavel1413);
    }

    public boolean isMeiJuHeiTaoSan() {
        return isWanFaByType(WXPDK_WANFA.WXPDK_WANFA_MeiJuHeiTaoSan);
    }

    public List<Integer> getJieSanTypeList() {
        return jieSanTypeList;
    }

    public void setJieSanTypeList(List<Integer> jieSanTypeList) {
        this.jieSanTypeList = jieSanTypeList;
    }

    /**
     * 解散房间同意
     *
     * @param pid 用户ID
     */
    @SuppressWarnings("rawtypes")
    public SData_Result dissolveRoomAgree(long pid) {
        SData_Result result = super.dissolveRoomAgree(pid);
        if(ErrorCode.Success.equals(result.getCode())){
            AbsRoomPos roomPos = this.getRoomPosMgr().getPosByPid(pid);
            if (null == roomPos) {
                // 玩家不存在
                return SData_Result.make(ErrorCode.NotAllow, "dissolveRoomAgree null == roomPos PID:{%d}", pid);
            }
            jieSanTypeList.set(roomPos.getPosID(),1);
        }
        return result;
    }

    /**
     * 解散房间
     *
     * @param pid 用户ID
     */
    @SuppressWarnings("rawtypes")
    public SData_Result dissolveRoom(long pid) {
        SData_Result result = super.dissolveRoom(pid);
        if(ErrorCode.Success.equals(result.getCode())){
            AbsRoomPos roomPos = this.getRoomPosMgr().getPosByPid(pid);
            if (null == roomPos) {
                // 玩家不存在
                return SData_Result.make(ErrorCode.NotAllow, "dissolveRoomAgree null == roomPos PID:{%d}", pid);
            }
            initAllDissolveRoom();
            jieSanTypeList.set(roomPos.getPosID(),-1);
        }
        return result;
    }

    /**
     * 初始化解散
     */
    public void initAllDissolveRoom(){
        jieSanTypeList = new ArrayList<>(Collections.nCopies(getPlayerNum(), 0));
    }

    /**
     * 获取解散状态
     * @return
     */
    public List<Integer> getResolveJieSanList(){
        List<Integer> jieSanTypeList = new ArrayList<>(Collections.nCopies(getPlayerNum(), 0));
        if(getDissolveRoom()!=null){
            if(RoomDissolutionState.Dissolution.equals(getDissolveRoom().getRoomDissolutionState()) || overTimeJieSan){
                jieSanTypeList = getJieSanTypeList();
            }
        }
        return jieSanTypeList;
    }

    /**
     * 检查是否解散房间
     *
     * @return T:解散,F:不解散
     */
    public boolean checkDissolveRoom(int curSec, DissolveType type) {
        if (null == this.getDissolveRoom()) {
            // 没有玩家发起解散。
            return false;
        }
        // 设置托管状态，发起解散
        this.getTrusteeship().setTrusteeshipState(TrusteeshipState.Dissolve);
        // 是否解散房间
        boolean needDissolve = this.getDissolveRoom().isDelay(curSec)
                || this.getDissolveRoom().isAllAgree(type);
        if (needDissolve) {
            this.getTrusteeship().setTrusteeshipState(TrusteeshipState.Wait);
            //超时解散需要更新，正常解散不需要
            if(!this.getDissolveRoom().isAllAgree(type) && this.getDissolveRoom().isDelay(curSec)){
                if(this.getDissolveRoom().isDelay(curSec)){
                    for (int i = 0; i < jieSanTypeList.size(); i++) {
                        if(jieSanTypeList.get(i)==0){
                            jieSanTypeList.set(i,2);
                        }
                    }
                    overTimeJieSan = true;
                }
            }
            // 解散房间
            this.doDissolveRoom(false);
//            CommLog.error("checkDissolveRoom RoomId:{},RoomKey:{},GameId:{},curSetID:{},DissolveMsg:{}", getRoomID(), getRoomKey(), getGameRoomBO().getGameType(), getCurSetID(), this.getDissolveRoom().getDissolveInfoLog());
        }
        return true;
    }
}
