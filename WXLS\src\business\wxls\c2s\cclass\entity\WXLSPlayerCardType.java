package business.wxls.c2s.cclass.entity;	
	
/**	
 * 十三水	
 * 玩家牌的类型	
 * <AUTHOR>	
 *	
 */	
public class WXLSPlayerCardType {	
	private long pid;	
	private int posIdx;	
	private int card = 0;	
	private String specialName="";	
	
	public String getSpecialName() {	
		return specialName;	
	}	
	
	public void setSpecialName(String specialName) {	
		this.specialName = specialName;	
	}	
	
	public WXLSPlayerCardType() {	
		super();	
	}	
		
	public WXLSPlayerCardType(long pid, int posIdx, int card) {	
		super();	
		this.pid = pid;	
		this.posIdx = posIdx;	
		this.card = card;	
	}	
	public WXLSPlayerCardType(long pid, int posIdx, int card, String specialName) {	
		super();	
		this.pid = pid;	
		this.posIdx = posIdx;	
		this.card = card;	
		this.specialName = specialName;	
	}	
	@Override	
	public String toString() {	
		return "WXLSPlayerCardType [pid=" + pid + ", posIdx=" + posIdx + ", card="	
				+ card + "]";	
	}	
	public long getPid() {	
		return pid;	
	}	
	public void setPid(long pid) {	
		this.pid = pid;	
	}	
	public int getPosIdx() {	
		return posIdx;	
	}	
	public void setPosIdx(int posIdx) {	
		this.posIdx = posIdx;	
	}	
	public int getCard() {	
		return card;	
	}	
	public void setCard(int card) {	
		this.card = card;	
	}	
		
		
		
	
}	
