package business.afmj.c2s.iclass;
import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.RoomEndResult;


@SuppressWarnings("serial")
public class SAFMJ_RoomEnd<T> extends BaseSendMsg {
    
    public T record;
	public RoomEndResult<?> sRoomEndResult;

    public static <T> SAFMJ_RoomEnd<T> make(T record, RoomEndResult<?> sRoomEndResult) {
    	SAFMJ_RoomEnd<T> ret = new SAFMJ_RoomEnd<T>();
        ret.record = record;
        ret.sRoomEndResult = sRoomEndResult;
        return ret;
    

    }
}