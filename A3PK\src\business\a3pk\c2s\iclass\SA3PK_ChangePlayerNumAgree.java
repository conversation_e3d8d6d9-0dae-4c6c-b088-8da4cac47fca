package business.a3pk.c2s.iclass;	
	
import jsproto.c2s.cclass.BaseSendMsg;	
	
	
public class SA3PK_ChangePlayerNumAgree extends BaseSendMsg {	
    	
    /**	
	 * 	
	 */	
	private static final long serialVersionUID = 1L;	
	public long roomID;	
    public int pos;	
    public boolean agreeChange;	
    public static SA3PK_ChangePlayerNumAgree make(long roomID, int pos, boolean agreeChange) {	
    	SA3PK_ChangePlayerNumAgree ret = new SA3PK_ChangePlayerNumAgree();	
        ret.roomID = roomID;	
        ret.pos = pos;	
        ret.agreeChange = agreeChange;	
        return ret;	
    	
	
    }	
}	
