package business.ahmj.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-10-23 09:14
 */
public class SAHMJ_OutCards extends BaseSendMsg {
    public List<Integer> cardList = new ArrayList<>();
    public Integer posId;

    public static SAHMJ_OutCards make(List<Integer> cardList, Integer posId) {
        SAHMJ_OutCards ret = new SAHMJ_OutCards();
        ret.cardList = cardList;
        ret.posId = posId;
        return ret;
    }

}
