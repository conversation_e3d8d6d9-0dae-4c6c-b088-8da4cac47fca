package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.Iterator;	
import java.util.Map;	
	
/**	
 * 四套三条	
 */	
public class WXLSSTaoSanTiaoRankingipml_T extends WXLSAbstractRanking {	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        WXLSRankingResult result = null;	
        boolean flag = false;	
        if (player.getCardSize() == 13) {	
            Map<Integer, Integer> rankCount = player.getCardsRankCountMap();	
            int threecnt = 0;	
            Iterator<Map.Entry<Integer, Integer>> it = rankCount.entrySet().iterator();	
            while (it.hasNext()) {	
                Map.Entry<Integer, Integer> entry = it.next();	
                int value = entry.getValue();	
                if (value == 3 || value == 4) {	
                    threecnt++;	
                }	
	
            }	
            int total = threecnt;	
            if (total >= 4) {	
                flag = true;	
            }	
            if (flag) {	
                result = new WXLSRankingResult();	
                result.setPockerCards(player.getCards());	
                result.setRankingEnum(WXLSRankingEnum.STaoSanTiao);	
            }	
        }	
        return result;	
    }	
}	
