package business.wxls.c2s.cclass.entity;	
	
public class WXLSPlayerCard {	
	public long pid;	
	public int posIdx;	
	private WXLSSimpleResult first;	
	private WXLSSimpleResult second;	
	private WXLSSimpleResult third;	
	
	public boolean havemapai= false;	
	
	public WXLSPlayerCard(long pid, int posIdx, WXLSSimpleResult first,	
						  WXLSSimpleResult second, WXLSSimpleResult third, boolean havemapai) {	
		super();	
		this.pid = pid;	
		this.posIdx = posIdx;	
		this.first = first;	
		this.second = second;	
		this.third = third;	
		this.havemapai = havemapai;	
	}	
	public long getPid() {	
		return pid;	
	}	
	public void setPid(long pid) {	
		this.pid = pid;	
	}	
	public int getPosIdx() {	
		return posIdx;	
	}	
	public void setPosIdx(int posIdx) {	
		this.posIdx = posIdx;	
	}	
	
	public WXLSSimpleResult getFirst() {	
		return first;	
	}	
	public void setFirst(WXLSSimpleResult first) {	
		this.first = first;	
	}	
	public WXLSSimpleResult getSecond() {	
		return second;	
	}	
	public void setSecond(WXLSSimpleResult second) {	
		this.second = second;	
	}	
	public WXLSSimpleResult getThird() {	
		return third;	
	}	
	public void setThird(WXLSSimpleResult third) {	
		this.third = third;	
	}	
		
	public boolean containMaPai()	
	{	
		return havemapai;	
	}	
		
	@Override	
	public String toString() {	
		return "WXLSPlayerCard [pid=" + pid + ", posIdx=" + posIdx + ", first="	
				+ first + ", second=" + second + ", third=" + third + "]";	
	}	
		
		
		
		
}	
