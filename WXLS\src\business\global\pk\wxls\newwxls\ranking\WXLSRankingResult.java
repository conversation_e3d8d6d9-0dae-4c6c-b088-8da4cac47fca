package business.global.pk.wxls.newwxls.ranking;	
	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.ArrayList;	
import java.util.List;	
	
public class WXLSRankingResult {	
	
    private WXLSPockerCard highCard; // 5张牌中最大的值	
    private WXLSRankingEnum RankingEnum; // 牌型	
    private List<WXLSPockerCard> pockerCards = new ArrayList<WXLSPockerCard>();	
    public WXLSPockerCard getHighCard() {	
        return highCard;	
    }	
	
    public void setHighCard(WXLSPockerCard highCard) {	
        this.highCard = highCard;	
    }	
	
    public WXLSRankingEnum getRankingEnum() {	
        return RankingEnum;	
    }	
	
    public void setRankingEnum(WXLSRankingEnum RankingEnum) {	
        this.RankingEnum = RankingEnum;	
    }	
	
	
	
    public List<WXLSPockerCard> getPockerCards() {	
        return pockerCards;	
    }	
	
    public void setPockerCards(List<WXLSPockerCard> pockerCards) {	
        this.pockerCards = pockerCards;	
    }	
	
    @Override	
    public String toString() {	
        return "WXLSRankingResult{" +	
                "RankingEnum=" + RankingEnum.getType() +	
                '}';	
    }	
}	
