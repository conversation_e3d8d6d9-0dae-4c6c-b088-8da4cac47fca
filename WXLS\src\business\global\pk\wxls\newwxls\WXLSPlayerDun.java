package business.global.pk.wxls.newwxls;	
	
import business.global.pk.wxls.newwxls.comparing.WXLSComparingFacade;	
import business.global.pk.wxls.newwxls.comparing.WXLSIComparing;	
import business.global.pk.wxls.newwxls.ranking.WXLSRankingResult;	
import business.wxls.c2s.cclass.newwxls.WXLSCardRankEnum;	
import business.wxls.c2s.cclass.newwxls.WXLSCardSuitEnum;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
import business.wxls.c2s.iclass.CWXLS_CreateRoom;	
	
import java.util.*;	
	
public class WXLSPlayerDun implements Comparable<WXLSPlayerDun> {	
    private List<WXLSPockerCard> cards = new ArrayList<WXLSPockerCard>(); // 牌	
    private WXLSRankingResult rankingResult; // 牌型校验结果	
    private int shuicnt = 0;	
    public CWXLS_CreateRoom cfg = null;// 开房配置
    public int playerNum;
	
	
    public WXLSPlayerDun(CWXLS_CreateRoom cfg,int playerNum) {
        this.cfg=cfg;
        this.playerNum=playerNum;
    }	
	
    public WXLSPlayerDun addData(List<WXLSPockerCard> cards) {	
        this.cards = cards;	
        Collections.sort(this.cards);	
        return this;	
    }	
	
    public WXLSPlayerDun(List<String> list,CWXLS_CreateRoom cfg) {	
        this.cards = new ArrayList<WXLSPockerCard>();	
        for (int i = 0; i < list.size(); i++) {	
            this.cards.add(new WXLSPockerCard(list.get(i)));	
        }	
        Collections.sort(this.cards);	
        this.cfg=cfg;	
    }	
	
    public List<String> get0xStr() {	
        List<String> ret = new ArrayList<String>();	
        cards.forEach(WXLSPockerCard -> {	
            ret.add(WXLSPockerCard.toString());	
        });	
        return ret;	
    }	
	
    public int getShuiCount() {	
        return this.shuicnt;	
    }	
	
    public void addShuiCount(int count) {	
        this.shuicnt += count;	
    }

    public int getPlayerNum() {
        return playerNum;
    }

    public void setPlayerNum(int playerNum) {
        this.playerNum = playerNum;
    }

    /**
     * 获得手上的牌的张数	
     *	
     * @return	
     */	
    public int getCardSize() {	
        return this.cards.size();	
    }	
	
    /**	
     * 增加手牌	
     *	
     * @param WXLSPockerCard	
     */	
    public void addCard(WXLSPockerCard WXLSPockerCard) {	
        this.cards.add(WXLSPockerCard);	
        Collections.sort(this.cards);	
    }	
	
    public List<WXLSPockerCard> getCards() {	
        return cards;	
    }	
	
    public List<String> getCardsString() {	
        List<String> ret = new ArrayList<String>();	
        for (int i = 0; i < cards.size(); i++) {	
            ret.add(cards.get(i).toString());	
        }	
        return ret;	
    }	
	
    public ArrayList<WXLSCardRankEnum> getRanks() {	
        ArrayList<WXLSCardRankEnum> ret = new ArrayList<WXLSCardRankEnum>();	
        for (int i = 0; i < cards.size(); i++) {	
                ret.add(cards.get(i).getRank());	
        }	
        return ret;	
    }	
	
    public WXLSRankingResult getRankingResult() {	
        if (rankingResult == null) {	
            rankingResult = new WXLSRankingResult();	
            rankingResult.setRankingEnum(WXLSRankingEnum.HIGH_CARD);	
            rankingResult.setHighCard(this.cards.get(0));	
        }	
        return rankingResult;	
    }	
	
    public Map<Integer, Integer> getCardsRankCountMap() {	
        List<WXLSPockerCard> cards = this.getCards();	
        Map<Integer, Integer> rankCount = new HashMap<Integer, Integer>();	
        for (WXLSPockerCard WXLSPockerCard : cards) {	
                Integer number = new Integer(WXLSPockerCard.getRank().getNumber());	
                if (!rankCount.containsKey(number)) {	
                    rankCount.put(number, 1);	
                } else {	
                    rankCount.put(number, rankCount.get(number) + 1);	
                }	
        }	
        return rankCount;	
    }
    public Integer getCardsRankCountMapOneCard() {
        List<WXLSPockerCard> cards = this.getCards();
        Map<Integer, Integer> rankCount = new HashMap<Integer, Integer>();
        for (WXLSPockerCard WXLSPockerCard : cards) {
            Integer number = new Integer(WXLSPockerCard.getRank().getNumber());
            if (!rankCount.containsKey(number)) {
                rankCount.put(number, 1);
            } else {
                rankCount.put(number, rankCount.get(number) + 1);
            }
        }

        for (WXLSPockerCard WXLSPockerCard : cards) {
            Integer number = new Integer(WXLSPockerCard.getRank().getNumber());
            if(rankCount.get(number).intValue()==1){
                return WXLSPockerCard.type;
            }
        }
        return 0;
    }
    public  List<WXLSPockerCard> getCardsRankCountMapOneCardList() {
        List<WXLSPockerCard> oneList=new ArrayList<>();
        List<WXLSPockerCard> cards = this.getCards();
        Map<Integer, Integer> rankCount = new HashMap<Integer, Integer>();
        for (WXLSPockerCard WXLSPockerCard : cards) {
            Integer number = new Integer(WXLSPockerCard.getRank().getNumber());
            if (!rankCount.containsKey(number)) {
                rankCount.put(number, 1);
            } else {
                rankCount.put(number, rankCount.get(number) + 1);
            }
        }

        for (WXLSPockerCard WXLSPockerCard : cards) {
            Integer number = new Integer(WXLSPockerCard.getRank().getNumber());
            if(rankCount.get(number).intValue()==1){
                oneList.add(WXLSPockerCard);
            }
        }
        return oneList;
    }
    public Map<WXLSCardSuitEnum, List<WXLSPockerCard>> getCardsSuitCountMap() {	
        List<WXLSPockerCard> cards = this.getCards();	
        Map<WXLSCardSuitEnum, List<WXLSPockerCard>> SuitCount = new HashMap<WXLSCardSuitEnum, List<WXLSPockerCard>>();	
        for (WXLSPockerCard WXLSPockerCard : cards) {	
            WXLSCardSuitEnum Suit = WXLSPockerCard.getSuit();	
            if (!SuitCount.containsKey(Suit)) {	
                List<WXLSPockerCard> list = new ArrayList<WXLSPockerCard>();	
                list.add(WXLSPockerCard);	
                SuitCount.put(Suit, list);	
            } else {	
                SuitCount.get(Suit).add(WXLSPockerCard);	
            }	
        }	
        return SuitCount;	
    }	
	
    public int getGuiCount() {	
        return 0;	
    }	
	
    public void setRankingResult(WXLSRankingResult rankingResult) {	
        this.rankingResult = rankingResult;	
    }	
	
    @Override	
    public int compareTo(WXLSPlayerDun o) {	
        if (o == null)	
            return 0;	
        int selfPriority = this.getRankingResult().getRankingEnum().getPriority();	
        int otherPriority = o.getRankingResult().getRankingEnum().getPriority();	
	
        if (selfPriority < otherPriority) {	
            return 1;	
        }	
        if (selfPriority > otherPriority) {	
            return -1;	
        }	
	
        if (selfPriority == otherPriority) {	
            WXLSIComparing cmp = WXLSComparingFacade.getComparing(this.getRankingResult().getRankingEnum());	
            return cmp.compare(this, o);	
        }	
        return 0;	
    }	
	
    @Override	
    public String toString() {	
        return "Player{" + "cards=" + cards + ", rankingResult=" + rankingResult + '}';	
    }	
	
	
}	
