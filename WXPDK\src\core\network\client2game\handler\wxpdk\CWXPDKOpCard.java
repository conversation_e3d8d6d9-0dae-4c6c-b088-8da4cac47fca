package core.network.client2game.handler.wxpdk;

import java.io.IOException;

import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.exception.WSException;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;

import business.wxpdk.c2s.iclass.CWXPDK_OpCard;
import business.global.pk.wxpdk.WXPDKRoom;
import business.global.pk.wxpdk.WXPDKRoomSet;
import business.global.pk.wxpdk.WXPDKRoomSetSound;
import business.global.room.RoomMgr;
import business.player.Player;
import core.network.client2game.handler.PlayerHandler;

/**
 * 操作牌
 * */

public class CWXPDKOpCard extends PlayerHandler{

	@Override
	public void handle(Player player, WebSocketRequest request, String message) throws WSException, IOException {
		final CWXPDK_OpCard clientPack = new Gson().from<PERSON>son(message, CWXPDK_OpCard.class);
    	
		WXPDKRoom room = (WXPDKRoom) RoomMgr.getInstance().getRoom(clientPack.roomID);
    	if (null == room){
    		request.error(ErrorCode.NotAllow, "CWXPDKOpenCard not find room:"+clientPack.roomID);
    		return;
    	}
    	WXPDKRoomSet set =  (WXPDKRoomSet) room.getCurSet();
    	if(null == set){
    		request.error(ErrorCode.NotAllow, "CWXPDKOpenCard not set room:"+clientPack.roomID);
    		return;
    	}
    	WXPDKRoomSetSound sound = set.getCurRound();
    	if(null == sound){
    		request.error(ErrorCode.NotAllow, "CWXPDKOpenCard not sound room:"+clientPack.roomID);
    		return;
    	}
    	sound.onOpCard(request,  clientPack,true);
	}
}
