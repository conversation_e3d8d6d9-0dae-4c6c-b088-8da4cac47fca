package business.wxls.c2s.iclass;	
	
	
import business.wxls.c2s.cclass.SWXLS_RankingResult;	
import business.wxls.c2s.cclass.entity.WXLSPlayerResult;	
import jsproto.c2s.iclass.S_GetRoomInfo;	
	
import java.util.ArrayList;	
import java.util.List;	
	
public class SWXLS_GetRoomInfo extends S_GetRoomInfo {	
    public List<WXLSPlayerResult> posResultList= new ArrayList<>();	
    public long  zjid=0;	
    public  List<CWXLS_Ranked> rankeds= new ArrayList<>();	
    public int  beishu=0;	
    public SWXLS_RankingResult rankingResults;	
    public List<WXLSPlayerResult> getPosResultList() {	
        return posResultList;	
    }	
	
    public void setPosResultList(List<WXLSPlayerResult> posResultList) {	
        this.posResultList = posResultList;	
    }	
	
    public long getZjid() {	
        return zjid;	
    }	
	
    public void setZjid(long zjid) {	
        this.zjid = zjid;	
    }	
	
    public List<CWXLS_Ranked> getRankeds() {	
        return rankeds;	
    }	
	
    public void setRankeds(List<CWXLS_Ranked> rankeds) {	
        this.rankeds = rankeds;	
    }	
	
    public int getBeishu() {	
        return beishu;	
    }	
	
    public void setBeishu(int beishu) {	
        this.beishu = beishu;	
    }	
}	
