package business.global.mj.ahmj;

import business.ahmj.c2s.cclass.AHMJPointItem;
import business.global.mj.*;
import business.global.mj.robot.MJRobotOpCard;
import business.global.mj.set.MJOpCard;
import business.ahmj.c2s.cclass.AHMJRoom_RoundPos;
import business.ahmj.c2s.iclass.SAHMJ_FlipCards;
import business.ahmj.c2s.iclass.SAHMJ_PosOpCard;
import business.ahmj.c2s.iclass.SAHMJ_StartRound;
import cenum.mj.MJHuOpType;
import cenum.mj.MJOpCardError;
import cenum.mj.OpType;
import com.ddm.server.common.utils.CommTime;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.ddm.server.websocket.handler.requset.WebSocketRequestDelegate;
import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.mj.BaseMJRoom_SetRound;
import jsproto.c2s.cclass.mj.BaseMJSet_Pos;
import jsproto.c2s.cclass.mj.NextOpType;
import jsproto.c2s.cclass.mj.NextRoundOpPos;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 靖州麻将 回合逻辑 每一次等待操作，都是一个round
 *
 * <AUTHOR>
 */

public class AHMJSetRound extends AbsMJSetRound {
    // 回合开始时间
    protected long startTimeHs;

    public AHMJSetRound(AbsMJSetRoom set, int roundID) {
        super(set, roundID);
        this.startTimeHs = CommTime.nowMS();
    }

    @Override
    public int opCard(WebSocketRequest request, int opPos, OpType opType, MJOpCard mOpCard) {
        return opCard(request, opPos, opType, mOpCard, false);
    }

    public synchronized int opCard(WebSocketRequest request, int opPos, OpType opType, MJOpCard mOpCard, boolean isFlash) {
        if (this.getEndTime() > 0) {
            request.error(ErrorCode.NotAllow, "end Time opPos has no round power");
            return MJOpCardError.ERROR_OP_TYPE.value();
        }
        AbsMJRoundPos pos = this.roundPosDict.get(opPos);
        if (null == pos) {
            request.error(ErrorCode.NotAllow, "opPos has no round power");
            return MJOpCardError.ROUND_POS_ERROR.value();
        }
        int opCardRet = pos.op(request, opType, mOpCard);
        if (opCardRet >= 0) {
            // 位置操作牌
            this.posOpCardRet(opCardRet, isFlash);
        }
        return opCardRet;
    }

    /**
     * 位置操作牌
     *
     * @param opPosRet 操作位置
     * @param isFlash  是否动画
     */
    protected void posOpCardRet(int opPosRet, boolean isFlash) {
        int opCardID = this.set.getLastOpInfo().getLastOutCard();
        AbsMJSetPos sPos = this.set.getMJSetPos(opPosRet);
        sPos.getPosOpNotice().clearTingCardMap();
        // 刷新可胡列表
        this.refreshHuCardTypes(sPos);
        // 吃碰杠-清理牌
        if (OpType.Peng.equals(this.getOpType()) || OpType.JieGang.equals(this.getOpType())
                || OpType.Chi.equals(this.getOpType())) {
            if (OpType.Peng.equals(this.getOpType()) || OpType.JieGang.equals(this.getOpType())) {
                // 主要是跟打清空使用。 清空打牌信息
                this.cleanOutCardInfo();
            }
            this.set.getLastOpInfo().clearLastOutCard();

            if (this.checkExistClearPass()) {
                // 过手
                sPos.clearPass();
                // 漏过的玩家
                this.passLeak(opCardID, sPos.getPosID());
            }
            this.set.getSetPosMgr().clearOpTypeInfoList();
        }
        // 补杠、暗杠时候，操作牌ID == 0
        if (OpType.Gang.equals(this.getOpType()) || OpType.AnGang.equals(this.getOpType())) {
            opCardID = 0;
        }
        int outPos = -1;
        List<Integer> flipCardList = new ArrayList<>();
        AHMJRoomSet set = (AHMJRoomSet) this.set;
        //过后清除翻开的牌，加入打牌区
        if (OpType.SQPass.equals(this.getOpType()) && ((AHMJRoomSet) this.set).getWaitFlipPos() == -1) {
            if (CollectionUtils.isNotEmpty(set.getFlipCardList())) {
                for (Integer card : set.getFlipCardList()) {
                    //剩余牌加入打牌区域
                    int ownnerPos = sPos.getMJSetCard().getCardByID(card).ownnerPos;
                    AHMJSetPos mjSetPos = (AHMJSetPos) set.getMJSetPos(ownnerPos);
                    mjSetPos.addOutCardIDs(card);
                    flipCardList.add(card);
                    outPos = ownnerPos;
                }
            }
            ((AHMJRoomSet) this.set).clearFlipCardList();
        }
        this.setExeOpPos(opPosRet);
        BaseMJSet_Pos posInfoOther = sPos.getNotify(false);
        BaseMJSet_Pos posInfoSelf = sPos.getNotify(true);
        this.set.getRoomPlayBack().playBack2Pos(opPosRet, this.posOpCard(this.room.getRoomID(), opPosRet, posInfoSelf, this.getOpType(), opCardID, isFlash, outPos, flipCardList), this.set.getSetPosMgr().getAllPlayBackNotify());
        this.set.getRoom().getRoomPosMgr().notify2ExcludePosID(opPosRet, this.posOpCard(this.room.getRoomID(), opPosRet, posInfoOther, this.getOpType(), opCardID, isFlash, outPos, flipCardList));
    }

    /**
     * 获取本轮信息
     *
     * @param pos 位置
     * @return
     */
    @Override
    public BaseMJRoom_SetRound getNotify_RoundInfo(int pos) {
        ret = new BaseMJRoom_SetRound();
        ret.setWaitID(this.roundID);
        ret.setStartWaitSec(this.startTime);
        ret.setRunWaitSec(CommTime.nowSecond() - this.startTime);
        for (AbsMJRoundPos roundPos : this.roundPosDict.values()) {
            if (roundPos.getOpType() != null) {
                continue;
            }
            // 自己 或 公开
            if (pos == roundPos.getOpPos() || roundPos.isPublicWait()) {
                AHMJRoom_RoundPos data = new AHMJRoom_RoundPos();
                boolean isSelf = pos == roundPos.getOpPos();
                data.setOpList(roundPos.getRecieveOpTypes());
                data.setChiList(roundPos.getPos().getPosOpNotice().getChiList());
                data.setLastOpCard(roundPos.getLastOutCard());
                data.setWaitOpPos(roundPos.getOpPos());
                data.setTingCardMap(isSelf ? roundPos.getPos().getPosOpNotice().getTingCardMap() : null);
                List<Integer> gangList = new ArrayList<>();
                gangList.addAll(isSelf ? ((AHMJSetPos) roundPos.getPos()).getAnGangList() : new ArrayList<>());
                gangList.addAll(isSelf ? ((AHMJSetPos) roundPos.getPos()).getBuGangList() : new ArrayList<>());
                data.setGangList(isSelf ? gangList : null);
                boolean allKing = roundPos.getPos().allCards().stream().allMatch((n -> set.getmJinCardInfo().checkJinExist(n.getType())));
                data.setAllKing(isSelf && allKing);
                ret.addOpPosList(data);
                // 设置动作列表
                roundPos.getPos().getPosOpRecord().setOpList(data.getOpList());
                if (room.isConnectClearTrusteeship()) {
                    // 重新记录打牌时间
                    roundPos.getPos().getRoomPos().setLatelyOutCardTime(CommTime.nowMS());
                }
                // 设置最后操作时间
                this.set.getLastOpInfo().setLastShotTime(CommTime.nowSecond());

            }
        }
        return ret;
    }

    /**
     * 自动打牌
     *
     * @param sec
     * @return
     */
    @Override
    protected boolean autoOutCard(int sec) {
        if (CommTime.nowMS() - this.startTimeHs < 500) {
            return false;
        }
        AHMJRoundPos roundPos;
        AHMJSetPos sPos;
        for (int posID = 0; posID < this.room.getPlayerNum(); posID++) {
            roundPos = (AHMJRoundPos) this.roundPosDict.get(posID);
            if (null == roundPos) {
                continue;
            }
            sPos = (AHMJSetPos) roundPos.getPos();
            if (null == sPos || sPos.getRoomPos().isTrusteeship()) {
                continue;
            }

            List<OpType> opList = roundPos.getRecieveOpTypes();
            if (null == opList || opList.size() <= 0) {
                continue;
            }
            if (sPos.isTing()) {
                if (opList.contains(OpType.Hu) || opList.contains(OpType.JiePao) || opList.contains(OpType.YaoGangHu) || opList.contains(OpType.QiangGangHu)) {
                    continue;
                }
                int cardID;
                if (opList.contains(OpType.Out)) {
                    if (null != sPos.getHandCard()) {
                        cardID = sPos.getHandCard().cardID;
                    } else {
                        continue;
                    }
                    this.opCard(new WebSocketRequestDelegate(), roundPos.getOpPos(), OpType.Out, MJOpCard.OpCard(cardID));
                } else {
                    if (opList.contains(OpType.Pass)) {
                        this.opCard(new WebSocketRequestDelegate(), roundPos.getOpPos(), OpType.Pass, MJOpCard.OpCard(0));
                    } else if (opList.contains(OpType.SQPass)) {
                        this.opCard(new WebSocketRequestDelegate(), roundPos.getOpPos(), OpType.SQPass, MJOpCard.OpCard(0));
                    }
                }
            }
        }
        return false;
    }

    @Override
    protected AbsMJRoundPos nextRoundPos(int pos) {
        return new AHMJRoundPos(this, pos);
    }

    // 尝试开始回合, 如果失败，则set结束
    public boolean tryStartRound() {
        if (getPreRound() != null && (getPreRound().getOpType() == OpType.Gang || getPreRound().getOpType() == OpType.JieGang
                || getPreRound().getOpType() == OpType.AnGang || ((AHMJRoomSet) set).getWaitFlipPos() != -1)) {
            return tryStartRoundYaoGang(getPreRound());
        }
        return super.tryStartRound();
    }

    /**
     * 开始本回合,并摸牌
     *
     * @param pos
     * @param isNormalMo
     * @return
     */
    public boolean startWithGetCard(int pos, boolean isNormalMo) {
        if (null == this.set.getMJSetPos(pos).getHandCard()) { // 作弊情况下，已经有手牌
            if (null == this.set.getCard(pos, isNormalMo)) {
                return false;
            }
        }
        if (this.set.isAtFirstHu()) {
            // 检查动作
            this.set.getSetPosMgr().checkOpType(this.set.getDPos(), 0, OpType.TianHu);
            // 获取可指定动作信息
            NextOpType nOpType = this.set.getSetPosMgr().exeCardAction(OpType.TianHu);
            if (null != nOpType) {
                // 下回合操作者
                for (int posID : nOpType.getPosOpTypeListMap().keySet()) {
                    AbsMJRoundPos nextPos = this.nextRoundPos(posID);
                    if (nOpType.getPosOpTypeListMap().containsKey(posID)) {
                        nextPos.addOpType(nOpType.getPosOpTypeListMap().get(posID));
                    }
                    nextPos.addOpType(OpType.Pass);
                    this.roundPosDict.put(nextPos.getOpPos(), nextPos);
                }
                this.set.setAtFirstHu(false);
                return true;
            }
            this.set.setAtFirstHu(false);
        }
        return MJRoundPos(pos);
    }

    /**
     * 尝试其他回合
     *
     * @param preRound 上回合
     * @return
     */
    @Override
    protected boolean tryStartRoundOther(AbsMJSetRound preRound) {
        // 上一轮暗杠，本轮继续抓牌
        if (preRound.getOpType() == OpType.SQPass) {
            //	如果都不能胡，则需要把该三张牌（或两张）直接打出去在桌面，此后开启系统打牌模式，摸什么打什么；
            AHMJSetPos setPos = (AHMJSetPos) this.set.getMJSetPos(this.set.getLastOpInfo().getLastOpPos());
            setPos.setTing(true);
            // 继续抓牌
            int opPos = (this.set.getLastOpInfo().getLastOpPos() + 1) % this.room.getPlayerNum();
            if (!startWithGetCard(opPos, true)) {
                return false;
            }
            notifyStart();
            return true;
        }
        //翻牌后出牌
        if (preRound.getOpType() == OpType.WaitOut) {
            AHMJSetPos setPos = (AHMJSetPos) set.getMJSetPos(preRound.getExeOpPos());
            //无人接手放入打牌区
            notifyPop(setPos, OpType.Out);
            setPos.setTing(true);
            this.setOpType(OpType.Out);
            this.setExeOpPos(setPos.getPosID());
            //手动设置结束时间 方便进入下一个
            this.updateEndTime();
            ((AHMJRoomSet) getSet()).clearFlipCardList();
            return true;
        }
        return false;
    }

    /**
     * 摇杠进行补牌
     *
     * @param preRound
     * @return
     */
    private boolean tryStartRoundYaoGang(AbsMJSetRound preRound) {
        AHMJRoomSet roomSet = (AHMJRoomSet) set;
        // 继续抓牌
        int opPos = ((AHMJRoomSet) set).getWaitFlipPos() != -1 ? roomSet.getWaitFlipPos() : preRound.getExeOpPos();
        if (preRound.checkExistNextRoundOp()) {
            //杠
            NextRoundOpPos<AbsMJSetRound> curOpPos = preRound.removeNextRountOp();
            curOpPos.getGetPosOpTypeListMap().entrySet().stream().forEach(entrySet -> {
                AbsMJRoundPos nextPos = this.nextRoundPos(entrySet.getKey());
                nextPos.addOpType(entrySet.getValue());
                nextPos.addOpType(OpType.SQPass);
                this.roundPosDict.put(nextPos.getOpPos(), nextPos);
            });
            roomSet.setWaitFlipPos(opPos);
            this.waitDealRound = preRound; // 本轮，接手处理 preRound
            this.notifyStart();
            return true;
        }
        // 作弊情况下，已经有手牌
        List<MJCard> cardList = null;
        AbsMJSetPos setPos = this.set.getMJSetPos(opPos);
        if (Objects.isNull(setPos.getHandCard())) {
            setPos.addPrivateCard(setPos.getHandCard());
            setPos.cleanHandCard();
        }
        if (Objects.isNull(setPos.getHandCard())) {
            cardList = ((AHMJRoomSet) set).flipCards(opPos);
        }
        if (CollectionUtils.isEmpty(cardList)) {
            return false;
        }
        List<Integer> cardIDList = cardList.stream().map(MJCard::getCardID).collect(Collectors.toList());
        roomSet.setFlipCardList(cardIDList);
        roomSet.setFlipCardPos(opPos);
        set.getRoomPlayBack().playBack2All(SAHMJ_FlipCards.make(cardIDList, opPos));
        //检测 胡
        AHMJSetPosMgr setPosMgr = (AHMJSetPosMgr) getSet().getSetPosMgr();
        //摇杠 杠上开花
        for (int i = 0; i < this.set.getRoom().getPlayerNum(); i++) {
            int nextPos = (opPos + i) % this.set.getRoom().getPlayerNum();
            AbsMJSetPos mjSetPos1 = this.set.getMJSetPos(nextPos);
            for (Integer n : cardIDList) {
                mjSetPos1.clearMCardInit();
                AHMJPointItem itemOld = (AHMJPointItem) mjSetPos1.getOpHuType();
                if (mjSetPos1.checkOpType(n, i == 0 ? OpType.GSKH : OpType.YaoGangHu)) {
                    AHMJPointItem itemNew = (AHMJPointItem) mjSetPos1.getOpHuType();
                    if (Objects.isNull(itemOld) || itemNew.getPoint() > itemOld.getPoint()) {
                        mjSetPos1.setOpHuType(itemNew);
                    }
                    AbsMJRoundPos tmPos = this.nextRoundPos(nextPos);
                    if (!tmPos.checkRecieveOpTypes(OpType.SQPass)) {
                        if (i == 0) {
                            mjSetPos1.setmHuOpType(MJHuOpType.ZiMo);
                            tmPos.addOpType(OpType.YaoGangHu);
                            setPosMgr.addOpTypeInfo1(nextPos, OpType.YaoGangHu);
                        } else {
                            mjSetPos1.setmHuOpType(MJHuOpType.JiePao);
                            tmPos.addOpType(OpType.YaoGangHu);
                            setPosMgr.addOpTypeInfo1(nextPos, OpType.YaoGangHu);
                        }
                        tmPos.addOpType(OpType.SQPass);
                        this.roundPosDict.put(tmPos.getOpPos(), tmPos);
                    }
                }
            }
        }
        if (MapUtils.isEmpty(roundPosDict)) {
            this.setOpType(OpType.WaitOut);
            this.setExeOpPos(opPos);
            //手动设置结束时间 方便进入下一个
            this.updateEndTime();
            return true;
        }
        // 记录最近操作玩家位置
        set.getLastOpInfo().setLastOpPos(opPos);
        // 记录抢杠胡，被抢的牌
        set.getLastOpInfo().setLastOpCard(this.opCard);
        ((AHMJRoomSet) set).setWaitFlipPos(-1);
        notifyStart();
        return true;
    }

    private void notifyPop(AbsMJSetPos setPos, OpType opType) {
        AHMJRoomSet roomSet = ((AHMJRoomSet) this.set);
        if (CollectionUtils.isNotEmpty(roomSet.getFlipCardList())) {
            roomSet.getFlipCardList().forEach(setPos::addOutCardIDs);
        }
        BaseMJSet_Pos posInfoOther = setPos.getNotify(false);
        BaseMJSet_Pos posInfoSelf = setPos.getNotify(true);
        this.set.getRoomPlayBack().playBack2Pos(setPos.getPosID(), this.posOpCard(this.room.getRoomID(), setPos.getPosID(), posInfoSelf, opType, 0, false), set.getSetPosMgr().getAllPlayBackNotify());
        this.set.getRoom().getRoomPosMgr().notify2ExcludePosID(setPos.getPosID(), this.posOpCard(this.room.getRoomID(), setPos.getPosID(), posInfoOther, opType, 0, false));
    }


    @Override
    protected boolean checkExistClearPass() {
        return false;
    }

    @Override
    protected <T> BaseSendMsg startRound(long roomID, T room_SetWait) {
        return SAHMJ_StartRound.make(roomID, room_SetWait);
    }

    @Override
    protected <T> BaseSendMsg posOpCard(long roomID, int pos, T set_Pos, OpType opType, int opCard,
                                        boolean isFlash) {
        return SAHMJ_PosOpCard.make(roomID, pos, set_Pos, opType, opCard, isFlash, ((AHMJRoomSet) set).getFlipCardList());
    }

    protected <T> BaseSendMsg posOpCard(long roomID, int pos, T set_Pos, OpType opType, int opCard,
                                        boolean isFlash, int outCardPos, List<Integer> flipCardList) {
        if (outCardPos >= 0) {
            AbsMJSetPos setPos = set.getMJSetPos(outCardPos);
            List<Integer> outCardList = Objects.isNull(setPos) ? new ArrayList<>() : setPos.getOutCardIDs();
            return SAHMJ_PosOpCard.make(roomID, pos, set_Pos, opType, opCard, isFlash, flipCardList, outCardPos, outCardList);
        } else {
            return SAHMJ_PosOpCard.make(roomID, pos, set_Pos, opType, opCard, isFlash, flipCardList);
        }
    }

    /**
     * 机器人操作
     *
     * @param posID
     */
    public void RobothandCrad(int posID) {
        if (this.getEndTime() > 0) {
            return;
        }
        if (this.getRoundPosDict().containsKey(posID)) {
            new AHMJRobotOpCard(this).RobothandCrad(posID);
        }
    }

    /**
     * 更新当前回合操作。
     *
     * @param sec
     * @return
     */
    public boolean update(int sec) {
        // 记录更新时间
        this.setUpdateTime(sec);
        // 已经结束
        if (this.endTime != 0) {
            return this.endTime >= this.startTime && checkOpType(CommTime.nowMS(), this.endTimeMillis);
        }
        // 自动打牌
        return this.autoOutCard(sec);
    }

    private boolean checkOpType(long nowMs, long endTimeMS) {
        if (opType == OpType.WaitOut) {
            return nowMs - endTimeMS >= 700;
        }
        return true;
    }

}
					
				
				
