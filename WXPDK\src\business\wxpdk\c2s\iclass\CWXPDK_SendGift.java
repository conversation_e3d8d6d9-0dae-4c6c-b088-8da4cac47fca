package business.wxpdk.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;

@SuppressWarnings("serial")
public class CWXPDK_SendGift extends BaseSendMsg {
	public long roomID;
	public int pos;
	public long productId;

    public static CWXPDK_SendGift make(long roomID, int pos, long productId) {
    	CWXPDK_SendGift ret = new CWXPDK_SendGift();
        ret.roomID = roomID;
        ret.pos = pos;
        ret.productId = productId;
        return ret;
    }
}
