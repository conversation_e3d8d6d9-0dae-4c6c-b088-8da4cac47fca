package business.global.mj.ahmj;

import business.ahmj.c2s.cclass.AHMJPointItem;
import business.global.mj.AbsCalcPosEnd;
import business.global.mj.AbsMJSetPos;
import cenum.mj.HuType;
import cenum.mj.MJHuOpType;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import business.global.mj.ahmj.AHMJRoomEnum.AHMJOpPoint;
import business.global.mj.ahmj.AHMJRoomEnum.AHMJEndType;
import org.apache.commons.collections.CollectionUtils;

/**
 * 青岛 算分
 *
 * <AUTHOR>
 */
public class AHMJCalcPosEnd extends AbsCalcPosEnd {
    private AHMJSetPos mSetPos;
    // 动作map
    private Map<AHMJOpPoint, Integer> huTypeMap = new LinkedHashMap<>();
    private AHMJRoom room;
    private AHMJRoomSet set;
    /**
     * @param mSetPos
     */
    public AHMJCalcPosEnd(AHMJSetPos mSetPos) {
        super(mSetPos);
        this.mSetPos = mSetPos;
        this.room = (AHMJRoom) mSetPos.getRoom();
        this.set = (AHMJRoomSet) mSetPos.getSet();
    }

    @Override
    public void calcPosEnd(AbsMJSetPos mSetPos) {
        this.calcPosEndYiKao();
    }

    @Override
    public void calcPosPoint(AbsMJSetPos mSetPos) {
        // 慌庄  不算任何分
        if (mSetPos.getSet().getMHuInfo().isHuEmpty()) {
            return;
        }
        // 计算胡分数
        this.calcHu();
    }

    /**
     * 计算动作分数类型
     */
    @Override
    public <T> void calcOpPointType(T opType, int count) {
        AHMJOpPoint opPoint = (AHMJOpPoint) opType;
        if(AHMJOpPoint.Not.equals(opPoint) || count == 0){
            return;
        }
        // 添加胡类型
        this.addhuType(opPoint, count, AHMJEndType.PLUS);
    }

    /**
     * 计算分数
     */
    private void calcHu() {
        switch (this.mSetPos.getHuType()) {
            case NotHu:
            case DianPao:
                break;
            case ZiMo:
            case JiePao:
            case QGH:
                this.calcHuPoint();
                break;
            default:
                break;
        }
    }

    /**
     * 计算胡分
     * 	仅胡牌玩家有的分：买码、牌型分、打宝分、杠分；（自摸每家出，点炮一家出）
     */
    private void calcHuPoint() {
        if (HuType.NotHu.equals(this.getMSetPos().getHuType()) || HuType.DianPao.equals(this.getMSetPos().getHuType())) {
            // 没有胡牌
            return;
        }
        //中鸟分
        int multiple = 1;
        if (CollectionUtils.isNotEmpty(mSetPos.getZhongNiaoList())) {
            multiple = (int) Math.pow(2, mSetPos.getZhongNiaoList().size());
            huTypeMap.put(AHMJOpPoint.ZhongNiao, multiple);
        }

        AHMJPointItem item = (AHMJPointItem) this.mSetPos.getOpHuType();
        if (Objects.isNull(item)) {
            return;
        }
        int point = 0;
        if(!item.getAHMJOpPoints().contains(AHMJOpPoint.PingHu)){
            //非小胡
            int daHuNum = item.getPoint() / 4;
            if(daHuNum>0){
                point = 4* (int)Math.pow(2,daHuNum-1);
            }
        }else{
            point = item.getPoint();
        }

        if (MJHuOpType.JiePao.equals(this.getMSetPos().getmHuOpType())) {
            this.setDianPao();
            mSetPos.setHuType(HuType.JiePao);
            AbsMJSetPos dianPaoPos = this.mSetPos.getMJSetPos(this.mSetPos.getSet().getLastOpInfo().getLastOpPos());
            this.calc1V1Op(dianPaoPos.getPosID(),AHMJOpPoint.Not, point*multiple);
            this.calc1V1Op(dianPaoPos.getPosID(),AHMJOpPoint.DaNiao, getDaNiaoPoint(dianPaoPos, mSetPos));
        } else if (MJHuOpType.QGHu.equals(this.getMSetPos().getmHuOpType())) {
            mSetPos.setHuType(HuType.QGH);
            this.setDianPao();
            AbsMJSetPos dianPaoPos = this.mSetPos.getMJSetPos(this.mSetPos.getSet().getLastOpInfo().getLastOpPos());
            this.calc1V1Op(this.mSetPos.getSet().getLastOpInfo().getLastOpPos(),AHMJOpPoint.Not, point*multiple);
            this.calc1V1Op(dianPaoPos.getPosID(),AHMJOpPoint.DaNiao, getDaNiaoPoint(dianPaoPos, mSetPos));
        } else {
            this.calc1V3Op(AHMJOpPoint.Not, point*multiple);
            set.getPosDict().values().forEach(n->{
                if(n.getPid()!=getMSetPos().getPid()){
                    AbsMJSetPos loserPos = this.mSetPos.getMJSetPos(n.getPosID());
                    this.calc1V1Op(loserPos.getPosID(),AHMJOpPoint.DaNiao, getDaNiaoPoint(loserPos, mSetPos));
                }
            });
        }
        huTypeMap.putAll(item.getMap());
    }

    /**
     * 获取打鸟分
     *
     * @param setPos1 设置pos1
     * @param setPos2 设置pos2
     */
    private int getDaNiaoPoint(AbsMJSetPos setPos1,AbsMJSetPos setPos2) {
        AHMJRoomPos roomPos1 = (AHMJRoomPos) setPos1.getRoomPos();
        AHMJRoomPos roomPos2 = (AHMJRoomPos) setPos2.getRoomPos();
        int daNiaoPoint = roomPos1.getDaNiao().getValue() + roomPos2.getDaNiao().getValue();
        return daNiaoPoint>0?daNiaoPoint:0;
    }

    /**
     * 添加胡类型
     *
     * @param opPoint
     * @param point
     * @param bEndType
     */
    private void addhuType(AHMJOpPoint opPoint, int point, AHMJEndType bEndType) {
        if (this.huTypeMap.containsKey(opPoint)) {
            // 累计
            int calcPoint = point;
            if (AHMJEndType.PLUS.equals(bEndType)) {
                calcPoint = this.huTypeMap.get(opPoint) + point;
            } else if (AHMJEndType.MULTIPLY.equals(bEndType)) {
                calcPoint = this.huTypeMap.get(opPoint) * point;
            }
            this.huTypeMap.put(opPoint, calcPoint);
        } else {
            this.huTypeMap.put(opPoint, point);
        }
    }

    @Override
    public CalcPosEnd getCalcPosEnd() {
        return new CalcPosEnd(this.huTypeMap);
    }

    @Override
    public int calcPoint(boolean isZhuang, Object... params) {
        return 0;
    }

    private class CalcPosEnd {
        @SuppressWarnings("unused")
        private Map<AHMJOpPoint, Integer> huTypeMap;

        public CalcPosEnd(Map<AHMJOpPoint, Integer> huTypeMap) {
            this.huTypeMap = huTypeMap;
        }
    }

}	
