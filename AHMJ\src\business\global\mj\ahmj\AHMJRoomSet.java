package business.global.mj.ahmj;

import business.global.mj.*;
import business.global.mj.ahmj.optype.AHMJKaiJinJJImpl;
import business.global.mj.manage.MJFactory;
import business.global.room.base.AbsRoomPos;
import business.global.room.mj.MahjongRoom;
import business.ahmj.c2s.cclass.AHMJRoomSetEnd;
import business.ahmj.c2s.cclass.AHMJRoomSetInfo;
import business.ahmj.c2s.iclass.*;
import cenum.mj.HuType;
import cenum.mj.MJSpecialEnum;
import cenum.room.SetState;
import com.ddm.server.common.utils.CommTime;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.mj.BaseMJSet_Pos;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 青岛 一局游戏逻辑
 *
 * <AUTHOR>
 */
@SuppressWarnings("ALL")
public class AHMJRoomSet extends AbsMJSetRoom {
    /**
     * 金列表
     */
    public final List<Integer> jinList;

    /**
     * 翻开的牌
     */
    private List<Integer> flipCardList = new ArrayList<>();
    /**
     * 翻牌pos
     */
    private int flipCardPos = -1;

    /**
     * 等待翻转pos
     */
    private int waitFlipPos = -1;
    /**
     * 鸟牌列表
     */
    private List<Integer> niaoPaiList = new ArrayList<>();
    /**
     * 进金
     */
    private int jinJin = 0;
    /**
     * WA
     *
     * @param setID
     * @param room  room
     * @param dPos
     */

    @SuppressWarnings("rawtypes")
    public AHMJRoomSet(int setID, MahjongRoom room, int dPos) {
        super(setID, room, dPos);
        this.dPos = dPos;
        this.startMS = CommTime.nowMS();
        jinList = getGodInfo().getJinList();
        this.addGameConfig();
        if(AHMJRoomEnum.DaNaio.Five.ordinal() == ((AHMJRoom)room).getRoomCfg().getDaniao()){
            this.initSetState();
        }else{
            this.startSet();
        }
    }

    /**
     * 初始化游戏状态
     */
    private void initSetState() {
        this.startNSet();
        // 设置所有用户的超时
        this.getRoom().getRoomPosMgr().setAllLatelyOutCardTime();
        //进入打鸟阶段
        setState(SetState.Waiting);
        return;
    }


    // 每200ms更新1次 秒
    public boolean update(int sec) {
        boolean isClose = false;

        if (this.state == SetState.Init) {
            if (CommTime.nowMS() > this.startMS + this.InitTime) {
                // 福鼎麻将-开金
                MJFactory.getOpCard(AHMJKaiJinJJImpl.class).checkOpCard(this.getMJSetPos(this.getDPos()), 0);
                this.sendSetPosCard();
                this.state = SetState.Playing;
                if (!this.startNewRound()) {
                    this.endSet();
                }
            }
        } else if (this.state == SetState.Waiting) {
            for (AbsRoomPos n : this.room.getRoomPosMgr().getPosList()) {
                AHMJRoomPos roomPos = (AHMJRoomPos) n;
                if (Objects.nonNull(roomPos) && roomPos.isTrusteeship() && roomPos.getDaNiao().equals(AHMJRoomEnum.AHMJDaNiao.NOT_OP)) {
                    opDaNiao(null, roomPos.getPid(), CAHMJ_DaNiao.make(this.room.getRoomID(), AHMJRoomEnum.AHMJDaNiao.NOT.ordinal() - 1));
                }
            }
        } else if (this.state == SetState.Playing) {
            boolean isRoundClosed = this.curRound.update(sec);
            if (isRoundClosed) {
                if (curRound.isSetHuEnd()) {
                    this.endSet();
                } else if (!this.startNewRound()) {
                    this.endSet();
                }
            }
        } else if (this.state == SetState.End) {
            this.clearEndSetRoom();
            isClose = true;

        }
        return isClose;
    }

    /**
     * 打鸟
     */
    public void opDaNiao(WebSocketRequest request, long pid, CAHMJ_DaNiao data) {
        if (!this.state.equals(SetState.Waiting)) {
            if (null != request) {
                request.error(ErrorCode.NotAllow, "opDaNiao cur setstate=" + this.state);
            }
            return;
        }
        AHMJRoomEnum.AHMJDaNiao daNiao = AHMJRoomEnum.AHMJDaNiao.valueOf(data.niao);

        AHMJRoomPos roomPos = (AHMJRoomPos) this.room.getRoomPosMgr().getPosByPid(pid);
        if (roomPos.getDaNiao().equals(daNiao) ||
                AHMJRoomEnum.AHMJDaNiao.NOT_OP.equals(daNiao)) {
            if (null != request) {
                request.error(ErrorCode.NotAllow, "opDaNiao  your niao = " + roomPos.getDaNiao());
            }
            return;
        }

        roomPos.setDaNiao(daNiao);
        if (null != request) {
            request.response();
        }
        this.room.getRoomPosMgr().notify2All(SAHMJ_Niao.make(data.roomID, roomPos.getPosID(), data.niao));

        AHMJRoomPosMgr roomPosMgr = (AHMJRoomPosMgr) this.room.getRoomPosMgr();
        // 清除最近出手的时间
        roomPos.setLatelyOutCardTime(0L);
        if (roomPosMgr.checkAllOpNiao()) {
            setStateInit();
        }
    }

    /**
     * 打牌开始通知
     */
    private void setStateInit() {
        this.state = SetState.Init;
        this.startMS = CommTime.nowMS();
        startSet();
    }

    /**
     * 通知房间内的所有玩家，指定玩家摸牌了。
     *
     * @param setPos 操作玩家信息
     */
    protected void notify2GetCard(AbsMJSetPos setPos) {
        BaseMJSet_Pos posInfoOther = setPos.getNotify(false);
        BaseMJSet_Pos posInfoSelf = setPos.getNotify(true);
        this.roomPlayBack.playBack2Pos(setPos.getPosID(),
                this.posGetCard(this.room.getRoomID(), setPos.getPosID(), this.setCard.getRandomCard().getNormalMoCnt(),
                        this.setCard.getRandomCard().getGangMoCnt(), posInfoSelf),
                this.setPosMgr.getAllPlayBackNotify());
        this.room.getRoomPosMgr().notify2ExcludePosID(setPos.getPosID(),
                this.posGetCard(this.room.getRoomID(), setPos.getPosID(), this.setCard.getRandomCard().getNormalMoCnt(),
                        this.setCard.getRandomCard().getGangMoCnt(), posInfoOther));
    }

    /**
     * 开癞子个数
     *
     * @return
     */
    @Override
    public int kaiJinNum() {
        return 1;
    }

    @Override
    public boolean isBaiBanTiJin() {
        return false;
    }

    @Override
    public void addGameConfig() {
        this.getRoomPlayBack().addPlaybackList(SAHMJ_Config.make((CAHMJ_CreateRoom) getRoom().getCfg(),
                this.room.getBaseRoomConfigure().getBaseCreateRoom().getClubId() > 0), null);
    }

    @Override
    protected <T> BaseSendMsg posGetCard(long roomID, int pos, int normalMoCnt, int gangMoCnt, T set_Pos) {
        return SAHMJ_PosGetCard
                .make(roomID, pos, normalMoCnt, gangMoCnt, set_Pos, this.setCard.getRandomCard().getSize());
    }

    /**
     * 创建新的当局麻将信息
     */
    @Override
    protected AHMJRoomSetInfo newMJRoomSetInfo() {
        return new AHMJRoomSetInfo();
    }

    /**
     * 获取通知当局信息
     */
    @Override
    public AHMJRoomSetInfo getNotify_set(long pid) {
        AHMJRoomSetInfo ret = (AHMJRoomSetInfo) this.getMJRoomSetInfo(pid);
        AHMJRoomPosMgr roomPosMgr = (AHMJRoomPosMgr) this.room.getRoomPosMgr();
        ret.setDaNiaoList(roomPosMgr.getDaNiaoList());
        ret.setFlipCardList(flipCardList);
        ret.setFlipCardPos(flipCardPos);
        ret.setJin(this.getmJinCardInfo().getJin(1).getCardID());
        ret.setJin2(this.getmJinCardInfo().getJin(2).getCardID());
        ret.setJinJin(jinJin);
        return ret;
    }

    /**
     * 一局结束的信息
     */
    @Override
    public AHMJRoomSetEnd getNotify_setEnd() {
        AHMJRoomSetEnd setEndInfo = (AHMJRoomSetEnd) this.mRoomSetEnd();
        setEndInfo.setJin(this.getmJinCardInfo().getJin(1).getCardID());
        setEndInfo.setJin2(this.getmJinCardInfo().getJin(2).getCardID());
        setEndInfo.setJinJin(jinJin);
        setEndInfo.setZhuaNiaoList(this.niaoPaiList);
        return setEndInfo;
    }

    /**
     * 麻将当局结算
     *
     * @return
     */
    @Override
    protected AHMJRoomSetEnd newMJRoomSetEnd() {
        return new AHMJRoomSetEnd();
    }

    /**
     * 牌数
     */
    @Override
    public int cardSize() {
        return MJSpecialEnum.SIZE_13.value();
    }

    @Override
    public int calcNextDPos() {
        AHMJRoom fRoom = (AHMJRoom) this.getRoom();
        // 第一局房主为庄家，此后谁胡谁为庄
        if (this.getMHuInfo().getHuPos() >= 0) {
            return this.getMHuInfo().getHuPos();
        }
        // 流局下家为庄
        return (this.dPos + 1) % this.getRoom().getPlayerNum();
    }

    /**
     * 小局结算消息
     */
    @Override
    protected <T> BaseSendMsg setEnd(long roomID, T setEnd) {
        return SAHMJ_SetEnd.make(roomID, setEnd, isRoomEnd());
    }

    @Override
    public void kaiJinNotify(MJCard jinCard, MJCard jinCard2) {
        int jin2 = 0;
        int jin = 0;
        if (((AHMJRoom)getRoom()).isKingsOfSeven()) {
            jin2 = ObjectUtils.defaultIfNull(jinCard2, new MJCard(0)).getCardID();
            jin = jinCard.getCardID();
        }else{
            jinJin = jinCard.getCardID();
            jin = ObjectUtils.defaultIfNull(jinCard2, new MJCard(0)).getCardID();
        }
        getRoomPlayBack().playBack2All(SAHMJ_Jin
                .make(getRoom().getRoomID(), jin,
                        jin2,
                        jinJin,
                        getMJSetCard().getRandomCard().getNormalMoCnt(),
                        getMJSetCard().getRandomCard().getGangMoCnt()));
        return;
    }

    @Override
    protected AbsMJSetPos absMJSetPos(int posID) {
        return new AHMJSetPos(posID, (AHMJRoomPos) this.room.getRoomPosMgr().getPosByPosID(posID), this);
    }

    @Override
    protected void absMJSetCard() {
        this.setSetCard(new AHMJSetCard(this));
    }

    @Override
    protected AbsMJSetPosMgr absMJSetPosMgr() {
        return new AHMJSetPosMgr(this);
    }

    @Override
    protected <T> BaseSendMsg setStart(long roomID, T setInfo) {
        return SAHMJ_SetStart.make(roomID, setInfo);
    }

    @Override
    protected AbsMJSetRound nextSetRound(int roundID) {
        return new AHMJSetRound(this, roundID);
    }

    @Override
    public boolean isConfigName() {
        return true;
    }

    @Override
    public void sendSetPosCard() {
        for (int i = 0; i < room.getPlayerNum(); i++) {
            AbsMJSetPos setPos = posDict.get(i);
            setPos.sortCards();
        }
        for (int i = 0; i < room.getPlayerNum(); i++) {
            long pid = this.room.getRoomPosMgr().getPosByPosID(i).getPid();
            this.room.getRoomPosMgr()
                    .notify2Pos(i, SAHMJ_SetPosCard.make(this.room.getRoomID(), this.setPosCard(pid)));
        }
    }

    @Override
    public void MJApplique(int pos) {

    }

    /**
     * 设置setstate
     */
    @Override
    public void setState(SetState setState) {
        this.state = setState;
        this.startMS = CommTime.nowMS();
        AHMJRoomPosMgr roomPosMgr = (AHMJRoomPosMgr) this.room.getRoomPosMgr();
        this.room.getRoomPosMgr().notify2All(SAHMJ_ChangeStatus
                .make(this.getRoom().getRoomID(), this.getRoom().getCurSetID(), setState, roomPosMgr.getDaNiaoList()));
    }

    /**
     * 计算当局每个pos位置的分数。
     */
    @Override
    protected void calcCurSetPosPoint() {
        //中鸟
        this.doZhongNiao();
        // 计算位置小局分数
        this.getPosDict().values().forEach(k -> k.calcPosPoint());
        // 其他特殊结算 连庄记录
        this.calcOtherPoint();
        this.calYiKaoPoint();
    }

    @Override
    public boolean equals(Object o) {
        if (o instanceof AHMJRoomSet) {
            if (getSetID() == ((AHMJRoomSet) o).getSetID()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 出金牌
     *
     * @return
     */
    public Integer popJin() {
        if (CollectionUtils.isNotEmpty(jinList)) {
            return jinList.get(0);
        }
        return 0;
    }

    /**
     * 开金
     */
    public MJCard kaiJin() {
        List<MJCard> leftCards = getSetCard().getRandomCard().getLeftCards();
        int jin = popJin();
        if (jin != 0) {
            jin = jin * 100 + 1;
        } else {
            jin = leftCards.get(new Random().nextInt(leftCards.size())).getCardID();
        }
        final int outJin = jin;
        leftCards.removeIf(n -> n.getCardID() == outJin);
        return new MJCard(jin);
    }

    public List<MJCard> flipCards(int opPos) {
        AbsMJSetPos setPos = this.posDict.get(opPos);
        List<MJCard> cardList = new ArrayList<>();
        int flipCardNum = ((AHMJRoom) room).getFlipCardNum();
        IntStream.range(0, flipCardNum).forEach(n -> {
            // 随机摸牌
            MJCard card = this.setCard.pop(false, this.getGodInfo().godHandCard(setPos));
            card.ownnerPos = opPos;
            if (Objects.nonNull(card)) {
                cardList.add(card);
            }
        });
        return cardList;
    }

    public List<Integer> getFlipCardList() {
        return flipCardList;
    }

    public void setFlipCardList(List<Integer> flipCardList) {
        this.flipCardList = flipCardList;
    }

    public void clearFlipCardList(){
        if(CollectionUtils.isNotEmpty(flipCardList)){
            this.flipCardList.clear();;
            this.flipCardPos = -1;
        }
    }

    public int getWaitFlipPos() {
        return waitFlipPos;
    }

    public void setWaitFlipPos(int waitFlipPos) {
        this.waitFlipPos = waitFlipPos;
    }

    public void setFlipCardPos(int flipCardPos) {
        this.flipCardPos = flipCardPos;
    }

    public int getFlipCardPos() {
        return flipCardPos;
    }

    /***
     * 159翻牌阶段
     * @return
     */
    private boolean doZhongNiao() {
        if(getMHuInfo().isHuEmpty()){
            return true;
        }
        int huOpPos = getMHuInfo().getHuPos();
        List<Integer> fanPaiZhaMa = new ArrayList<>(); //扎码的牌
        AHMJSetCard nSetCard = (AHMJSetCard) getSetCard();
        AHMJSetPos pos = null;
        int count = 0;
        for (int i = 1; i <= getPlayerNum(); i++) {
            int next = (huOpPos + i) % getPlayerNum();
            pos = (AHMJSetPos) getMJSetPos(next);
            if (null == pos) {
                continue;
            }
            if (pos.getHuType() == null || pos.getHuType() == HuType.NotHu || pos.getHuType() == HuType.DianPao) {
                continue;
            }
            this.setNiaoPaiList(nSetCard.getZhaMaCards());
            List<Integer> zhongList = nSetCard.calcZhongNiaoList(getNiaoPaiList());
            pos.setZhongNiaoList(zhongList);
        }

        return true;
    }


    public void setNiaoPaiList(List<Integer> niaoPaiList) {
        this.niaoPaiList = niaoPaiList;
    }

    public List<Integer> getNiaoPaiList() {
        return niaoPaiList;
    }

    /**
     * 小局托管自动解散回放记录 注意：需要自己重写
     *
     * @param roomId  房间id
     * @param pidList 托管玩家Pid
     * @param sec     记录时间
     * @return
     */
    @Override
    public BaseSendMsg DissolveTrusteeship(long roomId, List<Long> pidList, int sec) {
        return SAHMJ_DissolveTrusteeship.make(roomId, pidList, sec);
    }

    /**
     * 检查小局托管自动解散
     */
    @Override
    public boolean checkSetEndTrusteeshipAutoDissolution() {
        if(((AHMJRoom) getRoom()).isSetAutoJieSan()){
            return true;
        }else if(this.getRoom().getBaseRoomConfigure().getBaseCreateRoom().getFangjian().contains(AHMJRoomEnum.AHMJGameRoomConfigEnum.TuoGuan3XiaoJuJieSan.ordinal())){
            // 有玩家连续2局托管
            // 获取托管玩家pid列表
            List<Long> trusteeshipPlayerList = getRoom().getRoomPosMgr().getRoomPosList().stream()
                    .filter(n -> n.isTrusteeship() && ((AHMJRoomPos)n).getTuoGuanSetCount() >= 3).map(n -> n.getPid()).collect(Collectors.toList());
            if(trusteeshipPlayerList.size() > 0){
                return true;
            }
        }
        return false;
    }
}
