package business.wxls.c2s.iclass;	
	
import jsproto.c2s.cclass.BaseSendMsg;	
	
public class SWXLS_CardReadyChg extends BaseSendMsg {	
    public long roomID;	
    public int pos;	
    public boolean isReady;	
    public boolean isSpecial;	
    public CWXLS_Ranked ranked;	
	
	
    public static SWXLS_CardReadyChg make(long roomID, int pos, boolean isReady, CWXLS_Ranked ranked, boolean isSpecial) {	
    	SWXLS_CardReadyChg ret = new SWXLS_CardReadyChg();	
        ret.roomID = roomID;	
        ret.pos = pos;	
        ret.isReady = isReady;	
        ret.ranked = ranked;	
        ret.isSpecial = isSpecial;	
	
        return ret;	
    	
	
    }	
}	
