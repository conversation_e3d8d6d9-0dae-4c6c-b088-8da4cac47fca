package core.network.client2game.handler.wxls;	
	
import business.player.Player;	
import business.player.feature.PlayerRoom;	
import business.wxls.c2s.iclass.CWXLS_CreateRoom;	
import cenum.PrizeType;	
import com.ddm.server.websocket.def.ErrorCode;	
import com.ddm.server.websocket.handler.requset.WebSocketRequest;	
import com.google.gson.Gson;	
import core.network.client2game.handler.PlayerHandler;	
import core.network.http.proto.SData_Result;	
import core.server.wxls.WXLSAPP;	
import jsproto.c2s.cclass.room.BaseRoomConfigure;	
	
	
import java.io.IOException;	
	
/**	
 * 创建房间	
 * <AUTHOR>	
 *	
 */	
public class CWXLSCreateRoom extends PlayerHandler {	
		
	
    @Override	
    public void handle(Player player, WebSocketRequest request, String message) throws IOException {	
    		
    	final CWXLS_CreateRoom clientPack = new Gson().fromJson(message, CWXLS_CreateRoom.class);	
	
		// 公共房间配置	
		BaseRoomConfigure<CWXLS_CreateRoom> configure = new BaseRoomConfigure<CWXLS_CreateRoom>(	
				PrizeType.RoomCard,	
				WXLSAPP.GameType(),	
				clientPack.clone());	
	
		SData_Result resule = player.getFeature(PlayerRoom.class).createRoomAndConsumeCard(configure);	
		if (ErrorCode.Success.equals(resule.getCode())) {	
			request.response(resule.getData());	
		} else {	
			request.error(resule.getCode(),resule.getMsg());	
		}	
    		
    }	
}	
