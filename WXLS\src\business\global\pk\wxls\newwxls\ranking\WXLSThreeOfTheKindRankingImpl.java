package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.Iterator;	
import java.util.Map;	
	
/**	
 * 解析玩家手中的牌是不是三条(3+1+1)	
 */	
public class WXLSThreeOfTheKindRankingImpl extends WXLSAbstractRanking {	
	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        WXLSRankingResult result = null;	
        Map<Integer, Integer> rankCount = player.getCardsRankCountMap();	
	
        boolean hasThree = false;	
	
        Iterator<Map.Entry<Integer, Integer>> it = rankCount.entrySet().iterator();	
        while (it.hasNext()) {	
            int count = it.next().getValue() ;	
            if (count == 3  || (count == 2 && player.getGuiCount() == 1)) {	
                hasThree = true;	
                break;	
            }	
        }	
	
        if (hasThree) {	
            result = new WXLSRankingResult();	
            result.setRankingEnum(WXLSRankingEnum.THREE_OF_THE_KIND);	
        }	
	
        return result;	
    }	
	
}	
