package business.global.mj.wxmj;			
			
import java.util.List;			
			
import com.ddm.server.common.utils.CommMath;			
import com.ddm.server.websocket.handler.requset.WebSocketRequestDelegate;			
			
import business.global.mj.AbsMJRoundPos;			
import business.global.mj.AbsMJSetPos;			
import business.global.mj.AbsMJSetRound;			
import business.global.mj.robot.MJRobotOpCard;			
import business.global.mj.set.MJOpCard;			
import cenum.mj.HuType;			
import cenum.mj.MJCEnum;			
import cenum.mj.MJOpCardError;			
import cenum.mj.OpType;			
			
public class WXMJRobotOpCard extends MJRobotOpCard {			
			
	public WXMJRobotOpCard(AbsMJSetRound setRound) {			
		super(setRound);			
	}			
			
	@Override			
	public void RobothandCrad(int posID) {			
		// 获取当前操作位置			
		AbsMJRoundPos roundPos = this.getSetRound().getRoundPosDict().get(posID);			
		if (null == roundPos) {			
			// 检查超时等待时间			
			this.checkWaitTime();			
			return;			
		}			
		// 检查位置是否已经操作过			
		if (null != roundPos.getOpType()) {			
			// 检查超时等待时间			
			this.checkWaitTime();			
			return;			
		}			
		// 获取玩家信息			
		AbsMJSetPos mSetPos = roundPos.getPos();			
		if (mSetPos == null) {			
			// 检查超时等待时间			
			this.checkWaitTime();			
			return;			
		}			
			
		// 获取玩家可操作列表			
		List<OpType> opTypes = roundPos.getRecieveOpTypes();			
		if (opTypes == null || opTypes.size() <= 0) {			
			// 检查超时等待时间			
			this.checkWaitTime();			
			return;			
		}			
			
		// 操作结果			
		int opCardRet = null == mSetPos.getHandCard() ? this.notExistHandCard(opTypes, mSetPos)			
				: this.existHandCard(opTypes, mSetPos);			
		if (opCardRet >= 0) {			
			// 操作成功可以清除动作列表			
			mSetPos.getPosOpRecord().cleanOpList();			
		}			
	}			
			
	@Override			
	public int existHandCard(List<OpType> opTypes, AbsMJSetPos mSetPos) {			
		OpType opType = opTypes.stream().filter(k -> !HuType.NotHu.equals(MJCEnum.OpHuType(k))).findAny()			
				.orElse(opTypes.get(CommMath.randomInt(0, opTypes.size() - 1)));			
		if (HuType.ZiMo.equals(MJCEnum.OpHuType(opType)) || HuType.GSKH.equals(MJCEnum.OpHuType(opType))) {			
			return this.getSetRound().opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), opType,			
					MJOpCard.OpCard(0));			
		}			
		// 打牌			
		return this.getSetRound().opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), OpType.Out,			
				MJOpCard.OpCard(mSetPos.getHandCard().getCardID()));			
			
	}			
			
	/**			
	 * 不存在首牌			
	 *			
	 * @return			
	 */			
	@Override			
	public int notExistHandCard(List<OpType> opTypes, AbsMJSetPos mSetPos) {			
		OpType opType = opTypes.stream().filter(k -> !HuType.NotHu.equals(MJCEnum.OpHuType(k))).findAny()			
				.orElse(opTypes.get(CommMath.randomInt(0, opTypes.size() - 1)));			
		if (HuType.JiePao.equals(MJCEnum.OpHuType(opType)) || HuType.QGH.equals(MJCEnum.OpHuType(opType))) {			
			return this.getSetRound().opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), opType,			
					MJOpCard.OpCard(0));			
		}			
		if (opTypes.contains(OpType.Pass)) {			
			// 没有相应的动作直接过			
			return this.getSetRound().opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), OpType.Pass,			
					MJOpCard.OpCard(0));			
		} else if (opTypes.contains(OpType.Out)) {			
			return this.getSetRound().opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), OpType.Out,			
					MJOpCard.OpCard(mSetPos.getSetPosRobot().getAutoCard()));			
		}			
		return 0;			
	}			
}			
