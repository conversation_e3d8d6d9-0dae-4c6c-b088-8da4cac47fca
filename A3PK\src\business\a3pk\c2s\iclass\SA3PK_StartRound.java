package business.a3pk.c2s.iclass;	
import jsproto.c2s.cclass.*;	
	
	
@SuppressWarnings("serial")	
public class SA3PK_StartRound<T> extends BaseSendMsg {	
    	
    public long roomID;	
    public T room_SetWait;	
	
	
    public static <T>SA3PK_StartRound<T> make(long roomID, T room_SetWait) {	
    	SA3PK_StartRound<T> ret = new SA3PK_StartRound<T>();	
        ret.roomID = roomID;	
        ret.room_SetWait = room_SetWait;	
	
        return ret;	
    	
	
    }	
}	
