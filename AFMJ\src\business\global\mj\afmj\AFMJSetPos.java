package business.global.mj.afmj;

import java.util.*;
import java.util.stream.Collectors;
import business.global.mj.*;
import business.global.mj.afmj.hutype.AFMJDDHuImpl;
import business.global.mj.afmj.hutype.AFMJPPImpl;
import business.global.mj.afmj.hutype.AFMJSSBKImpl;
import business.global.mj.afmj.hutype.AFMJSSBKQingImpl;
import business.global.mj.afmj.optype.AFMJBuHuaImpl;
import business.global.mj.afmj.optype.AFMJTingImpl;
import business.global.mj.manage.MJFactory;
import business.global.room.mj.MJRoomPos;
import business.afmj.c2s.cclass.AFMJResults;
import business.afmj.c2s.cclass.AFMJRoom_PosEnd;
import business.afmj.c2s.cclass.AFMJSet_Pos;
import cenum.mj.*;
import com.ddm.server.common.CommLogD;
import com.ddm.server.common.utils.Lists;
import jsproto.c2s.cclass.mj.BaseMJRoom_PosEnd;
import jsproto.c2s.cclass.mj.BaseMJSet_Pos;
import jsproto.c2s.cclass.room.AbsBaseResults;
import business.global.mj.afmj.AFMJRoomEnum.*;


/**
 * 每一局每个玩家的详情
 */

public class AFMJSetPos extends AbsMJSetPos {
	private final AFMJSetOp setOp;
	private final AFMJRoomSet jset;
	private final List<Integer> gangList = new ArrayList<>(); // 报听的杠列表
	private int louHuZuiDaPaiXingFen = 0; // 漏胡最大牌型分
	private boolean isPiaoJing = false; // 是否飘精，飘精：将“精”牌打出去为飘精；
	private int xiaZhengJingFen = 0; // 下正精分
	private int xiaFuJingFen = 0; // 下副精分
	private int xiaJingChongGuan = 1; // 下精冲关数
	private boolean xiaBaWangJing = false; // 是否下霸王精
	private int xiaJingFen = 0; // 下精总分
	private final List<Integer> baoChongJingCardList = new ArrayList<>(); // 吃碰杠接炮得到别的玩家的飘精的列表
	// 杠分
	private int anGangPoint = 0; // 暗杠分
	private int pengGangPoint = 0; // 碰杠分
	private int zhiGangPoint = 0; // 直杠分
	// 单局结算的模块分数：胡牌分、上精分、下精分、飘精、杠分
	private int huPaiPoint = 0; // 胡牌分
	private int shangJingPoint = 0; // 上精分
	private int xiaJingPoint = 0; // 下精分
	private int boJingPoint = 0; // 博精倍数

	public int getAnGangPoint() {
		return anGangPoint;
	}

	public void setAnGangPoint(int anGangPoint) {
		this.anGangPoint = anGangPoint;
	}

	public int getPengGangPoint() {
		return pengGangPoint;
	}

	public void setPengGangPoint(int pengGangPoint) {
		this.pengGangPoint = pengGangPoint;
	}

	public int getZhiGangPoint() {
		return zhiGangPoint;
	}

	public void setZhiGangPoint(int zhiGangPoint) {
		this.zhiGangPoint = zhiGangPoint;
	}

	public int getHuPaiPoint() {
		return huPaiPoint;
	}

	public void setHuPaiPoint(int huPaiPoint) {
		this.huPaiPoint = huPaiPoint;
	}

	public int getShangJingPoint() {
		return shangJingPoint;
	}

	public void setShangJingPoint(int shangJingPoint) {
		this.shangJingPoint = shangJingPoint;
	}

	public int getXiaJingPoint() {
		return xiaJingPoint;
	}

	public void setXiaJingPoint(int xiaJingPoint) {
		this.xiaJingPoint = xiaJingPoint;
	}

	public int getBoJingPoint() {
		return boJingPoint;
	}

	public void setBoJingPoint(int boJingPoint) {
		this.boJingPoint = boJingPoint;
	}

	public List<Integer> getBaoChongJingCardList() {
		return baoChongJingCardList;
	}

	public int getXiaJingFen() {
		return xiaJingFen;
	}

	public void setXiaJingFen(int xiaJingFen) {
		this.xiaJingFen = xiaJingFen;
	}

	public int getXiaJingChongGuan() {
		return xiaJingChongGuan;
	}

	public void setXiaJingChongGuan(int xiaJingChongGuan) {
		this.xiaJingChongGuan = xiaJingChongGuan;
	}

	public void setXiaBaWangJing(boolean xiaBaWangJing) {
		this.xiaBaWangJing = xiaBaWangJing;
	}

	public int getXiaZhengJingFen() {
		return xiaZhengJingFen;
	}

	public void setXiaZhengJingFen(int xiaZhengJingFen) {
		this.xiaZhengJingFen = xiaZhengJingFen;
	}

	public int getXiaFuJingFen() {
		return xiaFuJingFen;
	}

	public void setXiaFuJingFen(int xiaFuJingFen) {
		this.xiaFuJingFen = xiaFuJingFen;
	}

	public boolean isPiaoJing() {
		return isPiaoJing;
	}

	public void setPiaoJing(boolean piaoJing) {
		this.isPiaoJing = piaoJing;
	}

	public void clearPiaoJing(){
		this.isPiaoJing = false;
	}

	public void setLouHuZuiDaPaiXingFen(int louHuZuiDaPaiXingFen) {
		this.louHuZuiDaPaiXingFen = louHuZuiDaPaiXingFen;
	}

	public List<Integer> getGangList() {
		return gangList;
	}

	// 清空报听的杠列表
	public void clearGangList() {
		this.gangList.clear();
	}

	public void setFangpao(int fangpao) {
	}

	public AFMJSetPos(int posID, MJRoomPos roomPos, AbsMJSetRoom set) {
		super(posID, roomPos, set, AFMJTingImpl.class);
		this.setMSetOp(new AFMJSetOp(this));
		this.jset = (AFMJRoomSet) set;
		this.setOp = (AFMJSetOp) this.getmSetOp();
		this.setCalcPosEnd(new AFMJCalcPosEnd(this));
	}

	public AFMJSet_Pos getNotify(boolean isSelf) {

		AFMJSet_Pos afmjSet_pos = this.getNotifyInfo(isSelf);

		// 获取手牌通知信息
		return afmjSet_pos;
	}


	/**
	 * 获取手牌通知信息
	 *
	 * @param isSelf 是否本身
	 */
	protected AFMJSet_Pos getNotifyInfo(final boolean isSelf) {
		AFMJSet_Pos ret = this.newMJSetPos();
		// 玩家位置
		ret.setPosID(this.getPosID());
		// 手牌
		ret.setShouCard(getShouCard(isSelf));
		// 可胡的牌
		ret.setHuCard(isSelf ? this.getHuCardTypes() : null);
		if (this.getHandCard() != null) {
			// 首牌
			ret.setHandCard(isSelf ? this.getHandCard().getCardID() : 5000);
		}
		// 打出的牌
		ret.setOutCard(this.getOutCardIDs());
		// 公共牌
		ret.setPublicCardList(this.getPublicCardList());
		// 精牌列表
		// 如果玩家只有打出精牌后才能胡，对应的听牌提示箭头需要变成“精”；（即“必博一精”,“必博正精或两副”玩法）
		if(((AFMJRoom)getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoYiJing)
				|| ((AFMJRoom)getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoZhengJingHuoLiangFu)){
			for(MJCard mjCard : allCards()){
				if(jset.getmJinCardInfo().checkJinExist(mjCard.type)){
					ret.getJingPai().add(mjCard.cardID);
				}
			}
		}
		// 博精倍数
		// 游戏中需要在各个玩家头像处实时统计倍数；
		int qiTaZhengJingShu = 0; // 打出牌的其他正精数
		int qiTaFuJingShu = 0; // 打出牌的其他副精数
		if(((AFMJRoom)getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BoJing)
				|| ((AFMJRoom)getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoYiJing)
				|| ((AFMJRoom)getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoZhengJingHuoLiangFu)){
			List<Integer> OutCardIDs = getOutCardIDs();
			for(Integer cardId : OutCardIDs){
				if(jset.getmJinCardInfo().getJinKeys().contains(cardId / 100)){
					if(jset.getZhengJing().type == cardId / 100){
						qiTaZhengJingShu += 1; // 其他正精数
					}else {
						qiTaFuJingShu += 1; // 其他副精数
					}
				}
			}
			ret.setBoJing((int)Math.pow(4,qiTaZhengJingShu) * (int)Math.pow(2,qiTaFuJingShu));
		}
		// 勾选“博精”,“必博一精”,“必博正精或两副”玩法后，飘精需要“精”动画； （同碰杠动画效果）
		if(((AFMJRoom)getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BoJing)
				|| ((AFMJRoom)getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoYiJing)
				|| ((AFMJRoom)getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoZhengJingHuoLiangFu)){
			ret.setPiaoJing(isPiaoJing());
		}
		// 掉线连接
		ret.setIsLostConnect(null);
		return ret;
	}

	public BaseMJSet_Pos getPlayBackNotify() {
		AFMJSet_Pos ret = this.newMJSetPos();
		ret.setPosID(this.getPosID());
		// 是自己
		ret.setShouCard(getShouCard(true));
		// 可胡牌的 type 和番数；私人独享
		ret.setHuCard(this.getHuCardTypes());
		// 首牌
		ret.setHandCard(this.getHandCard() != null ? this.getHandCard().getCardID() : -1);
		// 打牌列表
		ret.setOutCard(this.getOutCardIDs());
		// 公共牌列表
		ret.setPublicCardStrs(this.getPublicCardList().toString());
		// 掉线连接
		ret.setIsLostConnect(this.getRoomPos().isLostConnect());
		// 回放托管标识
		ret.setTrusteeship(this.getRoomPos().isTrusteeship());
		return ret;
	}

	/**
	 * 打牌、出牌
	 */
	public boolean outCard(MJCard card) {
		boolean ret = false;
		if (this.getHandCard() == card) {
			this.cleanHandCard();
			ret = true;
		} else if (0 < this.removePrivateCard(card)) {
			if (this.getHandCard() != null) {
				this.getPrivateCards().add(this.getHandCard());
				this.sortCards();
			}
			ret = true;
		}
		if (ret) {
			this.cleanHandCard();
			this.getOutCardIDs().add(card.cardID);
			this.getSet().getLastOpInfo().setLastOutCard(card.cardID);
			calcHuFan();
			this.getOutJinInfo().setOutJinCard(this.getSet().getmJinCardInfo().checkJinExist(card.type));
			setHaveOutCard(true);
		}
		return ret;
	}


	/**
	 * 新一局中各位置的信息
	 */
	@Override
	protected AFMJSet_Pos newMJSetPos() {
		return new AFMJSet_Pos();
	}


	@Override
	public void calcPosPoint() {
		this.getCalcPosEnd().calcPosPoint(this);
	}

//	public void calcPosPointEnd() {
//		this.qCalcPosEnd.calcPosEnd();
//	}

	@Override
	public boolean doOpType(int cardID, OpType opType) {
		return setOp.doOpType(cardID, opType);
	}

	@Override
	public boolean checkOpType(int cardID, OpType opType) {
		return setOp.checkOpType(cardID, opType);
	}

	@Override
	public OpType checkPingHu(int curOpPos, int cardID) {
		// 清空操作牌初始
		this.clearPaoHu();
		this.setOpHuType(OpType.Not);
		// 听牌数大于34张的时候 是游金 不能平胡
		if (this.sizeHuCardTypes() >= MJSpecialEnum.TING.value()) {
			return OpType.Not;
		}
		OpType opType = OpType.Not;
		if (checkOpType(cardID, OpType.Hu)) { // 玩家胡

			// 胡牌分+胡牌者杠分+点炮者杠分≥3；

			// 获取点炮者
			AFMJSetPos aSetPos = (AFMJSetPos)this.getMJSetPos(this.getSet().getLastOpInfo().getLastOpPos());
			if (null == aSetPos) {
				return OpType.Not;
			}
			opType = OpType.JiePao; // 不是自摸胡就是接炮胡
		}
		if (!OpType.Not.equals(opType)) { // 可以胡

			// 如果胡的是平胡类型：（天地胡也需要满足该规则）
			// 手里有精只能自摸；
			// 其他牌型无限制；
			if(!jset.isDaHu()) { // 平胡
				boolean isPresent = this.allCards().stream().anyMatch(k ->this.getSet().getmJinCardInfo().getJinKeys().contains(k.type));
				if(isPresent)return OpType.Not;
			}


			// 漏胡（这张漏掉的牌）：如果玩家漏掉炮胡，则玩家拒绝胡这张牌直到下次抽到
	/*		if (getPosOpRecord().getHuCardTypeList().contains(cardID / 100)) {
				if (this.getFangpao() != this.getSet().getDPos()// 如果放炮的人不是庄家且上把放炮的人不是庄家且自己不是庄家
						&& this.getSet().getLastOpInfo().getLastOpPos() == this.getSet().getDPos()) {
					return OpType.Hu;
				}
				return OpType.Not;
			}*/

			// 漏胡（所有牌）：如果玩家漏掉炮胡，则该玩家摸牌前禁止炮胡；‘1000’ 只是作为标记，没有什么特殊意义。
			if (this.getPosOpRecord().isHuCardType(1000)) {
				// 如果能胡的牌型分更大，无漏胡限制；
				boolean LouHuPaiXingFen = true; // 漏胡的牌型分，true:漏胡的牌型分更大,有漏胡限制；false：能胡的牌型分更大，无漏胡限制
				for(Object obj : getPosOpRecord().getOpHuList()){
					AFMJOpPoint opPoint =	(AFMJOpPoint)obj;
					if(louHuZuiDaPaiXingFen < opPoint.value()){
						LouHuPaiXingFen = false; // 能胡的牌型分更大,无漏胡限制
						break;
					}
				}
				if(LouHuPaiXingFen){
					CommLogD.info("checkPingHu isHuCardType Pid:{},RoomID:{},RoomKey:{},CardId:{},HuList:{}", getPid(), getRoom().getRoomID(), getRoom().getRoomKey(), cardID, getPosOpRecord().getOpHuList().toString());
					//	this.setCheckPao(false);
					return OpType.Not;
				}
			}


			// 大吊车（独钓、单吊）：吃、碰、杠后，手上只剩下一张牌时胡牌；
			if (1 >= this.sizePrivateCard()) {
			}

//			getPosOpRecord().setHuCardType(cardID / 100);  // 标记漏胡（这张牌）
			getPosOpRecord().setHuCardType(1000); // 标记漏胡（所有牌）
			getPosOpRecord().getOpHuList().forEach(
					obj -> {
						AFMJOpPoint opPoint =	(AFMJOpPoint)obj;
						if(louHuZuiDaPaiXingFen < opPoint.value())louHuZuiDaPaiXingFen = opPoint.value();
					}); // 设置漏胡的最大牌型分
			this.setFangpao(this.getSet().getLastOpInfo().getLastOpPos());
			this.setmHuOpType(MJHuOpType.JiePao);
		}
		return opType;
	}



	@Override
	public List<OpType> recieveOpTypes() {

		// 清空记录数据
		this.clearOutCard();
		this.setFangpao(0);
		// 清空动作记录
		this.getSet().getSetPosMgr().cleanAllOpType();
		OpType opType = OpType.Not;
		List<OpType> opTypes = new ArrayList<>();

		if (checkOpType(0, OpType.Hu)) { // 可以胡


			if (this.isOpSize() > 0) { // 杠上开花
				this.getPosOpRecord().addOpHuList(AFMJOpPoint.GSKH);
			}

			// 检测优先胡德国，可以胡的最大牌型--------------------------------

			if(((AFMJRoom)getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoYiJing)){
				// 有必博一精玩法------------------------------------
				if(!checkBo1Jing()){
					// 没有博1精
					if(this.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.DeGuo)){
						// 优先胡德国,重新检测无金的其他其他牌型是否可胡
						// 检测七对
						if(!MJFactory.getHuCard(AFMJDDHuImpl.class).checkHuCard(this, mCardInitNotJin(0))){
							// 无金不可胡
							this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.QiDui));
						}
						// 检测对对胡
						if(!MJFactory.getHuCard(AFMJPPImpl.class).checkHuCard(this, mCardInitNotJin(0))){
							// 无金不可胡
							this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.PPHu));
						}
						// 检测七星十三烂
						if(AFMJOpPoint.Not.equals(MJFactory.getHuCard(AFMJSSBKQingImpl.class).checkHuCardReturn(this, mCardInitNotJin(0)))){
							// 无金不可胡
							this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.QiXingShiSanLan));
						}
						// 检测十三烂
						if(AFMJOpPoint.Not.equals(MJFactory.getHuCard(AFMJSSBKImpl.class).checkHuCardReturn(this, mCardInitNotJin(0)))) {
							// 无金不可胡
							this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.ShiSanLan));
						}
					}
				}else {
					// 博1精
					// 优先胡其他其他牌型,重新检测德国
					// 检测七对
					if(this.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.QiDui)){
						// 有金可胡七对
						if(!MJFactory.getHuCard(AFMJDDHuImpl.class).checkHuCard(this, mCardInitNotJin(0))){
							// 无金不可胡
							this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.DeGuo));
						}
					}
					// 检测对对胡
					if(this.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.PPHu)){
						// 有金可胡对对胡
						if(!MJFactory.getHuCard(AFMJPPImpl.class).checkHuCard(this, mCardInitNotJin(0))){
							// 无金不可胡
							this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.DeGuo));
						}
					}
					// 检测七星十三烂
					if(this.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.QiXingShiSanLan)){
						// 有金可胡七星十三烂
						if(AFMJOpPoint.Not.equals(MJFactory.getHuCard(AFMJSSBKQingImpl.class).checkHuCardReturn(this, mCardInitNotJin(0)))){
							// 无金不可胡
							this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.DeGuo));
						}
					}
					// 检测十三烂
					if(this.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.ShiSanLan)){
						// 有金可胡十三烂
						if(AFMJOpPoint.Not.equals(MJFactory.getHuCard(AFMJSSBKImpl.class).checkHuCardReturn(this, mCardInitNotJin(0)))) {
							// 无金不可胡
							this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.DeGuo));
						}
					}
				}
			}else if(((AFMJRoom)getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoZhengJingHuoLiangFu)){
				// 有必博一精玩法------------------------------------
				if(!checkBo1ZhengJingOr2FuJingNotDeGuo()){
					// 没有1正精或2副精
					if(this.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.DeGuo)){
						// 优先胡德国,重新检测无金的其他其他牌型是否可胡
						// 检测七对
						if(!MJFactory.getHuCard(AFMJDDHuImpl.class).checkHuCard(this, mCardInitNotJin(0))){
							// 无金不可胡
							this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.QiDui));
						}
						// 检测对对胡
						if(!MJFactory.getHuCard(AFMJPPImpl.class).checkHuCard(this, mCardInitNotJin(0))){
							// 无金不可胡
							this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.PPHu));
						}
						// 检测七星十三烂
						if(AFMJOpPoint.Not.equals(MJFactory.getHuCard(AFMJSSBKQingImpl.class).checkHuCardReturn(this, mCardInitNotJin(0)))){
							// 无金不可胡
							this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.QiXingShiSanLan));
						}
						// 检测十三烂
						if(AFMJOpPoint.Not.equals(MJFactory.getHuCard(AFMJSSBKImpl.class).checkHuCardReturn(this, mCardInitNotJin(0)))) {
							// 无金不可胡
							this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.ShiSanLan));
						}
					}
				}else {
					// 1正精或2副精
					// 优先胡其他其他牌型,重新检测德国
					// 检测七对
					if(this.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.QiDui)){
						// 有金可胡七对
						if(!MJFactory.getHuCard(AFMJDDHuImpl.class).checkHuCard(this, mCardInitNotJin(0))){
							// 无金不可胡
							this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.DeGuo));
						}
					}
					// 检测对对胡
					if(this.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.PPHu)){
						// 有金可胡对对胡
						if(!MJFactory.getHuCard(AFMJPPImpl.class).checkHuCard(this, mCardInitNotJin(0))){
							// 无金不可胡
							this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.DeGuo));
						}
					}
					// 检测七星十三烂
					if(this.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.QiXingShiSanLan)){
						// 有金可胡七星十三烂
						if(AFMJOpPoint.Not.equals(MJFactory.getHuCard(AFMJSSBKQingImpl.class).checkHuCardReturn(this, mCardInitNotJin(0)))){
							// 无金不可胡
							this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.DeGuo));
						}
					}
					// 检测十三烂
					if(this.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.ShiSanLan)){
						// 有金可胡十三烂
						if(AFMJOpPoint.Not.equals(MJFactory.getHuCard(AFMJSSBKImpl.class).checkHuCardReturn(this, mCardInitNotJin(0)))) {
							// 无金不可胡
							this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.DeGuo));
						}
					}
				}
			}else{
				// 没有必博一精玩法/必博正精或两副玩法, 优先胡牌型大于德国的牌型，重新检测德国牌型
				// 优先胡其他其他牌型,重新检测德国
				// 检测七对
				if(this.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.QiDui)){
					// 有金可胡七对
					if(!MJFactory.getHuCard(AFMJDDHuImpl.class).checkHuCard(this, mCardInitNotJin(0))){
						// 无金不可胡
						this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.DeGuo));
					}
				}
				// 检测对对胡
				if(this.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.PPHu)){
					// 有金可胡对对胡
					if(!MJFactory.getHuCard(AFMJPPImpl.class).checkHuCard(this, mCardInitNotJin(0))){
						// 无金不可胡
						this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.DeGuo));
					}
				}
				// 检测七星十三烂
				if(this.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.QiXingShiSanLan)){
					// 有金可胡七星十三烂
					if(AFMJOpPoint.Not.equals(MJFactory.getHuCard(AFMJSSBKQingImpl.class).checkHuCardReturn(this, mCardInitNotJin(0)))){
						// 无金不可胡
						this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.DeGuo));
					}
				}
				// 检测十三烂
				if(this.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.ShiSanLan)){
					// 有金可胡十三烂
					if(AFMJOpPoint.Not.equals(MJFactory.getHuCard(AFMJSSBKImpl.class).checkHuCardReturn(this, mCardInitNotJin(0)))) {
						// 无金不可胡
						this.getPosOpRecord().getOpHuList().removeIf(k -> k.equals(AFMJOpPoint.DeGuo));
					}
				}
			}

			if(((AFMJRoom)getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoYiJing) && !this.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.DeGuo)){
				// 有必博一精玩法，且没有德国------------------------------
				boolean isBoJing= false; // 是否博精:打出精牌
				// 检测博精
				isBoJing = checkBo1Jing();
				if(isBoJing){
					// 博1精
					opType = OpType.Hu;	// 胡
				}else { // 没有打出精牌，检测手牌是否有精牌
					if(this.allCards().stream().noneMatch(k -> jset.getmJinCardInfo().getJinKeys().contains(k.type))){ // 手牌没有精牌
						opType = OpType.Hu;	// 胡
					}
				}

			}else if(((AFMJRoom)getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoZhengJingHuoLiangFu) && !this.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.DeGuo)){
				// 有必博正精或两副玩法，且没有德国------------------------------
				boolean isBoJing= false; // 是否博精:打出精牌
				// 检测博精
				isBoJing = checkBo1ZhengJingOr2FuJingByDeGuo();
				if(isBoJing){
					// 博1正精或2副精
					opType = OpType.Hu;	// 胡
				}else { // 没有打出精牌，检测手牌是否有精牌
					if(this.allCards().stream().noneMatch(k -> jset.getmJinCardInfo().getJinKeys().contains(k.type))){ // 手牌没有精牌
						opType = OpType.Hu;	// 胡
					}
				}

			}else{
				// 没有德国牌型时没有必博一精玩法且没有必博正精或两副玩法，或有德国牌型，不检测是否博精------------------------------
				// 必博一精, 胡德国不受限制。
				// 必博正精或两副, 胡德国不受限制。
				opType = OpType.Hu;	// 胡
			}

		}

		if (!OpType.Not.equals(opType)) { // 可以胡
			getPosOpRecord().setHuCardType(1000);
			opTypes.add(OpType.Hu);
		}

		if (opTypes.size() > 0) {
			this.setmHuOpType(MJHuOpType.ZiMo);
		}

		if (checkOpType(0, OpType.Ting)){
			opTypes.add(OpType.Ting);
		}

		// 有杠不杠后面可以继续杠：暗杠
		if (checkOpType(0, OpType.AnGang))
			opTypes.add(OpType.AnGang);
		// 有杠不杠后面可以继续杠：补杠
		if (checkOpType(0, OpType.Gang))
			opTypes.add(OpType.Gang);
		opTypes.add(OpType.Out);
		return opTypes;
	}

	@SuppressWarnings({"unchecked" })
	@Override
	public BaseMJRoom_PosEnd<AFMJCalcPosEnd> calcPosEnd() {
		// 玩家当局分数结算
		this.getCalcPosEnd().calcPosEnd(this);
		// 位置结算信息
		AFMJRoom_PosEnd ret = (AFMJRoom_PosEnd)this.posEndInfo();

		// 单局结算的模块分数：胡牌分、上精分、下精分、飘精、杠分
		// 胡牌分
		ret.setHuPaiPoint(this.huPaiPoint);
		// 上精分
		ret.setShangJingPoint(this.shangJingPoint);
		// 下精分
		ret.setXiaJingPoint(this.xiaJingPoint);
		// 博精
		ret.setBoJingPoint(this.boJingPoint);
		// 杠分
		ret.setGangPoint(this.anGangPoint + this.zhiGangPoint + this.pengGangPoint);

		ret.setEndPoint(this.getCalcPosEnd().getCalcPosEnd());
		return ret;
	}

	/**
	 * 清空操作状态
	 */
	public void cleanOp() {
		this.setOp.cleanOp();
		this.getPosOpNotice().clearBuNengChuList();
		this.clearGangList(); // 打牌后清空报听的杠列表
	}

	/**
	 * 操作
	 */
	public int isOpSize() {
		return this.setOp.isOpSize();
	}

	/**
	 * 添加补花操作记录
	 */
	public void addBuHuaOp() {
		this.setOp.addOp(this, true, OpType.BuHua);
	}

	public void calcResults() {
		AFMJResults lResults = (AFMJResults) this.mResultsInfo();
		this.setResults(lResults);

	}

	@Override
	public <T> void calcOpPointType(T opType, int count) {

	}

	/**
	 * 添加花
	 */
	public void addHua(int cardId) {
		int cardType = cardId / 100;
		if (cardType >= MJSpecialEnum.NOT_HUA.value()) {
			this.addBuHuaOp();
			this.getPosOpRecord().getHuaList().add(cardId);
			this.jset.setHuaCount();
		}
	}

	public void setMoPai(boolean isMoPai) {
	}

	public AFMJSetPosRobot getSetPosRobot() {
		return new AFMJSetPosRobot(this);
	}

	@Override
	protected AbsBaseResults newResults() {
		return new AFMJResults();
	}

	/**
	 * 获取玩家牌型数据和金数量
	 * @param allCardList 手上牌
	 * @param cardType    头牌
	 * @param isJin       是否金
	 */
	public MJCardInit mCardInit(List<MJCard> allCardList, int cardType, boolean isJin) {
		MJCardInit mCardInit = mjCardInit(allCardList, isJin);
		if (null == mCardInit) {
			return mCardInit;
		}
		// 牌类型大于 0 , 炮胡
		// 精当本身用的时候，可以胡别人打出的精；
		if (cardType > 0) {
			mCardInit.addCardInts(cardType);
		}
		return mCardInit;
	}

	/**
	 * 检查补花是否成功。
	 */
	public boolean checkBuHuaSuccess(AbsMJSetPos mSetPos,int cardID) {

		if (null == this.getHandCard() || this.sizePrivateCard() <= 0) {
			// 没有首牌 或者 私有牌 <= 0，则不成功
			return false;
		}
		// 检查是否首牌花
		if (this.getHandCard().cardID == cardID) {
			// 首牌补花
			MJFactory.getOpCard(AFMJBuHuaImpl.class).checkOpCard(mSetPos, FlowerEnum.HAND_CARD.ordinal());
			return true;
		}
		// 检查私有牌中是否有花
		if(this.getHandCard().cardID != cardID) {
			// 私有牌补花
			mSetPos.setOpCardId(cardID);
			MJFactory.getOpCard(AFMJBuHuaImpl.class).checkOpCard(mSetPos, FlowerEnum.PRIVATE.ordinal());
			return true;
		}
		return false;
	}

	/**
	 * 花数量
	 */
	public int sizeHua () {
		return this.getPosOpRecord().getHuaList().size();
	}



	/**
	 * 检查首牌是否花
	 */
	public boolean checkHandCardHua() {
		MJCard card = this.getHandCard();
		if (null != card) {
			// 首牌类型是花，强制补花。
			if (card.type >= 50) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 获取自动强制补花
	 */
	public int getAutoForceHua() {
		MJCard card = this.getHandCard();
		if (null != card) {
			// 首牌类型是花，强制补花。
			if (card.type >= 50) {
				return card.cardID;
			}
		}
		// 私有牌中找花;
		return this.getPrivateHua();
	}

	/**
	 * 私有牌中找花
	 */
	private int getPrivateHua() {
		// 是自己
		int length = sizePrivateCard();
		MJCard mCard;
		for (int i = 0; i < length; i++) {
			mCard = this.getPCard(i);
			if (null == mCard) {
				continue;
			}
			// 如果有指定的牌，直接返回成功
			if(mCard.type >= 50) {
				return mCard.cardID;
			}
		}
		return 0;
	}


	@Override
	protected List<Integer> getShouCard(final boolean isSelf) {
		List<Integer> mCards = Lists.newArrayList();
		/*for (int i = 0,length = sizePrivateCard(); i < length; i++) {
			//　金放在最左边
			if(this.checkJin(true, getPrivateCard().get(i).cardID)){
				mCards.add(isSelf ? getPrivateCard().get(i).cardID : 0);
			}
		}

		for (int i = 0,length = sizePrivateCard(); i < length; i++) {
			//　普通牌放在金右边
			if( !this.checkJin(true, getPrivateCard().get(i).cardID)){
				mCards.add(isSelf ? getPrivateCard().get(i).cardID : 0);
			}
		}*/
		// 金牌正常排序
		for (int i = 0, length = sizePrivateCard(); i < length; i++) {
			mCards.add(isSelf ? getPrivateCard().get(i).cardID : 0);
		}
		return mCards;
	}

	/**
	 * 去除重复的牌
	 */
	public void distinct() {
		setPrivateCard(getPrivateCard().stream().filter(card -> !(getHandCard() != null && card.cardID == getHandCard().cardID)).distinct().collect(Collectors.toList()));
	}


	@Override
	public void clear() {
		super.clear();
		setLouHuZuiDaPaiXingFen(0); // 清除漏胡最大牌型分
	}

	/**
	 * 过手
	 */
	public void clearPass() {
		// 清空漏胡类型列表
		this.getPosOpRecord().clearHuCardType();
		// 清空漏碰类型列表
		this.getPosOpRecord().clearOpCardType();
		// 清除漏胡最大牌型分
		setLouHuZuiDaPaiXingFen(0);
	}

	/**
	 * 检测是否博1精
	 * 至少打出去过1张精牌（正、副都行）
	 *
	 * @return 是否博1精
	 */
	public boolean checkBo1Jing(){
		boolean isBo1Jing = false; // 是否博一精
		// 打出去区域
		List<Integer> OutCardIDs = getOutCardIDs();
		for(Integer cardId : OutCardIDs){
			if(jset.getmJinCardInfo().getJinKeys().contains(cardId / 100)){
				isBo1Jing = true;
				break;
			}
		}
		return isBo1Jing;
	}

	/**
	 * 检测是否博1正精或2副精
	 * 至少打出去过1张正精或2张副精
	 * 没有德国牌型：打出2张副精
	 *
	 * @return 是否博1正精或2副精
	 */
	public boolean checkBo1ZhengJingOr2FuJingNotDeGuo(){
		boolean isBo1ZhengJingOr2FuJing = false; // 是否博1正精或2副精
		// 打出去区域
		int outfuJingCount = 0; // 打出去的副精牌数
		List<Integer> OutCardIDs = getOutCardIDs();
		for(Integer cardId : OutCardIDs){
			if(jset.getZhengJing().type == cardId / 100){ // 有打出去的正精
				isBo1ZhengJingOr2FuJing = true;
				break;
			}
			if(jset.getFuJing().type == cardId / 100){ // 有打出去的副精
				outfuJingCount += 1;
			}
			if(outfuJingCount == 2){ // 打出去2张副精
				isBo1ZhengJingOr2FuJing = true;
				break;
			}
		}
		return isBo1ZhengJingOr2FuJing;
	}

	/**
	 * 检测是否博1正精或2副精
	 * 至少打出去过1张正精或2张副精
	 *
	 * @return 是否博1正精或2副精
	 */
	public boolean checkBo1ZhengJingOr2FuJingByDeGuo(){
		boolean isBo1ZhengJingOr2FuJing = false; // 是否博1正精或2副精
		// 打出去区域
		int outfuJingCount = 0; // 打出去的副精牌数
		List<Integer> OutCardIDs = getOutCardIDs();
		for(Integer cardId : OutCardIDs){
			if(jset.getZhengJing().type == cardId / 100){ // 有打出去的正精
				isBo1ZhengJingOr2FuJing = true;
				break;
			}
			if(jset.getFuJing().type == cardId / 100){ // 有打出去的副精
				outfuJingCount += 1;
			}
			if(outfuJingCount == 2){ // 打出去2张副精
				isBo1ZhengJingOr2FuJing = true;
				break;
			}
		}
		return isBo1ZhengJingOr2FuJing;
	}

	/**
	 * 获取无金牌
	 *
	 * @param cardType 牌类型
	 * @return 无金牌
	 */
	public MJCardInit mCardInitNotJin(int cardType) {
		MJCardInit mCardInit = this.mCardInit(this.allCards(), cardType, false);
		return mCardInit;
	}


	/**
	 * 检测新位置结算信息
	 *
	 * @return 新位置结算信息
	 */
	@Override
	protected AFMJRoom_PosEnd newMJSetPosEnd() {
		return new AFMJRoom_PosEnd();
	}


	/**
	 * 检测玩家手牌里是否有精
	 * 冰冻功能：勾选后，玩家手牌（碰杠的精不计）里有精只能自摸；未勾选，无该限制。
	 *
	 * @return true: 玩家手牌里有精; false: 玩家手牌里没有精;
	 */
	public boolean existJing(){
		return allCards().stream().anyMatch(k -> k.type == jset.getZhengJing().type || k.type == jset.getFuJing().type);
	}

	/**
	 * 更新胡牌分
	 */
	public void addHuPaiPoint(int huPaiPoint){
		this.huPaiPoint += huPaiPoint;
	}

	/**
	 * 更新上精分
	 */
	public void addShangJingPoint(int shangJingPoint){
		this.shangJingPoint += shangJingPoint;
	}

	/**
	 * 更新下精分
	 */
	public void addXiaJingPoint(int xiaJingPoint){
		this.xiaJingPoint += xiaJingPoint;
	}

	/**
	 * 更新博精分
	 */
	public void addBoJingPoint(int boJingPoint){
		this.boJingPoint += boJingPoint;
	}

	/**
	 * 更新暗杠分
	 */
	public void addAnGangPoint(int anGangPoint){
		this.anGangPoint += anGangPoint;
	}

	/**
	 * 更新碰杠分
	 */
	public void addPengGangPoint(int pengGangPoint){
		this.pengGangPoint += pengGangPoint;
	}

	/**
	 * 更新直杠分
	 */
	public void addZhiGangPoint(int zhiGangPoint){
		this.pengGangPoint += pengGangPoint;
	}

	/**
	 * 手牌牌序
	 * 精牌牌型不放在最左边，正常次序摆牌
	 */
	@Override
	public void sortCards() {
		Collections.sort(this.getPrivateCards(), new Comparator<MJCard>() {
			@Override
			public int compare(MJCard o1, MJCard o2) {
				// 从小到大
				return o1.cardID - o2.cardID;
			}
		});
		this.sortBaiBanCards();
	}

	/**
	 * 玩家是否可以检查胡
	 */
	public boolean checkPosTing(MJCardInit mInit, OpType opType) {
		int sizeHua = this.calcHuaPoint(mInit);
		// 无花可胡：对对胡、清一色、混一色、大字；（仅这些牌型可以自摸+点炮可胡）
		if (sizeHua >= 1) {
			// 一花可胡：自摸（抢杠算自摸，被抢杠者承包所有人的分）(所有牌型都能自摸+无花的牌型点炮可胡)
			return true;
		}
		return false;
	}

	/**
	 * 计算花分
	 *
	 * @param :胡牌后，F:胡牌前
	 */
	public int calcHuaPoint(MJCardInit mInit) {

		int value = this.getPosOpRecord().getHuaList().size();

		return value;
	}

	/**
	 * 手牌是否有精牌
	 *
	 * @return true:手牌有精牌; false:手牌没有精牌
	 */
	public boolean existJingByAllCards(){
		return this.allCards().stream().anyMatch(k -> jset.getmJinCardInfo().getJinKeys().contains(k.type));
	}

	/**
	 * 是否博一精
	 * “必博一精”或“必博正精或两副”玩法下，如果玩家手牌没有精，且没有满足博精条件，则不能炮胡、抢杠胡，只能自摸。
	 *
	 * @return true:博一精 false:没有博一精
	 */
	public boolean existBo1Jin(){
		// 不是随精玩法
		if(this.getOutCardIDs().stream().noneMatch(k -> jset.getmJinCardInfo().checkJinExist(k))) {
			return false;
		}
		return true;
	}

	/**
	 * 是否博一正精或博两副精
	 * “必博一精”或“必博正精或两副”玩法下，如果玩家手牌没有精，且没有满足博精条件，则不能炮胡、抢杠胡，只能自摸。
	 *
	 * @return true:博一正精或博两副精；false:没有博一正精且没有博两副精
	 */
	public boolean existBo1ZhengJinOrBo2FuJing(){
		// 博正精数
		long boZhengJingCount = this.getOutCardIDs().stream().filter(k -> jset.getZhengJing().type == k / 100).count();
		// 博副精数
		long boFuJingCount = this.getOutCardIDs().stream().filter(k -> jset.getFuJing().type == k / 100).count();
		if(boZhengJingCount < 1L && boFuJingCount < 2L) {
			// 没有博一正精、没有博两副精
			return false;
		}
		return true;
	}

}
