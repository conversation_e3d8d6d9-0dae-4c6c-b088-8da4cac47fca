package business.wxpdk.c2s.iclass;

import cenum.RoomTypeEnum;
import jsproto.c2s.cclass.room.BaseCreateRoom;
import jsproto.c2s.iclass.room.SBase_Config;


@SuppressWarnings("serial")
public class SWXPDK_Config extends SBase_Config {
    public static SWXPDK_Config make(BaseCreateRoom cfg,RoomTypeEnum roomTypeEnum) {
    	SWXPDK_Config ret = new SWXPDK_Config();
        ret.setCfg(cfg);
        ret.setRoomType(roomTypeEnum);
        return ret;
    }
}
