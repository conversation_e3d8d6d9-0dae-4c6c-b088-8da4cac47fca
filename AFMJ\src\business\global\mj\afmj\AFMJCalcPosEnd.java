package business.global.mj.afmj;

import java.util.*;

import business.global.mj.MJCard;
import business.global.mj.MJSetPos;
import business.global.mj.afmj.AFMJRoomEnum.*;
import business.global.mj.AbsCalcPosEnd;
import business.global.mj.AbsMJSetPos;
import cenum.mj.HuType;
import cenum.mj.MJHuOpType;
import cenum.mj.OpType;

/**
 * 打完计分
 */
public class AFMJCalcPosEnd extends AbsCalcPosEnd {

	private final AFMJSetPos mSetPos;
	private final Map<AFMJOpPoint, Integer> huTypeMap = new LinkedHashMap<>(); // 有顺序的动作map
	private final AFMJRoom room;
	private final AFMJRoomSet set;

	private int huPaiFen = 0; // 胡牌分
	private int paiXingFen = AFMJOpPoint.PaiXingDiFen.value(); // 牌型分

	// 杠分
	private int anGangFen = 0; // 暗杠分
	private int pengGangFen = 0; // 碰杠分
	private int zhiGangFen = 0; // 直杠分
	// 精分
	private int zhengJingFen = 0; // 正精分
	private int fuJingFen = 0; // 副精分
	private int shangJingFen = 0; // 上精分
	private int xiaJingFen = 0; // 下精分
	private int zhengGangJingFen = 0; // 正杠精分
	private int fuGangJingFen = 0; // 副杠精分
	private int qiTaGangJingFen = 0; // 其他杠精分
	private int jingFen = 0; // 精分总分
	// 精数
	private int qiTaZhengJingShu = 0; // 其他正精数
	private int qiTaFuJingShu = 0; // 其他副精数
	private int baWangJing = 1; // 霸王精
	// 冲关
	private int chongGuanShu = 1; // 冲关数
	// 博精
	private int boJingBeiShu = 1; // 博精倍数
	// 包冲
	private int baoChongPosA = -1; // 包冲玩家A,玩家A对玩家B包冲
	private int baoChongPosB = -1; // 包冲玩家B,玩家A对玩家B包冲


	public AFMJCalcPosEnd(AFMJSetPos mSetPos) {
		super(mSetPos);
		this.mSetPos = mSetPos;
		this.set = (AFMJRoomSet) mSetPos.getSet();
		this.room = (AFMJRoom) mSetPos.getRoom();
	}

	public Map<AFMJOpPoint, Integer> getAfmjHuTypeMap() {
		return huTypeMap;
	}


	@Override
	public void calcPosEnd(AbsMJSetPos mSetPos) {}

	@Override
	public void calcPosPoint(AbsMJSetPos mSetPos) {

		// 流局时，算杠分、精分；
		// 中途解散，算杠分、精分；
		if (set.getMHuInfo().isHuEmpty() || !set.getEndFlag()) {
			// 杠分： ——所有玩家的杠都算分，每家付（精牌的杠分依然需要计算）
			calcGang((AFMJSetPos) mSetPos); // 结算杠分
			// 精分：——所有玩家的精都算分
			calcJing((AFMJSetPos)mSetPos); // 结算精分
			return;
		}

		// 杠分： ——所有玩家的杠都算分，每家付（精牌的杠分依然需要计算）
		calcGang((AFMJSetPos)mSetPos); // 结算杠分

		// 精分：——所有玩家的精都算分
		calcJing((AFMJSetPos)mSetPos); // 结算精分

		countHuPoint(mSetPos); // 胡牌玩家结算分

	}


	/**
	 * 结算分
	 */
	public void countHuPoint(AbsMJSetPos mSetPos) {
		switch (mSetPos.getHuType()) {
			case NotHu:
			case DianPao:
				break;
			default:
				calcHu(mSetPos);
				break;
		}
	}


	/**
	 * 结算分
	 */
	public void calcHu(AbsMJSetPos mSetPos) {

		calcpaiXingFen(mSetPos); // 结算牌型分

		if (MJHuOpType.JiePao.equals(mSetPos.getmHuOpType()) || MJHuOpType.QGHu.equals(mSetPos.getmHuOpType())) {
			this.setDianPao(mSetPos);
			this.calcPingHu((AFMJSetPos)mSetPos); // 结算平胡
		} else if (MJHuOpType.ZiMo.equals(mSetPos.getmHuOpType())) {
			this.calcZiMoHu((AFMJSetPos)mSetPos); // 结算自摸
		}

		setClientHuTypeMap(); // 客户端显示胡牌玩家的牌型、结算分

	}

	/**
	 * 设置点炮用户
	 */
	public void setDianPao (MJSetPos mSetPos) {
		MJSetPos aSetPos = mSetPos.getMJSetPos(mSetPos.getSet().getLastOpInfo().getLastOpPos());
		aSetPos.setHuType(HuType.DianPao);
	}


	// 结算平胡
	public void calcPingHu (AFMJSetPos mSetPos) {
		AFMJSetPos aSetPos;
		AFMJSetPos dianPaoSetPos = (AFMJSetPos)mSetPos.getMJSetPos(mSetPos.getSet().getLastOpInfo().getLastOpPos());
		for (int i = 0; i < mSetPos.getPlayerNum();i++) {
			int calcPoint;
			int aPaiXingFen = paiXingFen; // 每个玩家的牌型分
			aSetPos = (AFMJSetPos)mSetPos.getMJSetPos(i); // 闲家
			if (null == aSetPos) {
				continue;
			}
			if (aSetPos.getPosID() == mSetPos.getPosID()) {
				continue;
			}
			// 胡牌分：天胡/地胡/   牌型分×博精倍数
			// 总分：胡牌分+精分+杠分
			if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.TianHu)
					|| mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.DiHu)){
				// 天地胡不叠加牌型分
			}else {
				// 牌型分×博精倍数
				if(aSetPos.getPosID() == dianPaoSetPos.getPosID()){
					if(MJHuOpType.QGHu.equals(mSetPos.getmHuOpType())){	// 抢杠胡：×4
						aPaiXingFen *= AFMJOpPoint.QGH.value();
					}else { // 炮胡：点炮玩家×2，其他玩家×1；
						aPaiXingFen *= AFMJOpPoint.PaoHu.value();
					}
				}
				if(mSetPos.getPosID() == set.getDPos()){ // 庄家胡
					aPaiXingFen *= AFMJOpPoint.ZhuangJia.value(); // 庄家输赢都翻倍
				}else{ // 闲家胡
					if(aSetPos.getPosID() == set.getDPos()){
						aPaiXingFen *= AFMJOpPoint.ZhuangJia.value(); // 庄家输赢都翻倍
					}
				}
				// 德国：×2+5（此处的+5需要在牌型倍数都×完再＋）
				if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.DeGuo)){
					aPaiXingFen += AFMJOpPoint.DeGuoJiaFen.value();
				}

				aPaiXingFen *= boJingBeiShu; // 牌型分×博精倍数
				aPaiXingFen = fenDing(aPaiXingFen); // 封顶
			}
			if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.TianHu)
					|| mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.DiHu)){ // 天地胡
				calcPoint = huPaiFen;
			}else {
				calcPoint = aPaiXingFen;
			}
			if(room.RoomCfg(AFMJKeXuanWanFa.XiaoJu4She5Ru)){
				// 小局四舍五入：勾选后，计分时，胡牌分和精分都四舍五入，杠分不受影响。未勾选，则实际几分就按几分算。
				// “小局四舍五入”玩法下，胡牌分和精分都四舍五入，例：24按20算，28按30算。
				int huPaiFenGeWeiShu = calcPoint % 10; // 胡牌分个位数
				if(huPaiFenGeWeiShu <= 4){ // 四舍
					calcPoint = (calcPoint / 10) * 10;
				}else { // 五入
					calcPoint = (calcPoint / 10) * 10 + 10;
				}
			}
			// 结算胡牌分
			aSetPos.setEndPoint(aSetPos.getEndPoint() - calcPoint); // 扣分玩家
			mSetPos.setEndPoint((mSetPos.getEndPoint() + calcPoint)); // 胡的玩家
			// 更新胡牌分
			aSetPos.addHuPaiPoint(calcPoint * -1); // 扣分玩家
			mSetPos.addHuPaiPoint(calcPoint); // 胡的玩家
		}

	}

	// 结算自摸
	public void calcZiMoHu (AFMJSetPos mSetPos) {
		AFMJSetPos aSetPos;
		for (int i = 0; i < mSetPos.getPlayerNum();i++) {
			int calcPoint;
			int aPaiXingFen = paiXingFen; // 每个玩家的牌型分
			aSetPos = (AFMJSetPos)mSetPos.getMJSetPos(i); // 闲家
			if (null == aSetPos)continue;
			if (aSetPos.getPosID() == mSetPos.getPosID())continue;
			// 胡牌分：天胡/地胡/   牌型分×博精倍数
			// 总分：胡牌分+精分+杠分
			if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.TianHu)
					|| mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.DiHu)){
				// 天地胡不叠加牌型分
			}else {
				// 牌型分×博精倍数
				aPaiXingFen *= AFMJOpPoint.ZiMo.value(); // 自摸翻倍
				if(mSetPos.getPosID() == set.getDPos()){ // 庄家胡
					aPaiXingFen *= AFMJOpPoint.ZhuangJia.value(); // 庄家输赢都翻倍
				}else{ // 闲家胡
					if(aSetPos.getPosID() == set.getDPos()){
						aPaiXingFen *= AFMJOpPoint.ZhuangJia.value(); // 庄家输赢都翻倍
					}
				}
				// 德国：×2+5（此处的+5需要在牌型倍数都×完再＋）
				if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.DeGuo)){
					aPaiXingFen += AFMJOpPoint.DeGuoJiaFen.value();
				}
				aPaiXingFen *= boJingBeiShu; // 牌型分×博精倍数
				aPaiXingFen = fenDing(aPaiXingFen); // 封顶
			}
			if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.TianHu)
					|| mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.DiHu)){ // 天地胡
				calcPoint = huPaiFen;
			}else {
				calcPoint = aPaiXingFen;
			}
			if(room.RoomCfg(AFMJKeXuanWanFa.XiaoJu4She5Ru)){
				// 小局四舍五入：勾选后，计分时，胡牌分和精分都四舍五入，杠分不受影响。未勾选，则实际几分就按几分算。
				// “小局四舍五入”玩法下，胡牌分和精分都四舍五入，例：24按20算，28按30算。
				int huPaiFenGeWeiShu = calcPoint % 10; // 胡牌分个位数
				if(huPaiFenGeWeiShu <= 4){ // 四舍
					calcPoint = (calcPoint / 10) * 10;
				}else { // 五入
					calcPoint = (calcPoint / 10) * 10 + 10;
				}
			}
			// 结算胡牌分
			aSetPos.setEndPoint(aSetPos.getEndPoint() - calcPoint); // 扣分玩家
			mSetPos.setEndPoint((mSetPos.getEndPoint() + calcPoint)); // 胡的玩家
			// 更新胡牌分
			aSetPos.addHuPaiPoint(calcPoint * -1); // 扣分玩家
			mSetPos.addHuPaiPoint(calcPoint); // 胡的玩家
		}
	}

	@Override
	public int calcPoint(boolean isZhuang, Object... params) {
		return 0;
	}

	@Override
	public <T> void calcOpPointType(T opType, int count) {

	}


	@Override
	public CalcPosEnd getCalcPosEnd() {
		//return null;
		return new CalcPosEnd(this.huTypeMap);
	}

	private class CalcPosEnd {
		@SuppressWarnings("unused")
		private Map<AFMJOpPoint, Integer> huTypeMap = new HashMap<>();

		public CalcPosEnd(Map<AFMJOpPoint, Integer> huTypeMap) {
			this.huTypeMap = huTypeMap;
		}
	}


	/**
	 * 结算杠、明刻
	 */
	public void calcGang(AFMJSetPos mSetPos) {
		List<List<Integer>> publicCardList = new ArrayList<>(mSetPos.getPublicCardList());
		for (List<Integer> publicCards : publicCardList) {
			int type = publicCards.get(0);
			int cardType = publicCards.get(2) / 100;
			if(set.getmJinCardInfo().getJinKeys().contains(cardType)){ // 杠精,不叠加以下杠分
				continue;
			}
			if (type == OpType.JieGang.value()) { // 明杠：接杠
				zhiGangFen += AFMJOpPoint.ZhiGang.value();
			} else if (type == OpType.Gang.value()) { // 明杠：补杠
				pengGangFen += AFMJOpPoint.PengGang.value();
			}else if (type == OpType.AnGang.value()) { // 暗杠
				anGangFen += AFMJOpPoint.AnGang.value();
			}
		}
		// 杠分
		AFMJSetPos aSetPos;
		int calcPoint;
		for (int i = 0; i < mSetPos.getPlayerNum();i++) {
			aSetPos = (AFMJSetPos)mSetPos.getMJSetPos(i); // 玩家
			if (null == aSetPos) continue;
			if (aSetPos.getPosID() == mSetPos.getPosID()) continue;
			calcPoint =  zhiGangFen + pengGangFen + anGangFen;
			// 结算杠分
			aSetPos.setEndPoint(aSetPos.getEndPoint() - calcPoint); // 扣杠分玩家
			mSetPos.setEndPoint((mSetPos.getEndPoint() + calcPoint)); // 开杠玩家
			// 更新杠分
			aSetPos.addZhiGangPoint(zhiGangFen * -1); // 扣杠分玩家
			aSetPos.addPengGangPoint(pengGangFen * -1); // 扣杠分玩家
			aSetPos.addAnGangPoint(anGangFen * -1); // 扣杠分玩家
			mSetPos.addZhiGangPoint(zhiGangFen); // 开杠玩家
			mSetPos.addPengGangPoint(pengGangFen); // 开杠玩家
			mSetPos.addAnGangPoint(anGangFen); // 开杠玩家
		}
	}

	/**
	 * 结算精分
	 * 精牌分：正精一张2分，副精一张1分。
	 * 需计算每个玩家手牌、吃碰杠区域以及打出去区域的精牌；
	 * 被别人吃、碰、杠、胡的精算得到玩家的；
	 * 冲关：
	 * 当玩家的精牌分满足以下条件时，有相关冲关倍数；
	 * 总和为5分，冲2关
	 * 总和为6分，冲3关
	 * 总和为7分，冲4关
	 * ···以此类推
	 * 总和为12分，冲9关
	 * 冲关后精分为：精牌分和×关数；
	 * 例：有两个正精，一个副精，精牌分为5分，冲两关，精分为5×2=10分，每个玩家给10分；
	 * 霸王精：所有玩家中仅有一个玩家有精，称为霸王精；
	 * 霸王精玩家精分翻倍；
	 * 仅一个玩家有精，即只有一个玩家的吃碰杠区域、手牌、打出的的牌有精，其他人该区域没有；（具体情况还要看精牌玩法分区域）
	 * 杠精：
	 * 正精：20分；
	 * 副精：10分；
	 * 其他杠精：5分；
	 * 精分总分：精牌分和×冲关倍数×霸王精倍数+杠精分
	 * 不管胡牌或者流局，每个玩家的精都算分；
	 * 精分总分，每家出；（除了“冲一包三”情况）
	 * 冲一包三，由包分玩家包出，出精分总和×（游戏人数-1）
	 * 冲一包三：最后一个打精的玩家A，打出的精被其他玩家B得到，且B玩家冲关或霸王，A玩家包出精分，其他玩家不用出；
	 * 霸王和冲关有满足一个就算；
	 * 玩家B无论得到前是否冲关，得到后有冲关就需要包。
	 */
	public void calcJing(AFMJSetPos mSetPos) {
		// 手牌
		List<MJCard> allCards = mSetPos.allCards();
		for(MJCard mjCard : allCards){
			if(set.getmJinCardInfo().getJinKeys().contains(mjCard.type)){
				if(set.getZhengJing().type == mjCard.type){
					zhengJingFen += AFMJOpPoint.ZhengJing.value(); // 正精分
					// “精不能吃碰”玩法下，玩家手里+打出的精，有四张一样的就算杠精。
					if(room.RoomCfg(AFMJKeXuanWanFa.JinGBuNengChiPeng)){
						qiTaZhengJingShu += 1; // 其他正精数
					}
				}else {
					fuJingFen += AFMJOpPoint.FuJing.value();  // 副精分
					// “精不能吃碰”玩法下，玩家手里+打出的精，有四张一样的就算杠精。
					if(room.RoomCfg(AFMJKeXuanWanFa.JinGBuNengChiPeng)){
						qiTaFuJingShu += 1; // 其他副精数
					}
				}
			}
		}
		// 打出去区域
		List<Integer> OutCardIDs = mSetPos.getOutCardIDs();
		for(Integer cardId : OutCardIDs){
			if(cardId == this.set.getJiePaoCardId()){
				// 被别人吃、碰、杠、胡的精算得到玩家的；
				continue;
			}
			if(set.getmJinCardInfo().getJinKeys().contains(cardId / 100)){
				if(set.getZhengJing().type == cardId / 100){
					zhengJingFen += AFMJOpPoint.ZhengJing.value(); // 正精分
					qiTaZhengJingShu += 1; // 其他正精数
				}else {
					fuJingFen += AFMJOpPoint.FuJing.value();  // 副精分
					qiTaFuJingShu += 1; // 其他副精数
				}
			}
		}
		// 其他杠精分
		if(qiTaZhengJingShu == 4){
			qiTaGangJingFen += AFMJOpPoint.QiTaGangJing.value();
		}
		if(qiTaFuJingShu == 4){
			qiTaGangJingFen += AFMJOpPoint.QiTaGangJing.value();
		}

		// 吃碰杠区域
		List<List<Integer>> publicCardList = new ArrayList<>(mSetPos.getPublicCardList());
		for (List<Integer> publicCards : publicCardList) {
			int type = publicCards.get(0);
			int cardType = publicCards.get(2) / 100;
			if (type == OpType.JieGang.value() || type == OpType.Gang.value() || (type == OpType.AnGang.value())) { // 杠
				if(set.getmJinCardInfo().getJinKeys().contains(cardType)){
					if(set.getZhengJing().type == cardType){
						zhengGangJingFen += AFMJOpPoint.ZhengGangJing.value(); // 正杠精分
						zhengJingFen += 4 * AFMJOpPoint.ZhengJing.value(); // 正精分
					}else {
						fuGangJingFen += AFMJOpPoint.FuGangJing.value();  // 副杠精分
						fuJingFen += 4 * AFMJOpPoint.FuJing.value();  // 副精分
					}
				}
			} else if (type == OpType.Peng.value()) { // 碰
				if(set.getmJinCardInfo().getJinKeys().contains(cardType)){
					if(set.getZhengJing().type == cardType){
						zhengJingFen += 3 * AFMJOpPoint.ZhengJing.value(); // 正精分
					}else {
						fuJingFen += 3 * AFMJOpPoint.FuJing.value();  // 副精分
					}
				}
			}else if (type == OpType.Chi.value()) { // 吃
				if(set.getZhengJing() != null && set.getFuJing() != null){
					if(publicCards.contains(set.getZhengJing().type)){
						zhengJingFen += AFMJOpPoint.ZhengJing.value(); // 正精分
					}
					if(publicCards.contains(set.getFuJing().type)){
						fuJingFen += AFMJOpPoint.FuJing.value(); // 副精分
					}
				}
			}

		}

		// 霸王精
		List<Integer> jingPosList = new ArrayList<>();
		for (int i = 0;i < mSetPos.getPlayerNum();i++) {
			AFMJSetPos aSetPos = (AFMJSetPos)mSetPos.getMJSetPos(i); // 闲家
			if (null == aSetPos) continue;
			if(baWangJing(aSetPos))jingPosList.add(aSetPos.getPosID());
		}
		if(jingPosList.size() == 1){
			if(jingPosList.get(0) == mSetPos.getPosID())baWangJing *= AFMJOpPoint.BaWangJing.value();
		}

		// 冲关
		switch (zhengJingFen + fuJingFen){
			case 5:chongGuanShu = 2;break;
			case 6:chongGuanShu = 3;break;
			case 7:chongGuanShu = 4;break;
			case 8:chongGuanShu = 5;break;
			case 9:chongGuanShu = 6;break;
			case 10:chongGuanShu = 7;break;
			case 11:chongGuanShu = 8;break;
			case 12:chongGuanShu = 9;break;
			default:chongGuanShu = 1;break;
		}
		// 精分总分：精牌分和×冲关倍数×霸王精倍数+杠精分
		// 精分：上精分
		shangJingFen = (zhengJingFen + fuJingFen) * chongGuanShu * baWangJing  + (zhengGangJingFen + fuGangJingFen + qiTaGangJingFen);
		if(room.RoomCfg(AFMJKeXuanWanFa.XiaoJu4She5Ru)){
			// 小局四舍五入：勾选后，计分时，胡牌分和精分都四舍五入，杠分不受影响。未勾选，则实际几分就按几分算。
			// “小局四舍五入”玩法下，胡牌分和精分都四舍五入，例：24按20算，28按30算。
			int shangJingFenGeWeiShu = shangJingFen % 10; // 上精分个位数
			if(shangJingFenGeWeiShu <= 4){ // 四舍
				shangJingFen = (shangJingFen / 10) * 10;
			}else { // 五入
				shangJingFen = (shangJingFen / 10) * 10 + 10;
			}
		}
		// 下精分
		xiaJingFen = mSetPos.getXiaJingFen();
		if(room.RoomCfg(AFMJKeXuanWanFa.XiaoJu4She5Ru)){
			// 小局四舍五入：勾选后，计分时，胡牌分和精分都四舍五入，杠分不受影响。未勾选，则实际几分就按几分算。
			// “小局四舍五入”玩法下，胡牌分和精分都四舍五入，例：24按20算，28按30算。
			int xiaJingFenGeWeiShu = xiaJingFen % 10; // 下精分个位数
			if(xiaJingFenGeWeiShu <= 4){ // 四舍
				xiaJingFen = (xiaJingFen / 10) * 10;
			}else { // 五入
				xiaJingFen = (xiaJingFen / 10) * 10 + 10;
			}
		}
		// 总精分
		jingFen = shangJingFen + xiaJingFen;

		// 不冲关不算精分：勾选后，算精分时，如果玩家精分未冲关，则该玩家的精分不计。未勾选，无论是否冲关，精分都要算。
		if(room.RoomCfg(AFMJKeXuanWanFa.BuChongGuanBuSuanJingFen) && chongGuanShu == 1){
			shangJingFen = 0;
			xiaJingFen = 0;
			jingFen = 0;
		}

		// 冲一包三
		// 冲一包三：最后一个打精的玩家A，打出的精被其他玩家B得到，且B玩家冲关或霸王，A玩家包出精分，其他玩家不用出；
		// 如果B不是霸王，则A不包出；
		// 如果B玩家得到这张精牌之前不冲关，得到后冲关，则A包出；
		// 如果B玩家得到这张精牌前冲关，则A不包出；
		if(room.RoomCfg(AFMJKeXuanWanFa.ChongYiBaoSan)){ // 可选玩法：冲一包三
			if(set.getDaJingPosList().size() > 0
					&& set.getDaJingCardIdList().size() > 0
					&& set.getDaJingPosList().size() == set.getDaJingCardIdList().size()){ // 有飘精玩家
				int lastDaJingPos = set.getDaJingPosList().get(set.getDaJingPosList().size() - 1); // 最后飘精玩家位置
				int lastDaJingCardId = set.getDaJingCardIdList().get(set.getDaJingPosList().size() - 1); // 最后飘精的精牌id
				// 检测最后飘精的精牌所在玩家
				int lastDeJingPaiPos = cardIdPos(lastDaJingCardId); // 最后飘精的精牌所在玩家
				if(lastDeJingPaiPos >= 0){
					if(lastDaJingPos != lastDeJingPaiPos){
						// 最后打出精牌的玩家A打出的精牌，被玩家B得到
						if(posJing(mSetPos.getMJSetPos(lastDeJingPaiPos),lastDaJingCardId)){ // 包冲
							baoChongPosA = lastDaJingPos;
							baoChongPosB = lastDeJingPaiPos;
						}
					}
				}
			}
		}

		AFMJSetPos aSetPos;
		if(baoChongPosA >= 0 && baoChongPosB >= 0){ // 包冲
			if(mSetPos.getPosID() == baoChongPosB){ // 当前玩家是包冲玩家B
				aSetPos = (AFMJSetPos)mSetPos.getMJSetPos(baoChongPosA); // 包冲玩家A
				// 包冲
				// 结算精分
				aSetPos.setEndPoint(aSetPos.getEndPoint() - jingFen * (mSetPos.getPlayerNum() - 1)); // 扣精分玩家
				mSetPos.setEndPoint(mSetPos.getEndPoint() + jingFen * (mSetPos.getPlayerNum() - 1)); // 加精分玩家
				// 更新上精分
				aSetPos.addShangJingPoint(shangJingFen * (mSetPos.getPlayerNum() - 1)); // 扣精分玩家
				mSetPos.addShangJingPoint(shangJingFen * (mSetPos.getPlayerNum() - 1)); // 加精分玩家
				// 更新下精分
				aSetPos.addXiaJingPoint(xiaJingFen * (mSetPos.getPlayerNum() - 1)); // 扣精分玩家
				mSetPos.addXiaJingPoint(xiaJingFen * (mSetPos.getPlayerNum() - 1)); // 加精分玩家
			}else{ // 当前玩家不是包冲玩家B
				for (int i = 0; i < mSetPos.getPlayerNum();i++) {
					aSetPos = (AFMJSetPos)mSetPos.getMJSetPos(i); // 玩家
					if (null == aSetPos) continue;
					if (aSetPos.getPosID() == mSetPos.getPosID()) continue;
					// 结算精分
					aSetPos.setEndPoint(aSetPos.getEndPoint() - jingFen); // 扣精分玩家
					mSetPos.setEndPoint((mSetPos.getEndPoint() + jingFen)); // 加精分玩家
					// 更新上精分
					aSetPos.addShangJingPoint(shangJingFen * -1); // 扣精分玩家
					mSetPos.addShangJingPoint(shangJingFen); // 加精分玩家
					// 更新下精分
					aSetPos.addXiaJingPoint(xiaJingFen * -1); // 扣精分玩家
					mSetPos.addXiaJingPoint(xiaJingFen); // 加精分玩家
				}
			}
		}else { // 没有包冲
			for (int i = 0; i < mSetPos.getPlayerNum();i++) {
				aSetPos = (AFMJSetPos)mSetPos.getMJSetPos(i); // 玩家
				if (null == aSetPos) continue;
				if (aSetPos.getPosID() == mSetPos.getPosID()) continue;
				// 结算精分
				aSetPos.setEndPoint(aSetPos.getEndPoint() - jingFen); // 扣精分玩家
				mSetPos.setEndPoint((mSetPos.getEndPoint() + jingFen)); // 加精分玩家
				// 更新上精分
				aSetPos.addShangJingPoint(shangJingFen * -1); // 扣精分玩家
				mSetPos.addShangJingPoint(shangJingFen); // 加精分玩家
				// 更新下精分
				aSetPos.addXiaJingPoint(xiaJingFen * -1); // 扣精分玩家
				mSetPos.addXiaJingPoint(xiaJingFen); // 加精分玩家
			}
		}

		// 客户端显示精分
		if(!room.RoomCfg(AFMJKeXuanWanFa.BuChongGuanBuSuanJingFen) || chongGuanShu > 1){ // 不冲关不算精分：勾选后，算精分时，如果玩家精分未冲关，则该玩家的精分不计。未勾选，无论是否冲关，精分都要算。
			// 正精分
			if(zhengJingFen > 0) // 客户端显示正精分
				huTypeMap.put(AFMJOpPoint.ZhengJing,zhengJingFen);
			// 副精分
			if(fuJingFen > 0) // 客户端显示副精分
				huTypeMap.put(AFMJOpPoint.FuJing,fuJingFen);

			// 客户端显示冲关数
			if(chongGuanShu >= 2){
				huTypeMap.put(AFMJOpPoint.ChongGuan,chongGuanShu);
			}

			// 客户端显示霸王精
			if(baWangJing > 1)
				huTypeMap.put(AFMJOpPoint.BaWangJing,baWangJing);

			// 客户端显示杠精分
			if(zhengGangJingFen > 0) // 客户端显示正杠精
				huTypeMap.put(AFMJOpPoint.ZhengGangJing,zhengGangJingFen);
			if(fuGangJingFen > 0) // 客户端显示副杠精
				huTypeMap.put(AFMJOpPoint.FuGangJing,fuGangJingFen);
			if(qiTaGangJingFen > 0) // 客户端显示其他杠精
				huTypeMap.put(AFMJOpPoint.QiTaGangJing,qiTaGangJingFen);
		}


	}

	/**
	 * 结算牌型分
	 * 胡牌分：天胡/地胡/   牌型分×博精倍数
	 * 起手牌型：（不叠加牌型分算分）（优先级：起手牌型>其他牌型）
	 * 天胡：20分
	 * 地胡：20分
	 */
	public void calcpaiXingFen(AbsMJSetPos mSetPos) {

		// 天胡
		if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.TianHu)){
			huPaiFen = AFMJOpPoint.TianHu.value();
			return;
		}

		// 地胡
		if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.DiHu)){
			huPaiFen = AFMJOpPoint.DiHu.value();
			return;
		}

		// 德国：×2+5（此处的+5需要在牌型倍数都×完再＋）
		if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.DeGuo)){
			paiXingFen *= AFMJOpPoint.DeGuo.value();
		}

		// 精吊：×2
		if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.JingDiao)){
			paiXingFen *= AFMJOpPoint.JingDiao.value();
		}

		// 杠上花：×4
		if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.GSKH)){
			paiXingFen *= AFMJOpPoint.GSKH.value();
		}

		// 大七对（即碰碰胡）：×4
		if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.PPHu)){
			paiXingFen *= AFMJOpPoint.PPHu.value();
		}

		// 七小对：×2
		if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.QiDui)){
			paiXingFen *= AFMJOpPoint.QiDui.value();
		}

		// 七星十三烂：×4（不叠加十三烂）
		if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.QiXingShiSanLan)){
			paiXingFen *= AFMJOpPoint.QiXingShiSanLan.value();
		}else if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.ShiSanLan)){
			// 十三烂：×2
			paiXingFen *= AFMJOpPoint.ShiSanLan.value();
		}

		// 博精倍数
		// 博精：
		// 胡牌玩家打出精牌，牌型分相应翻倍；
		// 打出正精：一张×4，两张×4×4，以此类推；
		// 打出副精：一张×2，两张×2×2，以此类推；
		boJingBeiShu *= Math.pow(4 , qiTaZhengJingShu) * Math.pow(2 , qiTaFuJingShu); // 博精倍数

	}


	/**
	 * 封顶
	 * @param point 封顶前的分数
	 * @return 封顶后的分数
	 */
	public int fenDing(int point){
		// 封顶：150封顶,200封顶,300封顶,500封顶,不封顶；
		// 此处的封顶为胡牌玩家的 牌型分×博精倍数 封顶；
		if(room.RoomCfgFengDing(AFMJFengDing.BuFengDing)){ // 不封顶
			return point;
		}else if(room.RoomCfgFengDing(AFMJFengDing.FengDing_150)){ // 150封顶
			if(point > 150)point = 150;
		}else if(room.RoomCfgFengDing(AFMJFengDing.FengDing_200)){ // 200封顶
			if(point > 200)point = 200;
		}else if(room.RoomCfgFengDing(AFMJFengDing.FengDing_300)){ // 300封顶
			if(point > 300)point = 300;
		}else if(room.RoomCfgFengDing(AFMJFengDing.FengDing_500)){ // 500封顶
			if(point > 500)point = 500;
		}
		return point;
	}


	/**
	 * 客户端显示牌型、结算分
	 */
	public void setClientHuTypeMap() {

		// 客户端显示胡牌分

		// 天胡
		if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.TianHu)){
			huTypeMap.put(AFMJOpPoint.TianHu,AFMJOpPoint.TianHu.value());
		}

		// 地胡
		if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.DiHu)){
			huTypeMap.put(AFMJOpPoint.DiHu,AFMJOpPoint.DiHu.value());
		}

		// 精吊：×2
		if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.JingDiao)){
			huTypeMap.put(AFMJOpPoint.JingDiao,AFMJOpPoint.JingDiao.value());
		}

		// 杠上花：×4
		if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.GSKH)){
			huTypeMap.put(AFMJOpPoint.GSKH,AFMJOpPoint.GSKH.value());
		}

		// 大七对（即碰碰胡）：×4
		if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.PPHu)){
			huTypeMap.put(AFMJOpPoint.PPHu,AFMJOpPoint.PPHu.value());
		}

		// 七小对：×2
		if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.QiDui)){
			huTypeMap.put(AFMJOpPoint.QiDui,AFMJOpPoint.QiDui.value());
		}

		// 七星十三烂：×4（不叠加十三烂）
		if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.QiXingShiSanLan)){
			huTypeMap.put(AFMJOpPoint.QiXingShiSanLan,AFMJOpPoint.QiXingShiSanLan.value());
		}else if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.ShiSanLan)){
			// 十三烂：×2
			huTypeMap.put(AFMJOpPoint.ShiSanLan,AFMJOpPoint.ShiSanLan.value());
		}

		// 庄家：×2（庄家输赢翻倍）
		if(mSetPos.getPosID() == set.getDPos()){
			if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.TianHu)
					|| mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.DiHu)) {
				// 天地胡不叠加牌型分
			}else {
				huTypeMap.put(AFMJOpPoint.ZhuangJia,AFMJOpPoint.ZhuangJia.value());
			}
		}


		// 德国：×2+5（此处的+5需要在牌型倍数都×完再＋）
		if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.DeGuo)){
			huTypeMap.put(AFMJOpPoint.DeGuo,AFMJOpPoint.DeGuo.value());
		}


		// 德国：×2+5（此处的+5需要在牌型倍数都×完再＋）
		if(mSetPos.getPosOpRecord().getOpHuList().contains(AFMJOpPoint.DeGuo)){
			huTypeMap.put(AFMJOpPoint.DeGuoJiaFen,AFMJOpPoint.DeGuoJiaFen.value());
		}

		// 博精
		if(qiTaZhengJingShu > 0){
			huTypeMap.put(AFMJOpPoint.ZhengBoJing , (int)Math.pow(4,qiTaZhengJingShu));
		}
		if(qiTaFuJingShu > 0){
			huTypeMap.put(AFMJOpPoint.FuBoJing , (int)Math.pow(2,qiTaFuJingShu));
		}
		// 更新博精倍数
		mSetPos.addBoJingPoint((int)Math.pow(4,qiTaZhengJingShu) * (int)Math.pow(2,qiTaFuJingShu));
	}


	/**
	 *
	 * @param cardId 精牌id
	 * @return 这张精牌所在的玩家位置
	 */
	public int cardIdPos(int cardId){

		AFMJSetPos aSetPos;

		for (int i = 0; i < mSetPos.getPlayerNum();i++) {
			aSetPos = (AFMJSetPos)mSetPos.getMJSetPos(i); // 玩家
			if (null == aSetPos) continue;

			if(aSetPos.getBaoChongJingCardList().contains(cardId)){
				return aSetPos.getPosID();
			}
		}
		return -1;

	}

	/**
	 * 检测玩家的精分是否冲一包三
	 *冲一包三：最后一个打精的玩家A，打出的精被其他玩家B得到，且B玩家冲关或霸王，A玩家包出精分，其他玩家不用出；
	 * 如果B不是霸王，则A不包出；
	 * 如果B玩家得到这张精牌之前不冲关，得到后冲关，则A包出；
	 * 如果B玩家得到这张精牌前冲关，则A不包出；
	 *
	 * @param mSetPos 玩家位置
	 * @param lastDaJingCardId 最后一个打精的精牌id
	 * @return true : 冲一包三，false:不是包冲
	 */
	public boolean posJing(AbsMJSetPos mSetPos,int lastDaJingCardId){

		int zhengJingFen = 0; // 正精分
		int zhengJingShu = 0; // 正精数
		int fuJingFen = 0; // 副精分
		int fuJingShu = 0; // 副精数
		int qiTaZhengJingShu = 0; // 其他正精数
		int qiTaFuJingShu = 0; // 其他副精数
		int qiTaGangJingFen = 0; // 其他杠精分
		int zhengGangJingFen = 0; // 正杠精分
		int fuGangJingFen = 0; // 副杠精分


		// 手牌
		List<MJCard> allCards = mSetPos.allCards();
		for(MJCard mjCard : allCards){
			if(set.getmJinCardInfo().getJinKeys().contains(mjCard.type)){
				if(set.getZhengJing().type == mjCard.type){
					zhengJingFen += AFMJOpPoint.ZhengJing.value(); // 正精分
					zhengJingShu += 1; // 正精数
				}else {
					fuJingFen += AFMJOpPoint.FuJing.value();  // 副精分
					fuJingShu += 1; // 副精数
				}
			}
		}
		// 打出去区域
		List<Integer> OutCardIDs = mSetPos.getOutCardIDs();
		for(Integer cardId : OutCardIDs){
			if(set.getmJinCardInfo().getJinKeys().contains(cardId / 100)){
				if(set.getZhengJing().type == cardId / 100){
					zhengJingFen += AFMJOpPoint.ZhengJing.value(); // 正精分
					zhengJingShu += 1; // 正精数
					qiTaZhengJingShu += 1; // 其他正精数
				}else {
					fuJingFen += AFMJOpPoint.FuJing.value();  // 副精分
					fuJingShu += 1; // 副精数
					qiTaFuJingShu += 1; // 其他副精数
				}
			}
		}
		// 其他杠精分
		if(qiTaZhengJingShu == 4){
			qiTaGangJingFen += AFMJOpPoint.QiTaGangJing.value();
		}
		if(qiTaFuJingShu == 4){
			qiTaGangJingFen += AFMJOpPoint.QiTaGangJing.value();
		}

		// 吃碰杠区域
		List<List<Integer>> publicCardList = new ArrayList<>(mSetPos.getPublicCardList());
		for (List<Integer> publicCards : publicCardList) {
			int type = publicCards.get(0);
			int cardType = publicCards.get(2) / 100;
			if (type == OpType.JieGang.value() || type == OpType.Gang.value() || (type == OpType.AnGang.value())) { // 杠
				if(set.getmJinCardInfo().getJinKeys().contains(cardType)){
					if(set.getZhengJing().type == cardType){
						zhengGangJingFen += AFMJOpPoint.ZhengGangJing.value(); // 正杠精分
						zhengJingFen += 4 * AFMJOpPoint.ZhengJing.value(); // 正精分
						zhengJingShu += 4; // 正精数
					}else {
						fuGangJingFen += AFMJOpPoint.FuGangJing.value();  // 副杠精分
						fuJingFen += 4 * AFMJOpPoint.FuJing.value();  // 副精分
						fuJingShu += 4; // 副精数
					}
				}
			} else if (type == OpType.Peng.value()) { // 碰
				if(set.getmJinCardInfo().getJinKeys().contains(cardType)){
					if(set.getZhengJing().type == cardType){
						zhengJingFen += 3 * AFMJOpPoint.ZhengJing.value(); // 正精分
						zhengJingShu += 3; // 正精数
					}else {
						fuJingFen += 3 * AFMJOpPoint.FuJing.value();  // 副精分
						fuJingShu += 3; // 副精数
					}
				}
			}else if (type == OpType.Chi.value()) { // 吃
				if(set.getZhengJing() != null && set.getFuJing() != null){
					if(publicCards.contains(set.getZhengJing().type)){
						zhengJingFen += AFMJOpPoint.ZhengJing.value(); // 正精分
						zhengJingShu += 1; // 正精数
					}
					if(publicCards.contains(set.getFuJing().type)){
						fuJingFen += AFMJOpPoint.FuJing.value(); // 副精分
						fuJingShu += 1; // 副精数
					}
				}
			}

		}

		// 霸王精
		List<Integer> jingPosList = new ArrayList<>();
		for (int i = 0;i < mSetPos.getPlayerNum();i++) {
			AFMJSetPos aSetPos = (AFMJSetPos)mSetPos.getMJSetPos(i); // 闲家
			if (null == aSetPos) continue;
			if(baWangJing(aSetPos))jingPosList.add(aSetPos.getPosID());
		}
		if(jingPosList.size() == 1){
			if(jingPosList.get(0) == mSetPos.getPosID())return true;
		}

		// 冲关
		if((zhengJingFen + fuJingFen) >= 5){ // 总和为5分，冲2关
			/*// 满足冲关必须是B玩家得到这张精牌之前不冲关，得到后冲关，则A包出；（即先不算A玩家打出的精，自己的精没有满足冲关）
			if(set.getZhengJing().type == lastDaJingCardId / 100){ // 正精
				return (zhengJingFen + fuJingFen - AFMJOpPoint.ZhengJing.value()) < 5;
			}else if(set.getFuJing().type == lastDaJingCardId / 100){ // 副精
				return (zhengJingFen + fuJingFen - AFMJOpPoint.FuJing.value()) < 5;
			}*/

			// 玩家B无论得到前是否冲关，得到后有冲关就需要包。
			return true;
		}

		return false;

	}

	public boolean baWangJing(AbsMJSetPos mSetPos){

		// 检测当前玩家是否有精牌
		// 检测吃碰杠区域
		List<List<Integer>> publicCardList = new ArrayList<>(mSetPos.getPublicCardList());
		for (List<Integer> publicCards : publicCardList) {
			int type = publicCards.get(0);
			int cardType = publicCards.get(2) / 100;
			// 吃
			if (type == OpType.Chi.value()) {
				if(publicCards.size() == 6){
					if(set.getmJinCardInfo().getJinKeys().contains(publicCards.get(3) / 100) ||
							set.getmJinCardInfo().getJinKeys().contains(publicCards.get(4) / 100) ||
							set.getmJinCardInfo().getJinKeys().contains(publicCards.get(5) / 100))
						return true;
				}
			}
			// 碰
			if (type == OpType.Peng.value()) {
				if(set.getmJinCardInfo().getJinKeys().contains(cardType))return true;
			}
			// 杠
			if (type == OpType.JieGang.value() || type == OpType.Gang.value() || type == OpType.AnGang.value()) {
				if(set.getmJinCardInfo().getJinKeys().contains(cardType))return true;
			}
		}
		// 检测手牌
		List<MJCard>  allCards = mSetPos.allCards();
		for (MJCard mjCard : allCards) {
			if(set.getmJinCardInfo().getJinKeys().contains(mjCard.type))return true;
		}
		// 检测打出的的牌
		List<Integer> OutCardIDs = mSetPos.getOutCardIDs();
		for(Integer outCardId : OutCardIDs){
			if(outCardId == this.set.getJiePaoCardId()){
				// 被别人吃、碰、杠、胡的精算得到玩家的；
				continue;
			}
			if(set.getmJinCardInfo().getJinKeys().contains(outCardId / 100))return true;
		}
		return false;
	}



}
