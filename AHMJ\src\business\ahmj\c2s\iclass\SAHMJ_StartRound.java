package business.ahmj.c2s.iclass;					
					
import jsproto.c2s.cclass.BaseSendMsg;		
					
					
public class SAHMJ_StartRound<T> extends BaseSendMsg {						
    						
    public long roomID;						
    public T room_SetWait;				
						
    public static <T> SAHMJ_StartRound make(long roomID, T room_SetWait) {				
    	SAHMJ_StartRound ret = new SAHMJ_StartRound();						
        ret.roomID = roomID;						
        ret.room_SetWait = room_SetWait;				
        return ret;						
    						
						
    }						
}						
