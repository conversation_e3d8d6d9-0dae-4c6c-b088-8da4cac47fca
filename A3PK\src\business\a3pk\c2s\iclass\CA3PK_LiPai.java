package business.a3pk.c2s.iclass;	
	
import com.ddm.server.common.utils.Lists;	
import jsproto.c2s.cclass.BaseSendMsg;	
	
import java.util.List;	
	
public class CA3PK_LiPai extends BaseSendMsg {	
    public long roomID;	
    public List<List<Integer>> liPaiList = Lists.newArrayList();	
	
    public static CA3PK_LiPai make(long roomID, List<List<Integer>> liPaiList) {	
        CA3PK_LiPai ret = new CA3PK_LiPai();	
        ret.roomID = roomID;	
        ret.liPaiList = liPaiList;	
        return ret;	
	
    }	
}	
