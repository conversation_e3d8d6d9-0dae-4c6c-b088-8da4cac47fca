package business.afmj.c2s.cclass;

import jsproto.c2s.cclass.mj.BaseMJSet_Pos;

import java.util.ArrayList;
import java.util.List;

/**
 * 一局中各位置的信息
 */
public class AFMJSet_Pos extends BaseMJSet_Pos{

    private List<Integer> jingPai = new ArrayList<>(); // 精牌cardId列表
    private int boJing = -1; // 博精倍数, -1：客户端不显示博精倍数
    private boolean isPiaoJing = false; // 是否飘精，飘精：将“精”牌打出去为飘精；

    public void setPiaoJing(boolean isPiaoJing) {
        this.isPiaoJing = isPiaoJing;
    }

    public void setBoJing(int boJing) {
        this.boJing = boJing;
    }

    public List<Integer> getJingPai() {
        return jingPai;
    }


}
