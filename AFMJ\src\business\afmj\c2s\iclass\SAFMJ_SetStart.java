package business.afmj.c2s.iclass;
import jsproto.c2s.cclass.*;


@SuppressWarnings("serial")
public class SAFMJ_SetStart<T> extends BaseSendMsg {
    
    public long roomID;
    public T setInfo;


    public static <T> SAFMJ_SetStart<T> make(long roomID, T setInfo) {
    	SAFMJ_SetStart<T> ret = new SAFMJ_SetStart<T>();
        ret.roomID = roomID;
        ret.setInfo = setInfo;
        //打印数组看看
       
        return ret;
    }
}