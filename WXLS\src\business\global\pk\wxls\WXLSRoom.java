package business.global.pk.wxls;	
	
import business.global.pk.wxls.utlis.WXLSConfigMgr;
import business.global.pk.wxls.utlis.WXLSRoomEnum;
import business.global.room.RoomRecordMgr;	
import business.global.room.base.AbsRoomPos;	
import business.global.room.base.AbsRoomPosMgr;	
import business.global.room.pk.PockerRoom;	
import business.player.Player;	
import business.player.PlayerMgr;	
import business.player.Robot.Robot;	
import business.player.Robot.RobotMgr;	
import business.wxls.c2s.cclass.*;	
import business.wxls.c2s.cclass.entity.WXLSPlayerResult;	
import business.wxls.c2s.cclass.newwxls.WXLSCardRankEnum;	
import business.wxls.c2s.cclass.newwxls.WXLSCardSuitEnum;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
import business.wxls.c2s.iclass.CWXLS_CreateRoom;	
import business.wxls.c2s.iclass.CWXLS_Ranked;	
import business.wxls.c2s.iclass.SWXLS_RoomEnd;	
import business.wxls.c2s.iclass.*;	
import cenum.*;	
import cenum.room.GameRoomConfigEnum;
import cenum.room.GaoJiTypeEnum;
import cenum.room.RoomState;
import com.ddm.server.common.CommLogD;	
import com.ddm.server.websocket.def.ErrorCode;	
import com.ddm.server.websocket.handler.requset.WebSocketRequest;	
import com.google.gson.Gson;	
import core.db.entity.clarkGame.ClubMemberBO;	
import core.network.http.proto.SData_Result;	
import jsproto.c2s.cclass.BaseSendMsg;	
import jsproto.c2s.cclass.pk.PKRoom_RecordPosInfo;	
import jsproto.c2s.cclass.room.BaseRoomConfigure;	
import jsproto.c2s.cclass.room.GetRoomInfo;	
import jsproto.c2s.cclass.room.RoomPosInfo;	
import jsproto.c2s.iclass.room.SBase_Dissolve;	
import jsproto.c2s.iclass.room.SBase_PosLeave;	
	
import java.util.*;	
	
/**	
 * 十三水 房间	
 * 	
 * <AUTHOR>	
 * @param 	
 *	
 */	
	
public class WXLSRoom extends PockerRoom {	
	public CWXLS_CreateRoom roomCfg = null;// 开房配置	
	
	public SWXLS_RankingResult rankingResult=new SWXLS_RankingResult();	
	public List<List<WXLSPlayerResult>> playerResult = new ArrayList<List<WXLSPlayerResult>>();	
	public List<WXLSResults> countRecords = new ArrayList<WXLSResults>();	
	public WXLSPockerCard mapai;	
	private long zhuanjia = -1;// 庄家ID	
	private int beishu = 1;	
	private int xiPaiNum = 0;	
	
	protected WXLSConfigMgr configMgr = new WXLSConfigMgr();	
	private Map<Long, Integer> xiPaiList = new HashMap<Long, Integer>();	
	/**	
	 * 记录第一个用户的牌	
	 */	
	private List<String> playerCard = new ArrayList<String>();	
	
	/**	
	 * 扑克公共父类构造函数	
	 *	
	 * @param baseRoomConfigure 公共配置	
	 * @param roomKey           房间key	
	 * @param ownerID	
	 */	
	protected WXLSRoom(BaseRoomConfigure baseRoomConfigure, String roomKey, long ownerID) {	
		super(baseRoomConfigure, roomKey, ownerID);	
		initShareBaseCreateRoom(CWXLS_CreateRoom.class, baseRoomConfigure);	
		this.roomCfg = (CWXLS_CreateRoom) baseRoomConfigure.getBaseCreateRoom();	
		if (baseRoomConfigure.getPrizeType() == PrizeType.RoomCard && (this.isMapai() || this.isMapaiHei())) {	
			this.initMapai();	
		}	
	}	

	public boolean isSpecialComapre(){
		return WXLSRoomEnum.WXLSSpecial.Compare.value()==roomCfg.getTeshupaixing();
	}
	public boolean isDaQiang(WXLSRoomEnum.WXLSDaQiang m){
		return m.value()==roomCfg.getDaqiang();
	}

	/**	
	 * 获取位置选项	
	 * @return	
	 */	
	public int getWeiZi () {	
		return 1;	
	}	
	/**	
	 * 选择位置	
	 *	
	 * @param pid	
	 *            用户ID	
	 * @return	
	 */	
	public boolean selectPosRoom(WebSocketRequest request,long pid, int posID) {	
		boolean ret = true;	
		try {	
			lock();	
			// 检查位置玩法	
			if (WXLS_define.WXLS_WEIZI_TYPE.KeXuan.ordinal() != getWeiZi()) {	
				ret = false;	
				request.error(ErrorCode.NotAllow,"WXLS_WEIZI_TYPE.KeXuan.ordinal() == getWeiZi()");	
				return ret;	
			}	
	
			WXLSRoomPos posPre = (WXLSRoomPos) this.getRoomPosMgr().getPosByPid(pid);	
			//检查玩家位置没有人。	
			if (null == posPre){	
				ret = false;	
				request.error(ErrorCode.NotAllow,"null == posPre");	
				return ret;	
			}	
			// 位置上的玩家ID != 选择位置的人	
			if (posPre.getPid() != pid) {	
				ret = false;	
				request.error(ErrorCode.NotAllow,"posPre.pid != pid");	
				return ret;	
			}	
	
			WXLSRoomPos pos = (WXLSRoomPos) this.getRoomPosMgr().getEmptyPos(posID);	
			// 检查选择的位置ID正确	
			if (null == pos) {	
				ret = false;	
				request.error(ErrorCode.NotAllow,"null == pos");	
				return ret;	
			}	
			boolean isReady = posPre.isReady();	
			ClubMemberBO clubMemberBO = posPre.getClubMemberBO();	
			// 重新选择位置	
			posPre.resetSelectPos();	
			//进入座位。	
			pos.seatPos(pid,0,isReady,clubMemberBO);	
	
			//检查玩家是否机器人。	
			if (pid < RobotMgr.getInstance().limitID && pid != 0) {	
				Robot rb = RobotMgr.getInstance().getRobot((int) pid);	
				//机器人是否存在	
				if (null != rb && this.getBaseRoomConfigure().getPrizeType() == PrizeType.Gold) {	
					// 设置用户准备	
					pos.setReady(true);	
				}	
			} else {	
				Player player = PlayerMgr.getInstance().getPlayer(pid);	
				//获取玩家信息	
				if (null != player) {	
					//设置玩家当前游戏	
					player.setCurrentGameType(getBaseRoomConfigure().getGameType().getId());	
					//正式进入房间	
					player.onEnterRoom(getRoomID());	
					//进入练习场	
					if (this.getBaseRoomConfigure().getPrizeType() == PrizeType.Gold) {	
						// 设置用户准备	
						pos.setReady(true);	
					}	
				}	
			}	
			request.response();	
		}catch (Exception e){	
			CommLogD.error(e.getMessage());	
		}finally {	
			unlock();	
		}	
		return ret;	
	}	
	
//		
//	/**	
//	 * 获取解散	
//	 * @return	
//	 */	
//	
//	protected int getjieSan() {	
//		return RoomJieSan.valueOf(this.roomCfg.jiesan).value();	
//	}	
	
	
	public boolean isMapai() {	
		return this.roomCfg.getKexuanwanfa().contains(0);	
	}	
		
	public boolean isMapaiHei() {	
		return this.roomCfg.getKexuanwanfa().contains(1);	
	}	
		
	public boolean isGuipai() {	
		return this.roomCfg.getKexuanwanfa().contains(2);	
	}	
	/**	
	 * @return configMgr	
	 */	
	public WXLSConfigMgr getConfigMgr() {	
		return configMgr;	
	}	
	public long getZJ() {	
		return this.zhuanjia;	
	}	
	
	public void setZJ(long zjid) {	
		this.zhuanjia = zjid;	
	}	
	
	public void setBS(int beishu) {	
		this.beishu = beishu;	
	}	
	
	public int getBS() {	
		return this.beishu;	
	}	
		
	public int getBaseScore() {	
		return 1;
	}	
	

	public void setBeishu(WebSocketRequest request, long pid, int pos, int beishu) {	
		((WXLSRoomSet)this.getCurSet()).setBeiShu(pid, pos, beishu);	
		request.response();	
	}	
	
	
	public int getXiPaiNum () {	
		return this.xiPaiNum;	
	}	
		
	public void addXiPaiNum (boolean isClean) {	
		if (isClean) {	
			this.xiPaiNum = 0;	
			return;	
		}	
		this.xiPaiNum++;	
	}	
	@Override	
	public SData_Result opXiPai(long pid) {	
		if (!xiPaiList.containsKey(pid)) {	
			xiPaiList.put(pid, 1);	
			this.getRoomPosMgr().notify2All(this.XiPai(this.getRoomID(), pid,	
					this.getBaseRoomConfigure().getGameType().getType()));	
			addXiPaiNum(false);	
			return SData_Result.make(ErrorCode.Success);	
		} else {	
			int curcnt = xiPaiList.get(pid);	
			int totalcnt =this.roomCfg.getSetCount();	
			if(curcnt < totalcnt)	
			{	
				xiPaiList.put(pid, curcnt + 1);	
				if (null != this.getCurSet()) {	
					((WXLSRoomSet)this.getCurSet()).setXiPai(pid);	
				}	
				this.getRoomPosMgr().notify2All(this.XiPai(this.getRoomID(), pid,	
						this.getBaseRoomConfigure().getGameType().getType()));	
				addXiPaiNum(false);	
				return SData_Result.make(ErrorCode.Success);	
			}	
			else	
			{	
				return SData_Result.make(ErrorCode.NotAllow, "not in pos");	
			}	
		}	
	}	
	
	public boolean isZJModel() {	
//		if (this.roomCfg.zhuangjiaguize == -1) {	
			return false;	
//		} else {	
//			return true;	
//		}	
	}	
	
	public void initMapai() {	
		if (isMapaiHei()) {	
			this.mapai = new WXLSPockerCard(WXLSCardSuitEnum.SPADES, WXLSCardRankEnum.CARD_FIVE);	
		} else {	
			Random random = new Random();	
			int randNum = random.nextInt(4);	
			int randNum2 = random.nextInt(13);	
			this.mapai = new WXLSPockerCard(WXLSCardSuitEnum.values()[randNum], WXLSCardRankEnum.values()[randNum2]);	
		}	
			
	
	}	
	
	/**	
	 * 房间内玩家的牌已经准备好了	
	 * 	
	 * @param request	
	 * @param isReady	
	 * @param pid	
	 * @param posIndex	
	 */	
	public void playerCardReady(WebSocketRequest request, boolean isReady, long pid, int posIndex, CWXLS_Ranked req,	
                                boolean isSpecial) {	
	
		try {	
			lock();	
			cardReady(request, isReady, pid, posIndex, req, isSpecial);	
		}catch (Exception e){	
			CommLogD.error(e.getMessage());	
		}finally {	
			unlock();	
		}	
	}	
	
	/**	
	 * 牌准备	
	 * 	
	 * @param request	
	 * @param isReady	
	 * @param pid	
	 * @param posIndex	
	 * @param req	
	 * @return	
	 */	
	@SuppressWarnings("rawtypes")	
	public boolean cardReady(WebSocketRequest request, boolean isReady, long pid, int posIndex, CWXLS_Ranked req,	
                             boolean isSpecial) {	
		WXLSRoomPos pos = (WXLSRoomPos) this.getRoomPosMgr().getPosByPosID(posIndex);	
		if (null == pos) {	
			request.error(ErrorCode.NotAllow, "cardReady not find posIndex:" + (posIndex));	
			return false;	
		}	
	
		if (null == this.getCurSet()) {	
			request.error(ErrorCode.NotExist_CurSet, "cardReady not curset null ");	
			return false;	
		}	
	
		if (!isSpecial) {	
	
			if (null == req) {	
				request.error(ErrorCode.NotExist_Ranked, "cardReady not CWXLS_Ranked null ");	
				return false;	
			}	
	
			if (req.pid == 0L || pid == 0L) {	
				request.error(ErrorCode.NotExist_Ranked, "cardReady not find pid " + pid);	
				return false;	
			}	
	
			if (null != req.dunPos) {	
				if (req.dunPos.first.size() == 0) {
					request.error(ErrorCode.NotAllow, "cardReady req.dunPos.first.size 0");	
					return false;	
				}	
			} else if (null == req.dunPos) {	
				request.error(ErrorCode.NotAllow, "cardReady not req.dunPos null");	
				return false;	
			}	
		}	
	
		if(pos.isCardReady())	
		{	
			request.error(ErrorCode.NotAllow, "card alread Ready!");	
			return false;	
		}	
	
		if (!((WXLSRoomSet)this.getCurSet()).setRankeds(req, posIndex, isSpecial)) {	
			request.error(ErrorCode.Card_Error, "Card Error  setRankeds");	
			return false;	
		}	
		if (!((WXLSRoomSet)this.getCurSet()).checkRanked(pid)) {	
			request.error(ErrorCode.Card_Error, "Card Error checkRanked ");	
			return false;	
		}	
		pos.playerCardReady(request, isReady, pid,req);	
		return true;	
	}	
	
	@Override	
	public void clearEndRoom() {	
		super.clear();	
		this.roomCfg = null;	
		this.configMgr = null;	
	}	
	
	
	
	@Override	
	public void roomTrusteeship(int pos) {	
		((WXLSRoomSet) this.getCurSet()).roomTrusteeship(pos);	
	}	
	
	@Override	
	public void cancelTrusteeship(AbsRoomPos pos) {	
		((WXLSRoomSet) this.getCurSet()).roomTrusteeship(pos.getPosID());	
	}	
	
	@Override	
	public boolean isCanChangePlayerNum() {	
		return this.getBaseRoomConfigure().getBaseCreateRoom().getFangjian().contains(GameRoomConfigEnum.FangJianQieHuanRenShu.ordinal());	
	}	
	
	@Override	
	public <T> T getCfg() {	
		return (T) getRoomCfg();	
	}	
	/**	
	 * 获取房间配置	
	 *	
	 * @return	
	 */	
	public CWXLS_CreateRoom getRoomCfg() {	
		if(this.roomCfg == null){	
			initShareBaseCreateRoom(CWXLS_CreateRoom.class, getBaseRoomConfigure());	
			return (CWXLS_CreateRoom) getBaseRoomConfigure().getBaseCreateRoom();	
		}	
		return this.roomCfg;	
	}	
	
	public int getSSSPlayerNum () {	
		return ((WXLSRoomPosMgr) this.getRoomPosMgr()).getAllPlayerNum();	
	}	
	
	@Override	
	public void startNewSet() {
		// 更新连续托管局数
		for(int i = 0; i < getPlayerNum(); i++){
			WXLSRoomPos WXLSRoomPos = (WXLSRoomPos)getRoomPosMgr().getPosByPosID(i);
			if(WXLSRoomPos.isTrusteeship()){ // 托管
				WXLSRoomPos.addTuoGuanSetCount();
			}
		}
		this.setCurSetID(this.getCurSetID() + 1);	
		this.createSet();	
		// 每个位置，清空准备状态	
		this.getRoomPosMgr().clearGameReady();	
		// 通知局数变化	
		this.getRoomTyepImpl().roomSetIDChange();	
	}	
	//创建set	
	public void  createSet(){	
		if (null != this.getCurSet()) {	
			this.getCurSet().clear();	
			this.setCurSet(null);	
		}	
		this.setCurSet(new WXLSRoomSet(this));	
	}	
	@Override	
	public void setEndRoom() {	
	
		if (null != this.getCurSet()) {	
			// 房间管理注销	
//			SSSRoomRecord record = new SSSRoomRecord(this);	
			if (getHistorySet().size() > 0) {	
				// 增加房局记录	
				RoomRecordMgr.getInstance().add(this);	
				WXLSRoom_Record sRecord = new WXLSRoom_Record();	
				sRecord.players = ((WXLSRoomPosMgr)this.getRoomPosMgr()).getShortPlayerList(countRecords);	
				int maxPoint=0;	
				for(WXLSResults zjplsResults :countRecords){	
					if(zjplsResults.getPoint()>=maxPoint){	
						maxPoint= zjplsResults.getPoint();	
					}	
				}	
				for(WXLSResults zjplsResults :countRecords){	
					if(zjplsResults.getPoint()==maxPoint){	
						zjplsResults.bigWinner=true;	
					}	
				}	
				sRecord.recordPosInfosList = countRecords;	
				sRecord.key = this.getRoomKey();	
				sRecord.roomID = getRoomID();	
				sRecord.endSec = getRoomEndResult().getEndTime();	
				sRecord.setCnt = getCurSetID();	
	
//				sRecord.posCnt= ShareDefine.MAXPLAYERNUM_SSS;	
				this.getRoomPosMgr().notify2All(SWXLS_RoomEnd.make(sRecord));	
				refererReceiveList();	
			}	
		}	
	}	
	
	@Override	
	public void calcEnd() {	
	
		for (AbsRoomPos pos : this.getRoomPosMgr().posList) {	
			WXLSRoomPos WXLSRoomPos = (WXLSRoomPos) pos;	
			if (null == WXLSRoomPos)	
				continue;	
			if (!WXLSRoomPos.isPlayTheGame())	
				continue;	
	
	
			WXLSResults countRecord = (WXLSResults) pos.getResults();	
			if (countRecord != null) {	
				if (countRecord == null || (countRecord == null && pos.getPid() >= 0L)) {	
					countRecord = new WXLSResults();	
					countRecord.setPid(pos.getPid());	
					countRecord.setPosId(pos.getPosID());	
				}	
				countRecords.add(countRecord);	
			}	
			if (PrizeType.Gold.equals(this.getBaseRoomConfigure().getPrizeType())) {	
				return;	
			}	
		}	
		// 更新游戏房间BO和更新玩家个人游戏记录BO	
		this.updateGameRoomBOAndPlayerRoomAloneBO();	
	}	
	
	
	
	public List<String> getPlayerCard() {	
		return playerCard;	
	}	
	
	public void setPlayerCard(List<String> playerCard) {	
		this.playerCard = playerCard;	
	}	
	
	
	
	public int getWXLSPlayerNum () {	
		return ((WXLSRoomPosMgr) this.getRoomPosMgr()).getAllPlayerNum();	
	}	
	
	@SuppressWarnings("rawtypes")	
	public List<WXLSRoomPos> getAllWXLSRoomPosList() {	
		return ((WXLSRoomPosMgr) this.getRoomPosMgr()).getAllWXLSRoomPosList();	
	
	}

//	/*	
//	 * 开始游戏的其他条件 条件不满足不进入	
//	 * */	
//	public boolean startGameOtherCondition(WebSocketRequest request, long pid) {	
//		// 如果人数 >= 8 ? 2-8人，可以中途加入:人满开始	
//		if (this.getPlayerNum() >= 8 ? ((WXLSRoomPosMgr) this.getRoomPosMgr()).isWXLSAllReady():this.getRoomPosMgr().isAllReady()) {	
//			return true;	
//		} else {	
//			request.error(ErrorCode.NotAllow, "ready player is last two!");	
//			return false;	
//		}	
//	}	
	
	@Override	
	public String dataJsonCfg() {	
		// 获取房间配置	
		return new Gson().toJson(this.getRoomCfg());	
	}	
	
	@Override	
	public AbsRoomPosMgr initRoomPosMgr() {	
		return new WXLSRoomPosMgr(this);	
	}	
	
	
	
	/*	
	 * 主动离开房间的其他条件 条件不满足不退出	
	 * */	
	@Override	
	public boolean exitRoomOtherCondition(long pid){	
		// 游戏已经开始，不能自由离开	
		if(this.getCurSet()!=null&&this.getCurSet().getSetID()==0&&!RoomState.Init.equals(this.getRoomState())){	
			return false;	
		}
		if(RoomState.Playing.equals(this.getRoomState())){
			return false;
		}
		// 玩家玩过游戏就不能离开	
		WXLSRoomPos pos = (WXLSRoomPos) this.getRoomPosMgr().getPosByPid(pid);	
		if (pos != null && pos.isPlayTheGame()) {	
			return false;	
		}	
		return true;	
	}	
		
	
		
	@Override	
	public int getPlayingCount() {	
		return ((WXLSRoomPosMgr) this.getRoomPosMgr()).getPlayerNum();	
	}	
	
	@Override	
	public BaseSendMsg Trusteeship(long roomID, long pid, int pos, boolean trusteeship) {	
		return SWXLS_Trusteeship.make(roomID, pid, pos, trusteeship);	
	}	
	
	@Override	
	public BaseSendMsg PosLeave(SBase_PosLeave posLeave) {	
		return SWXLS_PosLeave.make(posLeave);	
	}	
	
	@Override	
	public BaseSendMsg LostConnect(long roomID, long pid, boolean isLostConnect, boolean isShowLeave) {	
		return SWXLS_LostConnect.make(roomID, pid, isLostConnect,isShowLeave);	
	}	
	
	@Override	
	public BaseSendMsg PosContinueGame(long roomID, int pos) {	
		return SWXLS_PosContinueGame.make(roomID, pos);	
	}	
	
	@Override	
	public BaseSendMsg PosUpdate(long roomID, int pos, RoomPosInfo posInfo, int custom) {	
		return SWXLS_PosUpdate.make(roomID, pos, posInfo, custom,getRoomPosMgr().getNotify_PosList());	
	}	
	
	@Override	
	public BaseSendMsg PosReadyChg(long roomID, int pos, boolean isReady) {	
		return SWXLS_PosReadyChg.make(roomID, pos, isReady);	
	}	
	
	@Override	
	public BaseSendMsg Dissolve(SBase_Dissolve dissolve) {	
		return SWXLS_Dissolve.make(dissolve);	
	}	
	
	@Override	
	public BaseSendMsg StartVoteDissolve(long roomID, int createPos, int endSec) {	
		return SWXLS_StartVoteDissolve.make(roomID, createPos, endSec);	
	}	
	
	@Override	
	public BaseSendMsg PosDealVote(long roomID, int pos, boolean agreeDissolve, int endSec) {	
		return SWXLS_PosDealVote.make(roomID, pos, agreeDissolve);	
	}	
	
	@Override	
	public BaseSendMsg Voice(long roomID, int pos, String url) {	
		return SWXLS_Voice.make(roomID, pos, url);	
	}	
	
	@Override	
	public BaseSendMsg XiPai(long roomID, long pid, ClassType cType) {	
		return SWXLS_XiPai.make(roomID, pid, cType);	
	}	
	
	@Override	
	public BaseSendMsg ChatMessage(long pid, String name, String content, ChatType type, long toCId, int quickID) {	
		return SWXLS_ChatMessage.make(pid, name, content, type, toCId, quickID);	
	}	
	
	@Override	
	public <T> BaseSendMsg RoomRecord(List<T> records) {	
		return SWXLS_RoomRecord.make(records);	
	}	
	
	@Override	
	public BaseSendMsg ChangePlayerNum(long roomID, int createPos, int endSec, int playerNum) {	
		return SWXLS_ChangePlayerNum.make(roomID, createPos, endSec, playerNum);	
	}	
	
	@Override	
	public BaseSendMsg ChangePlayerNumAgree(long roomID, int pos, boolean agreeChange) {	
		return SWXLS_ChangePlayerNumAgree.make(roomID, pos, agreeChange);	
	}	
	
	@Override	
	public BaseSendMsg ChangeRoomNum(long roomID, String roomKey, int createType) {	
		return SWXLS_ChangeRoomNum.make(roomID, roomKey, createType);	
	}	
	
	@Override	
	public GetRoomInfo getRoomInfo(long pid) {	
		SWXLS_GetRoomInfo ret = new SWXLS_GetRoomInfo();	
		// 设置房间公共信息	
		this.getBaseRoomInfo(ret);	
		if (null != this.getCurSet()) {	
			ret.setSet(this.getCurSet().getNotify_set(pid));	
			ret.setBeishu(this.getBS());	
			ret.zjid=this.getZJ();	
			ret.rankeds=((WXLSRoomSet)this.getCurSet()).dealer.getSRankingResult().rankeds==null? new ArrayList<>():((WXLSRoomSet)this.getCurSet()).dealer.getSRankingResult().rankeds;	
			ret.posResultList=((WXLSRoomSet)this.getCurSet()).dealer.getSRankingResult().playerResults==null? new ArrayList<>():((WXLSRoomSet)this.getCurSet()).dealer.getSRankingResult().playerResults;	
			ret.rankingResults=rankingResult;	
		} else {	
			WXLSRoomSetInfo fqplsRoomSetInfo =new WXLSRoomSetInfo();	
			ret.rankingResults = new SWXLS_RankingResult();	
			fqplsRoomSetInfo.mapai=this.mapai == null ? "" : this.mapai.toString();	
			ret.setSet(fqplsRoomSetInfo);	
		}	
		return ret;	
	}	
	
	
	
	
	@Override	
	protected List<PKRoom_RecordPosInfo> getRecordPosInfoList() {	
		List<PKRoom_RecordPosInfo> sRecord = new ArrayList<PKRoom_RecordPosInfo>();	
		for(int i = 0; i < this.getPlayerNum() ; i++){	
			PKRoom_RecordPosInfo posInfo = new PKRoom_RecordPosInfo();	
			WXLSRoomPos roomPos = (WXLSRoomPos) this.getRoomPosMgr().getPosByPosID(i);	
			if(roomPos.getPid()==0){	
				continue;	
			}	
	
			WXLSResults cRecord = (WXLSResults)roomPos.getResults();	
			if(null!=cRecord){	
				posInfo.flatCount = cRecord.flatCount;	
				posInfo.loseCount = cRecord.loseCount;	
				posInfo.winCount = cRecord.winCount;	
				posInfo.setSportsPoint(cRecord.getSportsPoint());	
			}	
	
			posInfo.setPoint( roomPos.getPoint());	
			posInfo.setPos(i);	
			posInfo.setPid(roomPos.getPid());	
			sRecord.add(posInfo);	
		}	
		return sRecord;	
	}	
	
	protected void setDissolveRoom(BaseSendMsg baseSendMsg) {	
			
	}	
	
	
	/**	
	 * 自动开始游戏 所有玩家准备好自动开始。	
	 */	
	@Override	
	public boolean autoStartGame() {	
		//亲友圈 大联盟2-8人自动开始游戏	
		if(this.getRoomTyepImpl().getRoomTypeEnum().equals(RoomTypeEnum.CLUB)||this.getRoomTyepImpl().getRoomTypeEnum().equals(RoomTypeEnum.UNION)){	
			return true;	
		}	
		//特殊情况 2-4人不自动开始游戏	
		if(this.getBaseRoomConfigure().getBaseCreateRoom().getPlayerNum()!=this.getBaseRoomConfigure().getBaseCreateRoom().getPlayerMinNum()){	
			return false;	
		}	
		return true;	
	}	
	
	/**	
	 * 托管时间值	
	 *	
	 * @return	
	 */	
	@Override	
	public int trusteeshipTimeValue() {	
		return WXLSRoomEnum.WXLSXianShiConfigEnum.valueOf(getBaseRoomConfigure().getBaseCreateRoom().getXianShi()).getValue();
	}	
	
	/**	
	 * 获取练习场的基础分	
	 *	
	 * @return	
	 */	
	public int getBaseMark() {	
		if (PrizeType.Gold.equals(this.getBaseRoomConfigure().getPrizeType())) {	
			return this.getBaseRoomConfigure().getRobotRoomCfg().getBaseMark();	
		} else if (PrizeType.RoomCard.equals(this.getBaseRoomConfigure().getPrizeType())) {	
			return this.getBaseScore();	
		}	
		return 0;	
	}	
	/**	
	 * 加入房间的其他条件 条件不满足不进入	
	 */	
	@Override	
	public boolean enterRoomOtherCondition(long pid) {	
		if(this.getBaseRoomConfigure().getBaseCreateRoom().getPlayerMinNum()!=this.getBaseRoomConfigure().getBaseCreateRoom().getPlayerNum()){	
			return true;	
		}	
		return RoomState.Init.equals(this.getRoomState());	
	}
	/**
	 * 是否需要解散次数
	 * @return
	 */
	public boolean needDissolveCount(){
		if(this.getBaseRoomConfigure().getBaseCreateRoom().getFangjian().contains(WXLSRoomEnum.WXLSGameRoomConfigEnum.JieSanCiShu5.ordinal())){
			return true;
		}
		if(this.getBaseRoomConfigure().getBaseCreateRoom().getFangjian().contains(WXLSRoomEnum.WXLSGameRoomConfigEnum.JieSanCiShu3.ordinal())){
			return true;
		}
		return false;
	}
	/**
	 * 获取解散次数
	 * @return
	 */
	@Override
	public int getJieShanShu(){
		if(this.getBaseRoomConfigure().getBaseCreateRoom().getFangjian().contains(WXLSRoomEnum.WXLSGameRoomConfigEnum.JieSanCiShu5.ordinal())){
			return 5;
		}
		if(this.getBaseRoomConfigure().getBaseCreateRoom().getFangjian().contains(WXLSRoomEnum.WXLSGameRoomConfigEnum.JieSanCiShu3.ordinal())){
			return 3;
		}
		return 0;
	}
	/**
	 * 30秒未准备自动退出
	 * @return
	 */
	@Override
	public boolean is30SencondTimeOut() {
		return checkGaoJiXuanXiang(GaoJiTypeEnum.SECOND_TIMEOUT_30);
	}
}	
