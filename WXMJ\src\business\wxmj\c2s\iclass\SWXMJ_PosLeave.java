package business.wxmj.c2s.iclass;			
			
import jsproto.c2s.iclass.room.SBase_PosLeave;			
			
/**			
 * 位置离开通知			
 * 			
 * <AUTHOR>			
 *			
 */			
@SuppressWarnings("serial")			
public class SWXMJ_PosLeave extends SBase_PosLeave {			
			
	public static SWXMJ_PosLeave make(SBase_PosLeave posLeave) {			
		SWXMJ_PosLeave ret = new SWXMJ_PosLeave();			
		ret.setRoomID(posLeave.getRoomID());			
		ret.setPos(posLeave.getPos());			
		ret.setBeKick(posLeave.isBeKick());			
		ret.setOwnerID(posLeave.getOwnerID());			
		ret.setKickOutTYpe(posLeave.getKickOutTYpe());			
		ret.setMsg(posLeave.getMsg());			
		return ret;			
	}			
}			
