package core.network.client2game.handler.ahlxmj;										
										
import java.io.IOException;										
										
import com.ddm.server.websocket.handler.requset.WebSocketRequest;										
import com.google.gson.Gson;										
										
import business.ahlxmj.c2s.iclass.CAHLXMJ_CreateRoom;										
import business.player.Player;										
import business.player.feature.PlayerClubRoom;										
import cenum.PrizeType;										
import core.network.client2game.handler.PlayerHandler;										
import core.server.ahlxmj.AHLXMJAPP;										
import jsproto.c2s.cclass.room.BaseRoomConfigure;										
										
/**										
 * 亲友圈房间										
 * 										
 * <AUTHOR>										
 *										
 */										
public class CAHLXMJClubRoom extends PlayerHandler {										
										
	@Override										
	public void handle(Player player, WebSocketRequest request, String message)										
			throws IOException {										
										
		final CAHLXMJ_CreateRoom clientPack = new Gson().fromJson(message,										
				CAHLXMJ_CreateRoom.class);												
										
		// 公共房间配置										
		BaseRoomConfigure<CAHLXMJ_CreateRoom> configure = new BaseRoomConfigure<CAHLXMJ_CreateRoom>(										
				PrizeType.RoomCard,										
				AHLXMJAPP.GameType(),										
				clientPack.clone());										
		player.getFeature(PlayerClubRoom.class).createNoneClubRoom(request,configure);										
	}										
}										
