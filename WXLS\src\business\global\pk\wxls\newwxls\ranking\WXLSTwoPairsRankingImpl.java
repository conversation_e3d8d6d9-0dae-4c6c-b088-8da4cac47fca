package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.Map;	
	
/**	
 * Class {@code TwoPairsRankingImpl} 解析玩家手中的牌是不是两对(2+2+1)	
 */	
public class WXLSTwoPairsRankingImpl extends WXLSAbstractRanking {	
	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        boolean flag = false;	
        WXLSRankingResult result = null;	
        if (player.getCardSize() == 5) {	
            Map<Integer, Integer> rankCount = player.getCardsRankCountMap();	
            if (rankCount.size() == 3) {	
                flag = true;	
            }	
	
        }	
        if (flag) {	
            result = new WXLSRankingResult();	
            result.setRankingEnum(WXLSRankingEnum.TWO_PAIR);	
        }	
	
        return result;	
    }	
	
}	
