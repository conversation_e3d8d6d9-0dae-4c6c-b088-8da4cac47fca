package business.global.mj.afmj.hutype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCardInit;
import business.global.mj.hu.BaseHuCard;
import business.global.mj.manage.MJFactory;
import business.global.mj.util.HuZiUtil;
import business.global.mj.afmj.AFMJRoomEnum.*;


public class AFMJDeGuoImpl extends BaseHuCard {

	@Override
	public <T> Object checkHuCardReturn(AbsMJSetPos mSetPos, MJCardInit mCardInit, int cardType) {
		if (null == mCardInit) {
			return AFMJOpPoint.NotHu;
		}
		if (mCardInit.getJins().size() == 0) {
			// 如果无财
			if (HuZiUtil.getInstance().checkHu(mCardInit.getAllCardInts(), mCardInit.getJins().size())
					|| MJFactory.getHuCard(AFMJDDHuImpl.class).checkHuCard(mSetPos, mCardInit)
					|| !AFMJOpPoint.Not.equals((AFMJOpPoint) MJFactory.getHuCard(AFMJSSBKImpl.class).checkHuCardReturn(mSetPos, mCardInit))
					|| !AFMJOpPoint.Not.equals((AFMJOpPoint) MJFactory.getHuCard(AFMJSSBKQingImpl.class).checkHuCardReturn(mSetPos, mCardInit))
			) {
				// 德国平胡
				return AFMJOpPoint.PiHu;
			}
		} else {
			if (HuZiUtil.getInstance().checkHu(mCardInit.getAllCardInts(), mCardInit.getJins().size())
					|| MJFactory.getHuCard(AFMJDDHuImpl.class).checkHuCard(mSetPos, mCardInit)
					|| !AFMJOpPoint.Not.equals((AFMJOpPoint) MJFactory.getHuCard(AFMJSSBKImpl.class).checkHuCardReturn(mSetPos, mCardInit))
					|| !AFMJOpPoint.Not.equals((AFMJOpPoint) MJFactory.getHuCard(AFMJSSBKQingImpl.class).checkHuCardReturn(mSetPos, mCardInit))
			) {
				// 宝牌归位
				MJCardInit mInit = mCaiGui(mCardInit.getAllCardInts(), mCardInit.getJins(), mCardInit.getJins().size());
				if (mInit != null) {
					if (HuZiUtil.getInstance().checkHu(mInit.getAllCardInts(), mInit.getJins().size())
							|| MJFactory.getHuCard(AFMJDDHuImpl.class).checkHuCard(mSetPos, mInit)
							|| !AFMJOpPoint.Not.equals((AFMJOpPoint) MJFactory.getHuCard(AFMJSSBKImpl.class).checkHuCardReturn(mSetPos, mInit))
							|| !AFMJOpPoint.Not.equals((AFMJOpPoint) MJFactory.getHuCard(AFMJSSBKQingImpl.class).checkHuCardReturn(mSetPos, mInit))
					){
						// 德国平胡
						return AFMJOpPoint.NotJin;
					}
				}
				// 带宝平胡
				return AFMJOpPoint.Normal;
			}
		}
		return AFMJOpPoint.Not;
	}



	@Override
	public <T> Object checkHuCardReturn(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
		if (null == mCardInit) {
			return AFMJOpPoint.NotHu;
		}
		if (mCardInit.getJins().size() == 0) {
			// 如果无财
			if (HuZiUtil.getInstance().checkHu(mCardInit.getAllCardInts(), mCardInit.getJins().size())
					|| MJFactory.getHuCard(AFMJDDHuImpl.class).checkHuCard(mSetPos, mCardInit)
					|| !AFMJOpPoint.Not.equals((AFMJOpPoint) MJFactory.getHuCard(AFMJSSBKImpl.class).checkHuCardReturn(mSetPos, mCardInit))
					|| !AFMJOpPoint.Not.equals((AFMJOpPoint) MJFactory.getHuCard(AFMJSSBKQingImpl.class).checkHuCardReturn(mSetPos, mCardInit))
			) {
				// 德国平胡
				return AFMJOpPoint.PiHu;
			}
		} else {
			if (HuZiUtil.getInstance().checkHu(mCardInit.getAllCardInts(), mCardInit.getJins().size())
					|| MJFactory.getHuCard(AFMJDDHuImpl.class).checkHuCard(mSetPos, mCardInit)
					|| !AFMJOpPoint.Not.equals((AFMJOpPoint) MJFactory.getHuCard(AFMJSSBKImpl.class).checkHuCardReturn(mSetPos, mCardInit))
					|| !AFMJOpPoint.Not.equals((AFMJOpPoint) MJFactory.getHuCard(AFMJSSBKQingImpl.class).checkHuCardReturn(mSetPos, mCardInit))
			) {
				// 宝牌归位
				MJCardInit mInit = mCaiGui(mCardInit.getAllCardInts(), mCardInit.getJins(), mCardInit.getJins().size());
				if (mInit != null) {
					if (HuZiUtil.getInstance().checkHu(mInit.getAllCardInts(), mInit.getJins().size())
							|| MJFactory.getHuCard(AFMJDDHuImpl.class).checkHuCard(mSetPos, mInit)
							|| !AFMJOpPoint.Not.equals((AFMJOpPoint) MJFactory.getHuCard(AFMJSSBKImpl.class).checkHuCardReturn(mSetPos, mInit))
							|| !AFMJOpPoint.Not.equals((AFMJOpPoint) MJFactory.getHuCard(AFMJSSBKQingImpl.class).checkHuCardReturn(mSetPos, mInit))
					){
						// 德国平胡
						return AFMJOpPoint.NotJin;
					}
				}
				// 带宝平胡
				return AFMJOpPoint.Normal;
			}
		}
		return AFMJOpPoint.Not;
	}

}
