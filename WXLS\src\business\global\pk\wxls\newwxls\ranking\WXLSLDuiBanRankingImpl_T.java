package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.Iterator;	
import java.util.Map;	
	
/**	
 * 六对半	
 */	
public class WXLSLDuiBanRankingImpl_T extends WXLSAbstractRanking {	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        WXLSRankingResult result = null;	
        boolean flag = false;	
        if (player.getCardSize() == 13) {	
            Map<Integer, Integer> rankCount = player.getCardsRankCountMap();	
	
            int twocnt = 0;	
            int fourcnt = 0;	
            Iterator<Map.Entry<Integer, Integer>> it = rankCount.entrySet().iterator();	
            while (it.hasNext()) {	
                Map.Entry<Integer, Integer> entry = it.next();	
                if (entry.getKey() != 15 && entry.getKey() != 16) {	
                    if (entry.getValue() == 2) {	
                        twocnt++;	
                    } else if (entry.getValue() == 4) {	
                        fourcnt++;	
                    }	
                }	
            }	
	
            if (twocnt == 6) {	
                flag = true;	
            }	
            if (twocnt == 4 && fourcnt == 1) {	
                flag = true;	
            }	
            if (twocnt == 2 && fourcnt == 2) {	
                flag = true;	
            }	
	
	
	
            if (flag) {	
                result = new WXLSRankingResult();	
                result.setPockerCards(player.getCards());	
                result.setRankingEnum(WXLSRankingEnum.LDuiBan);	
            }	
        }	
        return result;	
    }	
	
	
	
}	
