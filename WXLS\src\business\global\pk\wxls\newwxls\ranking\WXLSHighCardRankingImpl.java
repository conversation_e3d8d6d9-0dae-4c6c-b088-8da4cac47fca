package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSConstants;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.List;	
import java.util.Map;	
	
/**	
 * Class {@code BJPKHighCardRankingImpl} 解析玩家手中的牌是不是单牌(1+1+1+1+1)	
 */	
public class WXLSHighCardRankingImpl extends WXLSAbstractRanking {	
	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        WXLSRankingResult result = null;	
        Map<Integer, Integer> rankCount = player.getCardsRankCountMap();	
	
        boolean allOne = false;	
        if (player.getCardSize() == 5) {	
            if (rankCount.size() == WXLSConstants.SECOND_CARD_NUMBER) {	
                List<WXLSPockerCard> cards = player.getCards();	
                if (!this.isSameSuit(cards)) {	
                    WXLSPockerCard maxCard = cards.get(0);	
                    WXLSPockerCard minCard = cards.get(cards.size() - 1);	
                    if (maxCard.getRank().getNumber() - minCard.getRank().getNumber() >= WXLSConstants.SECOND_CARD_NUMBER) {	
                        allOne = true;	
                    }	
                }	
            }	
        }	
        else if (player.getCardSize() == 3) {	
            if (rankCount.size() == WXLSConstants.FIRST_CARD_NUMBER) {	
                List<WXLSPockerCard> cards = player.getCards();	
                if (!this.isSameSuit(cards)) {	
                    WXLSPockerCard maxCard = cards.get(0);	
                    WXLSPockerCard minCard = cards.get(cards.size() - 1);	
                    if (maxCard.getRank().getNumber() - minCard.getRank().getNumber() >= WXLSConstants.FIRST_CARD_NUMBER) {	
                        allOne = true;	
                    }	
                }	
            }	
        }	
	
        if (allOne) {	
            result = new WXLSRankingResult();	
            result.setRankingEnum(WXLSRankingEnum.HIGH_CARD);	
        }	
	
        return result;	
    }	
	
}	
