package business.global.mj.afmj;

import business.global.mj.AbsMJRoundPos;
import business.global.mj.AbsMJSetPos;
import business.global.mj.AbsMJSetRound;
import business.global.mj.robot.MJRobotOpCard;
import business.global.mj.set.MJOpCard;
import cenum.mj.HuType;
import cenum.mj.MJCEnum;
import cenum.mj.MJOpCardError;
import cenum.mj.OpType;
import com.ddm.server.common.utils.CommMath;
import com.ddm.server.websocket.handler.requset.WebSocketRequestDelegate;

import java.util.List;

/**		
 * 麻将机器人打牌		
 *		
 * <AUTHOR>		
 */		
public class AFMJRobotOpCard extends MJRobotOpCard {
		
    public AFMJRobotOpCard(AbsMJSetRound setRound) {
        super(setRound);		
		
    }		
		
    private AFMJRoomSet set() {
        return (AFMJRoomSet) getSet();
    }		
		
    private AFMJSetRound setRound() {
        return (AFMJSetRound) getSetRound();
    }

    @Override
    public void RobothandCrad(int posID) {
        // 获取当前操作位置
        AbsMJRoundPos roundPos = this.getSetRound().getRoundPosDict().get(posID);
        if (null == roundPos) {
            // 检查超时等待时间
            this.checkWaitTime();
            return;
        }
        // 检查位置是否已经操作过
        if (null != roundPos.getOpType()) {
            // 检查超时等待时间
            this.checkWaitTime();
            return;
        }
        // 获取玩家信息
        AbsMJSetPos mSetPos = roundPos.getPos();
        if (mSetPos == null) {
            // 检查超时等待时间
            this.checkWaitTime();
            return;
        }

        // 获取玩家可操作列表
        List<OpType> opTypes = roundPos.getRecieveOpTypes();
        if (opTypes == null || opTypes.size() <= 0) {
            // 检查超时等待时间
            this.checkWaitTime();
            return;
        }
        if(moDa(mSetPos,posID)){
            //机器人摸打
            return;
        }
        // 操作结果
        int opCardRet = null == mSetPos.getHandCard() ? this.notExistHandCard(opTypes, mSetPos) : this.existHandCard(opTypes, mSetPos);
        if (opCardRet >= 0) {
            // 操作成功可以清除动作列表
            mSetPos.getPosOpRecord().cleanOpList();
        }
    }

    /**
     * 存在手牌
     * 托管：不吃不碰不杠不听，摸什么打什么，能胡牌的时候自动胡；
     */
    @Override
    public int existHandCard(List<OpType> opTypes, AbsMJSetPos mSetPos) {
        OpType opType = opTypes.stream().filter(k -> !HuType.NotHu.equals(MJCEnum.OpHuType(k))).findAny().orElse(opTypes.get(CommMath.randomInt(0, opTypes.size() - 1)));
        AFMJSetRound round = (AFMJSetRound) this.getSetRound();
        if (HuType.NotHu.equals(MJCEnum.OpHuType(opType))) {
            if (opTypes.contains(OpType.Out)) {
                // 打牌
                return round.opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), OpType.Out, MJOpCard.OpCard(mSetPos.getSetPosRobot().getAutoCard()), true);
            }else if (opTypes.contains(OpType.Pass)) {
                // 过操作
                return round.opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), OpType.Pass, MJOpCard.OpCard(0), true);
            }
            return MJOpCardError.ROBOT_OP_ERROR.value();
        } else {
            // 存在自摸胡牌
            return round.opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), opType, MJOpCard.OpCard(0), true);
        }
    }

    /**
     * 不存在首牌
     *托管：摸什么打什么，不吃不碰不杠，系统自动摸牌、打牌、胡。
     *
     * @return
     */
    @Override
    public int notExistHandCard(List<OpType> opTypes, AbsMJSetPos mSetPos) {
        OpType opType = opTypes.stream().filter(k -> !HuType.NotHu.equals(MJCEnum.OpHuType(k))).findAny().orElse(opTypes.get(CommMath.randomInt(0, opTypes.size() - 1)));
        if (HuType.NotHu.equals(MJCEnum.OpHuType(opType))) {
            if (OpType.JieGang.equals(opType) || OpType.Peng.equals(opType) || OpType.Pass.equals(opType)) {
                // 接杠\碰\过
//                return this.getSetRound().opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), opType, MJOpCard.OpCard(0));
                return this.getSetRound().opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), OpType.Pass, MJOpCard.OpCard(0));
            } else if (OpType.Chi.equals(opType) && mSetPos.getSetPosRobot().getChiCid() / 100 < 40) {
                // 吃牌
//                return this.getSetRound().opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), opType, MJOpCard.OpCard(mSetPos.getSetPosRobot().getChiCid()));
                return this.getSetRound().opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), OpType.Pass, MJOpCard.OpCard(mSetPos.getSetPosRobot().getChiCid()));
            } else {
                if (opTypes.contains(OpType.Pass)) {
                    // 没有相应的动作直接过
                    return this.getSetRound().opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), OpType.Pass, MJOpCard.OpCard(0));
                }else if (opTypes.contains(OpType.Out)) {
                    return this.getSetRound().opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), OpType.Out, MJOpCard.OpCard(mSetPos.getSetPosRobot().getAutoCard()));
                }
                return MJOpCardError.ROBOT_OP_ERROR.value();

            }
        } else {
            // 点炮胡或者抢杠胡
            return this.getSetRound().opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), opType, MJOpCard.OpCard(0));
        }
    }
		
}		
