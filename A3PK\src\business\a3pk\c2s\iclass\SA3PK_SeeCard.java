package business.a3pk.c2s.iclass;	
	
import jsproto.c2s.cclass.BaseSendMsg;	
	
import java.util.ArrayList;	
import java.util.List;	
	
	
public class SA3PK_SeeCard<T> extends BaseSendMsg {	
    public long roomID;	
    public int pos;	
    public List<Integer> shouCard = new ArrayList<>(); //手牌，如果不是自己，填0， 如果个数是3n+2,则独立显示手牌	
	
    public static <T> SA3PK_SeeCard<T> make(long roomID, int pos, List<Integer> shouCard) {	
        SA3PK_SeeCard<T> ret = new SA3PK_SeeCard<T>();	
        ret.roomID = roomID;	
        ret.pos = pos;	
        ret.shouCard = shouCard;	
        return ret;	
    }	
}	
