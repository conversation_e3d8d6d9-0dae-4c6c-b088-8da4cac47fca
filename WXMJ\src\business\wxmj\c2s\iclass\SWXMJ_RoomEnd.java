package business.wxmj.c2s.iclass;			
import jsproto.c2s.cclass.BaseSendMsg;			
import jsproto.c2s.cclass.RoomEndResult;			
			
			
@SuppressWarnings("serial")			
public class SWXMJ_RoomEnd<T> extends BaseSendMsg {			
    			
    public T record;			
	public RoomEndResult<?> sRoomEndResult;			
			
    public static <T> SWXMJ_RoomEnd<T> make(T record, RoomEndResult<?> sRoomEndResult) {			
    	SWXMJ_RoomEnd<T> ret = new SWXMJ_RoomEnd<T>();			
        ret.record = record;			
        ret.sRoomEndResult = sRoomEndResult;			
        return ret;			
    			
			
    }			
}			
