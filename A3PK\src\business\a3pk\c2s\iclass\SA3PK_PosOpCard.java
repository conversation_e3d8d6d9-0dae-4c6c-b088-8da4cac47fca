package business.a3pk.c2s.iclass;	
	
import cenum.PKOpType;	
import com.ddm.server.common.utils.Lists;	
import jsproto.c2s.cclass.BaseSendMsg;	
	
import java.util.List;	
	
	
@SuppressWarnings("serial")	
public class SA3PK_PosOpCard<T> extends BaseSendMsg {	
	
    public long roomID;	
    public int pos;	
    public T set_Pos;	
    public PKOpType opType;	
    public int cardType;	
    public List<Integer> opCards;	
    public boolean isFlash = false;	
	
    public static <T> SA3PK_PosOpCard<T> make(long roomID, int pos, T set_Pos, PKOpType opType, int cardType, List<Integer> opCards, boolean isFlash) {	
        SA3PK_PosOpCard<T> ret = new SA3PK_PosOpCard<T>();	
        ret.roomID = roomID;	
        ret.pos = pos;	
        ret.set_Pos = set_Pos;	
        ret.opType = opType;	
        ret.cardType = cardType;	
        ret.opCards = opCards;	
        ret.isFlash = isFlash;	
	
        return ret;	
	
	
    }	
}	
