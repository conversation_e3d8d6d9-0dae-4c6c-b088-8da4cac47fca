package business.global.pk.wxls;	
	
import business.global.room.base.AbsBaseRoom;	
import business.global.room.base.AbsRoomPos;	
import business.global.room.base.AbsRoomPosMgr;
import business.player.Robot.RobotMgr;
import business.wxls.c2s.cclass.WXLSResults;
import com.ddm.server.common.utils.CommTime;
import jsproto.c2s.cclass.Player.ShortPlayer;	
	
import java.util.ArrayList;	
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;	
	
public class WXLSRoomPosMgr extends  AbsRoomPosMgr {	
	
	@SuppressWarnings({ "unchecked", "rawtypes" })	
	public WXLSRoomPosMgr(AbsBaseRoom room) {	
		super(room);	
	
	}	
	@Override	
	protected void initPosList() {	
		// 初始化房间位置	
		for (int posID = 0; posID < super.getPlayerNum(); posID++) {	
			this.posList.add(new WXLSRoomPos(posID, room));	
		}	
	}	
	
	/**	
	 * 全部准备	
	 *	
	 * @param
	 *	
	 * @return	
	 */	
	public boolean isWXLSAllReady() {	
		boolean ret = true;	
		int count = 0;	
		for (AbsRoomPos pos : this.posList) {	
			if (pos.getPid() <= 0)	
				continue;	
			if (!pos.isReady()) {	
				ret = false;	
				break;	
			}	
			count++;	
		}	
	
		if (count >= 2 && ret) {	
			return true;	
		} else {	
			return false;	
		}	
	}	
	
	public int getAllPlayerNum() {	
		int count = 0;	
		for (AbsRoomPos pos : this.posList) {	
			if (pos.getPid() <= 0)	
				continue;	
			count++;	
		}	
		return count;	
	}	
	
	@SuppressWarnings("rawtypes")	
	public List<WXLSRoomPos> getAllWXLSRoomPosList () {	
		List<WXLSRoomPos> WXLSRoomPos = new ArrayList<WXLSRoomPos>();	
		for (AbsRoomPos pos : this.posList) {	
			if (null == pos)	
				continue;	
			if (pos.getPid() <= 0)	
				continue;	
			WXLSRoomPos.add((WXLSRoomPos) pos);	
		}	
	
		return WXLSRoomPos;	
	}	
	
	
	@SuppressWarnings("rawtypes")	
	public int getPlayerNum() {	
		int count = 0;	
		for (int i = 0; i < super.getPlayerNum() ;i++) {	
			WXLSRoomPos sPos = (WXLSRoomPos) posList.get(i);	
			if (null == sPos)	
				continue;	
			if (!sPos.isPlayTheGame())	
				continue;	
			count++;	
		}	
		return count;	
	}	
	
	
	/**	
	 * 十三水 清除牌序的状态	
	 */	
	public void clearCardReady() {	
		for (int i = 0; i < super.getPlayerNum(); i++) {	
			WXLSRoomPos pos = (WXLSRoomPos) posList.get(i);	
			pos.clearCardReady();	
		}	
	}	
	
	/**	
	 * 十三水 全部人是否已经摆好牌了。	
	 */	
	public boolean isAllCardReady() {	
		boolean ret = true;	
		for (int i = 0; i < super.getPlayerNum(); i++) {	
			WXLSRoomPos pos = (WXLSRoomPos) posList.get(i);	
			if (null == pos) {	
				continue;	
			}	
			if (pos.getPid() <= 0) {	
				continue;	
			}	
			if (!pos.isPlayTheGame()) {	
				continue;	
			}	
			if (!pos.isCardReady()) {	
				ret = false;	
				break;	
			}	
		}	
		return ret;	
	}	
	
	/**	
	 * 获取玩家简介列表	
	 * @return	
	 */	
	public List<ShortPlayer> getShortPlayerList (List<WXLSResults> zjplsResults) {	
		return this.getPosList().stream().filter(k ->  k.getPid() > 0L && k.isPlayTheGame()&&scoreFlag(zjplsResults,k.getPid())).map(k -> k.getShortPlayer()).collect(Collectors.toList());	
	}	
	public boolean scoreFlag(List<WXLSResults> zjplsResults, long pid){	
		for(WXLSResults con: zjplsResults){	
			if(con.getPid()==pid){	
				return true;	
			}	
		}	
		return false;	
	
	}	
	/**	
	 * 是否所有玩家继续下一局	
	 *	
	 * @return	
	 */	
	@Override	
	public boolean isAllContinue() {	
		if (null == this.getPosList() || this.getPosList().size() <= 1) {	
			// 玩家信息列表没数据	
			return false;	
		}	
		for(AbsRoomPos roomPos:this.getPosList()){	
			if(roomPos.getPid()<=0) {	
				continue;	
			}	
			if(!roomPos.isGameReady()){	
				return false;	
			}	
		}	
		return true;	
	}	
	/**	
	 * 是否所有玩家准备	
	 *	
	 * @return	
	 */	
	@Override	
	public boolean isAllReady() {	
	
		if(this.room.getBaseRoomConfigure().getBaseCreateRoom().getPlayerNum()==this.room.getBaseRoomConfigure().getBaseCreateRoom().getPlayerMinNum()){	
			if (null == this.getPosList() || this.getPosList().size() <= 1) {	
				// 玩家信息列表没数据	
				return false;	
			}	
			AbsRoomPos result = this.getPosList().stream().filter((x) -> !x.isReady()).findAny().orElse(null);	
			if (null != result) {	
				return false;	
			}	
			return true;	
		}else {	
			List<AbsRoomPos> result = this.getPosList().stream().filter((x) -> x.getPid()!=0).collect(Collectors.toList());	
			if (null == this.getPosList() || this.getPosList().size() <= 1) {	
				// 玩家信息列表没数据 人数少于两个无法开始	
				return false;	
			}	
			if(null==result||result.size()<2){	
				// 房间玩家 人数少于两个无法开始	
				return false;	
			}	
			for(AbsRoomPos con:result){	
				if(!con.isReady()){	
					return false;	
				}	
			}	
			return true;	
		}	
	}

	/**
	 * 检查用户超时
	 */
	@Override
	public void checkOverTime(int ServerTime) {
		if (ServerTime == 0) {
			return;
		}
		for (AbsRoomPos pos : this.getPosList()) {
			if (Objects.isNull(pos) || pos.getPid() <= 0L) {
				continue;
			}
			if (pos.getLatelyOutCardTime() <= 0) {
				continue;
			}
			if (pos.isTrusteeship()) {
				continue;
			}

			if (pos.isRobot() && CommTime.nowMS() > pos.getLatelyOutCardTime() + RobotMgr.getInstance().getThinkTime()) {
				this.getRoom().RobotDeal(pos.getPosID());
				continue;
			}
			if (CommTime.nowMS() > pos.getLatelyOutCardTime() + ServerTime) {
				pos.setLatelyOutCardTime(CommTime.nowMS());
				if (Objects.nonNull(this.getRoom())) {
					// 启动定时器
					this.getRoom().startTrusteeShipTime();
				}
				// 增加托管局数
				((WXLSRoomPos)pos).addTuoGuanSetCount();
				pos.setTrusteeship(true, false);
				if (Objects.nonNull(this.getRoom())) {
					if(room.needAtOnceOpCard()){
						room.roomTrusteeship(pos.getPosID());
					}
				}

			}
		}
	}	
}	
