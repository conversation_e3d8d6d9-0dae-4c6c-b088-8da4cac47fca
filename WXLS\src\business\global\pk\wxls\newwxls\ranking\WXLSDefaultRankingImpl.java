package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
public class WXLSDefaultRankingImpl extends WXLSAbstractRanking {	
	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
	
        WXLSRankingResult result = new WXLSRankingResult();	
        result.setRankingEnum(WXLSRankingEnum.HIGH_CARD);	
	
        return result;	
    }	
	
}	
	
