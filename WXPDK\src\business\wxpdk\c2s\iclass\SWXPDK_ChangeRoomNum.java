package business.wxpdk.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;


public class SWXPDK_ChangeRoomNum extends BaseSendMsg {
    
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	public long roomID;
    public String roomKey;
    public int createType;
    public static SWXPDK_ChangeRoomNum make(long roomID, String roomKey,int createType) {
    	SWXPDK_ChangeRoomNum ret = new SWXPDK_ChangeRoomNum();
        ret.roomID = roomID;
        ret.roomKey = roomKey;
        ret.createType = createType;
        return ret;
    }
}
