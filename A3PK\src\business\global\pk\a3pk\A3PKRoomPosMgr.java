package business.global.pk.a3pk;	
	
import business.global.room.base.AbsBaseRoom;	
import business.global.room.base.AbsRoomPosMgr;	
import com.ddm.server.common.utils.CommTime;
	
	
public class A3PKRoomPosMgr extends AbsRoomPosMgr {	
    private A3PKRoom a3pkRoom;	
    public A3PKRoomPosMgr(AbsBaseRoom room) {	
        super(room);	
        this.a3pkRoom = (A3PKRoom) room;	
    }	
	
    @Override	
    protected void initPosList() {	
        // 初始化房间位置	
        for (int posID = 0; posID < this.getPlayerNum(); posID++) {	
            this.posList.add(new A3PKRoomPos(posID, this.getRoom()));	
        }	
    }	
	
    /**	
     * 是否所有玩家继续下一局	
     *	
     * @return	
     */	
    @Override	
    public boolean isAllContinue() {	
        if (null == this.getPosList() || this.getPosList().size() <= 1) {	
            // 玩家信息列表没数据	
            return false;	
        }	
	
            // 不是罚分模式	
            if (this.room.getBaseRoomConfigure().getBaseCreateRoom().getFangjian().contains(A3PKRoomEnum.A3PKGameRoomConfigEnum.XiaoJu10Miao.ordinal())) {	
                int secLimit = 10;	
                // 超时继续，萍乡	
                this.getPosList().stream().forEach(k -> {	
                    if (k.getPid() > 0 && !k.isGameReady() && k.getTimeSec() > 0 && CommTime.nowSecond() - k.getTimeSec() >= secLimit) {	
                        getRoom().continueGame(k.getPid());	
                    }	
                });	
            }	
        // 玩家在游戏中并且没有准备。	
        return this.getPosList().stream().allMatch(k ->k.getPid()<=0L|| (k.getPid() > 0L && k.isGameReady()));	
    }	
	
	
	
	
	
	
	
	
}	
