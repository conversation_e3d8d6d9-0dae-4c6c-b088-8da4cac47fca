package core.network.client2game.handler.ahhbmj;						
						
import business.player.Player;						
import business.player.feature.PlayerRoom;						
import business.ahhbmj.c2s.iclass.CAHHBMJ_CreateRoom;						
import cenum.PrizeType;						
import com.ddm.server.websocket.def.ErrorCode;						
import com.ddm.server.websocket.handler.requset.WebSocketRequest;						
import com.google.gson.Gson;						
import core.network.client2game.handler.PlayerHandler;						
import core.network.http.proto.SData_Result;						
import core.server.ahhbmj.AHHBMJAPP;						
import jsproto.c2s.cclass.room.BaseRoomConfigure;						
						
import java.io.IOException;						
						
/**						
 * 济宁创建房间						
 *						
 * <AUTHOR>						
 */						
public class CAHHBMJCreateRoom extends PlayerHandler {						
						
	@Override						
	public void handle(Player player, WebSocketRequest request, String message)						
			throws IOException {						
						
		final CAHHBMJ_CreateRoom clientPack = new Gson().fromJson(message,						
				CAHHBMJ_CreateRoom.class);						
		// 公共房间配置												
		BaseRoomConfigure<CAHHBMJ_CreateRoom> configure = new BaseRoomConfigure<CAHHBMJ_CreateRoom>(						
				PrizeType.RoomCard,						
				AHHBMJAPP.GameType(),						
				clientPack.clone());						
		SData_Result resule = player.getFeature(PlayerRoom.class).createRoomAndConsumeCard(configure);						
		if (ErrorCode.Success.equals(resule.getCode())) {						
			request.response(resule.getData());						
		} else {						
			request.error(resule.getCode(), resule.getMsg());						
		}						
	}						
						
						
}												
