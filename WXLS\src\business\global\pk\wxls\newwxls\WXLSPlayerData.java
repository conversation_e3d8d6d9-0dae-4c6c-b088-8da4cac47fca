package business.global.pk.wxls.newwxls;	
	
import BaseCommon.CommLog;	
import business.global.pk.wxls.WXLSRoom;	
import business.global.pk.wxls.WXLSRoomPos;	
import business.global.pk.wxls.newwxls.ranking.WXLSRankingFacade;	
import business.global.pk.wxls.newwxls.ranking.WXLSRankingResult;	
import business.global.pk.wxls.newwxls.ranking.WXLSSShunZiRankingImpl_T;	
import business.wxls.c2s.cclass.CWXLS_PlayerRanked;	
import business.wxls.c2s.cclass.CWXLS_SpecialInfo;
import business.wxls.c2s.cclass.WXLSRoom_Cfg;
import business.wxls.c2s.cclass.WXLSSet_Pos;
import business.wxls.c2s.cclass.entity.WXLSPlayerCardType;	
import business.wxls.c2s.cclass.entity.WXLSPlayerResult;	
import business.wxls.c2s.cclass.newwxls.WXLSCardRankEnum;	
import business.wxls.c2s.cclass.newwxls.WXLSCardSuitEnum;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
import business.wxls.c2s.iclass.CWXLS_CreateRoom;	
import business.wxls.c2s.iclass.CWXLS_Ranked;	
import cenum.PrizeType;
import com.ddm.server.common.CommLogD;

import java.util.*;

public class WXLSPlayerData {	
    public WXLSPlayerDun allCards;	
    public WXLSPlayerDun first;	
    public WXLSPlayerDun second;	
    public WXLSPlayerDun third;	
    private long roomID;	
    public long pid;	
    public int posIdx;	
    private boolean isSpecial = false;	
    private int specialType = -1;// 特殊牌型	
    private int shuicnt = 0;// 三墩总水	
    private int specialcnt=0;//特殊牌型分数	
    private boolean isReadyPlayer = false;	
    private boolean isXiPai = false;	
    private List<WXLSPlayerSpecial> wxlsPlayerSpecials=new ArrayList<>();
    public CWXLS_CreateRoom roomCfg=null;
    public WXLSPlayerData(long roomID, long pid, int pos,CWXLS_CreateRoom roomCfg) {
        this.roomID = roomID;	
        this.pid = pid;	
        this.posIdx = pos;
        this.roomCfg = roomCfg;
    }	
	
	
    public void clean() {	
        this.allCards = null;
        this.roomCfg = null;
        this.first = null;	
        this.second = null;	
        this.third = null;	
        this.wxlsPlayerSpecials=null;	
        this.specialcnt=0;	
    }	
	
	
    public void setSpecialType(List<WXLSPlayerSpecial> wxlsPlayerSpecials){	
        this.wxlsPlayerSpecials=wxlsPlayerSpecials;	
    }	
	
    public boolean getSpecial() {	
        return this.isSpecial;	
    }	
	
    public int getSpecialType() {	
        return this.specialType;	
    }	
	
    public void setSpecialType(int specialType) {	
        this.specialType = specialType;	
        this.isSpecial = true;	
    }	
	
    public int getShui() {	
        return this.shuicnt;	
    }	
	
	
	
    public boolean isXiPai() {	
        return isXiPai;	
    }	
	
    public void setXiPai(boolean isXiPai) {	
        this.isXiPai = isXiPai;	
    }	
	
    /**	
     *	
     * @return F 出现null 错误	
     */	
    public boolean checkDun () {	
        if (this.isSpecial)	
            return true;	
        if (null == this.first || null == this.second || null == this.third) {
            CommLogD.error("null == this.first || null == this.second || null == this.third pid：{},roomID：{},specialTypt：{}",this.pid,this.roomID,this.specialType);
            return false;	
        } else if (this.first.getCardSize() <= 0 || this.second.getCardSize() <= 0 || this.third.getCardSize() <= 0) {
            CommLogD.error("this.first.getCardSize() <= 0 || this.second.getCardSize() <= 0 || this.third.getCardSize() <= 0 pid：{},roomID：{},specialTypt：{}",this.pid,this.roomID,this.specialType);
            return false;	
        } else if (this.pid <= 0L) {
            CommLogD.error("this.pid <= 0L pid：{},roomID：{},specialTypt：{}",this.pid,this.roomID,this.specialType);
            return false;	
        } else if (!this.isReadyPlayer) {	
			CommLogD.error("!this.isReadyPlayer pid：{},roomID：{},specialTypt：{}",this.pid,this.roomID,this.specialType);
            return false;	
        }	
        return true;	
    }	
	
	
    // 设置玩家手牌	
    public void setPlayerCards(List<WXLSPockerCard> cards, CWXLS_CreateRoom cfg,int playerNum) {
        this.allCards = new WXLSPlayerDun(cfg,playerNum);
        this.allCards.addData(cards);
        if(playerNum==3){
            //3人罗松没有特殊牌型
            return;
        }
        WXLSRankingFacade.getInstance().resolve(this.allCards);	
        List<WXLSRankingResult> wxlsRankingResults= WXLSRankingFacade.getInstance().getSpecial(this.allCards);	
        if (wxlsRankingResults.size()!=0) {	
            // 检测到特殊牌 TODO特殊牌型排序 可以删除掉，让客户端自己算，三顺子排序bug	
           List<WXLSPlayerSpecial> wxlsPlayerSpecials=new ArrayList<>();	
           for(WXLSRankingResult result:wxlsRankingResults){	
                WXLSPlayerSpecial wxlsPlayerSpecial=new WXLSPlayerSpecial();	
                wxlsPlayerSpecial.setSpecialType(result.getRankingEnum().getPriority());	
                wxlsPlayerSpecial.setSpecialName(result.getRankingEnum().getType());	
                //三顺子设置玩家墩牌	
                if(result.getRankingEnum()== WXLSRankingEnum.SShunZi||result.getRankingEnum() == WXLSRankingEnum.STongHuaShun){	
                    ArrayList<WXLSCardRankEnum> cardsnew = this.allCards.getRanks();	
                    ArrayList<ArrayList<WXLSCardRankEnum>> rets = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
                    WXLSSShunZiRankingImpl_T aaa = new WXLSSShunZiRankingImpl_T();	
                    if (this.allCards.getCardSize() == 13) {	
                        rets.addAll(aaa.check(cardsnew, 5, 5, 3));	
                    }	
                    Map<Integer, List<WXLSPockerCard>> rankMap = new HashMap<Integer, List<WXLSPockerCard>>();	
                    for (WXLSPockerCard WXLSPockerCard : cards) {	
                        Integer number = new Integer(WXLSPockerCard.getRank().getNumber());	
                        if (!rankMap.containsKey(number)) {	
                            List<WXLSPockerCard> tmp = new ArrayList<WXLSPockerCard>();	
                            tmp.add(WXLSPockerCard);	
                            rankMap.put(number, tmp);	
                        } else {	
                            rankMap.get(number).add(WXLSPockerCard);	
                        }	
                    }	
                    if (rets.size() == 3) {	
                        WXLSPlayerDun dun1 = new WXLSPlayerDun(cfg,playerNum);
                        for (int i = 0; i < rets.get(2).size(); i++) {	
                            int num = rets.get(2).get(i).getNumber();	
                            WXLSPockerCard card = rankMap.get(num).remove(0);	
                            dun1.addCard(card);	
                        }	
                        wxlsPlayerSpecial.first = dun1;	
                        WXLSPlayerDun dun2 = new WXLSPlayerDun(cfg,playerNum);
                        for (int i = 0; i < rets.get(1).size(); i++) {	
                            int num = rets.get(1).get(i).getNumber();	
                            WXLSPockerCard card = rankMap.get(num).remove(0);	
                            dun2.addCard(card);	
                        }	
                        wxlsPlayerSpecial.second = dun2;	
                        WXLSPlayerDun dun3 = new WXLSPlayerDun(cfg,playerNum);
                        for (int i = 0; i < rets.get(0).size(); i++) {	
                            int num = rets.get(0).get(i).getNumber();	
                            WXLSPockerCard card = rankMap.get(num).remove(0);	
                            dun3.addCard(card);	
                        }	
                        wxlsPlayerSpecial.third = dun3;	
                    }	
	
                    if (wxlsPlayerSpecial.second.compareTo(wxlsPlayerSpecial.third) == -1) {	
                        WXLSPlayerDun dun = wxlsPlayerSpecial.second;	
                        wxlsPlayerSpecial.second = wxlsPlayerSpecial.third;	
                        wxlsPlayerSpecial.third = dun;	
                    }	
                //三同花设置墩牌	
                }else if(result.getRankingEnum()== WXLSRankingEnum.STongHua){	
                    Map<WXLSCardSuitEnum, List<WXLSPockerCard>> suitCount = this.allCards.getCardsSuitCountMap();	
                    Iterator<Map.Entry<WXLSCardSuitEnum, List<WXLSPockerCard>>> it = suitCount.entrySet().iterator();	
                    if(suitCount.size()==3){	
                         boolean flag=true;	
                        while (it.hasNext()) {	
                            Map.Entry<WXLSCardSuitEnum, List<WXLSPockerCard>> entry = it.next();	
                            if(entry.getValue().size()==3){	
                               wxlsPlayerSpecial.first = new WXLSPlayerDun(cfg,playerNum).addData(entry.getValue());
                            }	
                            if(entry.getValue().size()==5&&flag){	
                                flag=false;	
                                wxlsPlayerSpecial.second = new WXLSPlayerDun(cfg,playerNum).addData(entry.getValue());
                             }	
                            if(entry.getValue().size()==5&&!flag){	
                                wxlsPlayerSpecial.third = new WXLSPlayerDun(cfg,playerNum).addData(entry.getValue());
                             }	
                        }	
                        if (wxlsPlayerSpecial.second.compareTo(wxlsPlayerSpecial.third) == -1) {	
                            WXLSPlayerDun dun = wxlsPlayerSpecial.second;	
                            wxlsPlayerSpecial.second = wxlsPlayerSpecial.third;	
                            wxlsPlayerSpecial.third = dun;	
                        }	
                    }else if(suitCount.size()==2) {	
                        while (it.hasNext()) {	
                            Map.Entry<WXLSCardSuitEnum, List<WXLSPockerCard>> entry = it.next();	
                            if(entry.getValue().size()==3){	
                                wxlsPlayerSpecial.first = new WXLSPlayerDun(cfg,playerNum).addData(entry.getValue());
                            }else if(entry.getValue().size()==10){	
                                wxlsPlayerSpecial.second = new WXLSPlayerDun(cfg,playerNum).addData(entry.getValue().subList(0,5));
                                wxlsPlayerSpecial.third = new WXLSPlayerDun(cfg,playerNum).addData(entry.getValue().subList(5,10));
                            }else if(entry.getValue().size()==8){	
                                wxlsPlayerSpecial.first = new WXLSPlayerDun(cfg,playerNum).addData(entry.getValue().subList(0,3));
                                wxlsPlayerSpecial.second = new WXLSPlayerDun(cfg,playerNum).addData(entry.getValue().subList(3,8));
                            }else if(entry.getValue().size()==5){	
                                wxlsPlayerSpecial.third = new WXLSPlayerDun(cfg,playerNum).addData(entry.getValue());
                            }	
                        }	
                        if (wxlsPlayerSpecial.second.compareTo(wxlsPlayerSpecial.third) == -1) {	
                            WXLSPlayerDun dun = wxlsPlayerSpecial.second;	
                            wxlsPlayerSpecial.second = wxlsPlayerSpecial.third;	
                            wxlsPlayerSpecial.third = dun;	
                        }	
                    }else{	
                        List<WXLSPockerCard> firstCard = this.allCards.getCards().subList(0, 3);	
                        wxlsPlayerSpecial.first = new WXLSPlayerDun(cfg,playerNum).addData(firstCard);
                        List<WXLSPockerCard> secondCard = this.allCards.getCards().subList(3, 8);	
                        wxlsPlayerSpecial.second = new WXLSPlayerDun(cfg,playerNum).addData(secondCard);
                        List<WXLSPockerCard> thirdCard = this.allCards.getCards().subList(8, 13);	
                        wxlsPlayerSpecial.third = new WXLSPlayerDun(cfg,playerNum).addData(thirdCard);
                    }	
                }else {	
                    List<WXLSPockerCard> firstCard = this.allCards.getCards().subList(0, 3);	
                    wxlsPlayerSpecial.first = new WXLSPlayerDun(cfg,playerNum).addData(firstCard);
                    List<WXLSPockerCard> secondCard = this.allCards.getCards().subList(3, 8);	
                    wxlsPlayerSpecial.second = new WXLSPlayerDun(cfg,playerNum).addData(secondCard);
                    List<WXLSPockerCard> thirdCard = this.allCards.getCards().subList(8, 13);	
                    wxlsPlayerSpecial.third = new WXLSPlayerDun(cfg,playerNum).addData(thirdCard);
                }	
                wxlsPlayerSpecials.add(wxlsPlayerSpecial);	
            }	
            setSpecialType(wxlsPlayerSpecials);	
        }	
	
    }	
	
    // 设置玩家墩牌	
    public boolean setCards(CWXLS_PlayerRanked ranked,CWXLS_CreateRoom cfg,WXLSPockerCard pockerCard) {
        this.isSpecial = false;	
        this.first = new WXLSPlayerDun(ranked.first,cfg);	
        this.second = new WXLSPlayerDun(ranked.second,cfg);	
        this.third = new WXLSPlayerDun(ranked.third,cfg);	
        if (checkCards(pockerCard)) {
            WXLSRankingFacade.getInstance().resolve(this.first);	
            WXLSRankingFacade.getInstance().resolve(this.second);	
            WXLSRankingFacade.getInstance().resolve(this.third);	
            int firstV = this.first.getRankingResult().getRankingEnum().getPriority();	
            int secondV = this.second.getRankingResult().getRankingEnum().getPriority();	
            int thirdV = this.third.getRankingResult().getRankingEnum().getPriority();	
            if(firstV>secondV || secondV>thirdV)	
            {	
                return false;	
            } else {	
                // 第一墩和第二墩牌型相同	
                if (firstV == secondV) {	
                    if(this.first.compareTo(this.second) < 0) {	
                        return false;	
                    }	
	
                }	
                if (secondV == thirdV) {	
                    if(this.second.compareTo(this.third) < 0) {	
                        return false;	
                    }	
                }	
            }	
            this.isReadyPlayer = true;	
            return true;	
        } else {	
            return false;	
        }	
    }	
	
    // 检测牌的合法性	
    public boolean checkCards(WXLSPockerCard pockerCard) {
        boolean flag = true;	
        if(this.first.getCardSize() !=3 || this.second.getCardSize()!=5 || this.third.getCardSize()!=5)	
        {	
            flag = false;	
        }
//        List<WXLSPockerCard> allCard=new ArrayList<>(this.allCards.getCards());
//        if(Objects.nonNull(pockerCard)){
//            allCard.add(pockerCard);
//        }
//        if (!allCard.containsAll(this.first.getCards())) {
//            flag = false;
//        }
//        if (!allCard.containsAll(this.second.getCards())) {
//            flag = false;
//        }
//        if (!allCard.containsAll(this.third.getCards())) {
//            flag = false;
//        }
	
        List<String> allCardStr = this.allCards.getCardsString();
        if(Objects.nonNull(pockerCard)){
            allCardStr.add(pockerCard.getKey());
        }
        List<String> checkAllCards = new ArrayList<String>();	
        checkAllCards.addAll(this.first.getCardsString());	
        checkAllCards.addAll(this.second.getCardsString());	
        checkAllCards.addAll(this.third.getCardsString());	
        int count = 0;	
        for (int i = 0 ;i<allCardStr.size() ;i++) {	
            for (int j = 0;j<checkAllCards.size();j++) {	
                if (allCardStr.get(i).equals(checkAllCards.get(j))) {	
                    count++;	
                    break;	
                }	
            }	
        }	
	
        if (count != 13) {	
            flag = false;	
        }	
	
	
        return flag;	
    }	
	
    public List<WXLSPockerCard> getCards() {	
        return this.allCards.getCards();	
    }
    public List<WXLSPockerCard> getCardsRank() {
        List<WXLSPockerCard> cardList=new ArrayList<>();
        cardList.addAll(this.first.getCards());
        cardList.addAll(this.second.getCards());
        cardList.addAll(this.third.getCards());
        return cardList;
    }
    // 获取墩牌牌型	
    public WXLSPlayerCardType getPlayerCardType(int index) {	
        WXLSPlayerCardType ret = new WXLSPlayerCardType();	
        ret.setPid(pid);	
        ret.setPosIdx(posIdx);	
        switch (index) {	
            case 1:	
                ret.setCard(this.first.getRankingResult().getRankingEnum().getPriority());	
                break;	
            case 2:	
                ret.setCard(this.second.getRankingResult().getRankingEnum().getPriority());	
                break;	
            case 3:	
                ret.setCard(this.third.getRankingResult().getRankingEnum().getPriority());	
                break;	
            default:	
                break;	
        }	
        return ret;	
    }	
	
    public void addBaseShuiCount(int tmpshui, int index) {	
        this.shuicnt += tmpshui;	
        if (1 == index) {	
            this.first.addShuiCount(tmpshui);	
        } else if (2 == index) {	
            this.second.addShuiCount(tmpshui);	
        } else if (3 == index) {	
            this.third.addShuiCount(tmpshui);	
        }	
    }	
	
    // 特殊牌、打枪使用	
    public void addShuiCount(int tmpshui) {	
        this.shuicnt += tmpshui;	
    }	
	
    public int getFirstTypeShui(boolean isSheJian) {
        int ret = this.first.getRankingResult().getRankingEnum().value();	
        if (this.first.getRankingResult().getRankingEnum() == WXLSRankingEnum.THREE_OF_THE_KIND) {	
            ret = 3 * ret;	
        }
        return isSheJian?ret*2:ret;
    }	
	
    public int getSecondTypeShui(boolean isSheJian) {
        WXLSRankingEnum re = this.second.getRankingResult().getRankingEnum();	
        int ret = re.value();	
        if ( re == WXLSRankingEnum.FOUR_OF_THE_KIND || re == WXLSRankingEnum.STRAIGHT_FLUSH) {	
            ret = 2 * ret;	
        }	
        if(re == WXLSRankingEnum.FULL_HOUSE ){	
            ret = 2* ret;
        }
        return isSheJian?ret*2:ret;
    }	
	
    public int getThirdTypeShui(boolean isSheJian) {
        int ret= this.third.getRankingResult().getRankingEnum().value();
        return isSheJian?ret*2:ret;
    }	
	
    // 获取对比结果	
    public WXLSPlayerResult getPlayerResult(int index) {	
        WXLSPlayerResult ret = new WXLSPlayerResult();	
        ret.setPid(pid);	
        ret.setPosIdx(posIdx);	
        switch (index) {	
            case 1:	
                ret.setShui(this.first.getShuiCount());	
                break;	
            case 2:	
                ret.setShui(this.second.getShuiCount());	
                break;	
            case 3:	
                ret.setShui(this.third.getShuiCount());	
                break;	
            default:	
                break;	
        }	
        return ret;	
    }	
	
    // 获取玩家设置好的牌序	
    public CWXLS_Ranked getRanked() {	
        CWXLS_Ranked ret = new CWXLS_Ranked();	
        ret.roomID = this.roomID;	
        ret.pid = this.pid;	
        ret.posIdx = this.posIdx;	
        ret.isSpecial = this.isSpecial;	
        ret.special = this.specialType;	
        ret.dunPos = this.getPlayerRanked();	
        return ret;	
    }	
	
    // 获取玩家数据	
    public WXLSPlayerResult getTotalShuiS(WXLSRoom room) {	
        WXLSPlayerResult ret = new WXLSPlayerResult();	
        ret.setPid(pid);	
        ret.setPosIdx(posIdx);	
        int total = this.shuicnt;	
        ret.setShui(total);	
        WXLSRoomPos zjplsRoomPos=(WXLSRoomPos)room.getRoomPosMgr().getPosByPosID(posIdx);	
        Double sportsPoint;	
        if(zjplsRoomPos.setSportsPoint(total)==null){	
            sportsPoint=0.0;	
        }else {	
            sportsPoint=zjplsRoomPos.setSportsPoint(total);	
        }	
        ret.setSportsPoint(sportsPoint);	
        if (room.getBaseRoomConfigure().getPrizeType() == PrizeType.Gold) {	
            ret.setShui(total *  room.getBaseMark());	
        }	
        return ret;	
    }	
    // 获取玩家特殊牌型数据	
    public WXLSPlayerResult getSpecialShuiS() {	
        WXLSPlayerResult ret = new WXLSPlayerResult();	
        ret.setPid(pid);	
        ret.setPosIdx(posIdx);	
        int total = this.specialcnt;	
        ret.setShui(total);	
        return ret;	
    }	
	
    private CWXLS_PlayerRanked getPlayerRanked() {	
        CWXLS_PlayerRanked ret = new CWXLS_PlayerRanked();	
        if (ret.first != null && ret.second != null && this.third != null) {	
            ret.first = this.first.get0xStr();	
            ret.second = this.second.get0xStr();	
            ret.third = this.third.get0xStr();	
        }	
	
        return ret;	
	
    }	
	
    public WXLSSet_Pos getSetPosInfo() {	
        WXLSSet_Pos ret = new WXLSSet_Pos();	
        ret.posID = this.posIdx;	
        ret.shouCard = allCards.getCardsString();	
        List<CWXLS_SpecialInfo> specialInfos=new ArrayList<>();	
        for(WXLSPlayerSpecial special:this.wxlsPlayerSpecials){	
            CWXLS_SpecialInfo specialInfo=new CWXLS_SpecialInfo();	
            specialInfo.first=special.first.getCardsString();	
            specialInfo.second=special.second.getCardsString();	
            specialInfo.third=special.third.getCardsString();	
            specialInfo.specialName=special.getSpecialName();	
            specialInfo.specialType=special.getSpecialType();	
            specialInfos.add(specialInfo);	
        }	
        ret.special = specialInfos;	
        return ret;	
    }	
	
    public void addSpecialCount(int shui) {	
        this.specialcnt += shui;	
	
    }

    @Override
    public String toString() {
        return "WXLSPlayerData{" +
                "allCards=" + allCards +
                ", first=" + first +
                ", second=" + second +
                ", third=" + third +
                ", roomID=" + roomID +
                ", pid=" + pid +
                ", posIdx=" + posIdx +
                ", isSpecial=" + isSpecial +
                ", specialType=" + specialType +
                ", shuicnt=" + shuicnt +
                ", specialcnt=" + specialcnt +
                ", isReadyPlayer=" + isReadyPlayer +
                ", isXiPai=" + isXiPai +
                ", wxlsPlayerSpecials=" + wxlsPlayerSpecials +
                ", roomCfg=" + roomCfg +
                '}';
    }
}
