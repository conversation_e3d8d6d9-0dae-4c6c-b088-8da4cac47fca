package business.global.mj.ahhbmj;							
							
import business.global.room.base.AbsBaseRoom;							
import business.global.room.mj.MJRoomPosMgr;							
							
import java.util.ArrayList;							
import java.util.stream.Collectors;							
							
/**							
 * 红中麻将 房间内每个位置信息							
 *							
 * <AUTHOR>							
 */							
							
public class AHHBMJRoomPosMgr extends MJRoomPosMgr {							
							
    public AHHBMJRoomPosMgr(AbsBaseRoom room) {							
        super(room);							
    }							
							
							
    @Override							
    protected void initPosList() {							
        // 初始化房间位置							
        for (int posID = 0; posID < this.getPlayerNum(); posID++) {							
            this.posList.add(new AHHBMJRoomPos<>(posID, room));							
        }							
    }							
							
							
    /**							
     * 获取飘分列表							
     */							
    public ArrayList<Integer> getPiaoFenList() {							
        ArrayList<Integer> piaofenList = new ArrayList<>();							
        piaofenList.addAll(this.getPosList().stream().map(roomPos -> ((AHHBMJRoomPos) roomPos).getPiaoPoint()).collect(Collectors.toList()));							
        return piaofenList;							
    }							
							
	
							
}							
