package business.global.pk.a3pk.optype;	
	
import business.global.pk.AbsPKSetPos;	
import business.global.pk.a3pk.A3PKRoomEnum;	
import business.global.pk.a3pk.A3PKRoomSet;	
import business.global.pk.a3pk.A3PKSetPos;	
import com.ddm.server.common.CommLogD;	
import org.apache.commons.collections.CollectionUtils;	
	
import java.util.List;	
import java.util.Objects;	
import java.util.stream.Collectors;	
	
/**	
 * 发完牌后，有红桃3的玩家为队友；	
 * 如果两张红桃3在同一个玩家手中，则默认对家为队友；	
 * 决定完阵营后，在玩家所选的决定阵营的牌上加上“伙”标识，敌方阵营加上“敌”标识；	
 * 同阵营不会改变房间座位；	
 */	
public class A3PKDistributionPartnerImpl<T> extends A3PKBaseCardType<T> {	
    @Override	
    public boolean resultType(AbsPKSetPos mSetPos, List<Integer> privateCardList) {	
        A3PKSetPos rcwskSetPos = (A3PKSetPos) mSetPos;	
        A3PKRoomSet roomSet = rcwskSetPos.getRoomSet();	
        A3PKSetPos challengePos = (A3PKSetPos) roomSet.getPosDict().values().stream().filter(k -> k.getPrivateCards().stream().filter(cardId-> A3PKRoomEnum.isBlackAor3(roomSet.isKing(),cardId)).count() == 2).findAny().orElse(null);	
        if(Objects.nonNull(challengePos)) {	
            // 记录挑战者位置	
            roomSet.setChallengePos(challengePos.getPosID());	
            challengePos.setRanksType(A3PKRoomEnum.A3PK_RANKS_TYPE.RED_RANKS.value());	
        } else {	
            List<AbsPKSetPos> blackPosList = roomSet.getPosDict().values().stream().filter(k -> k.getPrivateCards().stream().anyMatch(cardId -> A3PKRoomEnum.isBlackAor3(roomSet.isKing(),cardId))).collect(Collectors.toList());	
            if (CollectionUtils.isEmpty(blackPosList) || blackPosList.size() != 2) {	
                CommLogD.error("A3PKDistributionPartnerImpl not blackPosList");	
	
                List<List<Integer>> ccc = roomSet.getPosDict().values().stream().map(k->k.getPrivateCards().stream().filter(cardId-> A3PKRoomEnum.isBlackAor3(roomSet.isKing(),cardId)).collect(Collectors.toList())).collect(Collectors.toList());	
                CommLogD.error("ccc ===========:{}", ccc.toString());	
                return false;	
            }	
            A3PKSetPos ASetPos = (A3PKSetPos) blackPosList.get(0);	
            A3PKSetPos BSetPos = (A3PKSetPos) blackPosList.get(1);	
            ASetPos.setRanksType(A3PKRoomEnum.A3PK_RANKS_TYPE.RED_RANKS.value());	
            ASetPos.setPartnerPos(BSetPos.getPosID());	
            BSetPos.setRanksType(A3PKRoomEnum.A3PK_RANKS_TYPE.RED_RANKS.value());	
            BSetPos.setPartnerPos(ASetPos.getPosID());	
	
        }	
        return true;	
    }	
}	
