package core.network.client2game.handler.ahhbmj;						
						
import business.global.mj.ahhbmj.AHHBMJRoom;						
import business.global.room.RoomMgr;						
import business.ahhbmj.c2s.iclass.CAHHBMJ_PiaoFen;						
import business.player.Player;						
import com.ddm.server.websocket.def.ErrorCode;						
import com.ddm.server.websocket.handler.requset.WebSocketRequest;						
import com.google.gson.Gson;						
import core.network.client2game.handler.PlayerHandler;						
						
import java.io.IOException;						
						
public class CAHHBMJPiaoFen extends PlayerHandler {						
    @Override						
    public void handle(Player player, WebSocketRequest request, String message) throws IOException {						
        final CAHHBMJ_PiaoFen req = new Gson().fromJson(message, CAHHBMJ_PiaoFen.class);						
        long roomID = req.roomID;						
						
						
        AHHBMJRoom room = (AHHBMJRoom) RoomMgr.getInstance().getRoom(roomID);						
        if (null == room) {						
            request.error(ErrorCode.NotAllow, "CAHHBMJOpCard not find room:" + roomID);						
            return;						
        }						
						
//        room.opPiaoFen(request, player.getId(), req);					
    }						
}							
