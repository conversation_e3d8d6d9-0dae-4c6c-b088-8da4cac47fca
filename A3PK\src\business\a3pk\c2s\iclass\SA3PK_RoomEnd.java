package business.a3pk.c2s.iclass;	
import jsproto.c2s.cclass.BaseSendMsg;	
import jsproto.c2s.cclass.RoomEndResult;	
	
	
@SuppressWarnings("serial")	
public class SA3PK_RoomEnd<T> extends BaseSendMsg {	
    	
    public T record;	
	public RoomEndResult sRoomEndResult;	
	
    public static <T>SA3PK_RoomEnd<T> make(T record,RoomEndResult sRoomEndResult) {	
    	SA3PK_RoomEnd<T> ret = new SA3PK_RoomEnd<T>();	
        ret.record = record;	
        ret.sRoomEndResult = sRoomEndResult;	
        return ret;	
    	
	
    }	
}	
