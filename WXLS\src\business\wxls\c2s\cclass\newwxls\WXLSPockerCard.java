package business.wxls.c2s.cclass.newwxls;	
	
/**	
 * Class {@code Card} 一张扑克牌.	
 */	
public class WXLSPockerCard implements Comparable<WXLSPockerCard> {	
	
	private WXLSCardSuitEnum suit;	
	private WXLSCardRankEnum rank;	
	
	private String key;	
	public int type;// 0-3 方块 梅花 红桃 黑桃	
	public int cardID;// 1-13	
	public int ownnerPos = -1;	
	
	public WXLSPockerCard(WXLSCardSuitEnum suit, WXLSCardRankEnum rank) {	
		this.suit = suit;	
		this.rank = rank;	
		this.cardID = this.rank.getNumber() - 1;	
		this.type = this.suit.getNumber();	
		this.key = "0x" + String.valueOf(this.type) + Integer.toHexString(this.cardID + 1);	
	}
	public WXLSPockerCard(WXLSCardSuitEnum suit, WXLSCardRankEnum rank, Boolean addFlag) {
		this.suit = suit;
		this.rank = rank;
		this.cardID = this.rank.getNumber() - 1;
		this.type = this.suit.getNumber();

		this.key = "0x" + String.valueOf(addFlag?this.type+5:this.type) + Integer.toHexString(this.cardID + 1) ;

	}
	public WXLSPockerCard(WXLSCardSuitEnum suit, WXLSCardRankEnum rank, String keyflag) {	
		this.suit = suit;	
		this.rank = rank;	
		this.cardID = this.rank.getNumber() - 1;	
		this.type = this.suit.getNumber();	
	
		this.key = "0x" + String.valueOf(this.type) + Integer.toHexString(this.cardID + 1) + keyflag;	
	
	}	
	
	public WXLSPockerCard(String key) {	
		this.type = Integer.parseInt(key.substring(2, 3));	
		this.key = key;	
	
		switch (this.type) {	
		case 0:	
			this.suit = WXLSCardSuitEnum.DIAMONDS;	
			break;	
		case 1:	
			this.suit = WXLSCardSuitEnum.CLUBS;	
			break;	
		case 2:	
			this.suit = WXLSCardSuitEnum.HEARTS;	
			break;	
		case 3:
		case 8:
				this.suit = WXLSCardSuitEnum.SPADES;
			break;	
//		case 4:	
//			this.suit = WXLSCardSuitEnum.GUI;	
//			break;	
		default:	
			this.suit = WXLSCardSuitEnum.DIAMONDS;	
			break;	
		}	
//		if (this.suit == WXLSCardSuitEnum.GUI) {	
//			this.cardID = Integer.parseInt(key.substring(3, 4), 16);	
//			if (this.cardID == 1) {	
//				this.rank = WXLSCardRankEnum.CARD_DGUI;	
//			} else if (this.cardID == 2) {	
//				this.rank = WXLSCardRankEnum.CARD_XGUI;	
//			}	
//		} else {	
			this.cardID = Integer.parseInt(key.substring(3, 4), 16) - 1;	
			this.rank = WXLSCardRankEnum.values()[this.cardID - 1];	
//		}	
	}	
	
	public WXLSCardSuitEnum getSuit() {	
		return suit;	
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public int getRankNumber() {
		return this.rank.getNumber();	
	}	
	
	public void setSuit(WXLSCardSuitEnum suit) {	
		this.suit = suit;	
	}	
	
	public WXLSCardRankEnum getRank() {	
		return rank;	
	}	
	
	public void setRank(WXLSCardRankEnum rank) {	
		this.rank = rank;	
	}	
	
	@Override	
	public boolean equals(Object o) {	
		if (this == o)	
			return true;	
		if (o == null || getClass() != o.getClass())	
			return false;	
	
		WXLSPockerCard card = (WXLSPockerCard) o;	
	
		if (!this.suit.getName() .equals( card.suit.getName()))	
			return false;	
		return this.rank.getNumber() .equals( card.rank.getNumber())&&this.getKey().equals(card.getKey());
	
	}	
	public String getKey() {	
		return key;	
	}	
	@Override	
	public int hashCode() {	
		return this.suit.ordinal() + this.rank.getNumber();	
	}	
	
	public String toString2() {	
		return this.suit.getName() + this.rank.getNumber() + "【" + key + "】";	
	}	
	
	@Override	
	public String toString() {	
		return key;	
	}	
	
	/**	
	 * 实现Comparable接口, 获取最大的单牌, 直接使用牌的数字大小比较即可 使用降序排序, 因为第一个Card极为单牌最大值	
	 *	
	 * @param o	
	 * @return	
	 */	
	@Override	
	public int compareTo(WXLSPockerCard o) {	
		int selfNumber = this.rank.getNumber();	
		int otherNumber = o.rank.getNumber();	
	
		if (selfNumber < otherNumber) {	
			return 1;	
		}	
		if (selfNumber > otherNumber) {	
			return -1;	
		}	
		return 0;	
	}

	public int getValue() {
		return 16*this.type+this.cardID + 1;
	}
}
