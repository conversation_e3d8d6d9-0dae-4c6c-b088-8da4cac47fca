package business.global.mj.ahhbmj;

import business.global.room.base.AbsBaseRoom;
import business.global.room.mj.MJRoomPos;
import lombok.Getter;
import lombok.Setter;

/**
 * 房间内每个位置信息
 *
 * @param <T>
 * <AUTHOR>
 */
@Setter
@Getter
public class AHHBMJRoomPos<T> extends MJRoomPos {
    private int piaoPoint = -1;
    private int zuoFen = -1; //-1:不显示 0：“不下坐”、1：“1坐”、2：“2坐
    private int laFen = -1;//-1:不显示 0：“不下拉”、1：“1拉”、2：“2拉
    private int paoFen = -1;//-1:不显示 0：“不下跑”、1：“1跑”、2：“2跑

    public AHHBMJRoomPos(int posID, AbsBaseRoom room) {
        super(posID, room);
    }

    public int getPiaoPoint() {
        return piaoPoint;
    }

    public void setPiaoPoint(int piaoPoint) {
        this.piaoPoint = piaoPoint;
    }

    public int getZuoFen() {
        return zuoFen;
    }

    public void setZuoFen(int zuoFen) {
        this.zuoFen = zuoFen;
    }

    public int getLaFen() {
        return laFen;
    }

    public void setLaFen(int laFen) {
        this.laFen = laFen;
    }

    public int getPaoFen() {
        return paoFen;
    }

    public void setPaoFen(int paoFen) {
        this.paoFen = paoFen;
    }
}
