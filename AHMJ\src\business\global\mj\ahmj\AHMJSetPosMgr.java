package business.global.mj.ahmj;

import business.global.mj.*;
import cenum.mj.HuType;
import cenum.mj.MJCEnum;
import cenum.mj.OpType;
import jsproto.c2s.cclass.mj.NextOpType;
import jsproto.c2s.cclass.mj.OpTypeInfo;

import java.util.ArrayList;
import java.util.Collections;

/**
 * 靖州麻将
 *
 * <AUTHOR>
 */
public class AHMJSetPosMgr extends AbsMJSetPosMgr {
    public AHMJSetPosMgr(AbsMJSetRoom set) {
        super(set);
    }

    @Override
    public void startSetApplique() {
    }

    public void checkOpTypeQGH(int curOpPos, int curCardID) {
        check_QiangGangHu(curOpPos, curCardID);
    }

    /**
     * 检测可以抢杠胡的操作者
     *
     * @param curOpPos  操作者位置
     * @param curCardID 操作牌
     * @return
     */
    public void check_QiangGangHu(int curOpPos, int curCardID) {
        AHMJSetPos setPos;
        for (int i = 1; i < this.set.getRoom().getPlayerNum(); i++) {
            int nextPos = (curOpPos + i) % this.set.getRoom().getPlayerNum();
            setPos = (AHMJSetPos) this.set.getMJSetPos(nextPos);
            setPos.setQiangGangHuFlag(true);
            OpType oType = setPos.checkPingHu(nextPos, curCardID);
            if (!OpType.Not.equals(oType)) {
                this.addOpTypeInfo(nextPos, OpType.QiangGangHu);
            }
            setPos.setQiangGangHuFlag(false);
        }
    }

    /**
     * 检查动作类型。
     *
     * @param curOpPos  当前操作位置ID
     * @param curCardID 当前操作牌ID
     * @param opType    动作类型
     */
    public void checkOpType(int curOpPos, int curCardID, OpType opType) {
        // 清空所有动作类型操作
        this.cleanAllOpType();
        switch (opType) {
            case Out:
                checkOutOpType(curOpPos, curCardID);
                break;
            case TianHu:
                this.checkAtFirstHu();
                break;
            case Gang:
                checkOpTypeQGH(curOpPos, curCardID);
            default:
                break;
        }
    }

    /**
     * 下个动作类型。
     *
     * @param opType
     * @return
     */
    public NextOpType exeCardAction(OpType opType) {
        NextOpType nOpType = null;
        switch (opType) {
            case Out:
            case Gang:
            case TianHu:
                nOpType = opAllMapOutCard();
                break;
            default:
                break;
        }
        return nOpType;
    }

    @Override
    protected boolean checkExistYPDX() {
        return false;
    }

    @Override
    protected boolean checkExistPingHu() {
        return true;
    }

    /**
     * 检查是否存在接杠补杠 T:接杠时可选择碰后再补杠。
     *
     * @return
     */
    @Override
    protected boolean checkExistJGBG() {
        return true;
    }

    @Override
    protected boolean checkExistChi() {
        return true;
    }

    /**
     * 检测其他人是否可以碰
     *
     * @param curOpPos  当前操作位置ID
     * @param curCardID 当前操作牌ID
     */
    protected void check_otherPeng(int curOpPos, int curCardID) {
        AbsMJSetPos setPos = null;
        for (int i = 1; i < this.set.getRoom().getPlayerNum(); i++) {
            int nextPos = (curOpPos + i) % this.set.getRoom().getPlayerNum();
            setPos = this.set.getMJSetPos(nextPos);
            // 检查碰动作
            if (setPos.checkOpType(curCardID, OpType.Peng)) {
                this.addOpTypeInfo(nextPos, OpType.Peng);
                return;
            }
        }
    }

    /**
     * 添加动作信息
     *
     * @param posId  位置
     * @param opType 动作类型
     */
    protected void addOpTypeInfo1(Integer posId, OpType opType) {
        if (null == this.opTypeInfoList) {
            this.opTypeInfoList = Collections.synchronizedList(new ArrayList<>());
            ;
        }
        int count = this.opTypeInfoList.size();
        // 检查是否存在一炮多响
        if (this.checkExistYPDX()) {
            // 存在一炮多响
            if (!HuType.NotHu.equals(MJCEnum.OpHuType(opType))) {
                count = 0;
                this.huPosList.add(posId);
            }
        }
        // 添加动作信息
        this.opTypeInfoList.add(new OpTypeInfo(count + 1, posId, opType));
    }

    /**
     * 检查庄家胡牌
     */
    private void checkDPosHu() {
        AbsMJSetPos mPos = this.set.getMJSetPos(this.set.getDPos());
        if (mPos.getPosOpRecord().getOpHuList().size() <= 0) {
            if (mPos.checkOpType(0, OpType.TianHu)) {
                this.addOpTypeInfo(this.set.getDPos(), OpType.Hu);
            }
        }
    }

    /**
     * 检查烂胡
     */
    private void checkLanHu() {
        AbsMJSetPos mPos;
        for (int i = 1; i < this.set.getRoom().getPlayerNum(); i++) {
            int nextPos = (i + this.set.getDPos()) % this.set.getPlayerNum();
            mPos = this.set.getMJSetPos(nextPos);
            if (null == mPos) {
                continue;
            }
            if (mPos.checkOpType(0, OpType.SanJinDao)) {
                this.addOpTypeInfo(nextPos, OpType.Hu);
            }
        }
    }

    /**
     * 检查起手胡
     */
    private void checkAtFirstHu() {
        //	庄家天胡>闲家烂胡
        checkDPosHu();
//        // 检查烂胡
//        checkLanHu();
    }

}
