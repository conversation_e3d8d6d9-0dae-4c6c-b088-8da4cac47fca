package business.global.mj.wwmj;

import business.global.mj.AbsMJSetRoom;
import business.global.mj.template.MJTemplateRoomEnum;
import business.global.mj.template.MJTemplateSetPos;
import business.global.mj.template.wanfa.MJTemplateLouHu;
import business.global.room.mj.MJRoomPos;
import business.wwmj.c2s.cclass.WWMJResults;
import business.wwmj.c2s.cclass.WWMJRoom_PosEnd;
import cenum.mj.OpPointEnum;
import jsproto.c2s.cclass.mj.BaseMJRoom_PosEnd;
import jsproto.c2s.cclass.room.AbsBaseResults;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * 模板麻将 每一局每个位置信息
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class WWMJSetPos extends MJTemplateSetPos {

    public WWMJSetPos(int posID, MJRoomPos roomPos, AbsMJSetRoom set) {
        super(posID, roomPos, set);
        this.setCalcPosEnd(new WWMJCalcPosEnd(this));
    }

    @Override
    public WWMJRoom getRoom() {
        return (WWMJRoom) super.getRoom();
    }


    /**
     * 计算总结算信息
     */
    @Override
    public void calcResults() {
        // 获取总结算信息
        WWMJResults results = (WWMJResults) this.mResultsInfo();
        Map<OpPointEnum, Integer> huTypeMap = getCalcPosEnd().getHuTypeMap();
        results.addAnGangPoint(huTypeMap.get(OpPointEnum.AnGang) == null ? 0 : huTypeMap.get(OpPointEnum.AnGang));
        results.addMingGangPoint(huTypeMap.get(OpPointEnum.JieGang) == null ? 0 : huTypeMap.get(OpPointEnum.JieGang));
        results.addMingGangPoint(huTypeMap.get(OpPointEnum.GangPao) == null ? 0 : huTypeMap.get(OpPointEnum.GangPao));
        results.addZhongMaPoint(zhongList.size());
        // 并且设置覆盖
        this.setResults(results);
    }

    @Override
    protected AbsBaseResults newResults() {
        return new WWMJResults();
    }


    @Override
    protected MJTemplateLouHu newMJLouHu() {
        return new MJTemplateLouHu(this, MJTemplateRoomEnum.LouHuEnum.ZiMO_BUHU_SUAN_LOU_HU);
    }

    /**
     * 统计本局分数
     *
     * @return
     */
    @Override
    public BaseMJRoom_PosEnd calcPosEnd() {
        // 玩家当局分数结算
        this.getCalcPosEnd().calcPosEnd(this);
        WWMJRoom_PosEnd ret = (WWMJRoom_PosEnd) this.posEndInfo();
        ret.setPosId(getPosID());
        // 位置结算信息
        ret.setHuTypeMap(new HashMap<>(this.getCalcPosEnd().getHuTypeMap()));
        ret.setGangPoint(ret.getHuTypeMap().remove(OpPointEnum.GangNum));
        ret.setHuPoint(ret.getHuTypeMap().remove(OpPointEnum.HuPoint));
        return ret;
    }

    /**
     * 新位置结算信息
     *
     * @return
     */
    @SuppressWarnings("rawtypes")
    protected BaseMJRoom_PosEnd newMJSetPosEnd() {
        return new WWMJRoom_PosEnd();
    }
}					
