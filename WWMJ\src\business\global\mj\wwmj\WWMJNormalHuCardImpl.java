package business.global.mj.wwmj;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCardInit;
import business.global.mj.hu.*;
import business.global.mj.manage.MJFactory;
import business.global.mj.template.MJTemplateNormalHuCardImpl;
import business.global.mj.util.HuUtil;
import cenum.mj.HuType;
import cenum.mj.OpPointEnum;
import cenum.mj.OpType;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class WWMJNormalHuCardImpl extends MJTemplateNormalHuCardImpl {
    /**
     * @param mSetPos
     * @param mCardInit
     * @return
     */
    public boolean checkHuCard(AbsMJSetPos mSetPos, MJCardInit mCardInit) {

        if (Objects.isNull(mCardInit)) {
            return false;
        }
        if (Objects.isNull(mSetPos)) {
            return false;
        }
        return HuUtil.getInstance().checkHu(mCardInit);
    }

    @Override
    public <T> Object checkHuCardReturn(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
        WWMJSetPos setPos = (WWMJSetPos) mSetPos;
        List<Object> opHuList = setPos.getPosOpRecord().getOpHuList();
        opHuList.clear();
        //对子
        List<OpPointEnum> duiZiList = new ArrayList<>();
        if (setPos.getRoom().getRoomCfg().duizi != WWMJRoomEnum.DuiZi.BuDai.ordinal()) {
            setPos.getPublicCardList().forEach(k -> {
                if (k.get(0) == OpType.Peng.value()) {
                    duiZiList.add(OpPointEnum.PengBanZi);
                } else if (k.get(0) == OpType.AnGang.value()) {
                    duiZiList.add(OpPointEnum.AnGang);
                } else if (k.get(0) == OpType.Gang.value()) {
                    duiZiList.add(OpPointEnum.Gang);
                } else if (k.get(0) == OpType.JieGang.value()) {
                    duiZiList.add(OpPointEnum.JieGang);
                }
            });

        }
        opHuList.addAll(duiZiList);
        //胡：1分；（只要胡牌就有这个分）
        setPos.addOpPointEnum(OpPointEnum.Hu);
        //天胡：庄家起手14张且没有杠牌的情况下直接胡牌则为天胡；
        setPos.addOpPointEnum((OpPointEnum) MJFactory.getHuCard(MJTemplateTianHuImpl.class).checkHuCardReturn(mSetPos, mCardInit));
        //n 地胡：闲家炮胡庄家打出的第一张牌；
        setPos.addOpPointEnum((OpPointEnum) MJFactory.getHuCard(MJTemplateDiHu1Impl.class).checkHuCardReturn(mSetPos, mCardInit));
        //杠开：玩家杠牌后，摸回来的那张牌刚好胡牌，算自摸；
        if (setPos.isGSKH()) {
            setPos.addOpPointEnum(OpPointEnum.GSKH);
        }
        //压档：满足一下两种情况任意一种都为压档；
        //胡牌时，胡的那张牌为单边顺子的边张，如12胡3,89胡7；
        //胡牌时，胡的那张牌为顺子中间那张，如13胡2,46胡5；
        //注：单吊不算压档；
        //对对胡：胡牌时，手牌中没有钻和边的牌，且手牌中没有顺子；
        OpPointEnum opPointEnum = (OpPointEnum) MJFactory.getHuCard(MJTemplateBianKanDiaoImpl.class).checkHuCardReturn(mSetPos, mCardInit, mSetPos.getHandCard().type);
        if (opPointEnum.equals(OpPointEnum.BianZhang) || opPointEnum.equals(OpPointEnum.KanZhang)) {
            //绝张：在压档的基础上，听的牌已有3张在桌面上（弃牌区和碰、杠区），最后胡该张牌；
            //不叠加压档；
            if (!mSetPos.getMJSetCard().getRandomCard().getLeftCards().stream().anyMatch(k -> k.type == mSetPos.getHandCard().type)) {
                setPos.addOpPointEnum(OpPointEnum.HeJueZhang);
            } else {
                setPos.addOpPointEnum(OpPointEnum.YaDang);
            }
        } else {
            setPos.addOpPointEnum(opPointEnum);
        }
        if (MJFactory.getHuCard(PPHuCardImpl.class).checkHuCard(mSetPos, mCardInit)) {
            setPos.addOpPointEnum(OpPointEnum.PPH);
        }
        List<Integer> allCardIDs = setPos.allCardIDs();
        allCardIDs.addAll(mSetPos.publicCardList());
        int laoCount = Math.toIntExact(allCardIDs.stream().filter(k -> checkLao(k)).count());
        if (laoCount == allCardIDs.size()) {
            //全老：胡牌玩家手牌以及碰杠区的牌，全是点数≤5的牌；（只有万筒条算点数）
            //全老不叠加十老；
            setPos.addOpPointEnum(OpPointEnum.QuanDa);
        } else if (laoCount >= 10) {
            //十老：胡牌玩家手牌以及碰杠区的牌，点数≤5的有十张或以上；（只有万筒条算点数）
            setPos.addOpPointEnum(OpPointEnum.DaYuWu);
        }
        int xiaoCount = Math.toIntExact(allCardIDs.stream().filter(k -> checkXiao(k)).count());
        if (xiaoCount == allCardIDs.size()) {
            //全小：胡牌玩家手牌以及碰杠区的牌，全是点数≥5的牌；（只有万筒条算点数）
            //全小不叠加十小；
            setPos.addOpPointEnum(OpPointEnum.QuanXiao);

        } else if (xiaoCount >= 10) {
            //十小：胡牌玩家手牌以及碰杠区的牌，点数≥5的有十张或以上；（只有万筒条算点数）
            setPos.addOpPointEnum(OpPointEnum.XiaoYuWu);
        }
        Map<Integer, Long> map = allCardIDs.stream().collect(Collectors.groupingBy(k -> k / 100 > 40 ? k / 100 : k / 100 % 10, Collectors.counting()));
        Long maxLength = map.values().stream().max(Long::compareTo).get();
        //十通：胡牌玩家有十张及以上同点数的牌；
        //七通/八通/九通：胡牌玩家有七张/八张/九张同点数的牌；
        if (maxLength == 10) {
            setPos.addOpPointEnum(OpPointEnum.CCHDDHu);
        } else if (maxLength == 9) {
            setPos.addOpPointEnum(OpPointEnum.CHDDHu);
        } else if (maxLength == 8) {
            setPos.addOpPointEnum(OpPointEnum.HDDHu);
        } else if (maxLength == 7) {
            setPos.addOpPointEnum(OpPointEnum.QDHu);
        } else if (maxLength >= 5) {
            //五通/六通：胡牌玩家有五张/六张同点数的牌；
            //必须是刻子、杠、将牌的组合；
            //在满足有四张的情况下，顺子组合的也可以计入；（即第五张开始可以是顺子里的牌）
            //一份手牌中，可以有多个点数的五通牌型，分数叠加计算；
            List<String> huTypeList = HuUtil.getInstance().findHuTypeList(mCardInit.getAllCardInts(), mCardInit.sizeJin());
            List<OpPointEnum> maxTongList = new ArrayList<>();
            List<OpPointEnum> maxDuiList = new ArrayList<>();
            int maxPoint = 0;
            for (String huType : huTypeList) {
                List<OpPointEnum> curDuiZiList = new ArrayList<>();
                List<List<Integer>> huCards = Arrays.stream(huType.split(":")).map(k -> Arrays.stream(k.split(",")).map(p -> Integer.parseInt(p.trim())).collect(Collectors.toList())).collect(Collectors.toList());
                List<OpPointEnum> tongList = new ArrayList<>();
                int type;
                for (int i = 1; i < 9; i++) {
                    //先去碰杠区找
                    int finalI = i;
                    int count = 0;
                    List<List<Integer>> collect = huCards.stream().filter(card -> card.stream().anyMatch(p -> p % 10 == finalI)).collect(Collectors.toList());
                    boolean jiangDui = collect.stream().anyMatch(list -> list.stream().filter(v -> v / 10 == finalI).count() == 2);
                    for (List<Integer> k : mSetPos.getPublicCardList()) {
                        type = k.get(2) / 100;
                        if (type < 40 && type % 10 == i) {
                            if (k.size() == 7) {
                                //如果是杠牌
                                if (collect.size() > 0) {
                                    //杠+将
                                    if (jiangDui) {
                                        tongList.add(OpPointEnum.LianLiu);
                                    } else {
                                        //杠+1
                                        tongList.add(OpPointEnum.WuFanHe);
                                    }
                                }
                            } else {
                                //碰牌
                                count += 3;
                            }
                        }
                    }
                    //两个碰
                    if (count == 6) {
                        tongList.add(OpPointEnum.LianLiu);
                    } else if (count == 3 && jiangDui) {
                        //碰+将
                        tongList.add(OpPointEnum.WuFanHe);

                    } else if (collect.size() > 1) {
                        for (List<Integer> list : collect) {
                            if (list.get(0) == list.get(1)) {
                                count += list.size();
                            }
                        }
                        if (count == 6) {
                            //两暗刻
                            tongList.add(OpPointEnum.LianLiu);
                        } else if (count == 5) {
                            //暗刻+将
                            tongList.add(OpPointEnum.WuFanHe);
                        }
                    }
                }
                if (setPos.getRoom().getRoomCfg().duizi != WWMJRoomEnum.DuiZi.BuDai.ordinal()) {
                    for (List<Integer> list : huCards) {
                        if (list.size() == 3 && list.get(0) == list.get(1)) {
                            if (list.get(0) == mSetPos.getHandCard().type && !mSetPos.getHuType().equals(HuType.ZiMo))
                                curDuiZiList.add(OpPointEnum.PengBanZi);
                        } else {
                            curDuiZiList.add(OpPointEnum.AnKe);
                        }
                    }
                }
                if (setPos.getRoom().getRoomCfg().duizi == WWMJRoomEnum.DuiZi.DuiZiFanBei.ordinal() && curDuiZiList.size() > maxDuiList.size()) {
                    maxTongList = tongList;
                    maxTongList.addAll(curDuiZiList);
                    maxDuiList = curDuiZiList;
                } else {
                    int duiZiPoint = curDuiZiList.contains(OpPointEnum.PengBanZi) ? curDuiZiList.size() * 2 - 1 : curDuiZiList.size() * 2;
                    if (maxPoint < tongList.size() + duiZiPoint) {
                        maxTongList = tongList;
                        maxTongList.addAll(curDuiZiList);
                        maxDuiList = curDuiZiList;
                    }
                }

            }
            opHuList.addAll(maxTongList);
        }
        if (maxLength != 5 && maxLength != 6) {
            List<String> huTypeList = HuUtil.getInstance().findHuTypeList(mCardInit.getAllCardInts(), mCardInit.sizeJin());
            if (setPos.getRoom().getRoomCfg().duizi != WWMJRoomEnum.DuiZi.BuDai.ordinal()) {
                List<OpPointEnum> maxDuiList = new ArrayList<>();
                for (String huType : huTypeList) {
                    List<OpPointEnum> curDuiZiList = new ArrayList<>();
                    List<List<Integer>> huCards = Arrays.stream(huType.split(":")).map(k -> Arrays.stream(k.split(",")).map(p -> Integer.parseInt(p.trim())).collect(Collectors.toList())).collect(Collectors.toList());
                    for (List<Integer> list : huCards) {
                        if (list.size() == 3 && list.get(0) == list.get(1)) {
                            if (list.get(0) == mSetPos.getHandCard().type && !mSetPos.getHuType().equals(HuType.ZiMo))
                                curDuiZiList.add(OpPointEnum.PengBanZi);
                        } else {
                            curDuiZiList.add(OpPointEnum.AnKe);
                        }
                    }
                    if (curDuiZiList.size() > maxDuiList.size()) {
                        maxDuiList = curDuiZiList;
                    }
                }
                opHuList.addAll(maxDuiList);
            }
        }
        //四核：胡牌玩家手牌以及碰杠区的牌有一组四张一样的牌且不是杠牌组合（可以碰），也不包含将牌组合；例：444万 456万；
        //一份手牌中，可以有多组四核；
        Map<Integer, Long> siHeMap = mCardInit.getAllCardInts().stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        for (Map.Entry<Integer, Long> siHe : siHeMap.entrySet()) {
            if (siHe.getValue() == 4) {
                List<Integer> cards = new ArrayList<>(mCardInit.getAllCardInts());
                cards.remove(siHe.getKey());
                cards.remove(siHe.getKey());
                cards.remove(siHe.getKey());
                //暗刻+1
                if (HuUtil.getInstance().checkHu(cards, mCardInit.sizeJin())) {
                    setPos.addOpPointEnum(OpPointEnum.SiGuiYi);
                }
            } else if (siHe.getValue() == 1 && setPos.getPublicCardList().stream().anyMatch(k -> k.size() == 6 && k.get(2) / 100 == siHe.getKey())) {
                //碰+1
                setPos.addOpPointEnum(OpPointEnum.SiGuiYi);
            }
        }
        //清一色：胡牌玩家的手牌、碰杠区的牌，全是万筒条中的一色；
        //不叠加缺一门；
        if (MJFactory.getHuCard(QingYiSeImpl.class).checkHuCard(mSetPos, mCardInit)) {
            setPos.addOpPointEnum(OpPointEnum.QYS);
        } else if (MJFactory.getHuCard(HunYiSeImpl.class).checkHuCard(mSetPos, mCardInit)) {
            //混一色：胡牌玩家的手牌、碰杠区的牌，全是万筒条中的一色+发财；
            //不叠加缺一门；
            setPos.addOpPointEnum(OpPointEnum.HYS);
        } else if (setPos.checkQueYiMen(mCardInit)) {
            //缺一门：胡牌时，手牌（含碰杠区）只有万条筒其中2种花色，可以有发财；
            setPos.addOpPointEnum(OpPointEnum.QueYiSe);
        }
        Map<Integer, Long> baZhiMap = allCardIDs.stream().collect(Collectors.groupingBy(k -> k / 1000, Collectors.counting()));
        //十一支：同理八支，但需要达到十一张或以上才行；
        boolean anyMatch11 = baZhiMap.entrySet().stream().anyMatch(k -> k.getValue() >= 11);
        if (anyMatch11) {
            setPos.addOpPointEnum(OpPointEnum.SYZhi);
        }
        long count = baZhiMap.entrySet().stream().filter(k -> k.getValue() >= 8).count();
        //双八支：手牌中有两种花色都分别达到八支的情况，则为双八支；
        if (count == 2) {
            setPos.addOpPointEnum(OpPointEnum.ShuangYou);
        }
        if (MJFactory.getHuCard(LongHuCardImpl.class).checkHuCard(mSetPos, mCardInit)) {
            //顶九卡五：在满足顶九的前提下，听该花色中的46中的5，且胡5；
            Map.Entry<Integer, Long> entry = baZhiMap.entrySet().stream().filter(k -> k.getValue() >= 9).findFirst().orElse(null);
            if (Objects.nonNull(entry) && mSetPos.getHandCard().type == entry.getKey() * 10 + 5) {
                setPos.addOpPointEnum(OpPointEnum.QYSYTL);
            } else {
                //顶九：同一条龙，胡牌玩家手牌有同一花色的123、456、789三组顺子；
                setPos.addOpPointEnum(OpPointEnum.Long);
            }
        } else if (!(opHuList.contains(OpPointEnum.QYS) || opHuList.contains(OpPointEnum.HYS) || anyMatch11) && count == 1) {
            //八支：胡牌玩家的手牌以及碰杠区的牌，某一花色的牌有八张或以上；
            //u清一色、混一色、顶九、顶九卡五牌型不叠加八支算分；
            setPos.addOpPointEnum(OpPointEnum.DanYou);
        }
        if (checkMenQing(mSetPos)) {
            if (mSetPos.getHuType().equals(HuType.ZiMo)) {
                //万事：有玩家自摸后，手牌中没有吃、碰、杠牌，可以有暗杠；
                setPos.addOpPointEnum(OpPointEnum.MenQing);
            } else {
                //门清：有玩家炮胡后，手牌中没有吃、碰、杠牌，可以有暗杠；
                setPos.addOpPointEnum(OpPointEnum.MenQianQing);
            }
        }
        //断幺九：胡牌后，手牌中没有点数为1和9的牌和发财；
        if (allCardIDs.stream().noneMatch(k -> checkYaoJiuZi(k))) {
            setPos.addOpPointEnum(OpPointEnum.DuanYao);
        }

        return false;
    }

    private boolean checkYaoJiuZi(Integer k) {
        if (k > 100) {
            k = k / 100;
        }
        if (k > 40) {
            return true;
        }
        return k % 10 == 1 || k % 10 == 9;
    }

    public boolean checkLao(int type) {
        if (type > 1000) {
            type /= 100;
        }
        return type < 40 && type % 10 >= 5;
    }

    public boolean checkXiao(int type) {
        if (type > 1000) {
            type /= 100;
        }
        return type < 40 && type % 10 <= 5;
    }

    /**
     * //门清：胡牌时，没有吃、碰、杠牌；
     *
     * @param mSetPos
     * @return
     */
    public boolean checkMenQing(AbsMJSetPos mSetPos) {
        return mSetPos.sizePublicCardList() == 0 || mSetPos.getPublicCardList().stream().noneMatch(k -> k.get(0) != OpType.AnGang.value());
    }
}	
