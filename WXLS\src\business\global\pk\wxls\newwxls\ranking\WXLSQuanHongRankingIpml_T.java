package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSCardSuitEnum;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.Iterator;	
import java.util.List;	
import java.util.Map;	
	
/**	
 * 全红	
 */	
public class WXLSQuanHongRankingIpml_T extends WXLSAbstractRanking {	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        WXLSRankingResult result = null;	
        if (player.getCardSize() == 13) {	
            Map<WXLSCardSuitEnum, List<WXLSPockerCard>> suitCount = player.getCardsSuitCountMap();	
            Iterator<Map.Entry<WXLSCardSuitEnum, List<WXLSPockerCard>>> it = suitCount.entrySet().iterator();	
            boolean flag = true;	
            while (it.hasNext()) {	
                Map.Entry<WXLSCardSuitEnum, List<WXLSPockerCard>> entry = it.next();	
                if (entry.getKey() == WXLSCardSuitEnum.SPADES||entry.getKey()==WXLSCardSuitEnum.CLUBS) {	
                    flag=false;	
                }	
            }	
            if (flag) {	
                result = new WXLSRankingResult();	
                result.setPockerCards(player.getCards());	
                result.setRankingEnum(WXLSRankingEnum.QUANHONG);	
                return result;	
            }	
        }	
        return result;	
    }	
}	
