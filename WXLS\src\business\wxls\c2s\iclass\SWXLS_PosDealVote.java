package business.wxls.c2s.iclass;	
	
import jsproto.c2s.cclass.BaseSendMsg;	
	
public class SWXLS_PosDealVote extends BaseSendMsg {	
    	
    public long roomID;	
    public int pos;	
    public boolean agreeDissolve;	
	
	
    public static SWXLS_PosDealVote make(long roomID, int pos, boolean agreeDissolve) {	
    	SWXLS_PosDealVote ret = new SWXLS_PosDealVote();	
        ret.roomID = roomID;	
        ret.pos = pos;	
        ret.agreeDissolve = agreeDissolve;	
	
        return ret;	
    	
	
    }	
}	
