package business.wxpdk.c2s.iclass;

import java.util.Map;

import jsproto.c2s.cclass.BaseSendMsg;

/**
 * 接收客户端数据
 * 记牌器
 * <AUTHOR>
 *
 */

@SuppressWarnings("serial")
public class SWXPDK_CardNumber extends BaseSendMsg {

	public long roomID;
	public Map<Integer, Integer> cardNumMap;

    public static SWXPDK_CardNumber make(long roomID, Map<Integer, Integer> cardNumMap) {
    	SWXPDK_CardNumber ret = new SWXPDK_CardNumber();
        ret.cardNumMap = cardNumMap;
        ret.roomID = roomID;
        return ret;
    }
}
