package business.ahmj.c2s.iclass;
import jsproto.c2s.cclass.BaseSendMsg;

/**
 * 莆田麻将
 * 接收客户端数据
 * 创建房间
 * <AUTHOR>
 *
 */
@SuppressWarnings("serial")
public class SAHMJ_Niao extends BaseSendMsg{

	public long roomID;  
	public int  niao;  // -1 没有操作  0不打鸟 1 打5鸟
	public int  pos;
	
    public static SAHMJ_Niao make(long roomID, int  pos, int niao) {
    	SAHMJ_Niao ret = new SAHMJ_Niao();
    	ret.roomID = roomID;
    	ret.pos = pos;
    	ret.niao = niao ;
        return ret;
    }
}