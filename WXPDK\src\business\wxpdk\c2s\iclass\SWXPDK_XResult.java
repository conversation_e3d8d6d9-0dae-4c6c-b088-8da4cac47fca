package business.wxpdk.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SWXPDK_XResult extends BaseSendMsg {
    //用户信息
    private Map<Integer, SWXPDK_UserInfo> userInfo = new HashMap<>();
    //局数信息
    private List<SWXPDK_SetInfo> setInfo = new ArrayList<>();

    public List<SWXPDK_SetInfo> getSetInfo() {
        return setInfo;
    }

    public Map<Integer, SWXPDK_UserInfo> getUserInfo() {
        return userInfo;
    }

}
