package business.ahhbmj.c2s.iclass;						
						
import cenum.room.SetState;						
import jsproto.c2s.cclass.BaseSendMsg;						
						
import java.util.ArrayList;						
						
/**						
 * 接收客户端数据						
 * 状态改变						
 *						
 * <AUTHOR>						
 */						
						
@SuppressWarnings("serial")						
public class						
SAHHBMJ_ChangeStatus extends BaseSendMsg {						
						
    public long roomID;						
    public int setID;//局数						
    public SetState state;  //位置							
    private ArrayList<Integer> piaoFenList = new ArrayList<>(); // 飘分							
						
    public static SAHHBMJ_ChangeStatus make(long roomID, int setID, SetState state, ArrayList<Integer> piaoFenList) {						
        SAHHBMJ_ChangeStatus ret = new SAHHBMJ_ChangeStatus();						
        ret.roomID = roomID;						
        ret.setID = setID;						
        ret.state = state;						
        ret.piaoFenList = piaoFenList;						
        return ret;						
    }						
}												
