package business.wxls.c2s.iclass;	
	
import business.wxls.c2s.cclass.CWXLS_PlayerRanked;	
import jsproto.c2s.cclass.BaseSendMsg;	
	
/**	
 * 确认好牌的位置	
 * 	
 * <AUTHOR>	
 *	
 */	
public class CWXLS_Ranked extends BaseSendMsg {	
	public long roomID;	
	public long pid;	
	public int posIdx;	
	public boolean isSpecial;	
	public int special = -1;	
	public String specialName="";	
	public CWXLS_PlayerRanked dunPos = new CWXLS_PlayerRanked();	
	
	public CWXLS_Ranked() {	
		super();	
	}	
	
	public CWXLS_Ranked(long roomID, long pid, int posIdx, CWXLS_PlayerRanked dun) {	
		super();	
		this.roomID = roomID;	
		this.pid = pid;	
		this.posIdx = posIdx;	
		this.dunPos = dun;	
	}	
	
	public static CWXLS_Ranked make(long roomID, long pid, int posIndex, CWXLS_PlayerRanked dun, boolean isSpecial, int special) {	
		CWXLS_Ranked ret = new CWXLS_Ranked();	
		ret.roomID = roomID;	
		ret.pid = pid;	
		ret.posIdx = posIndex;	
		ret.dunPos = dun;	
		ret.isSpecial = isSpecial;	
		ret.special = special;	
		return ret;	
	}	
	
	@Override	
	public String toString() {	
		return "CWXLS_Ranked [roomID=" + roomID + ", pid=" + pid + ", posIdx=" + posIdx + ", dunPos=" + dunPos + "]\n";	
	}	
}	
