package business.afmj.c2s.iclass;
import jsproto.c2s.cclass.*;


@SuppressWarnings("serial")
public class SAFMJ_StartRound<T> extends BaseSendMsg {
    
    public long roomID;
    public T room_SetWait;


    public static <T> SAFMJ_StartRound<T> make(long roomID, T room_SetWait) {
    	SAFMJ_StartRound<T> ret = new SAFMJ_StartRound<T>();
        ret.roomID = roomID;
        ret.room_SetWait = room_SetWait;

        return ret;
    

    }
}