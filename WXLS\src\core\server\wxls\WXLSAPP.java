package core.server.wxls;	
	
import core.config.server.GameTypeMgr;	
import core.server.GameServer;	
import jsproto.c2s.cclass.GameType;	
	
public class  WXLSAPP {
	
    public final static int gameTypeId = 475;

    /**	
     * 游戏类型	
     *	
     * @return	
     */	
    public static GameType GameType() {	
        return GameTypeMgr.getInstance().gameType(gameTypeId);	
    }	
	
    public static void main(String[] args) throws Exception {	
        GameServer app = new GameServer();	
        app.init(args);	
	
	
    }	
}	
