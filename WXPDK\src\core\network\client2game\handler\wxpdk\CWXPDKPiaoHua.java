package core.network.client2game.handler.wxpdk;

import business.global.pk.wxpdk.WXPDKRoom;
import business.global.room.RoomMgr;
import business.player.Player;
import business.wxpdk.c2s.cclass.CWXPDK_PiaoHua;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;
import core.network.client2game.handler.PlayerHandler;

import java.io.IOException;

/**
 * 打牌
 *
 * <AUTHOR>
 */
public class CWXPDKPiaoHua extends PlayerHandler {


    @Override
    public void handle(Player player, WebSocketRequest request, String message) throws IOException {
        final CWXPDK_PiaoHua req = new Gson().fromJson(message, CWXPDK_PiaoHua.class);
        long roomID = req.roomID;

        WXPDKRoom room = (WXPDKRoom) RoomMgr.getInstance().getRoom(roomID);
        if (null == room) {
            request.error(ErrorCode.NotAllow, "CWXPDK_<PERSON>ua not find room:" + roomID);
            return;
        }

        room.opPiaoHua(request, player.getId(), req);
    }
}
