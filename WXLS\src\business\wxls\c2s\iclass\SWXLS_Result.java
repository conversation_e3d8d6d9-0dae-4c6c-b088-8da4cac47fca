package business.wxls.c2s.iclass;	
	
import business.wxls.c2s.cclass.SWXLS_RankingResult;	
import jsproto.c2s.cclass.BaseSendMsg;	
	
/**	
 * 十三水，对比结算排名	
 * <AUTHOR>	
 *	
 */	
public class SWXLS_Result extends BaseSendMsg {	
		
    public long roomID;	
    public SWXLS_RankingResult sRankingResult = new SWXLS_RankingResult();	
    public static SWXLS_Result make(long roomID, SWXLS_RankingResult sRankingResult) {	
    	SWXLS_Result ret = new SWXLS_Result();	
        ret.roomID = roomID;	
        ret.sRankingResult = sRankingResult;	
        return ret;	
    }	
}	
