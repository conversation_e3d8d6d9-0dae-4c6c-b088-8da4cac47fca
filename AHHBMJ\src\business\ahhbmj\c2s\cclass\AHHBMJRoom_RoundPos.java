package business.ahhbmj.c2s.cclass;

import jsproto.c2s.cclass.mj.BaseMJRoom_RoundPos;

import java.util.ArrayList;
import java.util.List;

public class AHHBMJRoom_RoundPos extends BaseMJRoom_RoundPos {
    /**
     * 能杠的牌
     */
    private List<Integer> gangList = new ArrayList<>();
    public int xiaPao = 2;//1：显示 1跑  2：显示 1跑 2跑

    public List<Integer> getGangList() {
        return gangList;
    }

    public void setGangList(List<Integer> gangList) {
        this.gangList = gangList;
    }

    public int getXiaPao() {
        return xiaPao;
    }

    public void setXiaPao(int xiaPao) {
        this.xiaPao = xiaPao;
    }
}
