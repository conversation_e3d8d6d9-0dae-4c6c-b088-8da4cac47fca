package business.global.pk.wxls.newwxls.comparing;	
	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.HashMap;	
import java.util.Map;	
	
public class WXLSComparingFacade {	
	
    private final static Map<WXLSRankingEnum, WXLSIComparing> maps = new HashMap<WXLSRankingEnum, WXLSIComparing>();	
    private final static WXLSIComparing defaultComparing = new WXLSDefaultComparingImpl();	
	
    static {	
        maps.put(WXLSRankingEnum.STRAIGHT_FLUSH, new WXLSStraightFlushComparingImpl());	
        maps.put(WXLSRankingEnum.FOUR_OF_THE_KIND, new WXLSFourOfTheKindComparingImpl());	
        maps.put(WXLSRankingEnum.FULL_HOUSE, new WXLSFullHouseComparingImpl());	
//        maps.put(WXLSRankingEnum.FLUSH_TWO_PAIR, new WXLSTwoPairFlushComparingImpl());	
//        maps.put(WXLSRankingEnum.FLUSH_ONE_PAIR, new WXLSOnePairFlushComparingImpl());	
        maps.put(WXLSRankingEnum.FLUSH, new WXLSFlushComparingImpl());	
        maps.put(WXLSRankingEnum.STRAIGHT, new WXLSStraightComparingImpl());	
        maps.put(WXLSRankingEnum.THREE_OF_THE_KIND, new WXLSThreeOfTheKindComparingImpl());	
        maps.put(WXLSRankingEnum.TWO_PAIR, new WXLSTwoPairsComparingImpl());	
        maps.put(WXLSRankingEnum.ONE_PAIR, new WXLSOnePairComparingImpl());	
        maps.put(WXLSRankingEnum.HIGH_CARD, new WXLSHighCardComparingImpl());	
    }	
	
    public static WXLSIComparing getComparing(WXLSRankingEnum WXLSRankingEnum) {	
        WXLSIComparing cmp = maps.get(WXLSRankingEnum);	
        if (cmp == null) {	
            return defaultComparing;	
        } else {	
            return cmp;	
        }	
    }	
	
}	
