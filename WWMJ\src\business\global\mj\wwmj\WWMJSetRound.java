package business.global.mj.wwmj;


import business.global.mj.AbsMJRoundPos;
import business.global.mj.AbsMJSetRoom;
import business.global.mj.template.MJTemplateSetRound;
import cenum.mj.OpType;


/**
 * 基础模板 回合逻辑 每一次等待操作，都是一个round
 *
 * <AUTHOR>
 */

public class WWMJSetRound extends MJTemplateSetRound {

    public WWMJSetRound(AbsMJSetRoom set, int roundID) {
        super(set, roundID);
    }


    @Override
    protected boolean autoOutCard(int sec) {
        return false;
    }

    @Override
    public AbsMJRoundPos nextPosOpType(AbsMJRoundPos nextPos) {
        if (nextPos.getPos().checkOpType(0, OpType.Ting)) {
            nextPos.addOpType(OpType.Ting);
        }
        nextPos.addOpType(OpType.Out);
        return nextPos;
    }
}
