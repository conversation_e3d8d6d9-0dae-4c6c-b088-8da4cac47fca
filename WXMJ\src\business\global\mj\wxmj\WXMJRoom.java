package business.global.mj.wxmj;

import business.global.mj.AbsMJSetRoom;
import business.global.mj.wxmj.WXMJRoomEnum.WXMJCfg;
import business.global.room.base.AbsRoomPosMgr;
import business.global.room.mj.MahjongRoom;
import business.wxmj.c2s.cclass.WXMJRoomSetInfo;
import business.wxmj.c2s.iclass.*;
import cenum.ChatType;
import cenum.ClassType;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;
import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.RoomEndResult;
import jsproto.c2s.cclass.room.BaseRoomConfigure;
import jsproto.c2s.cclass.room.GetRoomInfo;
import jsproto.c2s.cclass.room.RoomPosInfo;
import jsproto.c2s.iclass.S_GetRoomInfo;
import jsproto.c2s.iclass.room.SBase_Dissolve;
import jsproto.c2s.iclass.room.SBase_PosLeave;

import java.util.*;

/**
 * 游戏房间
 *
 * <AUTHOR>
 */
public class WXMJRoom extends MahjongRoom {
    // 房间配置		
    private CWXMJ_CreateRoom roomCfg = null;
    public boolean isEnd = false;
    Map<Integer, Integer> taiMap = new HashMap<Integer, Integer>();
    private boolean realFirst = true;

    public boolean isRealFirst() {
        return realFirst;
    }

    public void setRealFirst(boolean realFirst) {
        this.realFirst = realFirst;
    }

    public Map<Integer, Integer> getTaiMap() {
        return taiMap;
    }


    protected WXMJRoom(BaseRoomConfigure<CWXMJ_CreateRoom> baseRoomConfigure, String roomKey, long ownerID) {
        super(baseRoomConfigure, roomKey, ownerID);
        initShareBaseCreateRoom(CWXMJ_CreateRoom.class, baseRoomConfigure);
        this.roomCfg = (CWXMJ_CreateRoom) baseRoomConfigure.getBaseCreateRoom();
        this.evenDpos = 0;
    }


    /**
     * 房主是否需要准备
     *
     * @return
     */
    @Override
    public boolean ownerNeedReady() {
        return true;
    }

    /**
     * 30秒未准备自动退出
     *
     * @return
     */
    @Override
    public boolean is30SencondTimeOut() {
        return getRoomCfg().getGaoji().contains(4);
    }

    /**
     * 需要立马打牌
     *
     * @return
     */
    public boolean needAtOnceOpCard() {
        return true;
    }

    /**
     * 清除记录。
     */
    @Override
    public void clearEndRoom() {
        super.clear();
        this.roomCfg = null;
    }

    @Override
    public boolean autoStartGame() {
        return true;
    }

    /**
     * 存在有玩家离开、踢出清空所有玩家准备状态
     *
     * @return T: 清空,F:不清空
     */
    @Override
    public boolean existLeaveClearAllPosReady() {
        return true;
    }

    /**
     * 自动准备游戏 玩家加入房间时，自动进行准备。
     */
    @Override
    public boolean autoReadyGame() {
        return this.getRoomCfg().getFangjian().contains(1);
    }

    /**
     * 飘分
     */
    public void opPiaoFen(WebSocketRequest request, long pid, CWXMJ_PiaoFen data) {
        lock();
        do {
            if (null == this.getCurSet()) {
                request.error(ErrorCode.NotAllow, "");
                break;
            }
            WXMJRoomSet roomSet = (WXMJRoomSet) this.getCurSet();
            roomSet.opPiaoFen(request, pid, data);
        } while (false);
        unlock();
    }

    @Override
    public int getWanfa() {
        return this.getRoomCfg().getWanfa();
    }

    public boolean isPiao() {
        return this.getRoomCfg().getPiaoHua() == WXMJRoomEnum.WXMJPiaoFenGuiZe.ftf.ordinal() || this.getRoomCfg().getPiaoHua() == WXMJRoomEnum.WXMJPiaoFenGuiZe.otf.ordinal();
    }

    /**
     * 获取房间配置
     *
     * @return
     */
    public CWXMJ_CreateRoom getRoomCfg() {
        if (Objects.isNull(this.roomCfg)) {
            initShareBaseCreateRoom(CWXMJ_CreateRoom.class, getBaseRoomConfigure());
            return (CWXMJ_CreateRoom) getBaseRoomConfigure().getBaseCreateRoom();
        }
        return this.roomCfg;
    }


    @SuppressWarnings("unchecked")
    @Override
    public <T> T getCfg() {
        return (T) getRoomCfg();
    }

    @Override
    public String dataJsonCfg() {
        // 获取房间配置		
        return new Gson().toJson(this.getRoomCfg());
    }

    @Override
    public <E> boolean RoomCfg(E m) {
        WXMJCfg cfgEnum = (WXMJCfg) m;
        int cfgInt = cfgEnum.ordinal();
        if (this.getRoomCfg().getKexuanwanfa().contains(cfgInt)) {
            return true;
        }
        return false;
    }

    @Override
    protected AbsMJSetRoom newMJRoomSet(int curSetID, MahjongRoom room, int dPos) {
        return new WXMJRoomSet(curSetID, (WXMJRoom) room, dPos);
    }

    @Override
    public void startNewSet() {
        if (this.isRealFirst()) {
            this.setCurSetID(this.getCurSetID() + 1);
        } else if (((WXMJRoomSet) this.getCurSet()) != null && !this.isRealFirst() && ((AbsMJSetRoom) this.getCurSet()).getMHuInfo().isHuEmpty() && this.getRoomCfg().getKexuanwanfa().contains(WXMJCfg.lianzhuangbusuanbashu.ordinal())) {
            this.setCurSetID(this.getCurSetID());
        } else if (this.getCurSetID() != 0 && (((AbsMJSetRoom) this.getCurSet()).getMHuInfo().getHuPos() == ((AbsMJSetRoom) this.getCurSet()).getDPos()) && this.getRoomCfg().getKexuanwanfa().contains(WXMJCfg.lianzhuangbusuanbashu.ordinal())) {
            this.setCurSetID(this.getCurSetID());
        } else {
            this.setCurSetID(this.getCurSetID() + 1);
        }
        // / 计算庄位
        if (this.getCurSetID() == 1) {
            setDPos(0);
        } else if (this.getCurSet() != null) {
            AbsMJSetRoom mRoomSet = (AbsMJSetRoom) this.getCurSet();
            // 根据上一局计算下一局庄家		
            setDPos(mRoomSet.calcNextDPos());
            mRoomSet.clear();
        }

        // 每个位置，清空准备状态		
        this.getRoomPosMgr().clearGameReady();
        // 通知局数变化		
        this.getRoomTyepImpl().roomSetIDChange();
        this.setCurSet(this.newMJRoomSet(this.getCurSetID(), this, this.getDPos()));
    }

    @SuppressWarnings("rawtypes")
    @Override
    public GetRoomInfo getRoomInfo(long pid) {
        S_GetRoomInfo ret = new S_GetRoomInfo();
        // 设置房间公共信息		
        this.getBaseRoomInfo(ret);
        if (null != this.getCurSet()) {
            ret.setSet(this.getCurSet().getNotify_set(pid));
        } else {
            ret.setSet(new WXMJRoomSetInfo());
        }
        return ret;
    }

    @Override
    public BaseSendMsg Trusteeship(long roomID, long pid, int pos, boolean trusteeship) {
        return SWXMJ_Trusteeship.make(roomID, pid, pos, trusteeship);
    }

    @Override
    public BaseSendMsg PosLeave(SBase_PosLeave posLeave) {
        return SWXMJ_PosLeave.make(posLeave);
    }

    @Override
    public BaseSendMsg LostConnect(long roomID, long pid, boolean isLostConnect, boolean isShowLeave) {
        return SWXMJ_LostConnect.make(roomID, pid, isLostConnect, isShowLeave);
    }

    @Override
    public BaseSendMsg PosContinueGame(long roomID, int pos) {
        return SWXMJ_PosContinueGame.make(roomID, pos);
    }

    @Override
    public BaseSendMsg PosUpdate(long roomID, int pos, RoomPosInfo posInfo, int custom) {
        return SWXMJ_PosUpdate.make(roomID, pos, posInfo, custom);
    }

    @Override
    public BaseSendMsg PosReadyChg(long roomID, int pos, boolean isReady) {
        return SWXMJ_PosReadyChg.make(roomID, pos, isReady);
    }

    @Override
    public BaseSendMsg Dissolve(SBase_Dissolve dissolve) {
        return SWXMJ_Dissolve.make(dissolve);
    }

    @Override
    public BaseSendMsg StartVoteDissolve(long roomID, int createPos, int endSec) {
        return SWXMJ_StartVoteDissolve.make(roomID, createPos, endSec);
    }

    @Override
    public BaseSendMsg PosDealVote(long roomID, int pos, boolean agreeDissolve, int endSec) {
        return SWXMJ_PosDealVote.make(roomID, pos, agreeDissolve);
    }

    @Override
    public BaseSendMsg Voice(long roomID, int pos, String url) {
        return SWXMJ_Voice.make(roomID, pos, url);
    }

    @Override
    public <T> BaseSendMsg RoomRecord(List<T> records) {
        return SWXMJ_RoomRecord.make(records);
    }

    @Override
    public <T> BaseSendMsg RoomEnd(T record, RoomEndResult<?> sRoomEndResult) {
        return SWXMJ_RoomEnd.make(this.getMJRoomRecordInfo(), this.getRoomEndResult());
    }

    @Override
    public BaseSendMsg XiPai(long roomID, long pid, ClassType cType) {
        return SWXMJ_XiPai.make(roomID, pid, cType);
    }

    @Override
    public BaseSendMsg ChatMessage(long pid, String name, String content, ChatType type, long toCId, int quickID) {
        return SWXMJ_ChatMessage.make(pid, name, content, type, toCId, quickID);
    }

    @Override
    public BaseSendMsg ChangePlayerNum(long roomID, int createPos, int endSec, int playerNum) {
        return SWXMJ_ChangePlayerNum.make(roomID, createPos, endSec, playerNum);
    }

    @Override
    public BaseSendMsg ChangePlayerNumAgree(long roomID, int pos, boolean agreeChange) {
        return SWXMJ_ChangePlayerNumAgree.make(roomID, pos, agreeChange);
    }

    @Override
    public BaseSendMsg ChangeRoomNum(long roomID, String roomKey, int createType) {
        return SWXMJ_ChangeRoomNum.make(roomID, roomKey, createType);
    }

    /**
     * 房间内每个位置信息 管理器
     */
    @Override
    public AbsRoomPosMgr initRoomPosMgr() {
        return new WXMJRoomPosMgr(this);
    }

    @Override
    public boolean isEnd() {
        return isEnd;
    }

}			
