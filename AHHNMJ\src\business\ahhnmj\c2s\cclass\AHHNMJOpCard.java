package business.ahhnmj.c2s.cclass;

import business.global.mj.set.MJOpCard;

import java.util.List;

/**
 * 麻将打牌操作
 */
public class AHHNMJOpCard extends MJOpCard {

    public AHHNMJOpCard(int opCard, List<Integer> chiList) {
        this.setOpCard(opCard);
    }

    public AHHNMJOpCard(int opCard) {
        super(opCard);
    }


    public final static AHHNMJOpCard OpCard(int opCard, List<Integer> chiList) {
        return new AHHNMJOpCard(opCard, chiList);
    }


    public static AHHNMJOpCard OpCard(int opCard) {
        return new AHHNMJOpCard(opCard);
    }
}			
