package business.wxls.c2s.cclass.newwxls;	
	
/**	
 * Enum {@code WXLSRankingEnum} 牌型的规则排列大小类型  ,牌型改动，需要通知客户端修改	
 */	
public enum WXLSRankingEnum {	
	HIGH_CARD(0,"乌龙",1 ),	
	ONE_PAIR(1, "一对",1),	
	TWO_PAIR(2, "两对",1),	
	THREE_OF_THE_KIND(3, "三条",1),	
	STRAIGHT(4, "顺子",1),	
	FLUSH(5, "同花",1),	
	FULL_HOUSE(6, "葫芦",1),	
	FOUR_OF_THE_KIND(7, "铁支",4),	
	STRAIGHT_FLUSH(8, "同花顺",5),

	<PERSON><PERSON><PERSON>(56, "独炸",4),
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(57, "十二皇族",12),
	<PERSON><PERSON><PERSON>(58,"独三",3),
	<PERSON><PERSON><PERSON>(59,"独对",3),
	QUANH<PERSON>(60, "全黑",26),
	QU<PERSON><PERSON><PERSON><PERSON>(61, "全红",26),
	QUANHONGYIDIANHEI(62, "全红一点黑",13),
	QUANHEIYIDIANHONG(63, "全黑一点红",13),
	SHIQI(64, "10起",10),
	JIUQI(65, "9起",9),
	BAQI(66, "8起",8),
	QIQI(67, "7起",7),
	LIUQI(68, "6起",6),
	BANDA(69, "半大",3),
	BANXIAO(70, "半小",3),
	<PERSON>hunZi(71, "三顺子",3),	
	STongHua(72, "三同花",3),	
	LDuiBan(73, "六对半",6),	
	WDuiYiKe(74, "五对一刻",9),	
	STaoSanTiao(75, "四套三条",13),	
	QXiao(76, "全小",6),	
	QDa(77, "全大",6),
	STongHuaShun(78, "三同花顺",12),	
	YTiaoLong(79, "一条龙",13),	
	YIBAILINGBALUOHAN(80,"一百零八罗汉",108);	
	
		
	private String type;	
	private int priority;	
	private int value;	
	
	WXLSRankingEnum(int priority, String type, int value) {	
		this.type = type;	
		this.priority = priority;	
		this.value = value ;	
	}	
		
	 public static WXLSRankingEnum valueOf(int value) {	
		 for (WXLSRankingEnum flow : WXLSRankingEnum.values()) {	
				if (flow.priority == value) {	
					return flow;	
				}	
			}	
		 return null;	
	 }  	
	
	public String getType() {	
		return type;	
	}	
	
	public void setType(String type) {	
		this.type = type;	
	}	
	
	public int getPriority() {	
		return priority;	
	}	
	
	public void setPriority(int priority) {	
		this.priority = priority;	
	}	
		
	public int value() {	
		return value;	
	}	
}	
