package business.afmj.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;

@SuppressWarnings("serial")
public class SAFMJ_Jin extends BaseSendMsg {
	public long roomID;
	public int jin; // 正精
	public int jin2; // 副精
	public int jinJin;
	public int normalMoCnt = 0; // 普通摸牌数量
	public int gangMoCnt = 0; // 杠后摸牌数量
	public boolean isPlayJingAnimation = false; // 是否播放翻金动画
	public static SAFMJ_Jin make(long roomID, int jin, int jin2, int jinJin, int normalMoCnt, int gangMoCnt , boolean isPlayJingAnimation) {
		SAFMJ_Jin ret = new SAFMJ_Jin();
    	ret.roomID = roomID;
    	ret.jin = jin;
    	ret.jin2 = jin2;
		ret.jinJin = jinJin;
    	ret.normalMoCnt = normalMoCnt;
    	ret.gangMoCnt = gangMoCnt;
    	ret.isPlayJingAnimation = isPlayJingAnimation;

        return ret;
	    
	}
}
