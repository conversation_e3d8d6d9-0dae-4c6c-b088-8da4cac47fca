package business.global.pk.a3pk;	
	
import business.a3pk.c2s.iclass.SA3PK_PosOpCard;	
import business.a3pk.c2s.iclass.SA3PK_StartRound;	
import business.global.pk.*;	
import cenum.PKOpType;	
import cenum.mj.MJOpCardError;	
import cenum.pk.PKOpCardError;	
import com.ddm.server.common.CommLogD;	
import com.ddm.server.common.utils.CommTime;	
import com.ddm.server.common.utils.Lists;	
import com.ddm.server.websocket.def.ErrorCode;	
import com.ddm.server.websocket.handler.requset.WebSocketRequest;	
import jsproto.c2s.cclass.BaseSendMsg;	
import jsproto.c2s.cclass.pk.base.BasePKRoom_RoundPos;	
import jsproto.c2s.cclass.pk.base.BasePKRoom_SetRound;	
import jsproto.c2s.cclass.pk.base.BasePKSet_Pos;	
import lombok.Data;	
import java.util.Collections;	
import java.util.List;	
import java.util.Map;	
import java.util.Objects;	
import java.util.stream.Collectors;	
	
/**	
 * 余干510K 回合逻辑 每一次等待操作，都是一个round	
 *	
 * <AUTHOR>	
 */	
@Data	
public class A3PKSetRound extends AbsPKSetRound {	
    /**	
     * 当前局	
     */	
    private A3PKRoomSet roomSet;	
    /**	
     * 自动打牌秒数	
     */	
    private int autoOutCardSec = 0;	
	
    public A3PKSetRound(AbsPKSetRoom set, int roundID) {	
        super(set, roundID);	
        this.roomSet = (A3PKRoomSet) set;	
    }	
	
	
    /**	
     * 更新当前回合操作。	
     *	
     * @param sec	
     * @return	
     */	
    @Override	
    public boolean update(int sec) {	
        // 记录更新时间	
        this.setUpdateTime(sec);	
        // 已经结束	
        if (this.endTime != 0) {	
            if (this.endTime >= this.startTime) {	
                return true;	
            }	
            return false;	
        }	
        // 自动打牌	
        return this.autoOutCard(sec);	
    }	
	
    @Override	
    protected boolean autoOutCard(int sec) {	
        if (this.getEndTime() > 0) {	
            return false;	
        }	
        return false;	
    }	
	
	
    @Override	
    protected <T> BaseSendMsg startRound(long roomID, T room_SetWait) {	
        return SA3PK_StartRound.make(roomID, room_SetWait);	
    }	
	
    @Override	
    protected AbsPKRoundPos nextRoundPos(int pos) {	
        return new A3PKRoundPos(this, pos);	
    }	
	
    @Override	
    public synchronized int opCard(WebSocketRequest request, int opPos, PKOpType opType, PKOpCard mOpCard) {	
        if (this.getEndTime() > 0) {	
            request.error(ErrorCode.NotAllow, "end Time opPos has no round power");	
            return MJOpCardError.ERROR_OP_TYPE.value();	
        }	
        A3PKRoundPos pos = (A3PKRoundPos) this.getRoundPosDict().get(opPos);	
        if (Objects.nonNull(pos)) {	
            int opCardRet = pos.op(request, opType, mOpCard);	
            if (opCardRet >= 0) {	
                this.posOpCardRet(opCardRet, opType, mOpCard, false);	
            }	
            if(opCardRet == PKOpCardError.LINE_UP.value()) {	
                this.getNotifyRoundInfo(opPos);	
            }	
            return opCardRet;	
        }	
        request.error(ErrorCode.NotAllow, "opPos has no round power");	
        return MJOpCardError.ROUND_POS_ERROR.value();	
    }	
	
    /**	
     * 位置操作牌	
     *	
     * @param opPosRet 操作位置	
     * @param isFlash  是否动画	
     */	
    protected void posOpCardRet(int opPosRet, PKOpType opType, PKOpCard mOpCard, boolean isFlash) {	
        A3PKSetPos sPos = (A3PKSetPos) this.set.getPKSetPos(opPosRet);	
        this.setExeOpPos(opPosRet);	
        BasePKSet_Pos posInfoOther = sPos.getNotify(false);	
        BasePKSet_Pos posInfoSelf = sPos.getNotify(true);	
        this.set.getRoomPlayBack().playBack2Pos(opPosRet, this.posOpCard(this.room.getRoomID(), opPosRet, posInfoSelf, opType, mOpCard, isFlash), set.getSetPosMgr().getPKAllPlayBackNotify());	
        for (Map.Entry<Integer, AbsPKSetPos> entrySet : this.roomSet.getPosDict().entrySet()) {	
            if (opPosRet == entrySet.getKey()) {	
                continue;	
            }	
            if(sPos.getWhoIsSeeMePid() == entrySet.getValue().getPid()) {	
                this.getRoom().getRoomPosMgr().notify2Pos(entrySet.getKey(),this.posOpCard(this.room.getRoomID(), opPosRet, posInfoSelf, opType, mOpCard, isFlash) );	
            } else {	
                this.getRoom().getRoomPosMgr().notify2Pos(entrySet.getKey(),this.posOpCard(this.room.getRoomID(), opPosRet, posInfoOther, opType, mOpCard, isFlash) );	
            }	
        }	
        if (!A3PKRoomEnum.A3PK_SET_POS_END_TYPE.NOT.equals(sPos.getEndType())) {	
            // 看下家对应的牌	
            this.roomSet.seeNextRanksCard(sPos.getRanksType(), sPos);	
        }	
    }	
	
	
	
    protected <T> BaseSendMsg posOpCard(long roomID, int pos, T set_Pos, PKOpType opType, PKOpCard mOpCard, boolean isFlash) {	
        return SA3PK_PosOpCard.make(roomID, pos, set_Pos, opType, mOpCard.getOpValue(), mOpCard.getCardList(), isFlash);	
    }	
	
    @Deprecated	
    @Override	
    protected <T> BaseSendMsg posOpCard(long roomID, int pos, T set_Pos, PKOpType opType, int opCard, boolean isFlash) {	
        return null;	
    }	
	
	
    /**	
     * 机器人操作	
     *	
     * @param posID	
     */	
    @Override	
    public void RobothandCrad(int posID) {	
        if (this.getEndTime() > 0) {	
            return;	
        }	
        if (this.getRoundPosDict().containsKey(posID)) {	
            new A3PKRobotOpCard(this).RobothandCrad(posID);	
        }	
    }	
	
	
    /**	
     * 尝试开始回合, 如果失败，则set结束	
     *	
     * @return	
     */	
    @Override	
    public boolean tryStartRound() {	
        // 前一个可参考的操作round	
        AbsPKSetRound preRound = getPreRound();	
        // 第一次，庄家作为操作者，抓牌，等待出牌	
        if (Objects.isNull(preRound)) {	
            if (!startWithGetCard(this.getRoomSet().getFirstOutPos())) {	
                return false;	
            }	
            this.notifyStart();	
            return true;	
        }	
        // 打牌操作	
        if (PKOpType.Out.equals(preRound.getOpType())) {	
            return tryStartRoundOut(preRound);	
        }	
        // 不出	
        if (PKOpType.Pass.equals(preRound.getOpType())) {	
            return tryStartRoundOut(preRound);	
        }	
        return true;	
    }	
	
    /**	
     * 当局第一回合	
     *	
     * @param pos 回合位置	
     * @return	
     */	
    public boolean startWithGetCard(int pos) {	
        A3PKSetPos setPos = (A3PKSetPos) this.getSet().getPosDict().get(pos);	
        if (Objects.isNull(setPos)) {	
            CommLogD.error("A3PKSetRound startWithGetCard Pos:{}", pos);	
            return false;	
        }	
        return getOpTypeList(pos,Collections.emptyList());	
    }	
	
	
	
    /**	
     * 获取可操作的类型	
     *	
     * @param opPos	
     * @return	
     */	
	
    public boolean getOpTypeList(int opPos,List<PKOpType> opTypeList) {	
        AbsPKRoundPos tmPos = this.nextRoundPos(opPos);	
        tmPos.addOpType(tmPos.getPos().receiveOpTypes());	
        tmPos.addOpType(opTypeList);	
        this.roundPosDict.put(tmPos.getOpPos(), tmPos);	
        if(tmPos.getReceiveOpTypes().size()<=0){	
            return false;	
        }	
        return true;	
    }	
	
	
    /**	
     * 尝试开始回合-上回合打牌	
     *	
     * @param preRound 上回合操作	
     * @return	
     */	
    public boolean tryStartRoundOut(AbsPKSetRound preRound) {	
        int opPos = preRound.getExeOpPos();	
        // 只能顺序的抓牌，打牌	
        opPos = this.roomSet.nextOpPos(opPos);	
        if (!getOpTypeList(opPos)) {	
            return false;	
        }	
        notifyStart();	
        return true;	
    }	
	
	
    public void getNotifyRoundInfo (int posID) {	
        this.set.getRoomPlayBack().playBack2Pos(posID, this.startRound(this.set.getRoom().getRoomID(), this.getNotify_RoundInfo(posID)), set.getSetPosMgr().getAllPlayBackNotify());	
    }	
	
    /**	
     * 获取本轮信息	
     *	
     * @param pos 位置	
     * @return	
     */	
    @Override	
    public BasePKRoom_SetRound getNotify_RoundInfo(int pos) {	
        ret = new BasePKRoom_SetRound();	
        ret.setWaitID(this.roundID);	
        ret.setStartWaitSec(this.startTime);	
        ret.setRunWaitSec(CommTime.nowSecond() - this.startTime);	
        for (AbsPKRoundPos roundPos : this.roundPosDict.values()) {	
            if (roundPos.getOpType() != null) {	
                continue;	
            }	
            // 自己 或 公开	
            if (pos == roundPos.getOpPos() || roundPos.isPublicWait()) {	
                BasePKRoom_RoundPos data = new BasePKRoom_RoundPos();	
                if(pos != roundPos.getOpPos()) {	
                    data.setOpList(roundPos.getReceiveOpTypes().stream().filter(k->!A3PKRoomEnum.ChallengeList.contains(k)).collect(Collectors.toList()));	
                } else {	
                    data.setOpList(roundPos.getReceiveOpTypes());	
                }	
                data.setWaitOpPos(roundPos.getOpPos());	
                ret.addOpPosList(data);

                if (room.isConnectClearTrusteeship()) {
                    // 重新记录打牌时间
                    roundPos.getPos().getRoomPos().setLatelyOutCardTime(CommTime.nowMS());
                }
                // 设置最后操作时间
                this.set.setLastShotTime(CommTime.nowSecond());

            }
        }
        return ret;	
    }	
	
	
	
}	
