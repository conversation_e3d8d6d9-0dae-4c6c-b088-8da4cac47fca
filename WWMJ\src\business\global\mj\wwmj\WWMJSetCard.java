package business.global.mj.wwmj;

import business.global.mj.RandomCard;
import business.global.mj.template.MJTemplateRoomEnum;
import business.global.mj.template.MJTemplateSetCard;
import cenum.mj.MJCardCfg;

import java.util.ArrayList;
import java.util.List;

/**
 * 保定易县麻将
 * 每一局麻将底牌信息
 * 抓牌人是逆时针出手
 * 牌是顺时针被抓
 *
 * <AUTHOR>
 */
public class WWMJSetCard extends MJTemplateSetCard {


    public WWMJSetCard(WWMJRoomSet set) {
        super(set);
    }

    /**
     * 洗牌
     */
    @Override
    public void randomCard() {
        List<MJCardCfg> mCfgs = new ArrayList<MJCardCfg>();
        mCfgs.add(MJCardCfg.WANG);
        mCfgs.add(MJCardCfg.TIAO);
        mCfgs.add(MJCardCfg.TONG);
        mCfgs.add(MJCardCfg.FA);
        this.setRandomCard(new RandomCard(mCfgs, this.room.getPlayerNum(), this.room.getXiPaiList().size()));
        this.initDPos(this.set);
    }

    /**
     * 初始化留牌
     */
    @Override
    protected void initLiuPai() {
        this.liuPai = getRoom().wanFa_LiuPaiNum();
        if (getRoom().wanFa_LiuPaiExtrasOptions().equals(MJTemplateRoomEnum.LiuPaiExtrasOptions.MAI_MA_LIU_PAI)) {
            liuPai += getRoom().wanFa_MaiMa().value;
        }
    }

    /**
     * T:首次随机庄，F:房主庄
     */
    @Override
    protected boolean firstRandomDPos() {
        return true;
    }

    @Override
    public boolean isPopCardNull() {
        return liuPai >= getRandomCard().getSize();
    }

    public WWMJRoom getRoom() {
        return (WWMJRoom) super.room;
    }

    /**
     * 是否可以海底捞月
     *
     * @param needCount 需要的张数
     * @return
     */
    @Override
    public boolean isStartHaiDiLaoYue(int needCount) {
        if (needCount <= 0) {
            return false;
        }
        return liuPai + needCount >= getRandomCard().getSize();
    }
}					
											
