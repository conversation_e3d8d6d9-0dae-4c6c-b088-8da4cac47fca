package business.a3pk.c2s.cclass;	
	
import business.global.pk.PKCurOutCardInfo;	
import com.ddm.server.common.utils.Lists;	
import jsproto.c2s.cclass.pk.base.BasePKRoom_RoundPos;	
import org.apache.commons.collections.CollectionUtils;	
	
import java.util.List;	
	
/**	
 * 具体回合操作位置	
 */	
public class A3PK_RoundPos extends BasePKRoom_RoundPos {	
    private PKCurOutCardInfo curOutCardInfo;	
	
    public PKCurOutCardInfo getCurOutCardInfo() {	
        return curOutCardInfo;	
    }	
	
    public void setCurOutCardInfo(PKCurOutCardInfo curOutCardInfo) {	
        this.curOutCardInfo = curOutCardInfo;	
    }	
}	
