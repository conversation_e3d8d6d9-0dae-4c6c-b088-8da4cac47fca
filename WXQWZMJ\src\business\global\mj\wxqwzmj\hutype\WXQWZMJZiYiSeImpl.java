package business.global.mj.wxqwzmj.hutype;	
	
import business.global.mj.AbsMJSetPos;	
import business.global.mj.MJCardInit;	
import business.global.mj.hu.BaseHuCard;	
import business.global.mj.wxqwzmj.WXQWZMJRoomEnum.WXQWZMJOpPoint;	
import business.global.mj.manage.MJFactory;	
import business.wxqwzmj.c2s.cclass.WXQWZMJPointItem;	
import cenum.mj.MJCardCfg;	
	
import java.util.ArrayList;	
import java.util.List;	
import java.util.Map;	
import java.util.Objects;	
import java.util.stream.Collectors;	
	
/**	
 * 全字（字一色）：手上牌全部是“东南西北中发白”组成的，需要组成胡牌的牌型；	
 *	
 * <AUTHOR>	
 */	
public class WXQWZMJZiYiSeImpl extends BaseHuCard {	
	
    	
	
    @Override	
    public <T> Object checkHuCardReturn(AbsMJSetPos mSetPos, MJCardInit mCardInit) {	
	
//        //没有明摆不能胡	
//        if(!((WXQWZMJSetPos)mSetPos).isMingBai()&&!((WXQWZMJSetPos)mSetPos).isBaoDing()){	
//            return null;	
//        }	
        return this.checkHuCardReturn(mSetPos, mCardInit,0);	
    }	
    @Override	
    public <T> Object checkHuCardReturn(AbsMJSetPos mSetPos, MJCardInit mCardInit,int cardID) {	
        if (null == mCardInit) {	
            return null;	
        }	
	
        // 检查清一色	
        if (!checkZYS(mSetPos, mCardInit)) {	
            return null;	
        }	
        return this.checkHuType(mSetPos, mCardInit);	
    }	
	
	
    /**	
     * 检查胡牌类型	
     *	
     * @param mSetPos	
     * @param mCardInit	
     * @return	
     */	
    private WXQWZMJPointItem checkHuType(AbsMJSetPos mSetPos, MJCardInit mCardInit) {	
        WXQWZMJPointItem item;	
        item=(WXQWZMJPointItem)MJFactory.getHuCard(WXQWZMJQDCardImpl.class).checkHuCardReturn(mSetPos, mCardInit);	
        if (Objects.nonNull(item)) {	
            item.addWXQWZMJOpPoint(WXQWZMJOpPoint.QD,WXQWZMJOpPoint.QD.value());	
            item.addWXQWZMJOpPoint(WXQWZMJOpPoint.ZYS,WXQWZMJOpPoint.ZYS.value());	
            return item;	
        }	
        item=(WXQWZMJPointItem)MJFactory.getHuCard(WXQWZMJPPHuCardImpl.class).checkHuCardReturn(mSetPos, mCardInit);	
        if (Objects.nonNull(item)) {	
            item.addWXQWZMJOpPoint(WXQWZMJOpPoint.PPH,WXQWZMJOpPoint.PPH.value());	
            item.addWXQWZMJOpPoint(WXQWZMJOpPoint.ZYS,WXQWZMJOpPoint.ZYS.value());	
            return item;	
        }	
        item=new WXQWZMJPointItem();	
        item.addWXQWZMJOpPoint(WXQWZMJOpPoint.ZYS,WXQWZMJOpPoint.ZYS.value());	
        return item;	
    }	
	
    /**	
     * 检查字一色	
     *	
     * @param mSetPos   玩家位置信息	
     * @param mCardInit 玩家牌信息	
     * @return	
     */	
    protected boolean checkZYS(AbsMJSetPos mSetPos, MJCardInit mCardInit) {	
        List<Integer> allInt = new ArrayList<>();	
        // 获取牌列表	
        allInt.addAll(mCardInit.getAllCardInts());	
        // 获取顺子，刻子，杠组成的胡牌。	
        allInt.addAll(publicCardList(mSetPos));	
        // 分组列表	
        Map<Integer, Long> map = allInt.stream().collect(Collectors.groupingBy(p -> (p / 10), Collectors.counting()));	
        // 检查分组数据	
        if (null == map || map.size() <= 0) {	
            return false;	
        }	
        int size = map.size();	
	
        // 获取分数数	
        if (size == 1) {	
            Long count = map.get(MJCardCfg.FENG.value());	
            if (null != count && count > 0) {	
                return true;	
            }	
        }	
        return false;	
    }	
	
}	
