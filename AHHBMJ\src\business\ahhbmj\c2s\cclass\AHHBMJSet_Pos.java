package business.ahhbmj.c2s.cclass;					
					
import jsproto.c2s.cclass.mj.BaseMJSet_Pos;	
import lombok.Data;	
					
/**					
 * 红中麻将 配置					
 *					
 * <AUTHOR>					
 */					
// 一局中各位置的信息											
@Data					
public class AHHBMJSet_Pos extends BaseMJSet_Pos {					
	public boolean isTing;//报听
	private int zuoFen=-1; //-1:不显示 0：“不下坐”、1：“1坐”、2：“2坐
	private int laFen=-1;//-1:不显示 0：“不下拉”、1：“1拉”、2：“2拉
	private int paoFen=1;//-1:不显示 0：“不下跑”、1：“1跑”、2：“2跑
	private boolean isZuoLaPao;

}								
