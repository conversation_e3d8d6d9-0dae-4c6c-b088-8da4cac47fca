package business.ahmj.c2s.iclass;
	
import jsproto.c2s.cclass.BaseSendMsg;	
	
import java.util.HashMap;	
import java.util.Map;	
	
public class SLBZMJ_Piao<T> extends BaseSendMsg {	
	public long roomID;	
	public Map <Integer ,Integer> piaoPiont = new HashMap<>();	
 	public static <T> SLBZMJ_Piao make(long roomID, Map <Integer ,Integer> piaoPiont ) {	
		SLBZMJ_Piao ret = new SLBZMJ_Piao();	
		ret.roomID = roomID;	
		ret.piaoPiont = piaoPiont;	
		return ret;	
	}	
}	
