package business.a3pk.c2s.cclass;	
	
import com.ddm.server.common.utils.Lists;	
	
import java.util.List;	
import java.util.Objects;	
	
/**	
 * 当局回合操作内容	
 */	
public class A3PKRoom_SetRound {	
    /**	
     * 回合Id	
     */	
    private int waitID = 0;	
    /**	
     * 开始等待时间	
     */	
    private int startWaitSec = 0;	
    /**	
     * 操作位置列表	
     */	
    private final List<A3PK_RoundPos> opPosList = Lists.newArrayList();	
	
	
    public int getWaitID() {	
        return waitID;	
    }	
	
    public void setWaitID(int waitID) {	
        this.waitID = waitID;	
    }	
	
    public int getStartWaitSec() {	
        return startWaitSec;	
    }	
	
    public void setStartWaitSec(int startWaitSec) {	
        this.startWaitSec = startWaitSec;	
    }	
	
    public List<A3PK_RoundPos> getOpPosList() {	
        return opPosList;	
    }	
	
    public void addOpPosList(A3PK_RoundPos bRoundPos) {	
        if (Objects.nonNull(bRoundPos)) {	
            this.opPosList.add(bRoundPos);	
        }	
    }	
}	
