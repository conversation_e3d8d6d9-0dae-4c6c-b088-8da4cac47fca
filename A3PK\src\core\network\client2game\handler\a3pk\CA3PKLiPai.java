package core.network.client2game.handler.a3pk;	
	
import business.a3pk.c2s.iclass.CA3PK_LiPai;	
import business.global.pk.a3pk.A3PKRoom;	
import business.global.room.RoomMgr;	
import business.player.Player;	
import com.ddm.server.websocket.def.ErrorCode;	
import com.ddm.server.websocket.handler.requset.WebSocketRequest;	
import com.google.gson.Gson;	
import core.network.client2game.handler.PlayerHandler;	
import core.network.http.proto.SData_Result;	
	
import java.io.IOException;	
import java.util.Objects;	
	
public class CA3PKLiPai extends PlayerHandler {	
	
    @SuppressWarnings("rawtypes")	
    @Override	
    public void handle(Player player, WebSocketRequest request, String message) throws IOException {	
        final CA3PK_LiPai req = new Gson().fromJson(message, CA3PK_LiPai.class);	
        long roomID = req.roomID;	
        A3PKRoom room = (A3PKRoom) RoomMgr.getInstance().getRoom(roomID);	
        if (Objects.isNull(room)) {	
            request.error(ErrorCode.NotAllow, "CA3PKLiPai not find room:" + roomID);	
            return;	
        }	
        // 理牌操作	
        SData_Result result = room.opLiPai(player.getId(), req.liPaiList);	
        if (ErrorCode.Success.equals(result.getCode())) {	
            request.response();	
        } else {	
            request.error(result.getCode(), result.getMsg());	
        }	
    }	
}	
	
