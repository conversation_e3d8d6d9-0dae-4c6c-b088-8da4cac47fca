package business.a3pk.c2s.cclass;	
	
import business.global.pk.a3pk.A3PKRoomEnum;	
import com.ddm.server.common.utils.Lists;	
import jsproto.c2s.cclass.pk.base.BasePKSet_Pos;	
import org.apache.commons.collections.CollectionUtils;	
	
import java.io.Serializable;	
import java.util.ArrayList;	
import java.util.HashSet;	
import java.util.List;	
import java.util.Set;	
	
	
/**	
 *	
 */	
public class A3PKSet_Pos extends BasePKSet_Pos implements Cloneable, Serializable {	
    /**	
     * 打牌列表	
     */	
    private List<Integer> outCardList = Lists.newArrayList();	
    /**	
     * 剩余牌数	
     */	
    private int cardSize = -1;	
    /**	
     * 名次	
     */	
    private A3PKRoomEnum.A3PK_SET_POS_END_TYPE endType;	
    /**	
     * 显示牌列表	
     */	
    private Set<Integer> showCardList = new HashSet<>();	
    /**	
     * 出牌类型	
     */	
    private int outCardType = -1;	
    /**	
     * 比较值	
     */	
    private int compValue = 0;	
	
    /**	
     * 是否存在打独操作（需要判断是否存在）	
     */	
    private Boolean existChallenge;	
	
	
    public List<List<Integer>> liPais = Lists.newArrayList();	
	
	
    public void addOutCardList(List<Integer> outCardList) {	
        if (CollectionUtils.isNotEmpty(outCardList)) {	
            this.outCardList.addAll(outCardList);	
        }	
    }	
	
	
    public void setCardSize(int cardSize) {	
        this.cardSize = cardSize;	
    }	
	
	
	
	
    public A3PKRoomEnum.A3PK_SET_POS_END_TYPE getEndType() {	
        return endType;	
    }	
	
    public void setEndType(A3PKRoomEnum.A3PK_SET_POS_END_TYPE endType) {	
        this.endType = endType;	
    }	
	
	
    public void addShowCardList(Set<Integer> showCardList) {	
        if (CollectionUtils.isNotEmpty(showCardList)) {	
            this.showCardList.addAll(showCardList);	
        }	
    }	
	
    public void setOutCardType(int outCardType) {	
        this.outCardType = outCardType;	
    }	
	
    public void setCompValue(int compValue) {	
        this.compValue = compValue;	
    }	
	
    public List<List<Integer>> getLiPais() {	
        return liPais;	
    }	
	
    public void setLiPais(List<List<Integer>> liPais) {	
        this.liPais = liPais;	
    }	
	
    public Boolean getExistChallenge() {	
        return existChallenge;	
    }	
	
    public void setExistChallenge(Boolean existChallenge) {	
        this.existChallenge = existChallenge;	
    }	
}	
