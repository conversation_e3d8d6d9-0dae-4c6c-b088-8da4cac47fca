package business.global.mj.afmj.hutype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCardInit;
import business.global.mj.hu.BaseHuCard;
import business.global.mj.afmj.AFMJRoomEnum.*;
import business.global.mj.util.HuDuiUtil;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * 对对胡
 */
public class AFMJDDHuImpl extends BaseHuCard {

    @Override
    public boolean checkHuCard(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
        //检查是否有碰杠吃
        if (mSetPos.sizePublicCardList() > 0)
            return false;
        if (null == mCardInit)
            return false;
        return HuDuiUtil.getInstance().checkDuiHu(mCardInit.getAllCardInts(), mCardInit.sizeJin());

    }

    @Override
    public <T> Object checkHuCardReturn(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
        //检查是否有碰杠吃
        if (mSetPos.sizePublicCardList() > 0) {
            return AFMJOpPoint.Not;
        }
        // 获取牌值
        if (null == mCardInit) {
            return AFMJOpPoint.Not;
        }
        // 检查是否能胡
        if (!HuDuiUtil.getInstance().checkDuiHu(mCardInit.getAllCardInts(), mCardInit.sizeJin())) {
            return AFMJOpPoint.Not;
        }
        // 分组统计手上的相同类型的牌
        Map<Integer, Long> groupingByMap = mCardInit.getAllCardInts()
                .stream()
                .collect(Collectors.groupingBy(p -> p, Collectors.counting()));
        if (null == groupingByMap || groupingByMap.size() <= 0) {
            return AFMJOpPoint.QiDui;
        }

        return AFMJOpPoint.QiDui;
    }
}
