package business.ahhbmj.c2s.iclass;	
	
import cenum.mj.OpType;	
import jsproto.c2s.cclass.BaseSendMsg;	
	
	
public class SAHHBMJ_PosOpCard<T> extends BaseSendMsg {	
	
    public long roomID;	
    public int pos;	
    public T set_Pos;	
    public OpType opType;	
    public int opCard;	
    public boolean isFlash;	
    public boolean caiPiao;	
	
	
    public static <T> SAHHBMJ_PosOpCard make(long roomID, int pos, T set_Pos, OpType opType, int opCard, boolean isFlash) {	
        SAHHBMJ_PosOpCard ret = new SAHHBMJ_PosOpCard();	
        ret.roomID = roomID;	
        ret.pos = pos;	
        ret.set_Pos = set_Pos;	
        ret.opType = opType;	
        ret.opCard = opCard;	
        ret.isFlash = isFlash;	
        return ret;	
    }	
	
}												
