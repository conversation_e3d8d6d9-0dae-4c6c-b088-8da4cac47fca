package business.global.mj.ahhbmj.hutype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCardInit;
import business.global.mj.hu.abs.AbsSSBK;
import com.ddm.server.common.utils.CommMath;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 十三不靠：由147、258、369三组花色不同的序数牌和东南西北中发白7张牌中任意不同的5张牌组成的14张的牌型；
 */
public class AHHBMJ_QXSSLImpl extends AHHBMJ_SSLImpl {
    /**
     * @param cardList
     * @param totalJin,
     * @return
     */
    @Override
    public boolean checkFeng(List<Integer> cardList, int totalJin) {

        // 分组，计数	
        Map<Integer, Long> result = cardList.stream()
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        long max = result.values().stream().mapToLong((x) -> x).summaryStatistics().getMax();
        return max == 1 && cardList.size() == 7;
    }
}	
