package business.ahmj.c2s.iclass;						
						
import jsproto.c2s.cclass.BaseSendMsg;					
					
import java.util.ArrayList;					
import java.util.List;					
						
/**						
 * 重新设置玩家手牌						
 * <AUTHOR>						
 * @param <T>						
 *						
 */						
public class SAHMJ_SetPosCard<T> extends BaseSendMsg  {						
    public long roomID;						
	// 每个玩家的牌面						
	public List<T> setPosList = new ArrayList<>();						
						
						
    public static <T> SAHMJ_SetPosCard make(long roomID, List<T> setPosList) {						
    	SAHMJ_SetPosCard ret = new SAHMJ_SetPosCard();						
        ret.roomID = roomID;						
        ret.setPosList = setPosList;						
						
        return ret;						
    						
						
    }						
}						
