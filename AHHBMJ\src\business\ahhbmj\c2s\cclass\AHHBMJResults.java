package business.ahhbmj.c2s.cclass;						
						
						
import jsproto.c2s.cclass.room.AbsBaseResults;						
						
						
/**						
 * 总结算						
 */						
						
public class AHHBMJResults extends AbsBaseResults {		
    private int zhuangPoint;//坐庄次数		
    private boolean DianPaoWang = false;//点炮王		
    private boolean winner = false;//大赢家		
    private int mingGangPoint = 0;  //直杠		
    private int anGangPoint = 0; //暗杠		
    private int buGangPoint = 0;    // 碰杠		
		
    public void addMingGangPoint(int point) {		
        this.mingGangPoint += point;		
    }		
		
    public void addAnGangPoint(int point) {		
        this.anGangPoint += point;		
    }		
		
    public void addBuGangPoint(int point) {		
        this.buGangPoint += point;		
    }		
		
    public void setDianPaoWang(boolean dianPaoWang) {		
        DianPaoWang = dianPaoWang;		
    }		
		
    public void addZhuangPoint(int point) {		
        this.zhuangPoint += point;		
    }		
		
    public void setWinner(boolean winner) {		
        this.winner = winner;		
    }		
						
}											
