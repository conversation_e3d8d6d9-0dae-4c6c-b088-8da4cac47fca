package business.wxls.c2s.iclass;	
	
import jsproto.c2s.iclass.room.SBase_PosLeave;	
	
	
public class SWXLS_PosLeave extends SBase_PosLeave {	
	
    public static SWXLS_PosLeave make(SBase_PosLeave posLeave) {	
        SWXLS_PosLeave ret = new SWXLS_PosLeave();	
        ret.setRoomID(posLeave.getRoomID());	
        ret.setPos(posLeave.getPos());	
        ret.setBeKick(posLeave.isBeKick());	
        ret.setOwnerID(posLeave.getOwnerID());	
        ret.setKickOutTYpe(posLeave.getKickOutTYpe());	
        ret.setMsg(posLeave.getMsg());	
        return ret;	
    }	
}	
