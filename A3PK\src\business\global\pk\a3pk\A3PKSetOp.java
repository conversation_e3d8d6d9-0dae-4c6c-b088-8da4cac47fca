package business.global.pk.a3pk;	
	
import business.global.pk.AbsPKSetOp;	
import business.global.pk.PKFactory;	
import business.global.pk.PKOpCard;	
import business.global.pk.a3pk.optype.*;	
import cenum.PKOpType;	
import java.util.stream.Collectors;	
	
/**	
 * 当局操作动作	
 *	
 * <AUTHOR>	
public class A3PKSetOp extends AbsPKSetOp {	
    /**	
     * 玩家信息	
     */	
    private A3PKSetPos mSetPos;	
    /**	
     * 当前局信息	
     */	
    private A3PKRoomSet roomSet;	
	
    public A3PKSetOp(A3PKSetPos mSetPos) {	
        super();	
        this.mSetPos = mSetPos;	
        this.roomSet = mSetPos.getRoomSet();	
    }	
	
	
    @Override	
    public boolean doOpType(PKOpCard opCard, PKOpType opType) {	
        switch (opType) {	
            case Out:	
                return this.checkOpType(opCard, opType);	
            case Pass:	
                if (getmSetPos().getSet().getCurOutCard().getOutCardPos() >= 0 && getmSetPos().getSet().getCurOutCard().getOutCardPos() != this.getmSetPos().getPosID()) {	
                    // 清除打出牌列表	
                    this.mSetPos.clearOutCardList();	
                    return true;	
                }	
                return false;	
            case Challenge:	
                // 显示牌	
                this.getmSetPos().setShowCardList(this.getmSetPos().getPrivateCards().stream().filter(cardId->A3PKRoomEnum.isBlackAor3(this.roomSet.isKing(),cardId )).collect(Collectors.toSet()));	
                // 摆牌通知	
                this.getmSetPos().setPartnerPos();	
                return true;	
            case Refuse:	
                return true;	
            case Surrender:	
                return true;	
        }	
        return false;	
    }	
	
    /**	
     * 检查出牌类型	
     *	
     * @param opCard 牌ID	
     * @param opType 动作类型	
     * @return	
     */	
    @Override	
    public boolean checkOpType(PKOpCard opCard, PKOpType opType) {	
        // 牌类型	
        A3PKRoomEnum.A3PK_CARD_TYPE cardType = A3PKRoomEnum.A3PK_CARD_TYPE.valueOf(opCard.getOpValue());	
        if (A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_NOMARL.equals(cardType)) {	
            // 没有默认牌型	
            return false;	
        }	
        switch (cardType) {	
            case A3PK_CARD_TYPE_BUCHU:	
                return getmSetPos().getSet().getCurOutCard().getOutCardPos() >= 0 && getmSetPos().getSet().getCurOutCard().getOutCardPos() != this.getmSetPos().getPosID();	
            case A3PK_CARD_TYPE_SINGLECARD:	
                return PKFactory.getCardType(A3PKSingleCardImpl.class).resultType(getmSetPos(), getmSetPos().getPrivateCards(), opCard);	
            case A3PK_CARD_TYPE_DUIZI:	
                return PKFactory.getCardType(A3PKDuiZiImpl.class).resultType(getmSetPos(), getmSetPos().getPrivateCards(), opCard);	
            case A3PK_CARD_TYPE_3:	
                return PKFactory.getCardType(A3PK3ZhangImpl.class).resultType(getmSetPos(), getmSetPos().getPrivateCards(), opCard);	
            case A3PK_CARD_TYPE_4:	
                return PKFactory.getCardType(A3PK4ZhangImpl.class).resultType(getmSetPos(), getmSetPos().getPrivateCards(), opCard);	
            case A3PK_CARD_TYPE_SHUNZI:	
                return PKFactory.getCardType(A3PKShunZiImpl.class).resultType(getmSetPos(), getmSetPos().getPrivateCards(), opCard);	
            case A3PK_CARD_TYPE_TONGHUA:	
                return PKFactory.getCardType(A3PKTongHuaImpl.class).resultType(getmSetPos(), getmSetPos().getPrivateCards(), opCard);	
            case A3PK_CARD_TYPE_3DAI2:	
                return PKFactory.getCardType(A3PK3Dai2Impl.class).resultType(getmSetPos(), getmSetPos().getPrivateCards(), opCard);	
            case A3PK_CARD_TYPE_4DAI1:	
                return PKFactory.getCardType(A3PK4Dai1Impl.class).resultType(getmSetPos(), getmSetPos().getPrivateCards(), opCard);	
            case A3PK_CARD_TYPE_TONGHUASHUN:	
                return PKFactory.getCardType(A3PKTongHuaShunZiImpl.class).resultType(getmSetPos(), getmSetPos().getPrivateCards(), opCard);	
        }	
        return false;	
    }	
	
    public A3PKSetPos getmSetPos() {	
        return mSetPos;	
    }	
	
    public A3PKRoomSet getRoomSet() {	
        return roomSet;	
    }	
	
    @Override	
    public void clear() {	
	
    }	
}	
