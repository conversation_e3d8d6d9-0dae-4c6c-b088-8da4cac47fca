package business.ahmj.c2s.iclass;					
					
import business.global.mj.set.MJOpCard;					
					
					
import java.util.List;					
					
					
public class SAHMJ_OpCard extends MJOpCard {					
					
    private List<Integer> cardList;					
					
    public SAHMJ_OpCard(int opCard, List<Integer> cardList) {					
        super(opCard);					
        this.cardList = cardList;					
    }					
					
    public List<Integer> getCardList() {					
        return cardList;					
    }					
					
    public void setCardList(List<Integer> cardList) {					
        this.cardList = cardList;					
    }					
					
    public static MJOpCard OpCard(int opCard, List<Integer> cardList) {					
        return new SAHMJ_OpCard(opCard, cardList);					
    }					
}					
