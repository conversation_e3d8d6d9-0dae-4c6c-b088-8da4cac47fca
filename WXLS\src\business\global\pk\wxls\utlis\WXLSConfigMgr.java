package business.global.pk.wxls.utlis;	
	
import business.wxls.c2s.cclass.newwxls.WXLSCardRankEnum;	
import business.wxls.c2s.cclass.newwxls.WXLSCardSuitEnum;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
import com.ddm.server.common.utils.Txt2Utils;	
import jsproto.c2s.cclass.pk.BasePocker;	
	
import java.util.ArrayList;	
import java.util.HashMap;	
import java.util.List;	
import java.util.Map;	
	
public class WXLSConfigMgr {	
    public static final String fileName = "WXLSConfig.txt";	
    public static final String filePath = "conf/";	
    private Map<String, String> configMap = new HashMap<String, String>();	
    private int godCard = 0;//神牌模式	
    private int handleCard; //手牌张数	
    private List<WXLSPockerCard> privateCard1; //玩家一	
    private List<WXLSPockerCard> privateCard2; //玩家二	
    private List<WXLSPockerCard> privateCard3; //玩家三	
    private List<WXLSPockerCard> privateCard4; //玩家四
    private ArrayList<Integer> huase; //底分
    private int  special = 0;
    private ArrayList<Integer> specialList;
    private List<WXLSPockerCard> publicCard; //公共牌
    public WXLSConfigMgr(){	
        this.configMap = Txt2Utils.txt2Map(filePath, fileName, "GBK");
        this.huase = Txt2Utils.String2ListInteger(this.configMap.get("huase"));
        this.special = Integer.valueOf(this.configMap.get("special"));
        this.specialList = Txt2Utils.String2ListInteger(this.configMap.get("specialList"));
        this.godCard = Integer.valueOf(this.configMap.get("God_Card"));	
        this.handleCard = Integer.valueOf(this.configMap.get("handleCard"));	
        this.privateCard1 = getCard(Txt2Utils.String2ListInteger(this.configMap.get("Private_Card1")));	
        this.privateCard2 = getCard(Txt2Utils.String2ListInteger(this.configMap.get("Private_Card2")));	
        this.privateCard3 = getCard(Txt2Utils.String2ListInteger(this.configMap.get("Private_Card3")));	
        this.privateCard4 = getCard(Txt2Utils.String2ListInteger(this.configMap.get("Private_Card4")));
        this.publicCard = getCard(Txt2Utils.String2ListInteger(this.configMap.get("publicCard")));

    }

    public List<WXLSPockerCard> getPublicCard() {
        return publicCard;
    }

    public void setPublicCard(List<WXLSPockerCard> publicCard) {
        this.publicCard = publicCard;
    }

    public int getSpecial() {
        return special;
    }
    public ArrayList<Integer> getSpecialList() {
        return specialList;
    }
    public int getHandleCard() {	
        return handleCard;	
    }	
	
    public int getGodCard() {	
        return godCard;	
    }	
    public List<WXLSPockerCard> getPrivateCard1() {	
        return privateCard1;	
    }	
    public List<WXLSPockerCard> getPrivateCard2() {	
        return privateCard2;	
    }	
    public List<WXLSPockerCard> getPrivateCard3() {	
        return privateCard3;	
    }	
    public List<WXLSPockerCard> getPrivateCard4() {	
        return privateCard4;	
    }	
	
    public List<WXLSPockerCard> getCard(List<Integer> cards){	
        List<WXLSPockerCard> wxlsPockerCards=new ArrayList<>();	
        for(Integer integer:cards){	
            WXLSPockerCard wxlsPockerCard;	
            int color= BasePocker.getCardColor(integer);	
            int value=BasePocker.getCardValue(integer);	
        switch (color){	
            case 0:	
                wxlsPockerCard=new WXLSPockerCard(WXLSCardSuitEnum.DIAMONDS, WXLSCardRankEnum.valueOf(value));	
                break;	
            case 1:	
                wxlsPockerCard=new WXLSPockerCard(WXLSCardSuitEnum.CLUBS, WXLSCardRankEnum.valueOf(value));	
                break;	
            case 2:	
                wxlsPockerCard=new WXLSPockerCard(WXLSCardSuitEnum.HEARTS, WXLSCardRankEnum.valueOf(value));	
                break;	
            case 3:	
                wxlsPockerCard=new WXLSPockerCard(WXLSCardSuitEnum.SPADES, WXLSCardRankEnum.valueOf(value));	
                break;	
            default:	
                wxlsPockerCard=new WXLSPockerCard(WXLSCardSuitEnum.DIAMONDS, WXLSCardRankEnum.valueOf(value));	
            }	
            wxlsPockerCards.add(wxlsPockerCard);	
	
        }	
        return wxlsPockerCards;	
	
    }	
}	
