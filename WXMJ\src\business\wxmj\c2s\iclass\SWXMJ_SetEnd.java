package business.wxmj.c2s.iclass;			
import jsproto.c2s.cclass.*;			
			
			
@SuppressWarnings("serial")			
public class SWXMJ_SetEnd<T> extends BaseSendMsg {			
    			
    public long roomID;			
    public T setEnd;			
			
			
    public static <T> SWXMJ_SetEnd<T> make(long roomID, T setEnd) {			
    	SWXMJ_SetEnd<T> ret = new SWXMJ_SetEnd<T>();			
        ret.roomID = roomID;			
        ret.setEnd = setEnd;			
			
        return ret;			
    			
			
    }			
}			
