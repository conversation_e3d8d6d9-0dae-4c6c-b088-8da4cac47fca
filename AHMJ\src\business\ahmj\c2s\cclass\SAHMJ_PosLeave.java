package business.ahmj.c2s.cclass;						
						
import jsproto.c2s.iclass.room.SBase_PosLeave;						
						
/**						
 * 位置离开通知						
 * 						
 * <AUTHOR>						
 *						
 */						
@SuppressWarnings("serial")						
public class SAHMJ_PosLeave extends SBase_PosLeave {						
						
	public static SAHMJ_PosLeave make(SBase_PosLeave posLeave) {						
		SAHMJ_PosLeave ret = new SAHMJ_PosLeave();						
		ret.setRoomID(posLeave.getRoomID());						
		ret.setPos(posLeave.getPos());						
		ret.setBeKick(posLeave.isBeKick());						
		ret.setOwnerID(posLeave.getOwnerID());						
		ret.setKickOutTYpe(posLeave.getKickOutTYpe());						
		ret.setMsg(posLeave.getMsg());						
		return ret;						
	}						
}						
