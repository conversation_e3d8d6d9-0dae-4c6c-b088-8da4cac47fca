package business.global.mj.wxmj;

import business.global.mj.*;
import business.global.mj.manage.MJFactory;
import business.global.mj.op.BuHuaImpl;
import business.global.mj.wxmj.WXMJRoomEnum.WXMJPiaoFen;
import business.global.room.base.AbsRoomPos;
import business.global.room.mj.MJRoomPos;
import business.wxmj.c2s.cclass.WXMJRoomSetEnd;
import business.wxmj.c2s.cclass.WXMJRoomSetInfo;
import business.wxmj.c2s.cclass.WXMJSetRoonCfg;
import business.wxmj.c2s.cclass.WXMJSet_Pos;
import business.wxmj.c2s.iclass.*;
import cenum.PrizeType;
import cenum.mj.FlowerEnum;
import cenum.mj.MJSpecialEnum;
import cenum.mj.OpType;
import cenum.room.SetState;
import com.ddm.server.common.CommLogD;
import com.ddm.server.common.utils.CommTime;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.mj.BaseMJRoom_PosEnd;
import jsproto.c2s.cclass.mj.BaseMJRoom_SetEnd;
import jsproto.c2s.cclass.mj.BaseMJSet_Pos;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 仙游麻将 一局游戏逻辑
 *
 * <AUTHOR>
 */
public class WXMJRoomSet extends AbsMJSetRoom {
    // 设置局数配置		
    private WXMJSetRoonCfg qSetRoonCfg = new WXMJSetRoonCfg();
    private final static int INTERVAL = 3000;// 间隔3s		
    private final static int WAITINGTIME = 15000;// 等待15s		

    public boolean shifoutianting = false;

    public boolean isShifoutianting() {
        return shifoutianting;
    }

    public void setShifoutianting(boolean shifoutianting) {
        this.shifoutianting = shifoutianting;
    }


    public WXMJRoomSet(int setID, WXMJRoom room, int dPos) {
        super(setID, room, dPos);
        this.startMS = CommTime.nowMS();
        // 回放记录添加游戏配置		
        this.addGameConfig();
        initSetState();
    }

    /**
     * 检查小局托管自动解散
     */
    @Override
    public boolean checkSetEndTrusteeshipAutoDissolution() {
        return ((WXMJRoom) this.getRoom()).getRoomCfg().getFangjian().contains(2);
    }

    /**
     * 小局托管自动解散回放记录 注意：需要自己重写
     *
     * @param roomId  房间id
     * @param pidList 托管玩家Pid
     * @param sec     记录时间
     * @return
     */
    @Override
    public BaseSendMsg DissolveTrusteeship(long roomId, List<Long> pidList, int sec) {
        SWXMJ_DissolveTrusteeship ret = new SWXMJ_DissolveTrusteeship();
        ret.setRoomID(roomId);
        ret.setTrusteeship(pidList);
        ret.setEndSec(sec);
        return ret;
    }

    /**
     * 飘初始化位置
     */
    public void posInit() {
        // 洗底牌
        this.absMJSetCard();
        // 初始化本局位置管理器
        this.setSetPosMgr(this.absMJSetPosMgr());
        // 初始化玩家手上的牌
        this.initSetPosCard();
    }

    /**
     * 初始状态
     */
    public void initSetState() {

        if (((WXMJRoom) room).isPiao()) {
            this.posDict.entrySet().stream().forEach(k -> ((WXMJRoomPos) k.getValue().getRoomPos()).clearPiaoHua());
            posInit();
            if (((WXMJRoom) this.getRoom()).getRoomCfg().getPiaohuaxuanze() == WXMJRoomEnum.WXMJPiaoFenLeiXing.gudingpiao.ordinal()) {
                for (Map.Entry<Integer, Integer> mmm : ((WXMJRoom) room).getTaiMap().entrySet()) {
                    if (mmm.getValue() == 0) {
                        mmm.setValue(-1);
                    }
                }
            }
            if (((WXMJRoom) this.getRoom()).getRoomCfg().getPiaohuaxuanze() == WXMJRoomEnum.WXMJPiaoFenLeiXing.gudingpiao.ordinal() && !((WXMJRoom) this.getRoom()).isRealFirst()) {
                List<Integer> lll = new ArrayList<>();
                for (Map.Entry<Integer, Integer> ccc : ((WXMJRoom) this.getRoom()).getTaiMap().entrySet()) {
                    if (ccc.getValue() <= 0) {
                        lll.add(ccc.getValue());
                    }
                }
                if (lll.size() != 0) {
                    setState(SetState.WaitingEx);
                } else {
                    // 通知本局开始
                    this.notify2SetStart();
                    // 通知本局开始
                    for (Map.Entry<Integer, AbsMJSetPos> ccc : this.getPosDict().entrySet()) {
                        ((WXMJSetPos) ccc.getValue()).setPiaofen(((WXMJRoom) this.getRoom()).getTaiMap().get(ccc.getKey()));
                    }
                    // 一些基本数据初始，无需理会。
                    exeStartSet();
                }
            } else {
                setState(SetState.WaitingEx);
            }

        } else {
            setStateInit();
        }
    }

    /**
     * 本局结算， 计算下一局的坐庄信息
     */
    @Override
    public int calcNextDPos() {
        // 庄家胡牌、流局，庄家坐庄		
        if (this.getMHuInfo().isHuNotEmpty()) {
            if (this.getMHuInfo().getHuPosMap().get(this.getCurRound().getRoundID()).size() > 1) {
                return this.getLastOpInfo().getLastOpPos();
            } else if (this.getMHuInfo().getHuPos() == this.dPos) {
                this.room.setEvenDpos(this.room.getEvenDpos() + 1);
                return this.dPos;
            } else {
                this.room.setEvenDpos(0);
                return this.getMHuInfo().getHuPos();
            }
        } else {
            this.room.setEvenDpos(this.room.getEvenDpos() + 1);
            return this.dPos;
        }
    }

    // 每200ms更新1次 秒		
    @Override
    public boolean update(int sec) {
        boolean isClose = false;

        if (this.state == SetState.Init) {
            if (CommTime.nowMS() > this.startMS + this.InitTime) {
                this.state = SetState.Playing;
                if (!this.startNewRound()) {
                    this.endSet();
                }
            }
        } else if (this.state == SetState.Playing) {
            boolean isRoundClosed = this.curRound.update(sec);
            if (isRoundClosed) {
                if (curRound.isSetHuEnd() || !this.startNewRound()) {
                    this.endSet();
                }
            }
        } else if (this.state == SetState.End) {
            isClose = true;
            // 开金		
        } else if (this.state == SetState.WaitingEx) {
            if (((WXMJRoom) this.room).getRoomCfg().getPiaoHua() == WXMJRoomEnum.WXMJPiaoFenGuiZe.otf.ordinal()) {
                for (AbsRoomPos roomPos : this.room.getRoomPosMgr().getPosList()) {
                    WXMJRoomPos namjRoomPos = (WXMJRoomPos) roomPos;
                    if (namjRoomPos.isRobot() && CommTime.nowMS() > this.getStartMS() + INTERVAL
                            && namjRoomPos.getPiaoFenEnum().equals(WXMJPiaoFen.NOT_OP)) {
                        WXMJPiaoFen piaoFen = Math.random() > 0.5 ? WXMJPiaoFen.NOT_PIAO
                                : (Math.random() > 0.5 ? WXMJPiaoFen.NOT_PIAO : WXMJPiaoFen.NOT_PIAO);
                        opPiaoFen(null, namjRoomPos.getPid(),
                                CWXMJ_PiaoFen.make(this.room.getRoomID(), piaoFen.value()));
                    } else if (!namjRoomPos.isRobot() && CommTime.nowMS() > this.getStartMS() + WAITINGTIME
                            && namjRoomPos.getPiaoFenEnum().equals(WXMJPiaoFen.NOT_OP)) {
                        WXMJPiaoFen piaoFen = WXMJPiaoFen.NOT_PIAO;
                        opPiaoFen(null, namjRoomPos.getPid(),
                                CWXMJ_PiaoFen.make(this.room.getRoomID(), piaoFen.value()));
                    }
                }
            } else if (((WXMJRoom) this.room).getRoomCfg().getPiaoHua() == WXMJRoomEnum.WXMJPiaoFenGuiZe.ftf.ordinal()) {
                for (AbsRoomPos roomPos : this.room.getRoomPosMgr().getPosList()) {
                    WXMJRoomPos namjRoomPos = (WXMJRoomPos) roomPos;
                    if (namjRoomPos.isRobot() && CommTime.nowMS() > this.getStartMS() + INTERVAL
                            && namjRoomPos.getPiaoFenEnum().equals(WXMJPiaoFen.NOT_OP)) {
                        WXMJPiaoFen piaoFen = Math.random() > 0.5 ? WXMJPiaoFen.NOT_PIAO
                                : (Math.random() > 0.5 ? WXMJPiaoFen.NOT_PIAO : WXMJPiaoFen.NOT_PIAO);
                        opPiaoFen(null, namjRoomPos.getPid(),
                                CWXMJ_PiaoFen.make(this.room.getRoomID(), piaoFen.value()));
                    } else if (!namjRoomPos.isRobot() && CommTime.nowMS() > this.getStartMS() + WAITINGTIME
                            && namjRoomPos.getPiaoFenEnum().equals(WXMJPiaoFen.NOT_OP)) {
                        WXMJPiaoFen piaoFen = WXMJPiaoFen.NOT_PIAO;
                        opPiaoFen(null, namjRoomPos.getPid(),
                                CWXMJ_PiaoFen.make(this.room.getRoomID(), piaoFen.value()));
                    }
                }
            }

        }
        return isClose;
    }

    @Override
    public boolean equals(Object o) {
        if (o instanceof WXMJRoomSet) {
            if (getSetID() == ((WXMJRoomSet) o).getSetID()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 飘分
     */
    public void opPiaoFen(WebSocketRequest request, long pid, CWXMJ_PiaoFen data) {
        if (!this.state.equals(SetState.WaitingEx)) {
            if (null != request) {
                request.error(ErrorCode.NotAllow, "opPiaoFen cur setstate=" + this.state);
            }
            return;
        }
        WXMJPiaoFen piaoFen = WXMJPiaoFen.valueOf(data.piaoFen);

        WXMJRoomPos roomPos = (WXMJRoomPos) this.room.getRoomPosMgr().getPosByPid(pid);
        if (roomPos.getPiaoFenEnum().equals(piaoFen) || WXMJPiaoFen.NOT_OP.equals(piaoFen)) {
            if (null != request) {
                request.error(ErrorCode.NotAllow, "opPiaoFen  your PiaoFen = " + roomPos.getPiaoFenEnum());
            }
            return;
        }
        roomPos.setPiaoFenEnum(piaoFen);
        ((WXMJRoom) this.room).getTaiMap().put(roomPos.getPosID(), data.piaoFen);
        if (null != request) {
            request.response();
        }
        this.room.getRoomPosMgr().notify2All(SWXMJ_PiaoFen.make(data.roomID, roomPos.getPosID(), data.piaoFen));
        WXMJRoomPosMgr roomPosMgr = (WXMJRoomPosMgr) this.room.getRoomPosMgr();
        if (roomPosMgr.checkAllOpPiaoFen()) {
            // 通知本局开始
            for (Map.Entry<Integer, AbsMJSetPos> ccc : this.getPosDict().entrySet()) {
                ((WXMJSetPos) ccc.getValue()).setPiaofen(((WXMJRoom) this.getRoom()).getTaiMap().get(ccc.getKey()));
            }
            setState(SetState.Init);
            this.notify2SetStart();

            // 一些基本数据初始，无需理会。
            exeStartSet();
        }
    }

    /**
     * 麻将当局结算
     *
     * @return
     */
    @Override
    protected WXMJRoomSetEnd newMJRoomSetEnd() {
        return new WXMJRoomSetEnd();
    }

    @Override
    protected BaseMJRoom_SetEnd mRoomSetEnd() {
        if (null != setEnd) {
            return this.setEnd;
        }
        this.setEnd = this.newMJRoomSetEnd();
        WXMJRoomSetEnd setEnd = (WXMJRoomSetEnd) this.setEnd;
        setEnd.setDPos(this.getDPos());
        setEnd.setEndTime(CommTime.nowSecond());
        if (this.checkExistPrizeType(PrizeType.RoomCard)) {
            setEnd.setPlayBackCode(getPlayBackDateTimeInfo().getPlayBackCode());
            setEnd.setRoomDissolutionState(this.getRoom().getRoomDissolutionState());
        }
        setEnd.setSetId(this.getSetID());
        setEnd.setPosResultList(this.getPosDict().values().stream().map(k -> k.calcPosEnd())
                .sorted(Comparator.comparing(BaseMJRoom_PosEnd::getPos)).collect(Collectors.toList()));
        return setEnd;
    }

    @Override
    public void endSet() {
        CommLogD.info("endSet id:{}", getSetID());
        if (this.state == SetState.End) {
            return;
        }
        this.state = SetState.End;
        ((WXMJRoom) this.getRoom()).setRealFirst(false);
        setEnd(true);
        // 结算算分	
        this.calcPoint();
        // 广播	
        this.getRoomPlayBack().playBack2All(this.setEnd(room.getRoomID(), this.getNotify_setEnd()));
        // 小局托管自动解散	
        this.setTrusteeshipAutoDissolution();
        // 记录回放码	
        this.roomPlayBack();

    }


    /**
     * 游戏技术开金配置
     *
     * @return
     */
    private WXMJSetRoonCfg getRoomSetCfg() {
        return this.qSetRoonCfg;
    }


    /**
     * 发送设置位置的牌
     */
    @Override
    public void sendSetPosCard() {
        for (int i = 0; i < room.getPlayerNum(); i++) {
            AbsMJSetPos setPos = posDict.get(i);
            setPos.sortCards();
        }
        for (int i = 0; i < room.getPlayerNum(); i++) {
            long pid = this.getRoom().getRoomPosMgr().getPosByPosID(i).getPid();
            if (i == 0) {
                this.getRoomPlayBack().playBack2Pos(i,
                        SWXMJ_SetPosCard.make(this.room.getRoomID(), this.setPosCard(pid)), null);
            } else {
                this.room.getRoomPosMgr().notify2Pos(i,
                        SWXMJ_SetPosCard.make(this.room.getRoomID(), this.setPosCard(pid)));
            }
        }
    }

    @Override
    public void MJApplique(int pos) {
        AbsMJSetPos setPos = posDict.get(pos);// 鑾峰緱鐜╁
        BaseMJSet_Pos posInfoOther = setPos.getNotify(false);
        BaseMJSet_Pos posInfoSelf = setPos.getNotify(true);
        getRoomPlayBack().playBack2Pos(pos,
                SWXMJ_Applique.make(getRoom().getRoomID(), pos, OpType.Out, 0, false, posInfoSelf), null);
        for (int i = 0; i < getRoom().getPlayerNum(); i++) {
            if (i == pos)
                continue;
            getRoom().getRoomPosMgr().notify2Pos(i,
                    SWXMJ_Applique.make(getRoom().getRoomID(), pos, OpType.Out, 0, false, posInfoOther));
        }
    }

    @Override
    public void kaiJinNotify(MJCard jinCard, MJCard jinCard2) {

    }

    /**
     * 摸牌消息
     */
    @Override
    protected <T> BaseSendMsg posGetCard(long roomID, int pos, int normalMoCnt, int gangMoCnt, T set_Pos) {
        return SWXMJ_PosGetCard.make(roomID, pos, normalMoCnt, gangMoCnt, set_Pos,
                this.setCard.getRandomCard().getSize());
    }

    /**
     * 下回合操作位置
     */
    @Override
    protected AbsMJSetRound nextSetRound(int roundID) {
        return new WXMJSetRound(this, roundID);
    }

    /**
     * 小局结算消息
     */
    @Override
    protected <T> BaseSendMsg setEnd(long roomID, T setEnd) {
        return SWXMJ_SetEnd.make(roomID, setEnd);
    }

    /**
     * 玩家位置信息
     */
    @Override
    protected AbsMJSetPos absMJSetPos(int posID) {
        return new WXMJSetPos(posID, (MJRoomPos) this.room.getRoomPosMgr().getPosByPosID(posID), this);
    }

    /**
     * 本局牌管理
     */
    @Override
    protected void absMJSetCard() {
        // 设置当局牌		
        this.setSetCard(new WXMJSetCard(this));
    }

    /**
     * 牌局开始消息通知
     */
    @Override
    protected <T> BaseSendMsg setStart(long roomID, T setInfo) {
        return SWXMJ_SetStart.make(roomID, setInfo);
    }

    /**
     * 本局玩家操作管理
     */
    @Override
    protected AbsMJSetPosMgr absMJSetPosMgr() {
        return new WXMJSetPosMgr(this);
    }

    /**
     * 计算当局每个pos位置的分数。
     */
    @Override
    protected void calcCurSetPosPoint() {
        // 计算位置小局分数		
        this.getPosDict().values().forEach(k -> k.calcPosPoint());
    }

    /**
     * 牌数
     */
    @Override
    public int cardSize() {
        return MJSpecialEnum.SIZE_13.value();
    }

    /**
     * 清空数据
     */
    @Override
    public void clear() {
        super.clear();
    }

    /**
     * 清空BO数据
     */
    @Override
    public void clearBo() {
        super.clearBo();
    }

    /**
     * 创建新的当局麻将信息
     */
    @Override
    protected WXMJRoomSetInfo newMJRoomSetInfo() {
        return new WXMJRoomSetInfo();
    }

    /**
     * 获取通知当局信息
     */
    @Override
    public WXMJRoomSetInfo getNotify_set(long pid) {
        WXMJRoomPosMgr roomPosMgr = (WXMJRoomPosMgr) this.room.getRoomPosMgr();
        WXMJRoomSetInfo ret = (WXMJRoomSetInfo) this.getMJRoomSetInfo(pid);

        ret.setPiaoFenList(roomPosMgr.getPiaoFenList());

        // 金		
        ret.setRoomSetCfg(this.getRoomSetCfg());

        return ret;
    }

    /**
     * 开金数
     */
    @Override
    public int kaiJinNum() {
        return 1;
    }

    /**
     * 是否白板替金
     */
    @Override
    public boolean isBaiBanTiJin() {
        return false;
    }

    /**
     * 回放记录添加游戏配置
     */
    @Override
    public void addGameConfig() {
        this.getRoomPlayBack().addPlaybackList(
                SWXMJ_Config.make(this.getRoom().getCfg(), this.getRoom().getRoomTyepImpl().getRoomTypeEnum()), null);
    }

    /**
     * 配置文件是否需要游戏名
     *
     * @return T:需要,F:不需要
     */
    public boolean isConfigName() {
        return true;
    }

    /**
     * 摸牌
     *
     * @param opPos
     * @param isNormalMo
     * @return
     */
    public MJCard getCard(int opPos, boolean isNormalMo) {
        WXMJSetPos setPos = (WXMJSetPos) this.posDict.get(opPos);
        // 随机摸牌		
        MJCard card = this.setCard.pop(isNormalMo, this.getGodInfo().godHandCard(setPos));
        if (Objects.isNull(card)) {
            // 黄庄位置		
            this.getMHuInfo().setHuangPos(opPos);
            return null;
        }
        // 设置牌		
        setPos.getCard(card);
        // 通知房间内的所有玩家，指定玩家摸牌了。		
        this.notify2GetCard(setPos);
        // 通知房间内的所有玩家，指定玩家摸牌了。
        if (card != null) {
            if (card.type >= 50) {
                MJFactory.getOpCard(BuHuaImpl.class).checkOpCard(setPos, FlowerEnum.HAND_CARD.ordinal());
            }
        }
        return card;
    }

    /**
     * 设置为init状态
     */
    public void setStateInit() {
        setState(SetState.Init);
        startSet();
    }

    /**
     * 开始发牌
     */
    @Override
    public void startSet() {
        CommLogD.info("startSet id:{}", getSetID());
        // 洗底牌	
        this.absMJSetCard();
        // 初始化本局位置管理器	
        this.setSetPosMgr(this.absMJSetPosMgr());
        // 初始化玩家手上的牌	
        this.initSetPosCard();
        if (((WXMJRoom) this.getRoom()).getRoomCfg().getPiaoHua() == WXMJRoomEnum.WXMJPiaoFenGuiZe.no.ordinal()) {
            for (Map.Entry<Integer, AbsMJSetPos> ccc : this.getPosDict().entrySet()) {
                ((WXMJSetPos) ccc.getValue()).setPiaofen(0);
            }
        } else {
            for (Map.Entry<Integer, AbsMJSetPos> ccc : this.getPosDict().entrySet()) {
                ((WXMJSetPos) ccc.getValue()).setPiaofen(((WXMJRoom) this.getRoom()).getTaiMap().get(ccc.getKey()));
            }
        }
        // 通知本局开始
        this.notify2SetStart();
        // 一些基本数据初始，无需理会。	
        exeStartSet();
    }

    /**
     * 设置setstate
     */
    @Override
    public void setState(SetState setState) {
        this.state = setState;
        this.startMS = CommTime.nowMS();
        WXMJRoomPosMgr roomPosMgr = (WXMJRoomPosMgr) this.room.getRoomPosMgr();
        ArrayList<Integer> list = new ArrayList<Integer>();
        if (((WXMJRoom) this.room).getRoomCfg().getPiaoHua() == WXMJRoomEnum.WXMJPiaoFenGuiZe.no.ordinal()) {// 查实太
            for (int i = 0; i < this.getPlayerNum(); i++) {
                list.add(0);
            }
        }
        boolean xiaoyu = false;
        if (((WXMJRoom) this.room).getRoomCfg().getPiaohuaxuanze() == WXMJRoomEnum.WXMJPiaoFenLeiXing.gudingpiao.ordinal()
                && this.getSetID() != 1 || ((WXMJRoom) this.room).getTaiMap().size() != 0) {// 自选插台
            for (int i = 0; i < this.getPlayerNum(); i++) {
                list.add(((WXMJRoom) this.room).taiMap.get(i));
                if (((WXMJRoom) this.room).taiMap.get(i) < 0) {
                    xiaoyu = true;
                }
            }
        }
        if (((WXMJRoom) this.room).getRoomCfg().getPiaohuaxuanze() == WXMJRoomEnum.WXMJPiaoFenLeiXing.jujupiao.ordinal()) {
            this.room.getRoomPosMgr().notify2All(SWXMJ_ChangeStatus.make(this.room.getRoomID(), this.getSetID(),
                    this.getState(), this.dPos, roomPosMgr.getPiaoFenList()));
        } else if ((((WXMJRoom) this.room).getRoomCfg().getPiaohuaxuanze() == WXMJRoomEnum.WXMJPiaoFenLeiXing.gudingpiao.ordinal()
                && this.getSetID() == 1)) {
            this.room.getRoomPosMgr().notify2All(SWXMJ_ChangeStatus.make(this.room.getRoomID(), this.getSetID(),
                    this.getState(), this.dPos, list.size() == 0 ? roomPosMgr.getPiaoFenList() : list));
        } else if (((WXMJRoom) this.room).getRoomCfg().getPiaohuaxuanze() == WXMJRoomEnum.WXMJPiaoFenLeiXing.gudingpiao.ordinal()
                && this.getSetID() != 1) {
            this.room.getRoomPosMgr().notify2All(SWXMJ_ChangeStatus.make(this.room.getRoomID(), this.getSetID(),
                    this.getState(), this.dPos, list.size() == 0 ? roomPosMgr.getPiaoFenList() : list));
        }

    }

}			
