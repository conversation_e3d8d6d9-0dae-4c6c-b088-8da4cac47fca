package core.network.client2game.handler.wxpdk;

import business.global.pk.wxpdk.WXPDKRoom;
import business.global.pk.wxpdk.WXPDKRoomSet;
import business.global.room.RoomMgr;
import business.wxpdk.c2s.iclass.CWXPDK_FaPaiJieShu;
import business.player.Player;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.exception.WSException;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;
import core.network.client2game.handler.PlayerHandler;

import java.io.IOException;

public class CWXPDKFaPaiJieShu extends PlayerHandler {

    @Override
    public void handle(Player player, WebSocketRequest request, String message) throws WSException, IOException {
        final CWXPDK_FaPaiJieShu clientPack = new Gson().from<PERSON>son(message, CWXPDK_FaPaiJieShu.class);

        WXPDKRoom room = (WXPDKRoom) RoomMgr.getInstance().getRoom(clientPack.roomID);
        if (null == room){
            request.error(ErrorCode.NotAllow, "CWXPDKOpenCard not find room:"+clientPack.roomID);
            return;
        }
        WXPDKRoomSet set =  (WXPDKRoomSet) room.getCurSet();
        if(null == set){
            request.error(ErrorCode.NotAllow, "CWXPDKOpenCard not set room:"+clientPack.roomID);
            return;
        }
        set.faPaiJieShu(request,  clientPack.pos);
        request.response(ErrorCode.Success);
    }
}
