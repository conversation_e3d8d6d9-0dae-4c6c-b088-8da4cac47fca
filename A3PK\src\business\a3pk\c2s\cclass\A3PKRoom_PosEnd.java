package business.a3pk.c2s.cclass;	
	
import business.global.pk.a3pk.A3PKRoomEnum;	
import jsproto.c2s.cclass.pk.base.BasePKRoom_PosEnd;	
import lombok.Data;	
	
import java.util.HashSet;	
import java.util.List;	
import java.util.Set;	
	
@Data	
public class A3PKRoom_PosEnd extends BasePKRoom_PosEnd {	
    /**	
     * 输赢分	
     */	
    private int winLosePoint;	
	
    /**	
     * 分组	
     */	
    private int ranksType;	
	
    /**	
     * 结束顺序	
     */	
    private A3PKRoomEnum.A3PK_SET_POS_END_TYPE endType;	
    /**	
     * 玩家位置状态	
     */	
    private A3PKRoomEnum.A3PK_POS_STATE state = A3PKRoomEnum.A3PK_POS_STATE.NOT;	
	
	
    /**	
     * 显示牌列表	
     */	
    private Set<Integer> showCardList = new HashSet<>();	
	
}	
