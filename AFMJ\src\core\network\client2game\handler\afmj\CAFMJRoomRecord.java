package core.network.client2game.handler.afmj;

import java.io.IOException;

import business.global.mj.afmj.AFMJRoom;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;
import business.global.room.RoomMgr;
import business.player.Player;
import core.network.client2game.handler.PlayerHandler;
import jsproto.c2s.iclass.room.CBase_RoomRecord;

/**
 * 房间记录
 * <AUTHOR>
 *
 */
public class CAFMJRoomRecord extends PlayerHandler {
	

    @Override
    public void handle(Player player, WebSocketRequest request, String message) throws IOException {
    	final CBase_RoomRecord req = new Gson().fromJson(message, CBase_RoomRecord.class);
    	
    	AFMJRoom room = (AFMJRoom) RoomMgr.getInstance().getRoom(req.getRoomID());
    	if (null == room){
    		request.error(ErrorCode.NotAllow, "CAFMJRoomRecord not find room:"+req.getRoomID());
    		return;
    	}

    	request.response(room.getRecord());
    }
}
