package business.global.pk.a3pk.optype;	
	
import business.global.pk.AbsPKSetPos;	
import business.global.pk.CardType;	
import business.global.pk.PKCurOutCardInfo;	
import business.global.pk.PKOpCard;	
import business.global.pk.a3pk.A3PKRoomEnum;	
import cenum.pk.PKSpecialEnum;	
import jsproto.c2s.cclass.pk.BasePocker;	
	
import java.util.*;	
	
public abstract class A3PKBaseCardType<T> implements CardType<T> {	
    @Override	
    public boolean resultType(AbsPKSetPos mSetPos, List<Integer> privateCardList) {	
        return false;	
    }	
	
    @Override	
    public boolean resultType(AbsPKSetPos mSetPos, List<Integer> privateCardList, PKOpCard opCard) {	
        return false;	
    }	
	
    @Override	
    public PKOpCard robotResultType(AbsPKSetPos mSetPos, PKCurOutCardInfo curOutCardInfo, T item) {	
        return null;	
    }	
	
    /**	
     * 检查当前打出的牌	
     * 可以一直出牌	
     *	
     * @param opCardType 操作牌型	
     * @param curOutCard 当前出牌信息	
     * @param opPosId    操作位置	
     * @param opCards    牌列表	
     * @return	
     */	
    public boolean checkCurOutCard(A3PKRoomEnum.A3PK_CARD_TYPE opCardType, PKCurOutCardInfo curOutCard, int opPosId, List<Integer> opCards) {	
        // 没人操作，可以操作	
        if (curOutCard.getOutCardPos() == -1) {	
            return true;	
        }	
        // 检查是否自己操作的。如果是	
        if (opPosId == curOutCard.getOutCardPos()) {	
            return true;	
        }	
        // 如果类型一致，则比较类型的牌大小	
        if (curOutCard.getOutCardType() == opCardType.value()) {	
            return false;	
        } else if(opCardType.value() >= 6 && curOutCard.getOutCardType() >= 6) {	
            // 5张牌型的情况下	
            return opCardType.value() > curOutCard.getOutCardType();	
        }	
        return false;	
    }	
	
	
	
	
	
    /**	
     * 检查当前打出的牌	
     * 不可以直接出牌	
     *	
     * @param opCardType 操作牌型	
     * @param curOutCard 当前出牌信息	
     * @param opPosId    操作位置	
     * @param opCards    牌列表	
     * @return	
     */	
    public boolean checkNotCurOutCard(A3PKRoomEnum.A3PK_CARD_TYPE opCardType, PKCurOutCardInfo curOutCard, int opPosId, List<Integer> opCards) {	
        return !this.checkCurOutCard(opCardType, curOutCard, opPosId, opCards);	
    }	
	
    /**	
     * 检查是否存在2	
     *	
     * @return	
     */	
    public boolean checkExistA(List<Integer> opCards,boolean existA) {	
        return opCards.stream().anyMatch(k -> {	
            if (existA){	
                return BasePocker.getCardValue(k) == PKSpecialEnum.MAX_CARD_A_VALUE.value();	
            }	
            return A3PKRoomEnum.isTrump(k);	
        });	
    }	
	
	
	
	
}	
