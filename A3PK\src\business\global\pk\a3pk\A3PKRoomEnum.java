package business.global.pk.a3pk;	
	
import cenum.PKOpType;	
import com.ddm.server.common.utils.Lists;	
import jsproto.c2s.cclass.pk.BasePocker;	
import org.apache.commons.collections.CollectionUtils;	
	
import java.util.Collections;	
import java.util.List;	
import java.util.stream.Collectors;	
	
/**	
 * 长汀510K配置	
 *	
 * <AUTHOR>	
 */	
public class A3PKRoomEnum {	
    /**	
     * A 2 3	
     */	
    public static final List<Integer> CA23_LIST = Lists.newArrayList(0x03, 0x0E, 0x0F);	
    /**	
     * A 2 3	
     */	
    public static final List<Integer> C23_LIST = Lists.newArrayList(0x03, 0x0E, 0x0F);	
	
    /**	
     * 独食列表	
     */	
    public static final List<PKOpType> ChallengeList = Lists.newArrayList(PKOpType.Challenge, PKOpType.Surrender, PKOpType.Refuse);	
	
	
    /**	
     * 黑桃2	
     */	
    public final static int BLACK_2 = 0x3F;	
	
    /**	
     * 黑桃3	
     */	
    public final static int BLACK_3 = 0x33;	
	
    /**	
     * 黑桃A	
     */	
    public final static int BLACK_A = 0x3E;	
	
    /**	
     * 方块4	
     */	
    public final static int BLOCK_4 = 0x04;	
	
    /**	
     * 是否方块4	
     */	
    public final static boolean isBlock4(int cardId) {	
        return BasePocker.getCardValueEx(BLOCK_4) == BasePocker.getCardValueEx(cardId) && BasePocker.getCardColor(BLOCK_4) == BasePocker.getCardColor(cardId);	
    }	
	
    /**	
     * 是否黑桃3和黑桃A	
     */	
    public final static boolean isBlackAor3(boolean isKing, int cardId) {	
        if (BLACK_3 == cardId) {	
            // 黑桃3	
            return true;	
        }	
        if (isKing) {	
            // 黑桃2	
            return BLACK_2 == cardId;	
        } else {	
            // 黑桃A	
            return BLACK_A == cardId;	
        }	
	
    }	
	
	
    /**	
     * 转换值	
     *	
     * @param cardId	
     * @return	
     */	
    public static final int getCardValue(int cardId) {	
        int value = BasePocker.getCardValueEx(cardId);	
        if (value == 3) {	
            return 16;	
        }	
        return value;	
    }	
	
    /**	
     * 大小王为1组牌15	
     * 未计算过掩码值	
     */	
    public static boolean isTrump(int card) {	
        int cardValueEx = BasePocker.getCardValueEx(card);	
        return cardValueEx == 18 || cardValueEx == 17;	
    }	
	
    public static boolean isNotTrumpOrAEx(int card, boolean existA) {	
        if (existA && card == 14) {	
            return false;	
        }	
        if (card == 18 || card == 17) {	
            return false;	
        }	
        return true;	
    }	
	
	
    /**	
     * 比较牌id	
     * 值1 > 值2	
     *	
     * @param cardId1	
     * @param cardId2	
     * @return	
     */	
    public static final boolean compGtCardId(int cardId1, int cardId2) {	
        int cardValue1 = getCardValue(cardId1);	
        int cardValue2 = getCardValue(cardId2);	
        if (cardValue1 > cardValue2) {	
            return true;	
        }	
        if (cardValue1 == cardValue2) {	
            return BasePocker.getCardColor(cardId1) > BasePocker.getCardColor(cardId2);	
        }	
        return false;	
    }	
	
	
    /**	
     * 比较牌id	
     * 值1 <= 值2	
     *	
     * @param cardId1	
     * @param cardId2	
     * @return	
     */	
    public static final boolean compLeCardId(int cardId1, int cardId2) {	
        int cardValue1 = getCardValue(cardId1);	
        int cardValue2 = getCardValue(cardId2);	
        if (cardValue1 < cardValue2) {	
            return true;	
        }	
        if (cardValue1 == cardValue2) {	
            return BasePocker.getCardColor(cardId1) <= BasePocker.getCardColor(cardId2);	
        }	
        return false;	
    }	
	
    /**	
     * 可选玩法：顺子不带A,独食特殊算分；	
     */	
    public enum A3PKCfgEnum {	
        /**	
         * 顺子不带A	
         */	
        ShunZiBuDaiA,	
        /**	
         * 独食特殊算分	
         */	
        DuShiTeShuSuanFen,	
    }	
	
    /**	
     * 玩法：A3独食（不带大小王）,32牛鬼（带大小王）；	
     */	
    public enum A3PKWanfaEnum {	
        /**	
         * A3独食（不带大小王）	
         */	
        A3DuShi,	
        /**	
         * 32牛鬼（带大小王）	
         */	
        NiuGui32,;	
    }	
	
	
    public enum A3PKGameRoomConfigEnum {	
        /**	
         * 自动准备	
         */	
        ZiDongZhunBei,	
        /**	
         * 解散次数不超过5次	
         */	
        JieSanCishu5,	
        /**	
         * 小局10秒自动准备	
         */	
        XiaoJu10Miao,	
    }	
	
	
    /**	
     * 罚分	
     */	
    public enum A3PKFaFenEnum {	
        FA_5(5),	
        FA_10(10),	
        FA_20(20),	
        FA_50(50),	
        FA_NOT(0);	
        private int value;	
	
        A3PKFaFenEnum(int value) {	
            this.value = value;	
        }	
	
        public int getValue() {	
            return value;	
        }	
	
        public static int valueOf(int value) {	
            for (A3PKFaFenEnum faFenEnum : A3PKFaFenEnum.values()) {	
                if (faFenEnum.ordinal() == value) {	
                    return faFenEnum.getValue();	
                }	
            }	
            return A3PKFaFenEnum.FA_5.getValue();	
        }	
    }	
	
	
    /**	
     * 位置状态	
     *	
     * <AUTHOR>	
     */	
    public enum A3PK_POS_STATE {	
        NOT,	
        /**	
         * 胜利	
         */	
        WIN,	
        /**	
         * 失败	
         */	
        LOSE,	
        /**	
         * 完胜	
         */	
        COMPLETE_WIN,	
        /**	
         * 完败	
         */	
        COMPLETE_LOSE;	
    }	
	
	
    /**	
     * 牌类型	
     */	
    public enum A3PK_CARD_TYPE {	
        /**	
         * 默认状态	
         */	
        A3PK_CARD_TYPE_NOMARL(0),	
        /**	
         * 不出	
         */	
        A3PK_CARD_TYPE_BUCHU(1),	
        /**	
         * 单牌	
         */	
        A3PK_CARD_TYPE_SINGLECARD(2),	
        /**	
         * 对子	
         */	
        A3PK_CARD_TYPE_DUIZI(3),	
        /**	
         * 三张	
         */	
        A3PK_CARD_TYPE_3(4),	
        /**	
         * 四张	
         */	
        A3PK_CARD_TYPE_4(5),	
        /**	
         * 顺子	
         */	
        A3PK_CARD_TYPE_SHUNZI(6),	
        /**	
         * 同花	
         */	
        A3PK_CARD_TYPE_TONGHUA(7),	
        /**	
         * 3带2	
         */	
        A3PK_CARD_TYPE_3DAI2(8),	
        /**	
         * 4带1	
         */	
        A3PK_CARD_TYPE_4DAI1(9),	
        /**	
         * 同花顺	
         */	
        A3PK_CARD_TYPE_TONGHUASHUN(10),;	
        private int value;	
	
        A3PK_CARD_TYPE(int value) {	
            this.value = value;	
        }	
	
        public int value() {	
            return this.value;	
        }	
	
        public static A3PK_CARD_TYPE valueOf(int value) {	
            for (A3PK_CARD_TYPE flow : A3PK_CARD_TYPE.values()) {	
                if (flow.value == value) {	
                    return flow;	
                }	
            }	
            return A3PK_CARD_TYPE.A3PK_CARD_TYPE_NOMARL;	
        }	
    }	
	
	
    /**	
     * 本局结束顺序	
     */	
    public enum A3PK_SET_POS_END_TYPE {	
        NOT(0), // 默认	
        ONE(1), // 头	
        TWO(2), // 二	
        THREE(3), // 三	
        FOUR(4),// 四	
        ; // 蓝色	
        private int value;	
	
        private A3PK_SET_POS_END_TYPE(int value) {	
            this.value = value;	
        }	
	
        public int value() {	
            return this.value;	
        }	
	
        public static A3PK_SET_POS_END_TYPE valueOf(int value) {	
            for (A3PK_SET_POS_END_TYPE flow : A3PK_SET_POS_END_TYPE.values()) {	
                if (flow.value == value) {	
                    return flow;	
                }	
            }	
            return A3PK_SET_POS_END_TYPE.NOT;	
        }	
	
    }	
	
	
    /**	
     * 余干510K阵营	
     */	
    public enum A3PK_RANKS_TYPE {	
        NOT(0), // 默认	
        RED_RANKS(1), // 红色	
        BLUE_RANKS(2),; // 蓝色	
        private int value;	
	
        private A3PK_RANKS_TYPE(int value) {	
            this.value = value;	
        }	
	
        public int value() {	
            return this.value;	
        }	
	
    }	
	
    /**	
     * 获取指定key列表可能组成的所有顺子	
     *	
     * @param minValue 最小值	
     * @param maxValue 最大值	
     * @param minLian  最小连	
     * @param maxLian  最大连	
     * @param keyList  key列表	
     */	
    public static List<List<Integer>> getKeyListAllContinuous(int minValue, int maxValue, int minLian, int maxLian, List<Integer> keyList) {	
        List<List<Integer>> headLists = Lists.newArrayList();	
        // 最小值->最大值	
        for (int i = minValue; i <= maxValue; i++) {	
            // 最小顺->最大顺	
            for (int j = minLian; j <= maxLian; j++) {	
                List<Integer> head = Lists.newArrayList();	
                for (int k = 0; k < j; k++) {	
                    int card = i + k;	
                    if (card > 15) {	
                        card = card - 13;	
                    }	
                    if (keyList.contains(card)) {	
                        head.add(card);	
                    } else {	
                        break;	
                    }	
                }	
                if (CollectionUtils.isNotEmpty(head) && head.size() >= j) {	
                    headLists.add(head);	
                }	
	
            }	
        }	
        return headLists;	
    }	
	
	
    /**	
     * 获取顺子	
     *	
     * @param keyList 获取key列表	
     * @param minLian 最小顺子	
     * @return	
     */	
    public static List<List<Integer>>  getKeyList(List<Integer> keyList, int minLian, int maxLian) {	
        // 最小值	
        int minValue = keyList.get(keyList.size() - 1);	
        // 最大值	
        int maxValue = keyList.get(0);	
        List<List<Integer>> allKeyList =  getKeyListAllContinuous(minValue, maxValue, minLian, maxLian, keyList);	
        if (CollectionUtils.isNotEmpty(allKeyList)) {	
            return allKeyList.stream().filter(k -> !(k.get(0) > 10 && k.get(0) < 14)).collect(Collectors.toList());	
        }	
        return allKeyList;	
    }	
	
    /**	
     * 获取顺子	
     *	
     * @param keyList 获取key列表	
     * @param minLian 最小顺子	
     * @return	
     */	
    public static boolean existKeyList(List<Integer> keyList, int minLian, int maxLian) {	
        // 最小值	
        int minValue = keyList.get(keyList.size() - 1);	
        // 最大值	
        int maxValue = keyList.get(0);	
        List<List<Integer>> allKeyList = getKeyListAllContinuous(minValue, maxValue, minLian, maxLian, keyList);	
        // 无JQKA2牌型；无QKA23牌型； 无KA234牌型；	
        return allKeyList.stream().anyMatch(k->!(k.get(0) > 10 && k.get(0) < 14));	
    }	
	
	
    public static void main(String[] args) {	
        List<Integer> list = Lists.newArrayList(0x3A,0x3B,0x3C,0x3D,0x3E, 0x3F, 0x33, 0x34, 0x35).stream().map(k -> BasePocker.getCardValueEx(k)).collect(Collectors.toList());	
        System.out.println(getKeyListAllContinuous(3, 15, 5, 5, list));	
        System.out.println(BasePocker.getCardValueEx(55));	
	
    }	
	
	
}	
