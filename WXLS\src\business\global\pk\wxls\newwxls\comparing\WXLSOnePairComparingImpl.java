package business.global.pk.wxls.newwxls.comparing;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
	
import java.util.ArrayList;	
import java.util.List;	
import java.util.Map;
import java.util.stream.Collectors;

/**	
 * 一对的大小比较(先比对子, 再比三个单张)	
 */	
public class WXLSOnePairComparingImpl extends WXLSAbstractComparing {	
    @Override	
    public int compare(WXLSPlayerDun o1, WXLSPlayerDun o2) {	
        List<WXLSPockerCard> newcards1 = new ArrayList<WXLSPockerCard>();	
        List<WXLSPockerCard> newcards2 = new ArrayList<WXLSPockerCard>();	
        newcards1 = o1.getCards();	
        newcards2 = o2.getCards();	
        Map<Integer, Integer> p1CardMap = getCardsRankCountMap(newcards1);	
        Map<Integer, Integer> p2CardMap = getCardsRankCountMap(newcards2);	
        int ret = this.pairComparing(p1CardMap, p2CardMap, 2, 2);
        if(ret==0){
            List<WXLSPockerCard> oneCardList=o1.getCardsRankCountMapOneCardList().stream().sorted(WXLSPockerCard::compareTo).collect(Collectors.toList());
            List<WXLSPockerCard> oneCardTwo=o2.getCardsRankCountMapOneCardList().stream().sorted(WXLSPockerCard::compareTo).collect(Collectors.toList());
            return oneCardTwo.get(0).getType()-oneCardList.get(0).getType();
        }
        return ret;	
    }	
	
}	
	
