package core.network.client2game.handler.wxpdk;

import business.global.pk.wxpdk.WXPDKRoom;
import business.global.room.RoomMgr;
import business.global.room.base.AbsRoomPos;
import business.wxpdk.c2s.cclass.WXPDKRoom_RecordPosInfo;
import business.wxpdk.c2s.iclass.SWXPDK_SetInfo;
import business.wxpdk.c2s.iclass.SWXPDK_UserInfo;
import business.wxpdk.c2s.iclass.SWXPDK_XResult;
import business.player.Player;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;
import core.network.client2game.handler.PlayerHandler;
import jsproto.c2s.iclass.room.CBase_GetRoomInfo;

import java.io.IOException;
import java.util.List;

public class CWXPDKRoomXRecord extends PlayerHandler {

    @SuppressWarnings("rawtypes")
    @Override
    public void handle(Player player, WebSocketRequest request, String message) throws IOException {
        final CBase_GetRoomInfo req = new Gson().fromJson(message, CBase_GetRoomInfo.class);
        long roomID = req.getRoomID();

        WXPDKRoom room = (WXPDKRoom) RoomMgr.getInstance().getRoom(roomID);
        if (null == room){
            request.error(ErrorCode.NotAllow, "CHBMJRoomEndResult not find room:"+roomID);
            return;
        }
        SWXPDK_XResult result = new SWXPDK_XResult();
        for(AbsRoomPos roomPos: room.getRoomPosMgr().getPosList()){
            result.getUserInfo().put(roomPos.getPosID(),new SWXPDK_UserInfo(roomPos.getName(),roomPos.getPid(),roomPos.getPoint()));
            if(roomPos.getResults()!=null){
                List<Integer> pointList = ((WXPDKRoom_RecordPosInfo) roomPos.getResults()).getPointList();
                for(int i =0;i<pointList.size();i++){
                    if(result.getSetInfo().size()<i+1){
                        result.getSetInfo().add(new SWXPDK_SetInfo(i+1));
                    }
                    SWXPDK_SetInfo setInfo = result.getSetInfo().get(i);
                    setInfo.getPoint().put(roomPos.getPosID(),pointList.get(i));
                }
            }
        }
        request.response(result);
    }
}
