package core.network.client2game.handler.ahhbmj;						
						
import business.global.mj.ahhbmj.AHHBMJRoom;						
import business.global.mj.ahhbmj.AHHBMJRoomSet;						
import business.global.room.RoomMgr;						
import business.ahhbmj.c2s.cclass.AHHBMJResults;						
import business.player.Player;						
import com.ddm.server.websocket.def.ErrorCode;						
import com.ddm.server.websocket.handler.requset.WebSocketRequest;						
import com.google.gson.Gson;						
import core.network.client2game.handler.PlayerHandler;						
import jsproto.c2s.iclass.room.CBase_GetRoomInfo;						
						
import java.io.IOException;						
import java.util.HashMap;						
import java.util.List;						
import java.util.Map;						
import java.util.stream.Collectors;						
						
public class CAHHBMJRoomXRecord extends PlayerHandler {						
						
						
    @SuppressWarnings("rawtypes")						
    @Override						
    public void handle(Player player, WebSocketRequest request, String message) throws IOException {						
        final CBase_GetRoomInfo req = new Gson().fromJson(message, CBase_GetRoomInfo.class);						
        long roomID = req.getRoomID();						
						
        AHHBMJRoom room = (AHHBMJRoom) RoomMgr.getInstance().getRoom(roomID);						
        if (null == room) {						
            request.error(ErrorCode.NotAllow, "CAHHBMJRoomEndResult not find room:" + roomID);						
            return;						
        }						
        List<AHHBMJResults> results = room.getRoomPosMgr().getPosList().stream().filter(k -> k.getPid() > 0L && k.isPlayTheGame())						
                .map(k -> (AHHBMJResults) k.getResults()).filter(n -> n != null).collect(Collectors.toList());						
        request.response(results);						
    }						
}												
