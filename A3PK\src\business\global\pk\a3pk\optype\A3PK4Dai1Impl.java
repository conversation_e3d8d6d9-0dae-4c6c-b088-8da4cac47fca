package business.global.pk.a3pk.optype;	
	
import business.global.pk.AbsPKSetPos;	
import business.global.pk.PKCurOutCardInfo;	
import business.global.pk.PKOpCard;	
import business.global.pk.a3pk.A3PKRoomEnum;	
import com.ddm.server.common.CommLogD;	
import jsproto.c2s.cclass.pk.BasePocker;	
import org.apache.commons.collections.CollectionUtils;	
import org.apache.commons.collections.ListUtils;	
import org.apache.commons.collections.MapUtils;	
	
import java.util.Collections;	
import java.util.Comparator;	
import java.util.List;	
import java.util.Map;	
import java.util.stream.Collectors;	
	
/**	
 * 检查四带一牌型	
 */	
public class A3PK4Dai1Impl<T> extends A3PKBaseCardType<T> {	
	
    @Override	
    public boolean resultType(AbsPKSetPos mSetPos, List<Integer> privateCardList, PKOpCard opCard) {	
        PKCurOutCardInfo curOutCard = mSetPos.getSet().getCurOutCard();	
        if (opCard.getCardList().size() != 5) {	
            return false;	
        }	
        // 是否有重复的牌	
	
        Map<Integer, Long> map = opCard.getCardList().stream().collect(Collectors.groupingBy(k -> BasePocker.getCardValueEx(k), Collectors.counting()));	
        if (MapUtils.isEmpty(map)) {	
            CommLogD.error("A3PK4Dai1Impl map:{}",map.toString() );	
            return false;	
        }	
        int compCardId = 0;	
        for (Map.Entry<Integer, Long> entry : map.entrySet()) {	
            if (entry.getValue().intValue() == 4){	
                compCardId = opCard.getCardList().stream().filter(k->BasePocker.getCardValueEx(k) == entry.getKey()).max(Comparator.comparing(Integer::intValue)).orElse(0);	
            }	
        }	
        if (compCardId <= 0) {	
            return false;	
        }	
        if (checkNotCurOutCard(A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_4DAI1, curOutCard, mSetPos.getPosID(), opCard.getCardList()) && (A3PKRoomEnum.compLeCardId(compCardId, curOutCard.getCompValue()) || opCard.getCardList().size() != curOutCard.getCurOutCards().size())) {	
            // 不符合出牌规则	
            return false;	
        }	
        if(A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_4DAI1.value() < curOutCard.getOutCardType()) {	
            CommLogD.error("A3PK4Dai1Impl outCardType:{}", curOutCard.getOutCardType());	
            return false;	
        }	
        return curOutCard.setCurOutCards(mSetPos.getPosID(), A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_4DAI1.value(), opCard.getCardList(), compCardId);	
    }	
	
    @Override	
    public PKOpCard robotResultType(AbsPKSetPos mSetPos, PKCurOutCardInfo curOutCard, T item) {	
        Map<Integer, List<Integer>> map = (Map<Integer, List<Integer>>) item;	
        List<Integer> cardList = map.entrySet().stream().filter(k ->A3PKRoomEnum.compGtCardId(k.getValue().get(0),curOutCard.getCompValue()) && k.getValue().size() == 4).map(k -> k.getValue()).findFirst().orElse(Collections.emptyList());	
        if (CollectionUtils.isEmpty(cardList)) {	
            return null;	
        }	
        List<Integer> valueList = map.entrySet().stream().filter(k ->A3PKRoomEnum.compGtCardId(k.getValue().get(0),curOutCard.getCompValue()) && k.getValue().size() == 1).map(k -> k.getValue()).findFirst().orElse(Collections.emptyList());	
        if (CollectionUtils.isEmpty(valueList)) {	
            return null;	
        }	
        List<Integer> cardValueList = ListUtils.union(cardList, valueList);	
        return CollectionUtils.isNotEmpty(cardValueList) ? PKOpCard.OpCard(A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_4DAI1.value(), cardValueList) : null;	
    }	
}	
