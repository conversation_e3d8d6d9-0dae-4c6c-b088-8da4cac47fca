package business.wxls.c2s.cclass.newwxls;	
	
/**	
 * Enum {@code WXLSCardRankEnum} 扑克牌的数字.	
 */	
public enum WXLSCardRankEnum {	
    CARD_TWO(2),	
    CARD_THREE(3),	
    CARD_FOUR(4),	
    CARD_FIVE(5),	
    CARD_SIX(6),	
    CARD_SEVEN(7),	
    CARD_EIGHT(8),	
    CARD_NINE(9),	
    CARD_TEN(10),	
    CARD_JACK(11),	
    CARD_QUEUE(12),	
    CARD_KING(13),	
    CARD_ACE(14),	
	CARD_XGUI(15),	
	CARD_DGUI(16);	
	
    private Integer number;	
	
    WXLSCardRankEnum(Integer number) {	
        this.number = number;	
    }	
	
    public Integer getNumber() {	
        return number;	
    }	
	
    public void setNumber(Integer number) {	
        this.number = number;	
    }	
    	
    public static WXLSCardRankEnum valueOf(int value) {	
		 for (WXLSCardRankEnum flow : WXLSCardRankEnum.values()) {	
				if (flow.getNumber() == value) {	
					return flow;	
				}	
			}	
		 System.out.println("WXLSCardRankEnum not Card.........");	
		 return null;	
	 }  	
}	
