package business.wxmj.c2s.iclass;			
			
import jsproto.c2s.cclass.BaseSendMsg;			
			
			
public class SWXMJ_ChangePlayerNumAgree extends BaseSendMsg {			
    			
    /**			
	 * 			
	 */			
	private static final long serialVersionUID = 1L;			
	public long roomID;			
    public int pos;			
    public boolean agreeChange;			
    public static SWXMJ_ChangePlayerNumAgree make(long roomID, int pos, boolean agreeChange) {			
    	SWXMJ_ChangePlayerNumAgree ret = new SWXMJ_ChangePlayerNumAgree();			
        ret.roomID = roomID;			
        ret.pos = pos;			
        ret.agreeChange = agreeChange;			
        return ret;			
    			
			
    }			
}			
