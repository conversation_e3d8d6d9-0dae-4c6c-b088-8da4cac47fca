package business.global.pk.a3pk;	
	
import business.global.pk.AbsPKCalcPosEnd;	
	
	
public class A3PKCalcPosEnd extends AbsPKCalcPosEnd {	
    /**	
     * 玩家信息	
     */	
    private A3PKSetPos setPos;	
    /**	
     * 当局信息	
     */	
    private A3PKRoomSet roomSet;	
	
    public A3PKCalcPosEnd(A3PKSetPos setPos, A3PKRoomSet roomSet) {	
        this.setPos = setPos;	
        this.roomSet = roomSet;	
    }	
	
    /**	
     * 底分	
     */	
    private int diFen = 1;	
	
	
	
    /**	
     * 计算分数	
     */	
    public void calcPosEnd() {	
        getSetPos().setEndPoint(getSetPos().getEndPoint() + getSetPos().getDeductPoint());	
    }	
	
	
    public A3PKSetPos getSetPos() {	
        return setPos;	
    }	
	
    public void setSetPos(A3PKSetPos setPos) {	
        this.setPos = setPos;	
    }	
	
    public A3PKRoomSet getRoomSet() {	
        return roomSet;	
    }	
	
    public void setRoomSet(A3PKRoomSet roomSet) {	
        this.roomSet = roomSet;	
    }	
	
    public int getDiFen() {	
        return diFen;	
    }	
	
    public void setDiFen(int diFen) {	
        this.diFen = diFen;	
    }	
}	
