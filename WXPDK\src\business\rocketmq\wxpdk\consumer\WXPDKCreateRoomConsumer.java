package business.rocketmq.wxpdk.consumer;

import BaseCommon.CommLog;
import BaseThread.ThreadManager;
import business.global.sharegm.ShareNodeServerMgr;
import business.wxpdk.c2s.iclass.CWXPDK_CreateRoom;
import business.rocketmq.bo.MqAbsRequestBo;
import business.rocketmq.constant.MqTopic;
import business.rocketmq.consumer.BaseCreateRoomConsumer;
import cenum.PrizeType;
import com.ddm.server.annotation.Consumer;
import com.ddm.server.common.rocketmq.MqConsumerHandler;
import com.google.gson.Gson;
import core.server.wxpdk.WXPDKAPP;
import jsproto.c2s.cclass.room.BaseRoomConfigure;

/**
 * <AUTHOR> xush<PERSON>jun
 * create at:  2020-08-19  11:17
 * @description: 创建房间
 */
@Consumer(topic = MqTopic.BASE_CREATE_ROOM, id = WXPDKAPP.gameTypeId)
public class WXPDKCreateRoomConsumer extends BaseCreateRoomConsumer implements MqConsumerHandler {


    @Override
    public void action(Object body) throws ClassNotFoundException {
        MqAbsRequestBo mqAbsRequestBo = (MqAbsRequestBo) body;
        //判断游戏和请求创建节点一致
        if (mqAbsRequestBo.getGameTypeId() == WXPDKAPP.GameType().getId() && ShareNodeServerMgr.getInstance().checkCurrentNode(mqAbsRequestBo.getShareNode().getIp(), mqAbsRequestBo.getShareNode().getPort())) {
//            CommLog.info("创建房间[{}]", mqAbsRequestBo.getGameTypeName());
            final CWXPDK_CreateRoom clientPack = new Gson().fromJson(mqAbsRequestBo.getBody(),
                    CWXPDK_CreateRoom.class);
            // 公共房间配置
            BaseRoomConfigure<CWXPDK_CreateRoom> configure = new BaseRoomConfigure<CWXPDK_CreateRoom>(
                    PrizeType.RoomCard,
                    WXPDKAPP.GameType(),
                    clientPack.clone());
            super.action(body, WXPDKAPP.GameType().getId(), configure);
        }

    }
}
