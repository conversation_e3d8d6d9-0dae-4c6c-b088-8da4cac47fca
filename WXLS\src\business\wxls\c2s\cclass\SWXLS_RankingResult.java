package business.wxls.c2s.cclass;	
	
import business.wxls.c2s.cclass.entity.WXLSPlayerCardType;	
import business.wxls.c2s.cclass.entity.WXLSPlayerResult;	
import business.wxls.c2s.cclass.entity.WXLSRanking;	
import business.wxls.c2s.iclass.CWXLS_Ranked;	
	
import java.util.ArrayList;	
import java.util.List;	
	
/**	
 * 比牌结果	
 * 	
 * <AUTHOR> TODO 添加特殊牌结果，	
 */	
	
public class SWXLS_RankingResult {	
	public long zjid = 0;	
	public long beishu = 1;
	public String publicCard;
	// 所有玩家设置好的牌序	
	public List<CWXLS_Ranked> rankeds = new ArrayList<CWXLS_Ranked>();// 一个玩家，一条记录	
	
	// 第一轮PockerCard	
	public List<WXLSPlayerCardType> pCard1 = new ArrayList<WXLSPlayerCardType>();// 一个玩家，一条记录	
	public List<WXLSPlayerResult> pCardResult1 = new ArrayList<WXLSPlayerResult>();// 一个玩家，一条记录	
	
	// 第二轮PockerCard	
	public List<WXLSPlayerCardType> pCard2 = new ArrayList<WXLSPlayerCardType>();	
	public List<WXLSPlayerResult> pCardResult2 = new ArrayList<WXLSPlayerResult>();	
	
	// 第三轮PockerCard	
	public List<WXLSPlayerCardType> pCard3 = new ArrayList<WXLSPlayerCardType>();	
	public List<WXLSPlayerResult> pCardResult3 = new ArrayList<WXLSPlayerResult>();	
	
	// 打枪的	
	public List<WXLSRanking> killRankins = new ArrayList<WXLSRanking>();
	// 射箭（ 翻倍动画）
	public List<WXLSRanking> sheJianRankins = new ArrayList<WXLSRanking>();
	// 全垒打的	
	public WXLSPlayerResult fourbagger = new WXLSPlayerResult();;	
	// 特殊牌PockerCard	
	public List<WXLSPlayerCardType> specialPockerCard = new ArrayList<WXLSPlayerCardType>();	
	// 记录特殊牌的结果	
	public List<WXLSPlayerResult> specialResults = new ArrayList<WXLSPlayerResult>();// 没用？	
	
	// 没有打枪的结果	
	public List<WXLSPlayerResult> simPlayerResult = new ArrayList<WXLSPlayerResult>();	
	
	//   特殊牌的分数结果	
	public List<WXLSPlayerResult> specialResult = new ArrayList<WXLSPlayerResult>();	
	// 包括打枪的结果	
	public List<WXLSPlayerResult> simResults = new ArrayList<WXLSPlayerResult>();	
	// 总结算	
	public List<WXLSPlayerResult> playerResults = new ArrayList<WXLSPlayerResult>();	
		
	public void clean () {	
		if (null != this.rankeds) {	
			this.rankeds.clear();	
			this.rankeds = null;	
		}	
			
		if (null != this.pCard1) {	
			this.pCard1.clear();	
			this.pCard1 = null;	
		}	
		if (null != this.pCardResult1) {	
			this.pCardResult1.clear();	
			this.pCardResult1 = null;	
		}	
		if (null != this.pCard2) {	
			this.pCard2.clear();	
			this.pCard2 = null;	
		}	
		if (null != this.specialResult) {	
			this.specialResult.clear();	
			this.specialResult = null;	
		}	
		if (null != this.pCardResult2) {	
			this.pCardResult2.clear();	
			this.pCardResult2 = null;	
		}	
			
		if (null != this.pCard3) {	
			this.pCard3.clear();	
			this.pCard3 = null;	
		}	
		if (null != this.pCardResult3) {	
			this.pCardResult3.clear();	
			this.pCardResult3 = null;	
		}	
		if (null != this.killRankins) {	
			this.killRankins.clear();	
			this.killRankins = null;	
		}
		if (null != this.sheJianRankins) {
			this.sheJianRankins.clear();
			this.sheJianRankins = null;
		}
		if (null != this.fourbagger) {	
			this.fourbagger = null;	
		}	
			
		if (null != this.specialPockerCard) {	
			this.specialPockerCard.clear();	
			this.specialPockerCard = null;	
		}	
			
			
		if (null != this.specialResults) {	
			this.specialResults.clear();	
			this.specialResults = null;	
		}	
			
		if (null != this.simResults) {	
			this.simResults.clear();	
			this.simResults = null;	
		}	
			
		if (null != this.simResults) {	
			this.simResults.clear();	
			this.simResults = null;	
		}	
			
		if (null != this.playerResults) {	
			this.playerResults.clear();	
			this.playerResults = null;	
		}	
	}

	public String getPublicCard() {
		return publicCard;
	}

	public void setPublicCard(String publicCard) {
		this.publicCard = publicCard;
	}
}
