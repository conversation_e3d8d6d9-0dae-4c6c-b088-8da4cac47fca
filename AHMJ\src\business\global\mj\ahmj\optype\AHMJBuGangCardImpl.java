package business.global.mj.ahmj.optype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCard;
import business.global.mj.ahmj.AHMJSetPos;
import business.global.mj.manage.OpCard;
import cenum.mj.OpType;
import com.ddm.server.common.CommLogD;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

public class AHMJBuGangCardImpl implements OpCard {

    @Override
    public boolean checkOpCard(AbsMJSetPos mSetPos, int cardID) {
        AHMJSetPos jPos = (AHMJSetPos) mSetPos;
        if (jPos.isTing()) {
            return false;
        }
        boolean canBuGang = false;
        final List<Integer> pengList = mSetPos.getPublicCardList().stream().filter(k -> k.get(0) == OpType.Peng.value()).map(k -> k.get(2) / 100).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pengList)) {
            return false;
        }
        List<MJCard> cards = mSetPos.allCards();
        for (int i = 0, size = mSetPos.allCards().size(); i < size; i++) {
            if(pengList.contains(cards.get(i).type)){
                if(jPos.checkBaotingGang(cards.get(i).type)){
                    canBuGang = true;
                    jPos.getBuGangList().add(cards.get(i).type);
                }
            }
        }
        return canBuGang;
    }

    @Override
    public boolean doOpCard(AbsMJSetPos mSetPos, int cardID) {
        AHMJSetPos jPos = (AHMJSetPos) mSetPos;
        if (!jPos.getBuGangList().contains(cardID/100)) {
            return false;
        }
        if (doBuGang(cardID / 100, cardID,
                mSetPos.getPublicCardList(), mSetPos)) {
            return true;
        }
        return false;
    }

    // 点 补杠
    private boolean doBuGang(int type, int cardID,
                             List<List<Integer>> publicCardList, AbsMJSetPos mSetPos) {
        // 搜集碰
        int gangCard = 0;
        List<Integer> prePublicCard = null;
        for (int i = 0; i < publicCardList.size(); i++) {
            prePublicCard = publicCardList.get(i);
            if (prePublicCard.get(0) == OpType.Peng.value()) {
                if (prePublicCard.get(2) / 100 == type) {
                    gangCard = cardID;
                    break;
                }
            }
        }
        if (null == prePublicCard) {
            return false;
        }

        // peng -》gang
        if (null != prePublicCard) {
            if (gangCard == cardID) {
                prePublicCard.set(0, OpType.Gang.value());
                prePublicCard.add(cardID);
            } else {
                return false;
            }
        }
        // 找到手中对应牌类型的Id
        int id = mSetPos.allCards().stream().filter(k -> k.type == type).map(k -> k.cardID).findAny().orElse(0);
        if (id <= 0) {
            CommLogD.error("doBuGang allCards type:{},cardID:{}",type,cardID);
            return false;
        }
        if (mSetPos.getHandCard().cardID == id) {
            // 清理手牌
            mSetPos.cleanHandCard();
        } else {
            mSetPos.removePrivateCard(new MJCard(id));
            mSetPos.addPrivateCard(mSetPos.getHandCard());
            mSetPos.sortCards();
            mSetPos.cleanHandCard();
        }
        return true;
    }
}
