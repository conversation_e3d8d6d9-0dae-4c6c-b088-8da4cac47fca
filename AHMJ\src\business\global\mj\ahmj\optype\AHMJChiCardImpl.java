package business.global.mj.ahmj.optype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.AbsMJSetRoom;
import business.global.mj.MJCard;
import business.global.mj.ahmj.AHMJRoomSet;
import business.global.mj.ahmj.AHMJSetPos;
import business.global.mj.op.ChiCardNormalImpl;
import org.apache.commons.collections.CollectionUtils;

import java.util.Iterator;
import java.util.List;

/**
 * 检查吃
 *
 * <AUTHOR>
 */
public class AHMJChiCardImpl extends ChiCardNormalImpl {

	@Override public boolean checkOpCard(AbsMJSetPos mSetPos, int cardId) {
		if (((AHMJSetPos) mSetPos).isTing()) {
			return false;
		}
		boolean existChi = super.checkOpCard(mSetPos, cardId);
		if(existChi){
			List<MJCard> cardList = mSetPos.allCards();
			AHMJRoomSet set = (AHMJRoomSet)mSetPos.getSet();
			Iterator<List<Integer>> cursor = mSetPos.getPosOpNotice().getChiList().iterator();
			while (cursor.hasNext()){
				List<Integer> next = cursor.next();
				boolean allJin = cardList.stream().filter(n -> !next.contains(n)).allMatch(n -> set.getmJinCardInfo().checkJinExist(n.type));
				//吃完后都是金就不允许吃
				if(allJin){
					cursor.remove();
				}
			}
			if(CollectionUtils.isEmpty(mSetPos.getPosOpNotice().getChiList())){
				return false;
			}
			return true;
		}
		return false;
	}
}
