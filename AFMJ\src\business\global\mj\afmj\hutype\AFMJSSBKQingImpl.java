package business.global.mj.afmj.hutype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCardInit;
import business.global.mj.hu.abs.AbsSSBK;
import business.global.mj.afmj.AFMJRoomEnum.*;
import cenum.mj.MJCardCfg;
import com.ddm.server.common.utils.CommMath;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 七星十三烂：十三烂的基础上，且有七个字都有；
 * 不叠加十三烂算分；
 * 且字牌必须是真字牌，不能是精牌替代。（如果精是字牌，当本身是七星十三烂也算七星十三烂）
 */
public class AFMJSSBKQingImpl extends AbsSSBK {

    @Override
    public boolean checkHuCard(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
        if (mSetPos.sizePublicCardList() > 0) {
            return false;
        }
        if (null == mCardInit) {
            return false;
        }
        return checkSSBKNotBaiBanTiJin(mCardInit);
    }

    @Override
    public <T> Object checkHuCardReturn(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
        if (mSetPos.sizePublicCardList() > 0) {
            return AFMJOpPoint.Not;
        }
        if (null == mCardInit) {
            return AFMJOpPoint.Not;
        }
        if (checkSSBKNotBaiBanTiJin(mCardInit)) {
            return AFMJOpPoint.QiXingShiSanLan;
        }
        return AFMJOpPoint.Not;
    }

    /**
     * 检查风牌是否符合十三不靠
     *
     * @param cardList 风牌
     * @param totalJin 金总数
     * @return true:风牌符合十三不靠; false:true:风牌不符合十三不靠
     */
    public boolean checkFeng(List<Integer> cardList, int totalJin) {
        if (!CommMath.notHasSame(cardList))
            return false;
        int sizeCard = cardList.size();
        if (sizeCard == 7) {
            return true;
        } else if (sizeCard > 8) {
            return false;
        }
        for (int i = 1; i <= totalJin; i++) {
            int sCard = sizeCard + i;
            if (sCard == 7) {
                return true;
            }
        }
        return false;

    }


    /**
     * 检测十三不靠,无白板替金
     * @param mCardInit 麻将牌的初始信息
     * @return true:是十三不靠；false:不是十三不靠
     */
    public boolean checkSSBKNotBaiBanTiJin (MJCardInit mCardInit) {
        return checkSSBK(mCardInit.getAllCardInts(),mCardInit.getJins());
    }

    /**
     * 检查用户是否胡
     * @param allCards
     * @param jins
     * @return
     */
    public boolean checkSSBK (List<Integer> allCards,List<Integer> jins) {
        Map<Boolean, List<Integer>> partitioned = allCards
                .stream().collect(Collectors.partitioningBy(e -> e >= 40));
        if (null == partitioned) {
            return false;
        }
        // 检查风牌是否符合十三不靠
        int checkFengRt = checkFeng(partitioned.get(true), jins); // 替换字牌的金牌数
        if (checkFengRt < 0) {
            return false;
        }
        // 替换字牌后剩余金牌数
        int countLeftJins = jins.size() - checkFengRt;
        // 检查牌间距、花色
        if (checkCard(partitioned.get(false), countLeftJins)){
            return true;
        }
        return false;
    }

    /**
     * 检查风牌是否符合十三不靠
     *
     * @param cardList 风牌
     * @param jins 金列表
     * @return 0:风牌符合十三不靠,金不替换字牌; >0: 风牌符合十三不靠,替换字牌的金牌数;-1:true:风牌不符合十三不靠
     */
    public int checkFeng(List<Integer> cardList, List<Integer> jins) {
        // 风牌列表
        List<Integer> cardListTemp = new ArrayList<>(cardList);
        if (!CommMath.notHasSame(cardListTemp))
            return -1;
        int sizeCard = cardListTemp.size();
        if (sizeCard == 7) {
            return 0;
        } else if (sizeCard > 8) {
            return -1;
        }
        // 金牌替换字牌数
        int countJinTiZiPai = 0;

        for (int i = 0; i < jins.size(); i++) {
            // 检测金是否是字牌
            if(jins.get(i) < 41 || jins.get(i) > 47)continue;
            // 检测金是否当本身
            if(cardListTemp.contains(jins.get(i)))continue;
            // 更新风牌列表
            cardListTemp.add(jins.get(i));
            // 更新带金的风牌数
            sizeCard += 1;
            // 更新金牌替换字牌数
            countJinTiZiPai ++;
            if (sizeCard == 7) {
                return countJinTiZiPai;
            }
        }
        return -1;

    }

    /**
     * 检查牌间距、花色
     *
     * @param cardList 数牌
     * @param countLeftJins 替换字牌后剩余金牌数
     * @return true:牌间距、花色符合十三不靠；false：牌间距、花色不符合十三不靠
     */
    public boolean checkCard(List<Integer> cardList, int countLeftJins) {
        CommMath.getSort(cardList, false);
        // 万条筒三花色都要有；例：147万、147条、258筒
        boolean wanHuaSe = false; // 万花色
        boolean tiaoHuaSe = false; // 条花色
        boolean tongHuaSe = false; // 筒花色
        for (int i = 0, sizeI = cardList.size(); i < sizeI; i++) {
            if( MJCardCfg.WANG.value() ==cardList.get(i) / 10) wanHuaSe = true;
            if( MJCardCfg.TIAO.value() ==cardList.get(i) / 10) tiaoHuaSe = true;
            if( MJCardCfg.TONG.value() ==cardList.get(i) / 10) tongHuaSe = true;
        }
        if(!(wanHuaSe && tiaoHuaSe && tongHuaSe)){ // 少于万条筒3种花色
            // 金当没有的花色牌
            // 检测花色数
            int huaseShu = 0;
            if(wanHuaSe)huaseShu++;
            if(tiaoHuaSe)huaseShu++;
            if(tongHuaSe)huaseShu++;
            if(countLeftJins < 3 - huaseShu){
                return false;
            }
        }
        // 检测不同花色的间距
        for (int i = 0, sizeI = cardList.size(); i < sizeI; i++) {
            for (int j = i + 1, sizeJ = cardList.size(); j < sizeJ; j++) {
                if (hunYiSe(cardList.get(i) / 10) == hunYiSe(cardList.get(j) / 10) && cardList.get(i) - cardList.get(j) <= 2) {
                    return false;
                }
                break;
            }
        }
        return true;
    }


    public int hunYiSe(int cardType) {
        if (MJCardCfg.WANG.value() == cardType) {
            return MJCardCfg.WANG.value();
        } else if (MJCardCfg.TIAO.value() == cardType) {
            return MJCardCfg.TIAO.value();
        } else if (MJCardCfg.TONG.value() == cardType) {
            return MJCardCfg.TONG.value();
        } else if (MJCardCfg.FENG.value() == cardType) {
            return MJCardCfg.FENG.value();
        } else {
            return MJCardCfg.NOT.value();
        }
    }
}
