package core.network.client2game.handler.a3pk;	
	
import business.global.pk.a3pk.A3PKRoomEnum;	
import business.player.Player;	
import business.player.feature.PlayerClubRoom;	
import business.player.feature.PlayerUnionRoom;	
import business.a3pk.c2s.iclass.CA3PK_CreateRoom;	
import cenum.PrizeType;	
import com.ddm.server.websocket.def.ErrorCode;	
import com.ddm.server.websocket.handler.requset.WebSocketRequest;	
import com.google.gson.Gson;	
import core.network.client2game.handler.PlayerHandler;	
import core.server.a3pk.A3PKAPP;	
import jsproto.c2s.cclass.room.BaseRoomConfigure;	
	
import java.io.IOException;	
	
/**	
 * 亲友圈房间	
 * 	
 * <AUTHOR>	
 *	
 */	
public class CA3PKUnionRoom extends PlayerHandler {	
	
	@Override	
	public void handle(Player player, WebSocketRequest request, String message)	
			throws IOException {	
	
		final CA3PK_CreateRoom clientPack = new Gson().from<PERSON><PERSON>(message,	
				CA3PK_CreateRoom.class);	
	
	
		// 公共房间配置	
		BaseRoomConfigure<CA3PK_CreateRoom> configure = new BaseRoomConfigure<CA3PK_CreateRoom>(	
				PrizeType.RoomCard,	
				A3PKAPP.GameType(),	
				clientPack.clone());	
		player.getFeature(PlayerUnionRoom.class).createNoneUnionRoom(request,configure);	
	}	
}	
