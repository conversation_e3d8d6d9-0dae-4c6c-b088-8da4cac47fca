package business.global.mj.ahhbmj;

import business.ahhbmj.c2s.iclass.CAHHBMJ_CreateRoom;
import business.global.mj.AbsCalcPosEnd;
import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCardInit;
import business.global.mj.manage.MJFactory;
import cenum.mj.HuType;
import cenum.mj.MJEndType;
import cenum.mj.OpPointEnum;
import cenum.mj.OpType;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 算分
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class AHHBMJCalcPosEnd extends AbsCalcPosEnd {


    private AHHBMJSetPos pos = null;

    /**
     * 明杠
     */
    private int mingGangCount;
    /**
     * 暗杠次数
     */
    private int anGangCount;
    /**
     * 补杠次数
     */
    private int buGangCount;
    private int huaCount;

    private AHHBMJRoom<?> room;
    private AHHBMJRoomSet set;

    public AHHBMJCalcPosEnd(AHHBMJSetPos mSetPos) {
        super(mSetPos);
        this.pos = mSetPos;
        this.set = (AHHBMJRoomSet) mSetPos.getSet();
        this.room = (AHHBMJRoom<?>) mSetPos.getRoom();
    }

    @Override
    public int calcPoint(boolean isZhuang, Object... params) {
        int point = (int) params[0];
        return isZhuang ? point : Math.max(1, point - 2);
    }

    @Override
    public <T> void calcOpPointType(T opType, int count) {
        OpPointEnum opPoint = (OpPointEnum) opType;
        this.addhuType(opPoint, count, MJEndType.PLUS);
    }

    @Override
    public void calcPosEnd(AbsMJSetPos mSetPos) {
        mSetPos.setEndPoint(mSetPos.getEndPoint() + mSetPos.getDeductPoint());
    }

    @Override
    public void calcPosPoint(AbsMJSetPos mSetPos) {
        if (set.isGANG_SUI_HU_ZOU() && set.getMHuInfo().getHuPosList().contains(mSetPos.getPosID())) {
            calcGang();
        } else if (!set.isGANG_SUI_HU_ZOU()) {
            calcGang();
        }
        if (set.getMHuInfo().getHuPosList().contains(mSetPos.getPosID())) {
            MJCardInit mjCardInit = pos.mjCardInit(true);
            int multiple = this.calcHu(mjCardInit);
            this.calcJia(mjCardInit, multiple);
            this.calcZuo(multiple);
            this.calcLa(multiple);
            this.calcPao(multiple);
            if (getPos().getPosOpRecord().sizeHua() > 0) {
                calc1V3Op(OpPointEnum.Hua, getPos().getPosOpRecord().sizeHua());
            }
        }

    }

    private void calcJia(MJCardInit mjCardInit, int multiple) {
        pos.getPosOpRecord().getOpHuList().clear();
        MJFactory.getHuCard(AHHBMJNormalHuCardImpl.class).checkHuCardReturn(getPos(), mjCardInit, 0);
        int jiaPoint = 0;
        List<Object> opPointEnums = pos.getPosOpRecord().getOpHuList();
        for (Object opPointEnum : opPointEnums) {
            int point = pos.point((OpPointEnum) opPointEnum);
            huTypeMap.put((OpPointEnum) opPointEnum, point);
            jiaPoint += point;
        }
        jiaPoint *= multiple;
        if (pos.isZimo()) {
            calc1V3Op(OpPointEnum.JiaPai, jiaPoint);
        } else if (pos.getHuType().equals(HuType.QGH)) {
            CAHHBMJ_CreateRoom cfg = ((AHHBMJRoom) getRoom()).getCfg();
            if (cfg.getQiangganghu() == AHHBMJRoomEnum.QiangGangHu.BaoSanJia.ordinal()) {
                jiaPoint *= getRoom().getPlayerNum() - 1;
            }
            calc1V1Op(set.getLastOpInfo().getLastOpPos(), OpPointEnum.JiaPai, jiaPoint);
        } else if (pos.getHuType().equals(HuType.JiePao)) {
            calc1V1Op(set.getLastOpInfo().getLastOpPos(), OpPointEnum.JiaPai, jiaPoint);
        }
    }

    private void calcLa(int multiple) {
        if (!getRoom().RoomCfg(AHHBMJRoomEnum.KeXuanWanFa.ZUO_LA_PAO)) {
            return;
        }
        int nextPos;
        int point;
        AHHBMJSetPos mSetPos;
        //赢的是庄
        if (getPos().getPosID() == getSet().getDPos()) {
            if (pos.isZimo()) {
                for (int i = 1; i < getRoom().getPlayerNum(); i++) {
                    nextPos = (getPos().getPosID() + i) % this.set.getRoom().getPlayerNum();
                    mSetPos = (AHHBMJSetPos) getSet().getMJSetPos(nextPos);
                    if (mSetPos == null) {
                        continue;
                    }
                    point = mSetPos.getLaFen();
                    if (point <= 0) {
                        continue;
                    }
                    calc1V1Op(nextPos, OpPointEnum.PiaoFen, point * multiple);
                }
            } else {
                nextPos = getSet().getLastOpInfo().getLastOpPos();
                mSetPos = (AHHBMJSetPos) getSet().getMJSetPos(nextPos);
                if (mSetPos == null) {
                    return;
                }
                point = mSetPos.getLaFen();
                if (point <= 0) {
                    return;
                }
                calc1V1Op(nextPos, OpPointEnum.PiaoFen, point * multiple);
            }
        } else {
            if (!pos.isZimo() && getSet().getLastOpInfo().getLastOpPos() != getSet().getDPos()) {
                return;
            }
            point = getPos().getLaFen();
            calc1V1Op(getSet().getDPos(), OpPointEnum.PiaoFen, point * multiple);
        }

    }

    private void calcPao(int multiple) {
        if (!getRoom().RoomCfg(AHHBMJRoomEnum.KeXuanWanFa.ZUO_LA_PAO)) {
            return;
        }
        int nextPos;
        int point;
        AHHBMJSetPos mSetPos;
        //赢的是庄
        if (pos.isZimo()) {
            for (int i = 1; i < getRoom().getPlayerNum(); i++) {
                nextPos = (getPos().getPosID() + i) % this.set.getRoom().getPlayerNum();
                mSetPos = (AHHBMJSetPos) getSet().getMJSetPos(nextPos);
                if (mSetPos == null) {
                    continue;
                }
                point = mSetPos.getPaoFen() + pos.getPaoFen();
                calc1V1Op(nextPos, OpPointEnum.PaoFen, point * multiple);
            }
        } else {
            nextPos = getSet().getLastOpInfo().getLastOpPos();
            mSetPos = (AHHBMJSetPos) getSet().getMJSetPos(nextPos);
            if (mSetPos == null) {
                return;
            }
            point = getPos().getPaoFen() + mSetPos.getPaoFen();
            calc1V1Op(nextPos, OpPointEnum.PaoFen, point * multiple);
        }

    }

    private void calcZuo(int multiple) {
        if (!getRoom().RoomCfg(AHHBMJRoomEnum.KeXuanWanFa.ZUO_LA_PAO)) {
            return;
        }
        int nextPos;
        int point;
        AHHBMJSetPos mSetPos;
        //赢的是庄
        if (getPos().getPosID() == getSet().getDPos() && getPos().getZuoFen() > 0) {
            if (pos.isZimo()) {
                for (int i = 1; i < getRoom().getPlayerNum(); i++) {
                    nextPos = (getPos().getPosID() + i) % this.set.getRoom().getPlayerNum();
                    mSetPos = (AHHBMJSetPos) getSet().getMJSetPos(nextPos);
                    if (mSetPos == null) {
                        continue;
                    }
                    point = getPos().getZuoFen() * multiple;
                    calc1V1Op(nextPos, OpPointEnum.KanFen, point);
                }
            } else {
                nextPos = getSet().getLastOpInfo().getLastOpPos();
                mSetPos = (AHHBMJSetPos) getSet().getMJSetPos(nextPos);
                if (mSetPos == null) {
                    return;
                }
                point = getPos().getZuoFen() * multiple;
                calc1V1Op(nextPos, OpPointEnum.KanFen, point);
            }
        } else {
            if (!pos.isZimo() && getSet().getLastOpInfo().getLastOpPos() != getSet().getDPos()) {
                return;
            }
            nextPos = getSet().getDPos();
            mSetPos = (AHHBMJSetPos) getSet().getMJSetPos(nextPos);
            if (mSetPos == null || mSetPos.getZuoFen() <= 0) {
                return;
            }
            calc1V1Op(nextPos, OpPointEnum.KanFen, mSetPos.getZuoFen() * multiple);
        }

    }

    /**
     * 结算杠
     */
    private void calcGang() {
        this.getMSetPos().getPublicCardList().forEach(k -> {
            if (k.get(0) == OpType.AnGang.value()) {
                // 赢的分数计算。
                calc1V3Op(OpPointEnum.GangNum, 2);
                calcOpPointType(OpPointEnum.AnGang, 1);
            } else if (k.get(0) == OpType.Gang.value()) {
                calc1V3Op(OpPointEnum.GangNum, 1);
                calcOpPointType(OpPointEnum.Gang, 1);

            } else if (k.get(0) == OpType.JieGang.value()) {
                calc1V3Op(OpPointEnum.GangNum, 1);
                calcOpPointType(OpPointEnum.JieGang, 1);

            }

        });

    }


    @Override
    public <T> T getCalcPosEnd() {
        return (T) new CalcPosEnd(this.huTypeMap);
    }


    private class CalcPosEnd {
        @SuppressWarnings("unused")
        private Map<OpPointEnum, Integer> huTypeMap = new HashMap<>();

        /**
         * 输的的不加
         *
         * @param huTypeMap
         */
        public CalcPosEnd(Map<OpPointEnum, Integer> huTypeMap) {
            for (OpPointEnum opPointEnum : huTypeMap.keySet()) {
                if (opPointEnum == OpPointEnum.Not) {
                    continue;
                }
                //坐拉跑
//                if (opPointEnum == OpPointEnum.KanFen || opPointEnum == OpPointEnum.PiaoFen || opPointEnum == OpPointEnum.PaoFen) {
//                    continue;
//                }
                //杠 胡 加
                if (opPointEnum == OpPointEnum.Hu || opPointEnum == OpPointEnum.GangNum || opPointEnum == OpPointEnum.JiaPai) {
                    continue;
                }
                this.huTypeMap.put(opPointEnum, huTypeMap.get(opPointEnum));
            }
        }

    }

    /**
     * 计算胡芬
     */
    private int calcHu(MJCardInit mjCardInit) {
        MJFactory.getHuCard(AHHBMJNormalHuCardImpl.class).checkHuCardReturn(getPos(), mjCardInit);
        List<Object> opPointEnums = pos.getPosOpRecord().getOpHuList();
        int multiple = 1;
        int diFen = 1;
        for (Object opPointEnum : opPointEnums) {
            int point = pos.point((OpPointEnum) opPointEnum);
            huTypeMap.put((OpPointEnum) opPointEnum, point);
            multiple *= point;
        }
        if (multiple == 0) {
            multiple = 1;
        }
        if (getPos().getPosID() == set.getDPos()) {
            diFen++;
        }
        int huPoint = diFen * multiple;
        if (pos.isZimo()) {
            calc1V3Op(OpPointEnum.Hu, huPoint);
            if (getPos().getPosID() != set.getDPos()) {
                calc1V1Op(set.getDPos(), OpPointEnum.Hu, huPoint);
            }
        } else if (pos.getHuType().equals(HuType.JiePao)) {
            calc1V1Op(set.getLastOpInfo().getLastOpPos(), OpPointEnum.Hu, huPoint);
            if (set.getLastOpInfo().getLastOpPos() == set.getDPos()) {
                calc1V1Op(set.getDPos(), OpPointEnum.Hu, huPoint);
            }

        } else if (pos.getHuType().equals(HuType.QGH)) {
            CAHHBMJ_CreateRoom cfg = ((AHHBMJRoom) getRoom()).getCfg();
            if (cfg.getQiangganghu() == AHHBMJRoomEnum.QiangGangHu.BaoSanJia.ordinal()) {
                huPoint *= getRoom().getPlayerNum() - 1;
            }
            calc1V1Op(set.getLastOpInfo().getLastOpPos(), OpPointEnum.Hu, huPoint);
            if (set.getLastOpInfo().getLastOpPos() == set.getDPos()) {
                calc1V1Op(set.getDPos(), OpPointEnum.Hu, huPoint);
            }

        }
        return multiple;
    }


    public AHHBMJSetPos getPos() {
        return pos;
    }

    public void setPos(AHHBMJSetPos pos) {
        this.pos = pos;
    }

    public int getMingGangCount() {
        return mingGangCount;
    }

    public void setMingGangCount(int mingGangCount) {
        this.mingGangCount = mingGangCount;
    }

    public int getAnGangCount() {
        return anGangCount;
    }

    public void setAnGangCount(int anGangCount) {
        this.anGangCount = anGangCount;
    }

    public AHHBMJRoom<?> getRoom() {
        return room;
    }

    public void setRoom(AHHBMJRoom<?> room) {
        this.room = room;
    }

    public AHHBMJRoomSet getSet() {
        return set;
    }

    public void setSet(AHHBMJRoomSet set) {
        this.set = set;
    }
}	
