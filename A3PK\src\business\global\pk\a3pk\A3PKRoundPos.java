package business.global.pk.a3pk;	
	
import business.global.pk.AbsPKRoundPos;	
import business.global.pk.AbsPKSetRound;	
import business.global.pk.PKOpCard;	
import cenum.PKOpType;	
import cenum.mj.TryEndRoundEnum;	
import cenum.pk.PKOpCardError;	
import com.ddm.server.common.CommLogD;	
import com.ddm.server.websocket.def.ErrorCode;	
import com.ddm.server.websocket.handler.requset.WebSocketRequest;	
import lombok.Data;	
import org.apache.commons.collections.CollectionUtils;	
import java.util.List;	
import java.util.Objects;	
	
	
/**	
 * 长汀510K 一个round回合中，可能同时等待多个pos进行操作，eg:抢杠胡	
 *	
 * <AUTHOR>	
 */	
@Data	
public class A3PKRoundPos extends AbsPKRoundPos {	
    private A3PKSetPos cSetPos;	
    private A3PKRoomSet roomSet;	
    public A3PKRoundPos(AbsPKSetRound round, int opPos) {	
        super(round, opPos);	
        this.cSetPos = (A3PKSetPos) getPos();	
        this.roomSet = (A3PKRoomSet) this.set;	
    }	
	
	
    /**	
     * 操作打牌	
     *	
     * @param request 连接信息	
     * @param opType  操作类型	
     * @param mOpCard 操作参数	
     * @return	
     */	
    public int opOutCard(WebSocketRequest request, PKOpType opType, PKOpCard mOpCard) {	
        // 操作错误	
        if (errorOpType(request, opType) <= 0) {	
            return PKOpCardError.ERROR_OP_TYPE.value();	
        }	
        if (Objects.isNull(mOpCard)) {	
            CommLogD.error("list:{}",cSetPos.getPrivateCards() );	
        }	
        if (!cSetPos.checkCardList(mOpCard.getCardList())) {	
            request.error(ErrorCode.NotAllow, "not find checkCardList cardType:{%d},cardList:{%s}", mOpCard.getOpValue(), CollectionUtils.isEmpty(mOpCard.getCardList()) ? "[]" : mOpCard.getCardList().toString());	
            return PKOpCardError.CHECK_OP_TYPE_ERROR.value();	
        }	
        if (!cSetPos.doOpType(mOpCard, opType)) {	
            request.error(ErrorCode.NotAllow, "not find doOpType cardType:{%d},cardList:{%s}", mOpCard.getOpValue(), CollectionUtils.isEmpty(mOpCard.getCardList()) ? "[]" : mOpCard.getCardList().toString());	
            return PKOpCardError.CHECK_OP_TYPE_ERROR.value();	
        }	
        if (!cSetPos.outCard(mOpCard)) {	
            request.error(ErrorCode.NotAllow, "not find outCard cardType:{%d},cardList:{%s}", mOpCard.getOpValue(), CollectionUtils.isEmpty(mOpCard.getCardList()) ? "[]" : mOpCard.getCardList().toString());	
            return PKOpCardError.CHECK_OP_TYPE_ERROR.value();	
        }	
        this.getRound().setSetEnd(cSetPos.checkSetEnd());	
        if (this.roomSet.getChallengePos() == this.opPos) {	
            this.roomSet.setChallengeOp(PKOpType.Refuse);	
        }	
        return exeCardAction(opType);	
    }	
	
    /**	
     * 过	
     *	
     * @param request 连接请求	
     * @param opType  动作类型	
     * @return	
     */	
    public int opPass(WebSocketRequest request, PKOpType opType, PKOpCard mOpCard) {	
        // 操作错误	
        if (errorOpType(request, opType) <= 0) {	
            return PKOpCardError.ERROR_OP_TYPE.value();	
        }	
        if (!cSetPos.doOpType(mOpCard, opType)) {	
            request.error(ErrorCode.NotAllow, "not find opPass opType:{%s}", opType);	
            return PKOpCardError.CHECK_OP_TYPE_ERROR.value();	
        }	
        if (this.roomSet.getChallengePos() == this.opPos) {	
            this.roomSet.setChallengeOp(PKOpType.Refuse);	
        }	
        // 执行操作	
        return exeCardAction(opType);	
    }	
	
    /**	
     * 摆牌	
     *	
     * @param request 连接请求	
     * @param opType  动作类型	
     * @return	
     */	
    public int opChallenge(WebSocketRequest request, PKOpType opType) {	
        // 操作错误	
        if (errorOpType(request, opType) <= 0) {	
            return PKOpCardError.ERROR_OP_TYPE.value();	
        }	
        if (!cSetPos.doOpType(null, opType)) {	
            request.error(ErrorCode.NotAllow, "not find opPass opType:{%s}", opType);	
            return PKOpCardError.CHECK_OP_TYPE_ERROR.value();	
        }	
        // 设置独食操作	
        this.setChallengeOp(opType);	
        // 执行操作	
        return exeCardAction(opType);	
    }	
	
	
    /**	
     * 投降	
     *	
     * @param request 连接请求	
     * @return	
     */	
    public int opSurrender(WebSocketRequest request, PKOpType opType) {	
        // 操作错误	
        if (errorOpType(request, opType) <= 0) {	
            return PKOpCardError.ERROR_OP_TYPE.value();	
        }	
        if (!cSetPos.doOpType(null, opType)) {	
            request.error(ErrorCode.NotAllow, "not find opPass opType:{%s}", opType);	
            return PKOpCardError.CHECK_OP_TYPE_ERROR.value();	
        }	
        // 操作类型	
        this.getRound().setSetEnd(true);	
        // 设置独食操作	
        this.setChallengeOp(opType);	
        // 执行操作	
        return exeCardAction(opType);	
    }	
	
    /**	
     * 放弃、拒绝	
     *	
     * @param request 连接请求	
     * @param opType  动作类型	
     * @return	
     */	
    public int opRefuse(WebSocketRequest request, PKOpType opType) {	
        // 操作错误	
        if (errorOpType(request, opType) <= 0) {	
            return PKOpCardError.ERROR_OP_TYPE.value();	
        }	
        if (!cSetPos.doOpType(null, opType)) {	
            request.error(ErrorCode.NotAllow, "not find opPass opType:{%s}", opType);	
            return PKOpCardError.CHECK_OP_TYPE_ERROR.value();	
        }	
        // 设置独食操作	
        this.setChallengeOp(opType);	
        // 执行操作	
        return exeCardAction(opType);	
    }	
	
    /**	
     * 设置独食操作	
     * @param opType 操作类型	
     */	
    public void  setChallengeOp(PKOpType opType) {	
        this.roomSet.setChallengeOp(opType);	
        // 移除操作列表	
        this.receiveOpTypes.removeAll(A3PKRoomEnum.ChallengeList);	
        this.setOpType(CollectionUtils.isEmpty(this.receiveOpTypes) ? opType:null);	
        if (Objects.nonNull(getOpType())) {	
            this.tryEndRound(TryEndRoundEnum.ALL_WAIT);	
        }	
    }	
	
	
    @Override	
    public int op(WebSocketRequest request, PKOpType opType, PKOpCard mOpCard) {	
        if (Objects.nonNull(this.getOpType())) {	
            request.error(ErrorCode.NotAllow, "opPos has opered");	
            return PKOpCardError.REPEAT_EXECUTE.value();	
        }	
        int opCardRet = -1;	
	
        switch (opType) {	
            case Out:	
                opCardRet = this.opOutCard(request, opType, mOpCard);	
                break;	
            case Pass:	
                opCardRet = this.opPass(request, opType, mOpCard);	
                break;	
            case Challenge:	
                opCardRet = this.opChallenge(request, opType);	
                break;	
            case Surrender:	
                opCardRet = this.opSurrender(request, opType);	
                break;	
            case Refuse:	
                opCardRet = this.opRefuse(request, opType);	
                break;	
	
        }	
        request.response();	
        return opCardRet;	
    }	
	
    @Override	
    protected int getCardOpPos(PKOpType opType, int cardID) {	
        return 0;	
    }	
	
    @Override	
    public boolean isEnd(PKOpType opType, AbsPKSetRound round) {	
        return false;	
    }	
	
	
    /**	
     * 本回合位置操作 添加动作类型列表	
     *	
     * @param receiveOpTypes	
     */	
    @Override	
    public void addOpType(List<PKOpType> receiveOpTypes) {	
        if (CollectionUtils.isEmpty(receiveOpTypes)) {	
            return;	
        }	
        this.receiveOpTypes.addAll(receiveOpTypes);	
        this.publicWait = this.receiveOpTypes.contains(PKOpType.Out);	
    }	
	
    /**	
     * 本回合位置操作 添加动作类型	
     *	
     * @param opType	
     */	
    @Override	
    public void addOpType(PKOpType opType) {	
        if (Objects.isNull(opType)) {	
            return;	
        }	
        this.receiveOpTypes.add(opType);	
        this.publicWait = this.receiveOpTypes.contains(PKOpType.Out);	
    }	
	
	
}	
