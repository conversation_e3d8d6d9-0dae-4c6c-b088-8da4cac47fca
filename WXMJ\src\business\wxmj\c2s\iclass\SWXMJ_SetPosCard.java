package business.wxmj.c2s.iclass;			
			
import java.util.ArrayList;			
import java.util.List;			
			
import jsproto.c2s.cclass.BaseSendMsg;			
			
/**			
 * 重新设置玩家手牌			
 * <AUTHOR>			
 * @param <T>			
 *			
 */			
public class SWXMJ_SetPosCard<T> extends BaseSendMsg  {			
    public long roomID;			
	// 每个玩家的牌面			
	public List<T> setPosList = new ArrayList<>();			
			
			
    public static <T> SWXMJ_SetPosCard make(long roomID, List<T> setPosList) {			
    	SWXMJ_SetPosCard ret = new SWXMJ_SetPosCard();			
        ret.roomID = roomID;			
        ret.setPosList = setPosList;			
			
        return ret;			
    			
			
    }			
}			
