package business.afmj.c2s.iclass;

import jsproto.c2s.iclass.room.SBase_PosLeave;

/**
 * 位置离开通知
 * 
 * <AUTHOR>
 *
 */
@SuppressWarnings("serial")
public class SAFMJ_PosLeave extends SBase_PosLeave {

	public static SAFMJ_PosLeave make(SBase_PosLeave posLeave) {
		SAFMJ_PosLeave ret = new SAFMJ_PosLeave();
		ret.setRoomID(posLeave.getRoomID());
		ret.setPos(posLeave.getPos());
		ret.setBeKick(posLeave.isBeKick());
		ret.setOwnerID(posLeave.getOwnerID());
		ret.setKickOutTYpe(posLeave.getKickOutTYpe());
		ret.setMsg(posLeave.getMsg());
		return ret;
	}
}