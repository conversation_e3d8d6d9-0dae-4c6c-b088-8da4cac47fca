package business.global.mj.wwmj;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCardInit;
import business.global.mj.manage.MJFactory;
import business.global.mj.template.MJTemplateCalcPosEnd;
import cenum.mj.HuType;
import cenum.mj.MJEndType;
import cenum.mj.OpPointEnum;
import cenum.mj.OpType;

import java.util.List;
import java.util.Set;

/**
 * 模板麻将
 */
public class WWMJCalcPosEnd extends MJTemplateCalcPosEnd {

    public WWMJCalcPosEnd(AbsMJSetPos mSetPos) {
        super(mSetPos);
        huTypeMap.put(OpPointEnum.GangNum, 0);
        huTypeMap.put(OpPointEnum.HuPoint, 0);
    }


    @Override
    public void calcPosPoint(AbsMJSetPos mSetPos) {
        //如果不是胡牌的位置	
        Set<Integer> huPosList = getMSetPos().getSet().getMHuInfo().getHuPosList();
        if (huPosList.isEmpty() && getMSetPos().getRoom().RoomCfg(WWMJRoomEnum.KeXuanWanFa.HuangZhuangHuangGang)) {
            return;
        }
        if (mSetPos.getPosID() == mSetPos.getSet().getDPos()) {
            huTypeMap.put(OpPointEnum.Zhuang, 2);
        }
        if (huPosList.contains(getMSetPos().getPosID())) {
            calcHu();
        }
        calcGang();

    }

    /**
     * 结算杠
     */
    private void calcGang() {
        if (getMSetPos().getRoom().getRoomCfg().duizi == WWMJRoomEnum.DuiZi.BuDai.ordinal()) {
            for (List<Integer> k : this.getMSetPos().getPublicCardList()) {
                if (k.get(0) == OpType.AnGang.value()) {
                    calcOpPointType(OpPointEnum.AnGang, 1);
                    calc1V3Op(OpPointEnum.GangNum, 2);
                } else if (k.get(0) == OpType.Gang.value()) {
                    calcOpPointType(OpPointEnum.Gang, 1);
                    calc1V3Op(OpPointEnum.GangNum, 1);
                } else if (k.get(0) == OpType.JieGang.value()) {
                    calcOpPointType(OpPointEnum.JieGang, 1);
                    calc1V1Op(k.get(1), OpPointEnum.GangNum, 1);
                }
            }
        }
    }

    /**
     * 计算胡牌类型
     */

    protected void calcHu() {
        WWMJSetPos mSetPos = getMSetPos();
        MJCardInit mjCardInit = mSetPos.mjCardInit(true);
        List<Object> opHuList = mSetPos.getPosOpRecord().getOpHuList();
        MJFactory.getHuCard(WWMJNormalHuCardImpl.class).checkHuCardReturn(mSetPos, mjCardInit);
        OpPointEnum pointEnum;
        int point;
        int duiZiCount = 0;
        int calcPoint = 0;
        int multiple = 1;
        for (Object object : opHuList) {
            pointEnum = (OpPointEnum) object;
            point = point(pointEnum);
            if (point >= 0) {
                calcPoint += point;
                calcOpPointType(pointEnum, point);
            } else {
                if (getMSetPos().getRoom().getRoomCfg().duizi == WWMJRoomEnum.DuiZi.DuiZiFanBei.ordinal()) {
                    multiple *= 2;
                    calcOpPointType(OpPointEnum.DuanDui, 2, MJEndType.MULTIPLY);
                    continue;
                } else if (getMSetPos().getRoom().getRoomCfg().duizi == WWMJRoomEnum.DuiZi.DaiDuiZi.ordinal()) {
                    calcPoint -= point;
                    calcOpPointType(OpPointEnum.PengBanZi, -point);
                    continue;
                }
            }
        }
        if (getMSetPos().getHuType().equals(HuType.ZiMo)) {
            multiple *= 2;
            calcOpPointType(OpPointEnum.ZiMo, 2);
        }
        //总分
        calcPoint *= multiple;
        int lastOpPos = mSetPos.getSet().getLastOpInfo().getLastOpPos();
        if (calc1v1() && getMSetPos().getRoom().getRoomCfg().dianpao == WWMJRoomEnum.DianPao.YiJiaFu.ordinal()) {
            calc1V1Op(lastOpPos, OpPointEnum.HuPoint, calcPoint);
            if (lastOpPos == getMSetPos().getSet().getDPos() || getMSetPos().getPosID() == getMSetPos().getSet().getDPos()) {
                calc1V1Op(lastOpPos, OpPointEnum.HuPoint, 2 * multiple);
            }
        } else {

            calc1V3Op(OpPointEnum.HuPoint, calcPoint);
            if (getMSetPos().getPosID() == getMSetPos().getSet().getDPos()) {
                calc1V3Op(OpPointEnum.HuPoint, 2 * multiple);
            } else {
                calc1V1Op(getMSetPos().getSet().getDPos(), OpPointEnum.HuPoint, 2 * multiple);
            }
        }

    }


    public WWMJSetPos getMSetPos() {
        return (WWMJSetPos) super.getMSetPos();
    }

    @Override
    protected int point(OpPointEnum opPointEnum) {
        int point;
        switch (opPointEnum) {
            case GSKH://杠开
            case HeJueZhang://绝张
            case QuanDa://全老
            case QuanXiao://全小
            case CCHDDHu://十通
            case QYS:
            case HYS:
            case QYSYTL://顶九卡五
                point = 4;
                break;
            case Hu:
            case XiaoYuWu://十小
            case DaYuWu://十老
            case LianLiu:///六通
            case WuFanHe://五通
            case DanYou://八支
            case Long://顶九
            case MenQianQing://门清
            case DuanYao://门清
            case SiGuiYi://四核
            case QueYiSe://四核
                point = 1;
                break;
            case Zhuang://门清
            case TianHu://
            case DiHu://
            case YaDang://牙当
            case CHDDHu://9通
            case HDDHu://8通
            case QDHu://7通
            case SYZhi://11支
            case MenQing://万事
            case PPH://万事
            case ShuangYou://万事
                point = 2;
                break;
            default:
                if (opPointEnum.equals(OpPointEnum.PengBanZi)) {
                    point = 1;
                } else if (opPointEnum.equals(OpPointEnum.AnKe)) {
                    point = 2;
                } else if (opPointEnum.equals(OpPointEnum.AnGang)) {
                    point = 4;
                } else if (opPointEnum.equals(OpPointEnum.JieGang) || opPointEnum.equals(OpPointEnum.Gang)) {
                    point = 3;
                } else {
                    point = 0;
                }
                //如果是翻倍的标记负数
                if (getMSetPos().getRoom().getRoomCfg().duizi == WWMJRoomEnum.DuiZi.DuiZiFanBei.ordinal()) {
                    point *= -1;
                }
                break;
        }

        return point;
    }

    @Override
    public void calcPosEnd(AbsMJSetPos mSetPos) {
        calcPosEndYiKao();
    }
}
