## 淮北麻将新增协议接口##

### 胡分 花牌：8张；——2022.06.14增加

    GSKH(1),// 杠上开花	
	GSP(1),// 杠放
    ZiMo(1), // 自摸
    QGH(1), // 抢杠胡
    DiHu(1), // 地胡
    TianHu(1), // 天胡
    SSL(1),    //十三不靠
    QDHu(1),    //七对胡
    BianJiaDiao(1),//夹子
    DanTing(1),       //报胡
    QueYiSe(1),//缺一
    JieGang(1), // 直杠
    Gang(1), // 续杠
    AnGang(1), // 暗杠
    JiePao(1),// 炮胡
    PaoFen(1),//跑
    PiaoFen(1),//拉
    KanFen(1),//坐
    QXSSL(1),//七星十三不靠
    SSY(1),//十三幺
    Hua(1),//花
    PingHu(1), // 平胡

### 操作

    Chi(6), // 吃	
    Out(7),// 出牌	
    Peng(2),//碰	
    Gang(3),//|碰杠	
    JieGang(4),//直杠	
    AnGang(5),//暗杠	
    <PERSON>(27),//听	
    <PERSON>(1) ,//胡	
    <PERSON>u(9), // 抢杠胡	
    BaoTing(146),//报听
    Piao_Fen(129), //下坐 ：按钮“不下坐”、“1坐”、“2坐” 点的时候值发 0，1，2
    Fan(95), // 下拉 按钮“不下拉”、“1拉”、“2拉”  点的时候值发 0，1，2
    Pao(132), // 下跑 按钮“不下跑”、“1跑”、“2跑” 点的时候值发 0，1，2

### AHHBMJResults 总结算

    private int zhuangPoint;//坐庄次数						
    private boolean DianPaoWang = false;//点炮王						
    private boolean winner = false;//大赢家						
    // 胡次数					
    private int huCnt = 0;					
    // 胡类型列表					
    private List<HuType> huTypes = new ArrayList<HuType>();					
    // 点炮					
    private int dianPaoPoint = 0;					
    // 接炮					
    private int jiePaoPoint = 0;					
    // 自摸					
    private int ziMoPoint = 0;	

### AHHBMJSet_Pos 每局的玩家位置信息

    public boolean isTing;//报听
	private int zuoFen=-1; //-1:不显示 0：“不下坐”、1：“1坐”、2：“2坐
	private int laFen=-1;//-1:不显示 0：“不下拉”、1：“1拉”、2：“2拉
	private int paoFen=1;//-1:不显示 0：“不下跑”、1：“1跑”、2：“2跑

### AHHBMJRoom_PosEnd 每局的玩家位置结算信息

    public boolean isTing;//报听
	private int zuoFen=-1; //-1:不显示 0：“不下坐”、1：“1坐”、2：“2坐
	private int laFen=-1;//-1:不显示 0：“不下拉”、1：“1拉”、2：“2拉
	private int paoFen=1;//-1:不显示 0：“不下跑”、1：“1跑”、2：“2跑



	
   							
  								
