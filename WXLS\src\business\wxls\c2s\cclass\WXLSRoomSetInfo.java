package business.wxls.c2s.cclass;	
	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;
import business.wxls.c2s.iclass.CWXLS_Ranked;
import cenum.room.SetState;	
import jsproto.c2s.cclass.room.RoomSetInfo;	
	
import java.util.ArrayList;	
import java.util.List;	
	
/**	
 * 自由扑克 当前局游戏信息	
 * <AUTHOR>	
 *	
 */	
public class WXLSRoomSetInfo extends RoomSetInfo {	
		
	public long roomID = 0; // 房间ID	
	public SetState state = SetState.Init; // 游戏状态	
	public long setStartTime = 0;	
	public long setCurrentTime = 0;	
	public String mapai = "";	
	public long backerPos =0;	
	public  int beishu=0;	
	public boolean isXiPai = false;	
	public boolean isPlaying = false;	
	public int zhuangJia=-1;	
	public List<WXLSRoomSet_Pos> posInfo = new ArrayList<WXLSRoomSet_Pos>(); // 一局玩家列表	
	public List<WXLSRoomSet_End> posEndInfo = new ArrayList<>(); // 一局玩家列表	
	public List<WXLSSet_Pos> setPosList = new ArrayList<>();	
	public WXLSRoomSet_End setEnd = new WXLSRoomSet_End();	
	public CWXLS_Ranked ranked=new CWXLS_Ranked();
	public String publicCard;//公共牌

	public String getPublicCard() {
		return publicCard;
	}

	public void setPublicCard(String publicCard) {
		this.publicCard = publicCard;
	}
}
