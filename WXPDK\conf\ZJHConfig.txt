#底注bottomPoint =[1,2,5,10];#顶注topPoint =[5,10];#可比轮数comporeCount = [2,3,4];#炸金花底分配置difenList = [0,100,200,500];#机器人看牌的概率robotOpenCard = 80;#机器人棋牌的概率robotQiPai = 50;#机器人加注的概率robotJiaZhu = 20;#加注等级robotJiaZhuList = [1,2,3,4,5];#喜钱陪数xiQianBeiShu=[3,5];#全压顶注的5倍addScoreAll = 5;#轮数lunshushangxian=[10,20,30];#public final static int ZJH_DUIZI		=101;		//对子#public final static int ZJH_SHUNZI		=102;		//顺子#public final static int ZJH_JINHUA		=103;		//金华#public final static int ZJH_SHUNJIN	=104;		//顺金#public final static int ZJH_PAOZI      =105;  		//豹子#public final static int ZJH_TESHU      =106;  		//特殊#