package business.a3pk.c2s.cclass;	
	
public class A3PKUpdatePointItem {	
    private int prizePoint;	
    private int k510Point;	
    private int posID = -1;	
    private long pid = -1;	
    private int ranksType = 0; // 1:红方,2：蓝方	
	
	
    public A3PKUpdatePointItem(int posID, long pid,int prizePoint,int k510Point,int ranksType) {	
        this.prizePoint = prizePoint;	
        this.k510Point = k510Point;	
        this.posID = posID;	
        this.pid = pid;	
        this.ranksType = ranksType;	
    }	
	
    public int getPrizePoint() {	
        return prizePoint;	
    }	
	
    public void setPrizePoint(int prizePoint) {	
        this.prizePoint = prizePoint;	
    }	
	
    public int getPosID() {	
        return posID;	
    }	
	
    public void setPosID(int posID) {	
        this.posID = posID;	
    }	
	
    public long getPid() {	
        return pid;	
    }	
	
    public void setPid(long pid) {	
        this.pid = pid;	
    }	
	
    public int getK510Point() {	
        return k510Point;	
    }	
	
    public void setK510Point(int k510Point) {	
        this.k510Point = k510Point;	
    }	
	
    public int getRanksType() {	
        return ranksType;	
    }	
	
    public void setRanksType(int ranksType) {	
        this.ranksType = ranksType;	
    }	
}	
