package business.global.mj.ahhbmj.optype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCardInit;
import business.global.mj.ahhbmj.AHHBMJNormalHuCardImpl;
import business.global.mj.ahhbmj.AHHBMJSetPos;
import business.global.mj.manage.MJFactory;
import business.global.mj.op.JieGangCardImpl;
import cenum.mj.MJSpecialEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * 检查暗杠
 *
 * <AUTHOR>
 */
public class AHHBMJJieGangCardImpl extends JieGangCardImpl {

    @Override
    public boolean checkOpCard(AbsMJSetPos mSetPos, int cardID) {
        int type = cardID / 100;
        if (mSetPos.getSet().getmJinCardInfo().checkJinExist(type)) {
            return false;
        }

        long count = mSetPos.allCards().stream()
                // 筛选出所有的牌类型			
                .map(k -> k.getType())
                // 检查不等于金牌			
                .filter(k -> k == type)
                // 按牌类型分组			
                .count();
        if (count >= 3) {
            AHHBMJSetPos set_pos = (AHHBMJSetPos) mSetPos;
            set_pos.getGangList().clear();
            if (set_pos.isTing()) {
                if (checkGangHouTing(mSetPos, mSetPos.mjCardInit(false), type)) {
                    set_pos.getGangList().add(cardID / 100);
                    return true;
                }
            } else {
                return true;
            }
        }
        return false;
    }

    /**
     * 检测杠玩 是否还能听牌
     *
     * @return
     */
    public boolean checkGangHouTing(AbsMJSetPos mSetPos, MJCardInit mCardInit, int type) {
        List<Integer> cards = new ArrayList<>();
        for (int card : mCardInit.getAllCardInts()) {
            if (card == type) {
                cards.add(card);
            }
        }
        mCardInit.getAllCardInts().removeAll(cards);
        mCardInit.getJins().add(MJSpecialEnum.NOT_JIN.value());
        if (MJFactory.getHuCard(AHHBMJNormalHuCardImpl.class).checkHuCard(mSetPos, mCardInit)) {
            return true;
        }
        return false;
    }

}									
