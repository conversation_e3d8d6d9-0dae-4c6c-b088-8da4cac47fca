package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
	
import java.util.ArrayList;	
import java.util.List;	
	
public class WXLSRankingFacade implements WXLSIRanking {	
    private final static List<WXLSIRanking> rankings = new ArrayList<WXLSIRanking>();	
	
    static {
        rankings.add(new WXLSDuZhaRankingImpl_T());
        rankings.add(new WXLSYBLBLHRankingImpl_T());	
        rankings.add(new WXLSYTiaoLongRankingImpl_T());
        rankings.add(new WXLSShiErHuangZuRankingImpl_T());
        rankings.add(new WXLSSanTongHuaShunRankingImpl_T());	
        rankings.add(new WXLSQDaRankingImpl_T());
        rankings.add(new WXLSQXiaoRankingImpl_T());	
        rankings.add(new WXLSQuanHeiRankingIpml_T());	
        rankings.add(new WXLSQuanHongRankingIpml_T());	
        rankings.add(new WXLSSTaoSanTiaoRankingipml_T());	
        rankings.add(new WXLSWDuiYiKeRankingImpl_T());	
        rankings.add(new WXLSLDuiBanRankingImpl_T());	
        rankings.add(new WXLSSanTongHuaRankingImpl_T());	
        rankings.add(new WXLSSShunZiRankingImpl_T());	
        rankings.add(new WXLSQuanHongYiDianHeiRankingImpl_T());	
        rankings.add(new WXLSQuanHeiYiDianHongRankingImpl_T());	
        rankings.add(new WXLSShiQiRankingImpl_T());	
        rankings.add(new WXLSJiuQiRankingImpl_T());	
        rankings.add(new WXLSBaQiRankingImpl_T());	
        rankings.add(new WXLSQiQiRankingImpl_T());	
        rankings.add(new WXLSLiuQiRankingImpl_T());	
        rankings.add(new WXLSBanDaRankingImpl_T());	
        rankings.add(new WXLSBanXiaoRankingImpl_T());
        rankings.add(new WXLSDuDuiRankingImpl());
        rankings.add(new WXLSDuSanRankingImpl());

        rankings.add(new WXLSStraightFlushRankingImpl());	
        rankings.add(new WXLSFourOfTheKindRankingImpl());	
        rankings.add(new WXLSFullHouseRankingImpl());	
        rankings.add(new WXLSFlushRankingImpl());	
        rankings.add(new WXLSStraightRankingImpl());	
        rankings.add(new WXLSThreeOfTheKindRankingImpl());	
        rankings.add(new WXLSTwoPairsRankingImpl());	
        rankings.add(new WXLSOnePairRankingImpl());	
        rankings.add(new WXLSHighCardRankingImpl());	
        rankings.add(new WXLSDefaultRankingImpl());	
    }	
	
	
    private WXLSRankingFacade(){	
    }	
	
    private static class RankingFacadeHolder {	
        private final static WXLSRankingFacade instance = new WXLSRankingFacade();	
    }	
	
    public static WXLSRankingFacade getInstance() {	
        return RankingFacadeHolder.instance;	
    }	
	
	
	
    @Override	
    public WXLSRankingResult resolve(WXLSPlayerDun player) {	
        WXLSRankingResult result = null;	
        for (WXLSIRanking ranking : WXLSRankingFacade.rankings) {	
            result = ranking.resolve(player);	
            if (result != null) {	
                return result;	
            }	
        }	
        return result;	
    }	
    public List<WXLSRankingResult> getSpecial(WXLSPlayerDun player) {	
        List<WXLSRankingResult> wxlsRankingResults=new ArrayList<>();	
        for (WXLSIRanking ranking : WXLSRankingFacade.rankings) {	
            WXLSRankingResult result = null;	
            result = ranking.resolve(player);	
            if (result != null&&result.getRankingEnum().getPriority()>=50) {
                wxlsRankingResults.add(result);	
            }	
        }	
        return wxlsRankingResults;	
    }	
}	
