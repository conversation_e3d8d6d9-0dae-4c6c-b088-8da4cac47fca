package business.a3pk.c2s.iclass;	
	
import jsproto.c2s.cclass.BaseSendMsg;	
	
/**	
 * 位置继续游戏通知	
 * 	
 * <AUTHOR>	
 *	
 */	
@SuppressWarnings("serial")	
public class SA3PK_PosContinueGame extends BaseSendMsg {	
	// 房间ID	
	private long roomID;	
	// 位置	
	private int pos;	
	
	public static SA3PK_PosContinueGame make(long roomID, int pos) {	
		SA3PK_PosContinueGame ret = new SA3PK_PosContinueGame();	
		ret.setRoomID(roomID);	
		ret.setPos(pos);	
		return ret;	
	}	
	
	public long getRoomID() {	
		return roomID;	
	}	
	
	public void setRoomID(long roomID) {	
		this.roomID = roomID;	
	}	
	
	public int getPos() {	
		return pos;	
	}	
	
	public void setPos(int pos) {	
		this.pos = pos;	
	}	
	
}	
