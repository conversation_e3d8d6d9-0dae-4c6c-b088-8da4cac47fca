package business.global.pk.a3pk.optype;	
	
	
import business.global.pk.AbsPKSetPos;	
import business.global.pk.PKCurOutCardInfo;	
import business.global.pk.PKFactory;	
import business.global.pk.PKOpCard;	
import business.global.pk.a3pk.A3PKRoom;	
import business.global.pk.a3pk.A3PKRoomEnum;	
import business.global.pk.a3pk.A3PKRoomSet;	
import org.apache.commons.collections.CollectionUtils;	
	
import java.util.Collections;	
import java.util.List;	
import java.util.Map;	
import java.util.Objects;	
import java.util.stream.Collectors;	
	
	
/**	
 * 检查单张牌型	
 */	
public class A3PKSingleCardImpl<T> extends A3PKBaseCardType<T> {	
	
    @Override	
    public boolean resultType(AbsPKSetPos mSetPos, List<Integer> privateCardList, PKOpCard opCard) {	
        PKCurOutCardInfo curOutCard = mSetPos.getSet().getCurOutCard();	
        if (opCard.getCardList().size() != 1) {	
            return false;	
        }	
        // 先比较牌	
        int compCardId = opCard.getCardList().get(0);	
        if (checkNotCurOutCard(A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_SINGLECARD, curOutCard, mSetPos.getPosID(), opCard.getCardList()) && (A3PKRoomEnum.compLeCardId(compCardId, curOutCard.getCompValue()) || opCard.getCardList().size() != curOutCard.getCurOutCards().size())) {	
            // 不符合出牌规则	
            return false;	
        }	
        return curOutCard.setCurOutCards(mSetPos.getPosID(), A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_SINGLECARD.value(), opCard.getCardList(), compCardId);	
    }	
	
    @Override	
    public PKOpCard robotResultType(AbsPKSetPos mSetPos, PKCurOutCardInfo curOutCard, T item) {	
        A3PKRoomSet roomSet = (A3PKRoomSet) mSetPos.getSet();	
        if (roomSet.isAtFirstHu()) {	
            List<Integer> cards = mSetPos.getPrivateCards().stream().filter(k->A3PKRoomEnum.isBlock4(k)).collect(Collectors.toList());	
            return CollectionUtils.isNotEmpty(cards) ? PKOpCard.OpCard(A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_SINGLECARD.value(), cards) : null;	
        }	
	
        Map<Integer, List<Integer>> map = (Map<Integer, List<Integer>>) item;	
        List<Integer> cardList = map.entrySet().stream().filter(k ->A3PKRoomEnum.compGtCardId(k.getValue().get(0),curOutCard.getCompValue()) && k.getValue().size() == 1).map(k -> k.getValue()).findFirst().orElse(Collections.emptyList());	
        return CollectionUtils.isNotEmpty(cardList) ? PKOpCard.OpCard(A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_SINGLECARD.value(), cardList) : null;	
    }	
}	
