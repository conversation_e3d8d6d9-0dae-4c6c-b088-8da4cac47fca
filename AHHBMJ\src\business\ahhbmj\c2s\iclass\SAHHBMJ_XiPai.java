package business.ahhbmj.c2s.iclass;						
						
import cenum.ClassType;						
import jsproto.c2s.cclass.BaseSendMsg;						
						
						
public class SAHHBMJ_XiPai extends BaseSendMsg {						
    /**						
     *						
     */						
    private static final long serialVersionUID = 1L;						
    public long roomID;						
    public long pid;						
    public ClassType cType;						
						
    public static SAHHBMJ_Xi<PERSON>ai make(long roomID, long pid, ClassType cType) {						
        SAHHBMJ_XiPai ret = new SAHHBMJ_XiPai();						
        ret.roomID = roomID;						
        ret.pid = pid;						
        ret.cType = cType;						
        return ret;						
						
						
    }						
}												
