package business.global.pk.wxls.newwxls.comparing;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
	
import java.util.Map;	
	
/**	
 * 两对的大小比较(先比较第一对, 再比较第二对, 再比较单牌)	
 */	
public class WXLSTwoPairsComparingImpl extends WXLSAbstractComparing {	
	
    @Override	
    public int compare(WXLSPlayerDun o1, WXLSPlayerDun o2) {	
        Map<Integer, Integer> p1CardMap = o1.getCardsRankCountMap();	
        Map<Integer, Integer> p2CardMap = o2.getCardsRankCountMap();	
        int ret = this.pairComparing(p1CardMap, p2CardMap, 2, 3);
        if(ret==0){
            return o1.getCardsRankCountMapOneCard()-o2.getCardsRankCountMapOneCard();
        }
        return ret;	
    }	
	
}	
