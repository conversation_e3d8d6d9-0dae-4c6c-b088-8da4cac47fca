package business.ahmj.c2s.cclass;

import cenum.mj.HuType;
import jsproto.c2s.cclass.room.AbsBaseResults;

/**
 * 总结算
 */

public class AHMJResults extends AbsBaseResults {
    // 暗杠次数
    private int anGangPoint;
    // 杠次数
    private int gangPoint;

    public void setGangPoint(int gangPoint) {
        this.gangPoint = gangPoint;
    }

    public void setAnGangPoint(int anGangPoint) {
        this.anGangPoint = anGangPoint;
    }

    public int getAnGangPoint() {
        return anGangPoint;
    }

    public int getGangPoint() {
        return gangPoint;
    }

    @Override
    public void addJiePaoPoint(HuType hType) {
        if (HuType.JiePao.equals(hType)||HuType.QGH.equals(hType)) {
            this.setJiePaoPoint(this.getJiePaoPoint()+1);
        }
    }
}
