package business.ahmj.c2s.iclass;

import cenum.mj.OpType;
import jsproto.c2s.cclass.BaseSendMsg;

import java.util.ArrayList;
import java.util.List;

public class SAHMJ_PosOpCard<T> extends BaseSendMsg {

	public long roomID;
	public int pos;
	public T set_Pos;
	public OpType opType;
	public int opCard;
	public boolean isFlash;
	public List<Integer> outFlipCardList;//杠后翻开的牌列表（没人能胡或者不胡，丢弃到杠的人的outCard）
	public int outPos;//打牌的位置
	public List<Integer> outCardList = new ArrayList<>();//打牌人所有打出的牌

	public static <T> SAHMJ_PosOpCard make(long roomID, int pos, T set_Pos, OpType opType, int opCard,
										   boolean isFlash, List<Integer> outFlipCardList) {
		SAHMJ_PosOpCard ret = new SAHMJ_PosOpCard();
		ret.roomID = roomID;
		ret.pos = pos;
		ret.set_Pos = set_Pos;
		ret.opType = opType;
		ret.opCard = opCard;
		ret.isFlash = isFlash;
		ret.outFlipCardList = outFlipCardList;
		return ret;
	}

	public static <T> SAHMJ_PosOpCard make(long roomID, int pos, T set_Pos, OpType opType, int opCard,
										   boolean isFlash, List<Integer> outFlipCardList, int outPos, List<Integer> outCardList) {
		SAHMJ_PosOpCard make = make(roomID, pos, set_Pos, opType, opCard, isFlash, outFlipCardList);
		make.outPos = outPos;
		make.outCardList = outCardList;
		return make;
	}

}
