package business.global.mj.ahmj;

import business.global.mj.AbsMJSetPos;
import business.global.mj.AbsMJSetRound;
import business.global.mj.robot.MJRobotOpCard;
import business.global.mj.set.MJOpCard;
import cenum.mj.HuType;
import cenum.mj.MJCEnum;
import cenum.mj.MJOpCardError;
import cenum.mj.OpType;
import com.ddm.server.common.utils.CommMath;
import com.ddm.server.websocket.handler.requset.WebSocketRequestDelegate;

import java.util.List;

public class AHMJRobotOpCard extends MJRobotOpCard {
    public AHMJRobotOpCard(AbsMJSetRound setRound) {
        super(setRound);
    }

    /**
     * 机器人摸打
     * @param mSetPos
     * @param posID
     */
    @Override
    public boolean moDa(AbsMJSetPos mSetPos,int posID){
        if(getSet().getRoom().isMoDa()){
            boolean existYao= mSetPos.getPosOpRecord().getOpList().stream().anyMatch(n -> OpType.Hu.equals(n));
            if(existYao){
                this.getSetRound().opCard(new WebSocketRequestDelegate(), posID, OpType.Hu, MJOpCard.OpCard(0));
                return true;
            }
            boolean existPass = mSetPos.getPosOpRecord().getOpList().stream().anyMatch(n -> n==OpType.Pass);
            if(existPass){
                this.getSetRound().opCard(new WebSocketRequestDelegate(), posID, OpType.Pass, MJOpCard.OpCard(0));
                return true;
            }
            boolean existSQPass = mSetPos.getPosOpRecord().getOpList().stream().anyMatch(n -> n==OpType.SQPass);
            if(existSQPass){
                this.getSetRound().opCard(new WebSocketRequestDelegate(), posID, OpType.SQPass, MJOpCard.OpCard(0));
                return true;
            }
            boolean existOut = mSetPos.getPosOpRecord().getOpList().stream().anyMatch(n -> n==OpType.Out);
            if(existOut){
                this.getSetRound().opCard(new WebSocketRequestDelegate(), posID, OpType.Out, MJOpCard.OpCard(mSetPos.getSetPosRobot().getAutoCard2()));
                return true;
            }
        }
        return false;
    }
}
