package business.global.mj.wxmj;

import business.global.mj.AbsMJSetPos;
import business.global.mj.AbsMJSetPosMgr;
import business.global.mj.AbsMJSetRoom;
import business.global.mj.MJCard;
import business.global.mj.hu.NormalHuCardImpl;
import business.global.mj.manage.MJFactory;
import business.global.mj.op.BuHuaImpl;
import cenum.mj.FlowerEnum;
import cenum.mj.OpType;
import jsproto.c2s.cclass.mj.NextOpType;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class WXMJSetPosMgr extends AbsMJSetPosMgr {

    public WXMJSetPosMgr(AbsMJSetRoom set) {
        super(set);
    }

    @Override
    public void startSetApplique() {
        // 标识是否有开局补花
        List<Integer> list = new ArrayList<Integer>();
        boolean isStartSetApplique = false;
        AbsMJSetPos mPos = null;
        for (int i = 0; i < this.set.getPlayerNum(); i++) {
            int index = (this.set.getDPos() + i) % this.set.getPlayerNum();
            mPos = this.set.getMJSetPos(index);
            if (null == mPos) {
                continue;
            }
            if (MJFactory.getOpCard(BuHuaImpl.class).checkOpCard(mPos, FlowerEnum.PRIVATE.ordinal())) {
                isStartSetApplique = true;

            }
        }

        if (isStartSetApplique) {
            // 存在开局补花。
            startSetApplique();
        } else {
            return;
        }
    }

    /**
     * 检查动作类型。
     *
     * @param curOpPos  当前操作位置ID
     * @param curCardID 当前操作牌ID
     * @param opType    动作类型
     */
    @Override
    public void checkOpType(int curOpPos, int curCardID, OpType opType) {
        // 清空所有动作类型操作		
        this.cleanAllOpType();
        switch (opType) {
            case Out:
                this.checkOutOpType(curOpPos, curCardID);
                break;
            case Gang:
                checkOpTypeQGH(curOpPos, curCardID);
                break;
            default:
                break;
        }
    }

    /**
     * 下个动作类型。
     *
     * @param opType
     * @return
     */
    @Override
    public NextOpType exeCardAction(OpType opType) {
        NextOpType nOpType = null;
        switch (opType) {
            case Out:
            case TianHu:
            case Gang:
            case TianTing:
                nOpType = opAllMapOutCard();
                break;
            default:
                break;
        }
        return nOpType;
    }

    /**
     * 检测可以抢杠胡的操作者
     *
     * @param curOpPos  操作者位置
     * @param curCardID 操作牌
     * @return
     */
    @Override
    public void check_QiangGangHu(int curOpPos, int curCardID) {
        WXMJSetPos setPos = null;
        for (int i = 1; i < this.set.getRoom().getPlayerNum(); i++) {
            int nextPos = (curOpPos + i) % this.set.getRoom().getPlayerNum();
            setPos = (WXMJSetPos) this.set.getMJSetPos(nextPos);
            setPos.getPosOpRecord().clearOpHuList();
            setPos.clearMCardInit();// 加上抽上来的牌		
            if (setPos.checkOpType(curCardID, OpType.Hu)) {
                this.addOpTypeInfo(nextPos, OpType.QiangGangHu);
            }
        }
    }

    /**
     * 检查是否存在一炮多响
     */
    @Override
    protected boolean checkExistYPDX() {
        return true;
    }

    /**
     * 检查是否存在吃牌
     */
    @Override
    protected boolean checkExistChi() {
        return false;
    }

    /**
     * 检查是否存在接杠补杠 T:接杠时可选择碰后再补杠。
     *
     * @return
     */
    @Override
    protected boolean checkExistJGBG() {
        return false;
    }

    /**
     * 检查是否存在平胡
     *
     * @return
     */
    @Override
    protected boolean checkExistPingHu() {
        return false;
    }


    @Override
    protected void check_otherPingHu(int curOpPos, int curCardID) {
        WXMJSetPos setPos = null;
        for (int i = 1; i < this.set.getRoom().getPlayerNum(); i++) {
            int nextPos = (curOpPos + i) % this.set.getRoom().getPlayerNum();
            setPos = (WXMJSetPos) this.set.getMJSetPos(nextPos);
            OpType oType = setPos.checkPingHu(curOpPos, curCardID);
            if (!OpType.Not.equals(oType)) {
                this.addOpTypeInfo(nextPos, oType);
            }
        }
    }

    @Override
    protected void check_LowerChi(int curOpPos, int curCardID) {// chi		
        int nextPos = (curOpPos + 1) % this.set.getRoom().getPlayerNum();
        WXMJSetPos ssetPos = (WXMJSetPos) this.set.getMJSetPos(nextPos);
        Integer sum = 0;
        if (ssetPos.isTing) {
            return;
        }
        for (MJCard i : ssetPos.getPrivateCard()) {
            if (curCardID / 100 == i.getType()) {
                sum += 1;
            }
        }
        if (ssetPos.getPrivateCard().size() - sum <= 2) {
            return;
        }
        if (ssetPos.checkOpType(curCardID, OpType.Chi)) {
            // 添加动作信息		
            this.addOpTypeInfo(nextPos, OpType.Chi);
            return;
        }
    }

    @Override
    protected void check_otherJieGang(int curOpPos, int curCardID) {
        for (int i = 1; i < this.set.getRoom().getPlayerNum(); i++) {
            int nextPos = (curOpPos + i) % this.set.getRoom().getPlayerNum();
            WXMJSetPos ssetPos = (WXMJSetPos) this.set.getMJSetPos(nextPos);
            // 检查接杠动作		
            if (ssetPos.checkOpType(curCardID, OpType.JieGang)) {
                if (ssetPos.isTing) {
                    boolean nengangang = false;
                    Map<Integer, List<MJCard>> cardTypeList = ssetPos.allCards().stream().collect(Collectors.groupingBy(MJCard::getType, Collectors.toList()));
                    for (Map.Entry<Integer, List<MJCard>> ccc : cardTypeList.entrySet()) {
                        if (ccc.getValue().size() == 4) {
                            List<MJCard> cards = new ArrayList<>(ssetPos.allCards());
                            cards.removeAll(ccc.getValue());
                            if (MJFactory.getHuCard(NormalHuCardImpl.class).checkHuCard(ssetPos, ssetPos.mCardInit(cards, 0, false))) {
                                ssetPos.getTingGang().add(ccc.getKey());
                                nengangang = true;
                            }
                        }
                    }
                    if (nengangang) {
                        this.addOpTypeInfo(nextPos, OpType.JieGang);
                        if (!this.checkExistJGBG()) {
                            // 接杠时不可选择碰后再补杠。
                            this.set.getLastOpInfo().addBuGang(curCardID / 100, OpType.JieGang);
                        }
                    }
                } else {
                    this.addOpTypeInfo(nextPos, OpType.JieGang);
                    if (!this.checkExistJGBG()) {
                        // 接杠时不可选择碰后再补杠。
                        this.set.getLastOpInfo().addBuGang(curCardID / 100, OpType.JieGang);
                    }
                }
                return;
            }
        }
    }

    @Override
    protected void check_otherPeng(int curOpPos, int curCardID) {
        for (int i = 1; i < this.set.getRoom().getPlayerNum(); i++) {
            int nextPos = (curOpPos + i) % this.set.getRoom().getPlayerNum();
            WXMJSetPos ssetPos = (WXMJSetPos) this.set.getMJSetPos(nextPos);
            // 检查碰动作
            if (ssetPos.isTing) {
                continue;
            }
            if (ssetPos.checkOpType(curCardID, OpType.Peng)) {
                if (!ssetPos.getPosOpRecord().isOpCardType(curCardID / 100)) {
                    ssetPos.getPosOpRecord().setOpCardType(curCardID / 100);
                    this.addOpTypeInfo(nextPos, OpType.Peng);
                }

            }
        }
    }

    /**
     * 检查出牌后是否有人可以接手。
     *
     * @param curOpPos  当前操作位置ID
     * @param curCardID 当前操作牌ID
     */
    @Override
    protected void checkOutOpType(int curOpPos, int curCardID) {
        // 有玩家打出金牌		
        check_otherPingHu(curOpPos, curCardID);
        check_otherJieGang(curOpPos, curCardID);
        check_otherPeng(curOpPos, curCardID);
        check_LowerChi(curOpPos, curCardID);
    }
}			
