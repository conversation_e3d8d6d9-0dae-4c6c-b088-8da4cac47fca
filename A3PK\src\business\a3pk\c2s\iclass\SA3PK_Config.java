package business.a3pk.c2s.iclass;	
	
import cenum.RoomTypeEnum;	
import jsproto.c2s.cclass.room.BaseCreateRoom;	
import jsproto.c2s.iclass.room.SBase_Config;	
	
@SuppressWarnings("serial")	
public class SA3PK_Config extends SBase_Config {	
	public static SA3PK_Config make(BaseCreateRoom cfg,RoomTypeEnum roomTypeEnum) {	
		SA3PK_Config ret = new SA3PK_Config();	
		ret.setCfg(cfg);	
		ret.setRoomType(roomTypeEnum);	
		return ret;	
	}	
}	
