package business.global.mj.afmj;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCard;
import business.global.mj.robot.MJSetPosRobot;
import business.player.Robot.Tile;
import business.player.Robot.TileRank.NumberRank;
import business.player.Robot.TileRank.ZiRank;
import business.player.Robot.TileSuit;
import business.player.Robot.TileType;

import java.util.List;

/**
 * 机器人位置操作
 */
public class AFMJSetPosRobot extends MJSetPosRobot {
	public AFMJSetPosRobot(AbsMJSetPos mSetPos) {
		super(mSetPos);
		// TODO Auto-generated constructor stub
	}

	public int getAutoCard() {
		this.selfInfo.getAliveTiles().clear();
		List<MJCard> allCards = mSetPos.allCards();
		for (MJCard mCard : allCards) {
			if (!mSetPos.getSet().getmJinCardInfo().checkJinExist(mCard.type)) {
				addcard(mCard);
			}
		}
		int tmp = 0;
		List<Tile> lst = winType.getDiscardCandidates(selfInfo.getAliveTiles(), candidates);
		if (lst.size() <= 0) {
			if (mSetPos.sizePrivateCard() > 0) {
				MJCard mCard = mSetPos.getPCard(mSetPos.sizePrivateCard() - 1);
				if (null != mCard) {
					tmp = mCard.cardID;
				}
			}
		} else {
			tmp = lst.get(0).cardId();
		}
		return tmp;
	}

	protected void addcard(MJCard mj) {
		int type = mj.cardID / 1000;
		int no = mj.type % 10;
		int index = mj.cardID % 10;
		if (type <= 3) {
			selfInfo.getAliveTiles().add(Tile.of(TileType.of(TileSuit.ofNumber2(type), NumberRank.ofNumber(no)), index - 1));
		} else {
			if (type >= 5 || no > 7) {
				return;
			}
			selfInfo.getAliveTiles().add(Tile.of(TileType.of(TileSuit.ofNumber2(type), ZiRank.ofNumber(no)), index - 1));
		}
	}

}
