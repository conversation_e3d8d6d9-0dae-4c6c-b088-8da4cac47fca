package business.ahmj.c2s.iclass;						
						
import jsproto.c2s.cclass.BaseSendMsg;						
						
public class SAHMJ_ChangePlayerNumAgree extends BaseSendMsg {						
						
    private static final long serialVersionUID = 1L;						
    public long roomID;						
    public int pos;						
    public boolean agreeChange;						
    public static SAHMJ_ChangePlayerNumAgree make(long roomID, int pos, boolean agreeChange) {						
        SAHMJ_ChangePlayerNumAgree ret = new SAHMJ_ChangePlayerNumAgree();						
        ret.roomID = roomID;						
        ret.pos = pos;						
        ret.agreeChange = agreeChange;						
        return ret;						
						
						
    }						
}						
