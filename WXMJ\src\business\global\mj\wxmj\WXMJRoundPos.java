package business.global.mj.wxmj;

import business.global.mj.AbsMJRoundPos;
import business.global.mj.AbsMJSetRound;
import business.global.mj.MJCard;
import business.global.mj.set.MJOpCard;
import cenum.mj.MJCEnum;
import cenum.mj.MJOpCardError;
import cenum.mj.OpType;
import cenum.mj.TryEndRoundEnum;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;

/**
 * 一个round回合中，可能同时等待多个pos进行操作，eg:抢杠胡
 *
 * <AUTHOR>
 */
public class WXMJRoundPos extends AbsMJRoundPos {

    private WXMJSetPos pxPos;

    public WXMJRoundPos(AbsMJSetRound round, int opPos) {
        super(round, opPos);
        this.pxPos = (WXMJSetPos) this.getPos();
    }

    /**
     * 打牌
     *
     * @param request 连接请求
     * @param opType  动作类型
     * @param cardID  牌值
     * @return
     */
    public int opOutCard(WebSocketRequest request, OpType opType, int cardID) {
        // 操作错误s		
        if (errorOpType(request, opType) <= 0) {
            return MJOpCardError.ERROR_OP_TYPE.value();
        }
        // 检查牌是否存在		
        MJCard card = getCardByID(cardID);
        if (null == card) {
            request.error(ErrorCode.NotAllow, "1not find cardID:" + cardID);
            return MJOpCardError.CHECK_OP_TYPE_ERROR.value();
        }

        // 是不是自己身上的牌		
        if (!outCard(card)) {
            request.error(ErrorCode.NotAllow, "2not find cardID:" + cardID);
            return MJOpCardError.CHECK_OP_TYPE_ERROR.value();
        }
        if (((WXMJSetPos) this.pos).isBeiYongTing()) {
            ((WXMJSetPos) this.pos).setBeiYongTing(false);
            ((WXMJSetPos) this.pos).setTing(true);
        }
        // =====================================		
        // 记录当前回合操作的牌		
        this.setOpCard(cardID);
        pxPos.clearOp();
        // 执行动作		
        return this.exeCardAction(opType);
    }

    /**
     * 手上有门牌的操作。
     *
     * @param cardID
     */
    @Override
    protected int getCardOpPos(OpType opType, int cardID) {
        if (OpType.Hu.equals(opType)) {
            // 操作动作		
            if (!doOpType(cardID, opType)) {
                return MJOpCardError.ERROR_EXEC_OP_TYPE.value();
            }
        } else {
            return MJOpCardError.NONE.value();
        }
        // 记录操作的动作，并且尝试结束本回合		
        this.opTypeTryEndRound(this.opPos, opType, MJCEnum.OpHuType(opType), TryEndRoundEnum.ALL_WAIT);
        return this.opPos;
    }


    @Override
    public int op(WebSocketRequest request, OpType opType, MJOpCard mOpCard) {
        int opCardRet = -1;
        if (this.getOpType() != null) {
            request.error(ErrorCode.NotAllow, "opPos has opered");
            return MJOpCardError.REPEAT_EXECUTE.value();
        }
        switch (opType) {
            case Out:
                opCardRet = opOutCard(request, opType, mOpCard.getOpCard());
                break;
            case AnGang:
                opCardRet = opAnGang(request, opType, mOpCard.getOpCard());
                break;
            case JieGang:
                opCardRet = opJieGang(request, opType);
                break;
            case Gang:
                opCardRet = opGang(request, opType, mOpCard.getOpCard());
                break;
            case Pass:
                opCardRet = opPass(request, opType);
                break;
            case Peng:
                opCardRet = opPeng(request, opType);
                break;
            case Chi:
                opCardRet = opChi(request, opType, mOpCard.getOpCard());
                break;
            case Ting:
                ((WXMJRoomSet) set).setShifoutianting(true);
                ((WXMJSetPos) this.pos).setBeiYongTing(true);
                opCardRet = opOutCard(request, OpType.Out, mOpCard.getOpCard());
                break;
            // 自摸.		
            case Hu:
            case QiangGangHu:
            case JiePao:
                opCardRet = opHuType(request, opType);
                break;
            default:
                break;
        }
        request.response();
        return opCardRet;
    }

    @Override
    public int opHuType(WebSocketRequest request, OpType opType) {
        // 操作错误		
        if (errorOpType(request, opType) <= 0) {
            return MJOpCardError.ERROR_OP_TYPE.value();
        }
        // 执行操作		
        return opErrorReturn(request, opType, this.opReturn(opType, 0, TryEndRoundEnum.ALL_AT_ONCE));
    }


}			
