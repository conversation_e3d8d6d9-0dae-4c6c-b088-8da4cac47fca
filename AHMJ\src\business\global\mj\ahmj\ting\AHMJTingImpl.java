package business.global.mj.ahmj.ting;	
	
import business.ahmj.c2s.cclass.AHMJPointItem;
import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCard;	
import business.global.mj.MJCardInit;
import business.global.mj.ahmj.AHMJRoom;
import business.global.mj.ahmj.hutype.*;
import business.global.mj.ahmj.AHMJSetPos;
import business.global.mj.manage.MJFactory;
import business.global.mj.ting.AbsTing;
import com.ddm.server.common.utils.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;

import java.util.*;
import java.util.stream.Collectors;

import business.global.mj.ahmj.AHMJRoomEnum.AHMJOpPoint;

public class AHMJTingImpl extends AbsTing {	
    @Override	
    public boolean tingHu(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
        AHMJPointItem item = this.getPointItem((AHMJSetPos) mSetPos, mCardInit, true);
        if (Objects.nonNull(item)) {
            return true;
        }
        return false;	
    }
    
    /**
     * 根据可胡的胡牌牌型求最大分
     *
     * @param mSetPos
     * @param mCardInit
     * @return
     */
    public AHMJPointItem getPointItem(AHMJSetPos mSetPos, MJCardInit mCardInit, boolean isZiMo) {
        //如果玩家从发牌到胡牌都没摸到精，此时可以炮胡也可自摸
        List<AHMJPointItem> oItems = Collections.synchronizedList(Lists.newArrayList());
        AHMJRoom room = (AHMJRoom)mSetPos.getRoom();
        Arrays.asList(0,mCardInit.sizeJin()).parallelStream().forEach(jinSize->{
            List<Integer> allCardInit = ListUtils.union((jinSize > 0 ? new ArrayList() : mCardInit.getJins()), mCardInit.getAllCardInts());
            List<Integer> jins = Collections.nCopies(jinSize, mSetPos.getSet().getmJinCardInfo().getJin(1).getType());
            MJCardInit mInit = new MJCardInit();
            mInit.addAllCardInts(allCardInit);
            mInit.addAllJins(jins);
            boolean canHu = false;
            //小七对的检查
            AHMJPointItem item = (AHMJPointItem) MJFactory.getHuCard(AHMJDDHuCardImpl.class).checkHuCardReturn(mSetPos, mInit.clone());
            if (Objects.nonNull(item)) {
                checkOtherHuAdd(mSetPos, item, jinSize > 0,mInit);
                if(checkCanHuByKing(item,mCardInit.sizeJin(),isZiMo,mSetPos,room.kingHuFromQGH())){
                    oItems.add(item);
                    canHu = true;
                }
            }else{
                //	清一色：由筒或条组成的一色胡牌牌型； （不要求必须2、5、8做将）
                AHMJOpPoint sPoint = (AHMJOpPoint) MJFactory.getHuCard(AHMJQingYiSeHuCardImpl.class).checkHuCardReturn(mSetPos, mInit);
                if(!AHMJOpPoint.Not.equals(sPoint)){
                    //不用258做将
                    item = (AHMJPointItem) MJFactory.getHuCard(AHMJNormalHuCardImpl.class).checkHuCardReturn(mSetPos, mInit.clone());
                }else{
                    //258将胡
                    item = (AHMJPointItem) MJFactory.getHuCard(AHMJ258HuCardImpl.class).checkHuCardReturn(mSetPos, mInit.clone());
                }
                if (Objects.nonNull(item)) {
                    checkOtherHuAdd(mSetPos, item, jinSize > 0,mInit);
                    //小胡两分起胡,且小胡不叠加大胡
                    if(item.getAllPoint()>2){
                        item.removeAHMJOpPoint(AHMJOpPoint.PingHu,AHMJOpPoint.PingHu.value());
                    }
                    if(checkCanHuByKing(item,mCardInit.sizeJin(),isZiMo,mSetPos,room.kingHuFromQGH())){
                        oItems.add(item);
                        canHu = true;
                    }
                }
            }
//            if(!canHu){
//                //烂胡
//                AHMJOpPoint point = (AHMJOpPoint) MJFactory.getHuCard(AHMJKingHuCardImpl.class).checkHuCardReturn(mSetPos, null);
//                //	需求修改：三王不能烂胡
//                if (!AHMJOpPoint.Not.equals(point) && !AHMJOpPoint.King_3.equals(point)) {
//                    //3：烂胡只能自摸
//                    if(!mSetPos.isQiangGangHuFlag() && !mSetPos.isGangShangPaoFlag() && isZiMo){
//                        item = new AHMJPointItem();
//                        //	杠上花：玩家开杠后，补的这三张（或两张）牌刚好有听的牌，可以胡牌；
//                        if (mSetPos.isGangShangHuaFlag()) {
//                            item.addAHMJOpPoint(AHMJOpPoint.GSKH, AHMJOpPoint.GSKH.value());
//                        }
//                        item.addAHMJOpPoint(AHMJOpPoint.LanHu, AHMJOpPoint.LanHu.value());
//                        oItems.add(item);
//                    }
//                }
//            }
            // 不成牌型胡
            if(!canHu){
                //需求变更:有四张一样的王(六王3+3不能胡)，可以不成胡牌牌型；不成牌型时，除了王牌，其他牌都是同一花色，也要算清一色分数；
                AHMJOpPoint point = (AHMJOpPoint) MJFactory.getHuCard(AHMJKingHuCardImpl.class).checkHuCardReturn(mSetPos, null);
                if (!AHMJOpPoint.Not.equals(point) && !AHMJOpPoint.King_3.equals(point)) {
                    boolean kingHu = true;
                    if(AHMJOpPoint.King_6.equals(point)){
                        List<MJCard> cardList = mSetPos.allCards();
                        Map<Integer, Long> kingGroupMap = cardList.stream().filter(n -> mSetPos.getSet().getmJinCardInfo().checkJinExist(n.type)).collect(Collectors.groupingBy(MJCard::getType, Collectors.counting()));
                        if(kingGroupMap.containsValue(3L)){
                            //3+3的六王不能胡
                            kingHu = false;
                        }
                    }
                    //不成牌型胡
                    if(kingHu){
                        //3：烂胡只能自摸
                        if(!mSetPos.isQiangGangHuFlag() && !mSetPos.isGangShangPaoFlag() && isZiMo){
                            item = new AHMJPointItem();
                            //	杠上花：玩家开杠后，补的这三张（或两张）牌刚好有听的牌，可以胡牌；
                            if (mSetPos.isGangShangHuaFlag()) {
                                item.addAHMJOpPoint(AHMJOpPoint.GSKH, AHMJOpPoint.GSKH.value());
                            }
                            //	清一色：除了王牌，其他牌都是同一花色，也要算清一色分数；
                            AHMJOpPoint sPoint = (AHMJOpPoint) MJFactory.getHuCard(AHMJQingYiSeHuCardImpl.class).checkHuCardReturn(mSetPos, mCardInit);
                            if(!AHMJOpPoint.Not.equals(sPoint)){
                                item.addAHMJOpPoint(sPoint, sPoint.value());
                            }
                            //添加王胡
                            item.addAHMJOpPoint(point, point.value());
                            oItems.add(item);
                        }
                    }
                }
            }
        });
        return oItems.stream().max(Comparator.comparingInt(AHMJPointItem::getPoint)).orElse(null);
    }

    /**
     * 检查烂胡
     *
     * @return boolean
     */
    public AHMJPointItem checkLanhu(AbsMJSetPos mSetPos){
        //烂胡
        AHMJOpPoint point = (AHMJOpPoint) MJFactory.getHuCard(AHMJKingHuCardImpl.class).checkHuCardReturn(mSetPos, null);
        //	需求修改：三王不能烂胡
        if (!AHMJOpPoint.Not.equals(point) && !AHMJOpPoint.King_3.equals(point)) {
            AHMJPointItem item = new AHMJPointItem();
            item.addAHMJOpPoint(AHMJOpPoint.LanHu, AHMJOpPoint.LanHu.value());
            return item;
        }
        return null;
    }

    /**
     * 检查可以通过王胡
     *
     * @param item          项
     * @param sizeJin       大小金
     * @param isZiMo        是否子莫
     * @param mSetPos       m集pos
     * @param kingHuFromQGH 王从qgh胡
     * @return boolean
     */
    private boolean checkCanHuByKing(AHMJPointItem item, int sizeJin, boolean isZiMo, AHMJSetPos mSetPos, boolean kingHuFromQGH) {
        //没有硬庄
        if(!item.getAHMJOpPoints().contains(AHMJOpPoint.YZ)){
            //单王双王以及以上不能炮胡
            if(sizeJin>0&&!isZiMo){
                return false;
            }
            //没选玩法（	三王或以上代王硬不可接普通炮）不允许抢杠胡和杠上炮
            if((mSetPos.isQiangGangHuFlag() || mSetPos.isGangShangPaoFlag()) && !kingHuFromQGH){
                if(item.getAHMJOpPoints().contains(AHMJOpPoint.King_3)){
                    return false;
                }
                if(item.getAHMJOpPoints().contains(AHMJOpPoint.King_4)){
                    return false;
                }
                if(item.getAHMJOpPoints().contains(AHMJOpPoint.King_6)){
                    return false;
                }
                if(item.getAHMJOpPoints().contains(AHMJOpPoint.King_7)){
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 检查听到的牌
     *
     * @return
     */
    public boolean checkTingCardList(List<MJCard> allCardList,AbsMJSetPos setPos) {
        // 听牌
        List<Integer> tingList = absCheckTingCard(setPos, allCardList);
        // 判断听牌数
        return CollectionUtils.isNotEmpty(tingList);
    }

    /**
     * 检测是否还有其他的胡类型
     *
     * @param mSetPos
     * @param item
     */
    private void checkOtherHuAdd(AHMJSetPos mSetPos, AHMJPointItem item, boolean isConstantJin,MJCardInit mInit) {
        //	抢杠胡：别的玩家碰后杠时，我听这张牌，可以抢过来胡；
        if (mSetPos.isQiangGangHuFlag()) {
            item.addAHMJOpPoint(AHMJOpPoint.QiangGangHu, AHMJOpPoint.QiangGangHu.value());
        }
        AHMJRoom room = (AHMJRoom)mSetPos.getRoom();
        //	杠上炮：别的玩家开杠掷骰子补张后，补的这张牌刚好我可以胡；
        if (mSetPos.isGangShangPaoFlag() && room.gangShangPaoHuDa()) {
            item.addAHMJOpPoint(AHMJOpPoint.GSP, AHMJOpPoint.GSP.value());
        }
        //	杠上花：玩家开杠后，补的这三张（或两张）牌刚好有听的牌，可以胡牌；
        if (mSetPos.isGangShangHuaFlag()) {
            item.addAHMJOpPoint(AHMJOpPoint.GSKH, AHMJOpPoint.GSKH.value());
        }
        //	硬庄：胡牌的时候，手中无王（或王当本身用）；
        if (!isConstantJin && room.yingZhuang()) {
            item.addAHMJOpPoint(AHMJOpPoint.YZ, AHMJOpPoint.YZ.value());
        }
        //	三王：胡牌的时候，有三张一样的王或以上
        //	四王：胡牌的时候，有四张一样的王；——算两个大胡
        //	六王：胡牌的时候有两组三张一样的王或一组四王和一对其他王；——算两个大胡
        //	七王：胡牌的时候，七张王都有（即全部王牌）；——算两个大胡
        AHMJOpPoint point = (AHMJOpPoint) MJFactory.getHuCard(AHMJKingHuCardImpl.class).checkHuCardReturn(mSetPos, mInit);
        if(!AHMJOpPoint.Not.equals(point)){
            item.addAHMJOpPoint(point, point.value());
        }
        //	清一色：由筒或条组成的一色胡牌牌型； （不要求必须2、5、8做将）
        AHMJOpPoint sPoint = (AHMJOpPoint) MJFactory.getHuCard(AHMJQingYiSeHuCardImpl.class).checkHuCardReturn(mSetPos, mInit);
        if(!AHMJOpPoint.Not.equals(sPoint)){
            item.addAHMJOpPoint(sPoint, sPoint.value());
        }
    }

}					
