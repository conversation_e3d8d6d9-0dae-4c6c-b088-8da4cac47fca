package business.ahmj.c2s.cclass;	
	
import jsproto.c2s.cclass.mj.MJRoomSetInfo;
import java.util.ArrayList;
import java.util.List;

/**	
 * 当局信息	
 *	
 * <AUTHOR>	
 */	
public class AHMJRoomSetInfo extends MJRoomSetInfo {
    private List<Integer> daNiaoList = new ArrayList<>(); // 打鸟列表
    private List<Integer> flipCardList = new ArrayList<>(); // 遥杠翻开的牌
    private int flipCardPos = 0; // 翻开位置
    private int jin = 0;
    private int jin2 = 0;
    public int jinJin;

    public void setDaNiaoList(List<Integer> daNiaoList) {
        this.daNiaoList = daNiaoList;
    }

    public List<Integer> getDaNiaoList() {
        return daNiaoList;
    }

    public void setFlipCardList(List<Integer> flipCardList) {
        this.flipCardList = flipCardList;
    }

    public List<Integer> getFlipCardList() {
        return flipCardList;
    }

    public void setFlipCardPos(int flipCardPos) {
        this.flipCardPos = flipCardPos;
    }

    public void setJin(int jin) {
        this.jin = jin;
    }

    public void setJin2(int jin2) {
        this.jin2 = jin2;
    }

    public void setJinJin(int jinJin) {
        this.jinJin = jinJin;
    }
}
