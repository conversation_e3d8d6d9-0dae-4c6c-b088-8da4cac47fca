package business.global.mj.afmj;


public class AFMJRoomEnum {

	// 封顶 : 150封顶,200封顶,300封顶,500封顶,不封顶
	public enum AFMJFengDing {
		FengDing_150(0), // 150封顶
		FengDing_200(1), // 200封顶
		FengDing_300(2), // 300封顶
		FengDing_500(3), // 500封顶
		BuFengDing(4), // 不封顶
		;

		private final int value;

		private AFMJFengDing(int value) {
			this.value = value;
		}

		public int value() {
			return this.value;
		}

		public static AFMJFengDing valueOf(int value) {
			for (AFMJFengDing fengDing : AFMJFengDing.values()) {
				if (fengDing.ordinal() == value) {
					return fengDing;
				}
			}
			return null;
		}
	}


	// 玩法 : 上下翻精,翻上精,推精
	public enum AFMJWanFa {
		ShangXiaFanJing(0), // 上下翻精
		FanShangJing(1), // 翻上精
		TuiJing(2), // 推精
		;

		private final int value;

		private AFMJWanFa(int value) {
			this.value = value;
		}

		public int value() {
			return this.value;
		}

		public static AFMJWanFa valueOf(int value) {
			for (AFMJWanFa wanFa : AFMJWanFa.values()) {
				if (wanFa.ordinal() == value) {
					return wanFa;
				}
			}
			return null;
		}
	}

	// 飘精 : 不飘,博精,必博一精,必博正精或两副
	public enum AFMJPiaoJing{
		BuPiao(0), // 不飘
		BoJing(1), // 博精
		BiBoYiJing(2), // 必博一精
		BiBoZhengJingHuoLiangFu(3), // 必博正精或两副
		;

		private final int value;

		private AFMJPiaoJing(int value) {
			this.value = value;
		}

		public int value() {
			return this.value;
		}

		public static AFMJPiaoJing valueOf(int value) {
			for (AFMJPiaoJing piaoJing : AFMJPiaoJing.values()) {
				if (piaoJing.ordinal() == value) {
					return piaoJing;
				}
			}
			return null;
		}
	}

	// 可选玩法 : 精不能吃碰,冲一包三,不冲关不算精分,小局四舍五入,冰冻动能;
	public enum AFMJKeXuanWanFa {
		JinGBuNengChiPeng(0), // 精不能吃碰
		ChongYiBaoSan(1), // 冲一包三
		BuChongGuanBuSuanJingFen(2), // 不冲关不算精分
		XiaoJu4She5Ru(3), // 小局四舍五入
		BingDongGongNeng(4), // 冰冻动能
		;

		private final int value;

		private AFMJKeXuanWanFa(int value) {
			this.value = value;
		}

		public int value() {
			return this.value;
		}

		public static AFMJKeXuanWanFa valueOf(int value) {
			for (AFMJKeXuanWanFa keXuanWanFa : AFMJKeXuanWanFa.values()) {
				if (keXuanWanFa.ordinal() == value) {
					return keXuanWanFa;
				}
			}
			return null;
		}
	}

    // 房间 : 房间内切换人数,自动准备,解散次数不超过5次,小局10秒自动准备
	public enum AFMJFangJian {
		IsCanChangePlayerNum(0), // 房间内切换人数
		AutoReady(1), // 自动准备
		XiaoJuTuoGuanJieSan(2), // 小局托管解散
		JieSanCiShu5(3), // 解散次数不超过5次
		TuoGuan2XiaoJuJieSan(4), // 托管2小局解散:连续2小局托管
		JieSanCiShu3(5), // 解散次数不超过3次
		;
		private final int value;

		private AFMJFangJian(int value) {
			this.value = value;
		}

		public int value() {
			return this.value;
		}

		public static AFMJFangJian valueOf(int value) {
			for (AFMJFangJian fangjian : AFMJFangJian.values()) {
				if (fangjian.ordinal() == value) {
					return fangjian;
				}
			}
			return IsCanChangePlayerNum;
		}
	}


	// 限时：不限制，3分钟，5分钟，1分钟，30秒
	public enum AFMJXianShi {
		NOT(0), // 不限制
		SHi_30(180000), // 180秒出牌 ,3分钟
		SHI_60(300000),// 300秒出牌，5分钟
		SHi_1(60000), // 60秒出牌，1分钟
		SHI_03(30000),// 30秒出牌，30秒
		;

		private final int value;

		private AFMJXianShi(int value) {
			this.value = value;
		}

		public int value() {
			return this.value;
		}

		public static AFMJXianShi valueOf(int value) {
			for (AFMJXianShi huPai : AFMJXianShi.values()) {
				if (huPai.ordinal() == value) {
					return huPai;
				}
			}
			return NOT;
		}
	}


	// 解散：30秒,1分,3分,5分
	public enum AFMJMJJieSan {
		SEC_30(30), // 30秒
		MIN_1(60), // 1分
		MIN_3(180), // 3分
		MIN_5(300),// 5分
		MIN_0(0),// 5分
		;

		private final int value;

		private AFMJMJJieSan(int value) {
			this.value = value;
		}

		public int value() {
			return this.value;
		}

		public static AFMJMJJieSan valueOf(int value) {
			for (AFMJMJJieSan huPai : AFMJMJJieSan.values()) {
				if (huPai.ordinal() == value) {
					return huPai;
				}
			}
			return SEC_30;
		}
	}



	// 动作分数
	public enum AFMJOpPoint {
		// 默认值
		Not(0), // 没胡
		NotHu(0), // 没胡
		Hu(0), // 胡牌，平胡
		PiHu(0), // 屁胡,没有金平胡
		Normal(0), // 有金，带金平胡
		NotJin(0), // 德国平胡，金当本身平胡
		// 加分
		ZhengJing(2), // 正精
		FuJing(1), // 副精
		DeGuoJiaFen(5), // 德国加分
		ZhiGang(2), // 直杠
		PengGang(2), // 碰杠
		AnGang(2), // 暗杠
		ZhengGangJing(20), // 正杠精
		FuGangJing(10), // 副杠精
		QiTaGangJing(5), // 其他杠精
		PaiXingDiFen(1), // 牌型底分
		// 牌型、牌型分
		TianHu(20), // 天胡
		DiHu(20), // 地胡
		// 胡牌番数
		PaoHu(2), // 炮胡
		ZiMo(2), // 自摸
		PingHu(1), // 平胡
		QGH(4), // 抢杠胡
		DeGuo(2), // 德国
		JingDiao(2), // 精吊
		GSKH(4), // 杠上花
		PPHu(4), // 大七对
		QiDui(2), // 七小对
		ShiSanLan(2), // 十三烂
		QiXingShiSanLan(4), // 七星十三烂
		ZhuangJia(2), // 庄家
		ZhengBoJing(4), // 正博精
		FuBoJing(2), // 副博精
		ChongGuan(0), // 冲关
		BaWangJing(2), // 霸王精
		// 模块分数
		HuPaiPoint(0), // 胡牌分
		ShangJingPoint(0), // 上精分
		XiaJingPoint(0), // 下精分
		BoJingPoint(0), // 博精
		GangPoint(0) // 杠分
		;

		private final int value;

		private AFMJOpPoint(int value) {
			this.value = value;
		}

		public int value() {
			return this.value;
		}

	};






}