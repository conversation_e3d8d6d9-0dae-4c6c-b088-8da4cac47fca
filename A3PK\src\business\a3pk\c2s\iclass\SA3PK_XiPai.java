package business.a3pk.c2s.iclass;	
	
import cenum.ClassType;	
import jsproto.c2s.cclass.BaseSendMsg;	
	
	
public class SA3PK_XiPai extends BaseSendMsg {	
    /**	
	 * 	
	 */	
	private static final long serialVersionUID = 1L;	
	public long roomID;	
    public long pid;	
    public ClassType cType;	
    public static SA3PK_Xi<PERSON>ai make(long roomID, long pid,ClassType cType) {	
    	SA3PK_XiPai ret = new SA3PK_XiPai();	
        ret.roomID = roomID;	
        ret.pid = pid;	
        ret.cType = cType;	
        return ret;	
    	
	
    }	
}	
