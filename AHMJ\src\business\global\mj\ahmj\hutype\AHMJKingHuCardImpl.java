package business.global.mj.ahmj.hutype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCard;
import business.global.mj.MJCardInit;
import business.global.mj.ahmj.AHMJRoomSet;
import business.global.mj.hu.BaseHuCard;
import business.global.mj.util.Hu258JiangUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import business.global.mj.ahmj.AHMJRoomEnum.AHMJOpPoint;

/**
 * <AUTHOR>
 * @date 2020-10-23 13:56
 */
public class AHMJKingHuCardImpl extends BaseHuCard {
    @Override
    public boolean checkHuCard(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
        if (null == mCardInit) {
            return false;
        }
        return Hu258JiangUtil.getInstance().checkHu(mCardInit.getAllCardInts(), mCardInit.sizeJin());
    }

    @Override
    public <T> Object checkHuCardReturn(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
        List<MJCard> cardList = mSetPos.allCards();
        List<AHMJOpPoint> pointList = new ArrayList<>();
        AHMJRoomSet curSet = (AHMJRoomSet)mSetPos.getSet();
        if(CollectionUtils.isNotEmpty(cardList)){
            Map<Integer, Long> kingGroupMap = cardList.stream().filter(n -> curSet.getmJinCardInfo().checkJinExist(n.type)).collect(Collectors.groupingBy(MJCard::getType, Collectors.counting()));
            if(MapUtils.isNotEmpty(kingGroupMap)){
               for(Map.Entry<Integer,Long> item:kingGroupMap.entrySet()){
                   if(item.getValue()==4){
                       pointList.add(AHMJOpPoint.King_4);
                   }else if(item.getValue()==3){
                       pointList.add(AHMJOpPoint.King_3);
                   }else if(item.getValue()==2){
                       pointList.add(AHMJOpPoint.King_2);
                   }
               }
            }
            if(pointList.contains(AHMJOpPoint.King_4)){
                if(pointList.contains(AHMJOpPoint.King_3)){
                    pointList.add(AHMJOpPoint.King_7);
                }else if(pointList.contains(AHMJOpPoint.King_2)){
                    pointList.add(AHMJOpPoint.King_6);
                }
            }else if(pointList.contains(AHMJOpPoint.King_3)){
                long king3Count = pointList.stream().filter(AHMJOpPoint.King_3::equals).count();
                if(king3Count==2){
                    pointList.add(AHMJOpPoint.King_6);
                }
            }
            pointList.remove(AHMJOpPoint.King_2);
            return pointList.stream().max(Comparator.comparing(AHMJOpPoint::ordinal)).orElse(AHMJOpPoint.Not);
        }
        return AHMJOpPoint.Not;
    }
}
