package business.global.mj.ahmj;

import business.global.mj.AbsMJSetCard;
import business.global.mj.AbsMJSetRoom;
import business.global.mj.MJCard;
import business.global.mj.RandomCard;
import cenum.mj.MJCardCfg;
import com.ddm.server.common.CommLogD;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 万条筒108张牌
 *
 * <AUTHOR>
 */
public class AHMJSetCard extends AbsMJSetCard {
    public AHMJRoomSet set;
    public final List<Integer> zhongNiaoList = new ArrayList<>(Arrays.asList(1, 5, 9)); // 中码的点数

    public AHMJSetCard(AHMJRoomSet set) {
        this.set = set;
        this.room = set.getRoom();
        this.randomCard();
        this.initDPos(this.set);
    }

    /**
     * 洗牌
     */
    public void randomCard() {
        List<MJCardCfg> mCfgs = new ArrayList<MJCardCfg>();
        mCfgs.add(MJCardCfg.WANG);
        mCfgs.add(MJCardCfg.TIAO);
        mCfgs.add(MJCardCfg.TONG);
        baseRandomCard(this.set, mCfgs);
    }

    public void baseRandomCard(AbsMJSetRoom set, List<MJCardCfg> mCfgs) {
        this.randomCard = new RandomCard(mCfgs, this.room.getPlayerNum(), 0);
    }

    @Override
    public MJCard pop(boolean isNormalMo, int cardType) {
        int sizeCard = this.randomCard.getSize();
        // 留牌：留16张牌；
        if (sizeCard <= 0) {
            return null;
        }
        MJCard ret = this.getGodCard(cardType);
        try {
            ret = null != ret ? ret : this.randomCard.removeLeftCards(0);
        } catch (Exception e) {
            CommLogD.error(e.getMessage());
            return null;
        }
        if (isNormalMo)
            this.randomCard.setNormalMoCnt(this.randomCard.getNormalMoCnt() + 1);
        else
            this.randomCard.setGangMoCnt(this.randomCard.getGangMoCnt() + 1);
        return ret;
    }

    @Override
    protected boolean firstRandomDPos() {
        return false;
    }

    // 获取总中马数量
    public List<Integer> getZhaMaCards() {
        if(getRandomCard().getSize()>=((AHMJRoom) room).getZhuaNiaoNum()){
            return getRandomCard().getLeftCards().subList(0,((AHMJRoom) room).getZhuaNiaoNum()).stream().map(MJCard::getType).collect(Collectors.toList());
        }else{
            return getRandomCard().getLeftCards().stream().map(MJCard::getType).collect(Collectors.toList());
        }
    }

    public static void main(String args[]){
        List<Integer> xx = new ArrayList<>();
        xx.subList(0,0);
    }

    // 结算中码情况
    public List<Integer> calcZhongNiaoList(List<Integer> leftNiaoList) {
        List<Integer> zhongNiaoList = new ArrayList<>();
        for (Integer card : leftNiaoList) {
            if (this.isMa(card)) {
                zhongNiaoList.add(card);
            }
        }
        return zhongNiaoList;
    }

    // 判断某张牌是否中马
    private boolean isMa(int cardID) {
        int type = cardID;
        if (cardID > 100) {
            type = cardID / 100;
        }
        // 1/5/9中马
        int cardPoint = type % 10;
        if (zhongNiaoList.contains(cardPoint)) {
            return true;
        }
        return false;
    }
}

