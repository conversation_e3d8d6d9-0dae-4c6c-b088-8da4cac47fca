package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.List;	
import java.util.Map;	
	
/**	
 * 一百零八罗汉	
 */	
public class WXLSYBLBLHRankingImpl_T extends WXLSAbstractRanking {	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        WXLSRankingResult result = null;	
        if (player.getCardSize() == 13) {	
            List<WXLSPockerCard> cards = player.getCards();	
            Map<Integer, Integer> rankCount = player.getCardsRankCountMap();	
            if (this.isSameSuit(cards)) { // 如果是同色	
                if((rankCount.size()+ player.getGuiCount()) ==13)	
                {	
                    result = new WXLSRankingResult();	
                    result.setPockerCards(cards);	
                    result.setRankingEnum(WXLSRankingEnum.YIBAILINGBALUOHAN);	
                }	
            }	
        }	
        return result;	
    }	
}	
