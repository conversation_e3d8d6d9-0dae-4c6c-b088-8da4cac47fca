package business.wxmj.c2s.cclass;	
	
import cenum.mj.HuType;	
import jsproto.c2s.cclass.room.AbsBaseResults;	
	
/**	
 * 红中麻将总结算信息	
 *	
 * <AUTHOR>	
 */	
public class WXMJResults extends AbsBaseResults {	
	
    @SuppressWarnings("unused")	
    private int anGangPoint;// 飘分次数	
    private int mingGangPoint;// 飘分次数	
	
    public void addZimoPoint(HuType hType) {	
        switch (hType) {	
            case ZiMo:	
            case DanYou:	
            case ShuangYou:	
            case SanJinYou:	
            case TianHu:	
            case SanJinDao:	
            case SiJinDao:	
            case QiangJin:	
                this.setZiMoPoint(this.getZiMoPoint() + 1);	
                break;	
            default:	
                break;	
        }	
    }	
	
    /**	
     *	
     */	
    public void addAnGangPoint(int anGangPoint) {	
        this.anGangPoint += anGangPoint;	
    }	
	
    public void addMingGangPoint(int mingGangPoint) {	
        this.mingGangPoint += mingGangPoint;	
    }	
}		
