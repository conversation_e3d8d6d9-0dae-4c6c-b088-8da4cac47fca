package business.global.pk.a3pk;	
	
import java.util.*;	
import java.util.stream.Collectors;	
	
import business.a3pk.c2s.cclass.*;	
import business.a3pk.c2s.iclass.SA3PK_CleanPosCard;	
import business.a3pk.c2s.iclass.SA3PK_PartnerPosList;	
import business.global.pk.AbsPKSetPos;	
import business.global.pk.AbsPKSetRoom;	
import business.global.pk.PKOpCard;	
import business.global.room.base.AbsRoomPos;	
import cenum.PKOpType;	
import com.ddm.server.common.CommLogD;	
import com.ddm.server.common.utils.CommMath;	
import com.ddm.server.common.utils.Lists;	
import jsproto.c2s.cclass.pk.base.BasePKRoom_PosEnd;	
import jsproto.c2s.cclass.pk.base.BasePKSet_Pos;	
import jsproto.c2s.cclass.room.AbsBaseResults;	
import lombok.Data;	
import org.apache.commons.collections.CollectionUtils;	
	
/**	
 * A3扑克 每一局每个位置信息	
 *	
 * <AUTHOR>	
 */	
	
@Data	
public class A3PKSetPos extends AbsPKSetPos {	
    /**	
     * 当局信息	
     */	
    private A3PKRoomSet roomSet;	
    /**	
     * 打出牌列表	
     */	
    private List<Integer> outCardList = Lists.newArrayList();	
	
    /**	
     * 理牌的二维数量存放	
     */	
    private List<List<Integer>> liPais = Lists.newArrayList();	
	
    /**	
     * 伙伴位置	
     */	
    private int partnerPos = -1;	
	
    /**	
     * 标记打完牌并清空结算	
     */	
    private boolean isClearPosCard;	
	
    /**	
     * 设置结束类型	
     */	
    private A3PKRoomEnum.A3PK_SET_POS_END_TYPE endType = A3PKRoomEnum.A3PK_SET_POS_END_TYPE.NOT;	
    /**	
     * 玩家状态(胜利、失败、完胜、完败)	
     */	
    private A3PKRoomEnum.A3PK_POS_STATE posState = A3PKRoomEnum.A3PK_POS_STATE.NOT;	
	
    /**	
     * 阵营 1：红 ，2：蓝	
     */	
    private int ranksType = A3PKRoomEnum.A3PK_RANKS_TYPE.NOT.value();	
	
    /**	
     * 显示牌列表	
     */	
    private Set<Integer> showCardList = new HashSet<>();	
	
    /**	
     * 看哪个位置的牌:默认看本身	
     */	
    private int seePosId = -1;	
	
    /**	
     * 哪个玩家在看我	
     */	
    private long whoIsSeeMePid;	
	
    public A3PKSetPos(int posID, AbsRoomPos roomPos, AbsPKSetRoom set) {	
        super(posID, roomPos, set);	
        this.roomSet = (A3PKRoomSet) set;	
        this.setMSetOp(new A3PKSetOp(this));	
        this.setCalcPosEnd(new A3PKCalcPosEnd(this, this.getRoomSet()));	
	
    }	
	
    /**	
     * 初始化手牌	
     *	
     * @param cards 私有牌	
     */	
    @Override	
    public void init(List<Integer> cards) {	
        this.setPrivateCards(Lists.newArrayList(cards));	
        if (this.getRoomSet().getFirstOutPos() <= -1) {	
            // 首出：每局由发到方块4的玩家首出，且出的第一手牌必须包含方块4；	
            this.getRoomSet().setFirstOutPos(getPrivateCards().stream().anyMatch(k -> A3PKRoomEnum.isBlock4(k)) ? this.getPosID() : this.getRoomSet().getFirstOutPos());	
        }	
    }	
	
    /**	
     * 强制发牌	
     *	
     * @param cards 私有牌	
     */	
    @Override	
    public void forcePopCard(List<Integer> cards) {	
        getPrivateCards().addAll(cards);	
        if (this.getRoomSet().getFirstOutPos() <= -1) {	
            this.getRoomSet().setFirstOutPos(getPrivateCards().stream().anyMatch(k -> A3PKRoomEnum.isBlock4(k)) ? this.getPosID() : this.getRoomSet().getFirstOutPos());	
        }	
    }	
	
	
    @Override	
    protected AbsBaseResults newResults() {	
        return new A3PKResults();	
    }	
	
    /**	
     * 新一局中各位置的信息	
     *	
     * @return	
     */	
    @Override	
    protected BasePKSet_Pos newPKSetPos() {	
        return new A3PKSet_Pos();	
    }	
	
	
    @Override	
    public BasePKSet_Pos getNotify(long pid) {	
        return getNotify(pid == this.getPid() || this.whoIsSeeMePid == pid);	
    }	
	
    @Override	
    public A3PKSet_Pos getNotify(boolean isSelf) {	
        isSelf = isRevealCard() ? true : isSelf;	
        A3PKSet_Pos ret = (A3PKSet_Pos) newPKSetPos();	
        ret.setPid(this.getPid());	
        ret.setPosID(this.getPosID());	
        ret.setShouCard(isSelf ? this.getPrivateCards() : Collections.emptyList());	
        ret.setLiPais(isSelf ? this.getLiPais() : Collections.emptyList());	
        ret.setIsLostConnect(this.getRoomPos().isLostConnect());	
        ret.addOutCardList(this.getOutCardList());	
        ret.setCardSize(this.getPrivateCards().size());	
        ret.setEndType(this.getEndType());	
        ret.addShowCardList(CollectionUtils.isEmpty(this.showCardList) ? null : this.showCardList);	
        ret.setCompValue(this.getSet().getCurOutCard().getCompValue());	
        ret.setOutCardType(this.getSet().getCurOutCard().getOutCardType());	
        ret.setExistChallenge(isSelf ? this.getRoomSet().getChallengePos() == this.getPosID() ? PKOpType.Not.equals(this.getRoomSet().getChallengeOp()) : null : null);	
        return ret;	
    }	
	
    @Override	
    public A3PKSet_Pos getPlayBackNotify() {	
        A3PKSet_Pos ret = (A3PKSet_Pos) super.getPlayBackNotify();	
        ret.addOutCardList(this.getOutCardList());	
        ret.setCardSize(this.getPrivateCards().size());	
        ret.setEndType(this.getEndType());	
        ret.addShowCardList(CollectionUtils.isEmpty(this.showCardList) ? null : this.showCardList);	
        ret.setCompValue(this.getSet().getCurOutCard().getCompValue());	
        ret.setOutCardType(this.getSet().getCurOutCard().getOutCardType());	
        ret.setLiPais(this.getLiPais());	
        ret.setExistChallenge(this.getRoomSet().getChallengePos() == this.getPosID() ? PKOpType.Not.equals(this.getRoomSet().getChallengeOp()) : null);	
        return ret;	
    }	
	
	
    @Override	
    public List<PKOpType> receiveOpTypes() {	
        // 轮到你出牌了	
        List<PKOpType> opTypes = Lists.newArrayList(PKOpType.Out);	
        if (getSet().getCurOutCard().checkExistMustComeOutCard(this.getPosID())) {	
            // 必须出牌	
            getSet().getCurOutCard().clearCurOutCard();	
        } else {	
            // 你可以选择不出牌	
            opTypes.add(PKOpType.Pass);	
        }	
        return opTypes;	
    }	
	
	
    @Override	
    public boolean doOpType(PKOpCard opCard, PKOpType opType) {	
        return getMSetOp().doOpType(opCard, opType);	
    }	
	
	
    /**	
     * 统计本局分数	
     */	
    @SuppressWarnings({"unchecked", "rawtypes"})	
    @Override	
    public A3PKRoom_PosEnd calcPosEnd() {	
        // 小局分数结算	
        ((A3PKCalcPosEnd) this.getCalcPosEnd()).calcPosEnd();	
        // 位置结算信息	
        A3PKRoom_PosEnd ret = (A3PKRoom_PosEnd) this.posEndInfo();	
        // 输赢分	
        ret.setWinLosePoint(this.getDeductPoint());	
        // 结束顺序	
        ret.setEndType(this.getEndType());	
        // 玩家位置状态	
        ret.setState(this.getPosState());	
        // 分组	
        ret.setRanksType(this.getRanksType());	
        // 获取位置结算信息	
        ret.setShouCard(this.getPrivateCards());	
        // 伙伴牌列表	
        ret.setShowCardList(CollectionUtils.isEmpty(this.showCardList) ? null : this.showCardList);	
        return ret;	
    }	
	
	
    /**	
     * 新位置结算信息	
     *	
     * @return	
     */	
    @SuppressWarnings("rawtypes")	
    @Override	
    protected BasePKRoom_PosEnd newPKSetPosEnd() {	
        return new A3PKRoom_PosEnd();	
    }	
	
    /**	
     * 检查要出的牌列表	
     *	
     * @return	
     */	
    public boolean checkCardList(List<Integer> cardList) {	
        if (CollectionUtils.isEmpty(cardList)) {	
            CommLogD.error("A3PKSetPos checkCardList isEmpty cardList null or []");	
            return false;	
        }	
        // 检测牌的合理性 是否重复	
        if (CommMath.hasSame(cardList)) {	
            CommLogD.error("A3PKSetPos checkCardList hasSame cardList:{}", cardList);	
            return false;	
        }	
        if (cardList.size() > this.getPrivateCards().size()) {	
            CommLogD.error("A3PKSetPos checkCardList > cardList:{}", cardList);	
            return false;	
        }	
        if (cardList.stream().allMatch(k -> getPrivateCards().contains(k))) {	
            if (this.getRoomSet().isAtFirstHu()) {	
                // 且首出必须带方块4；	
                return cardList.stream().anyMatch(cardId -> A3PKRoomEnum.isBlock4(cardId));	
            }	
            return true;	
        } else {	
            return false;	
        }	
    }	
	
    /**	
     * 打牌、出牌	
     *	
     * @return	
     */	
    public boolean outCard(PKOpCard opCard) {	
        boolean isTab = false;	
        if (opCard.getCardList().stream().allMatch(k -> getPrivateCards().contains(k))) {	
            // 牌都在的话删除	
            isTab = getPrivateCards().removeAll(opCard.getCardList());	
        }	
        if (!isTab) {	
            CommLogD.error("A3PKSetPos removeAll  PrivateCards:{},cardList:{}", getPrivateCards(), opCard.getCardList());	
        } else {	
            if (this.getRoomSet().isAtFirstHu()) {	
                // 标记首出结束	
                this.getRoomSet().setAtFirstHu(false);	
            }	
            // 清除打出牌列表	
            this.clearOutCardList();	
            this.getOutCardList().addAll(opCard.getCardList());	
            if (CollectionUtils.isEmpty(this.getPrivateCards())) {	
                this.setEndType(A3PKRoomEnum.A3PK_SET_POS_END_TYPE.valueOf(this.getRoomSet().addRankingPos(this.getPosID())));	
            }	
            List<Integer> cardList = opCard.getCardList().stream().filter(cardId -> A3PKRoomEnum.isBlackAor3(this.getRoomSet().isKing(), cardId)).collect(Collectors.toList());	
            if (CollectionUtils.isNotEmpty(cardList)) {	
                this.showCardList.addAll(cardList);	
                this.getRoomSet().getShowCardList().addAll(cardList);	
            }	
            if (CollectionUtils.isNotEmpty(this.getLiPais())) {	
                for (Integer card : opCard.getCardList()) {	
                    for (List<Integer> arrayList : getLiPais()) {	
                        if (arrayList.contains(card)) {	
                            arrayList.clear();	
                            break;	
                        }	
                    }	
                }	
                for (int i = 0; i < getLiPais().size(); ) {	
                    if (i >= getLiPais().size()) {	
                        continue;	
                    }	
                    if (getLiPais().get(i).size() <= 0) {	
                        getLiPais().remove(i);	
                    } else {	
                        i++;	
                    }	
                }	
	
                if (this.getRoomSet().getCurOutCard().checkExistOurCardErrorInfo(getPosID())) {	
                    CommLogD.error("A3PK RoomId:{},PrivateCard:{},Cur:{}", getRoomSet().getRoom().getRoomID(), this.getPrivateCards().toString(), this.getRoomSet().getCurOutCard().toString());	
                }	
            }	
	
        }	
        return isTab;	
    }	
	
	
    /**	
     * 清除打出牌列表	
     */	
    public void clearOutCardList() {	
        this.outCardList.clear();	
    }	
	
	
    /**	
     * 检查该玩家是否过	
     *	
     * @return	
     */	
    public int isPass() {	
        if (!A3PKRoomEnum.A3PK_SET_POS_END_TYPE.NOT.equals(this.endType)) {	
            // 检查到该玩家出牌，但是该玩家已经打完	
            if (!this.isClearPosCard) {	
                this.isClearPosCard = true;	
                this.clearOutCardList();	
                this.getRoomSet().getRoom().getRoomPosMgr().notify2All(SA3PK_CleanPosCard.make(this.getSet().getRoom().getRoomID(), this.getPosID()));	
            }	
            // 如果最后出手的人还是我	
            if (this.getRoomSet().getCurOutCard().getOutCardPos() == this.getPosID()) {	
                // 清空当前打出的牌	
                this.getRoomSet().getCurOutCard().clearCurOutCard();	
                // 如果未确定伙伴身份则直接由下家出牌	
                return this.getRoomSet().nextOpPos(this.getPosID());	
            } else {	
                return this.getRoomSet().nextOpPos(this.getPosID());	
            }	
        }	
        return -1;	
    }	
	
	
    /**	
     * 检查本局是否可以结束了	
     *	
     * @return	
     */	
    public boolean checkSetEnd() {	
        if (A3PKRoomEnum.A3PK_SET_POS_END_TYPE.NOT.equals(this.endType)) {	
            // 还没打完	
            return false;	
        }	
        if (this.getRoomSet().getChallengePos() == this.getPosID()) {	
            // 必须出牌	
            getSet().getCurOutCard().clearCurOutCard();	
            return true;	
        } else if (A3PKRoomEnum.A3PK_SET_POS_END_TYPE.THREE.equals(this.endType)) {	
            // 必须出牌	
            getSet().getCurOutCard().clearCurOutCard();	
            return true;
        } else if (this.getRoomSet().getChallengePos() < 0) {
            if (A3PKRoomEnum.A3PK_SET_POS_END_TYPE.TWO.equals(this.endType)) {
                A3PKSetPos a3PKSetPos = (A3PKSetPos) this.getRoomSet().getPKSetPos(this.getRoomSet().getRankingPosList().get(0));
                if (Objects.nonNull(a3PKSetPos) && a3PKSetPos.getRanksType() == getRanksType()) {
                    // 必须出牌
                    getSet().getCurOutCard().clearCurOutCard();
                    return true;
                }
            }
        }
        return false;	
    }	
	
	
    /**	
     * 结算信息	
     *	
     * @return	
     */	
    @Override	
    protected AbsBaseResults mResultsInfo() {	
        A3PKResults mResults = (A3PKResults) this.getResults();	
        if (Objects.isNull(mResults)) {	
            // new 总结算	
            mResults = (A3PKResults) this.newResults();	
            // 用户PID	
            mResults.setPid(this.getPid());	
            // 位置	
            mResults.setPosId(this.getPosID());	
            // 是否房主	
            mResults.setOwner(this.getPid() == this.getRoom().getOwnerID());	
        }	
        // 总分数	
        mResults.setPoint(this.pidSumPointEnd());	
        // 总竞技点分数	
        mResults.setSportsPoint(this.getRoomPos().sportsPoint());	
        return mResults;	
    }	
	
    @Override	
    public A3PKSetPosRobot newPKSetPosRobot() {	
        return new A3PKSetPosRobot(this);	
    }	
	
	
    /**	
     * 通知独食	
     */	
    public void setPartnerPos() {	
        this.getRoomSet().getRoomPlayBack().playBack2Pos(getPosID(), SA3PK_PartnerPosList.make(this.getSet().getRoom().getRoomID(), this.getRoomSet().getChallengePos(), this.getShowCardList()), getRoomSet().getSetPosMgr().getAllPlayBackNotify());	
        this.getRoomSet().getRoom().getRoomPosMgr().notify2ExcludePosID(getPosID(), SA3PK_PartnerPosList.make(this.getSet().getRoom().getRoomID(), this.getRoomSet().getChallengePos(), this.getShowCardList()));	
    }	
	
    /**	
     * 检查理牌列表是否正确	
     *	
     * @param liPais	
     * @return	
     */	
    public boolean checkLiPai(List<List<Integer>> liPais) {	
        if (CollectionUtils.isEmpty(liPais)) {	
            // 理牌列表为空	
            this.liPais.clear();	
            return true;	
        }	
        List<Integer> liPaiList = liPais.stream().flatMap(k -> k.stream()).collect(Collectors.toList());	
        if (CommMath.hasSame(liPaiList)) {	
            // 出现重复的牌	
            return false;	
        }	
        if (!liPaiList.stream().allMatch(k -> getPrivateCards().contains(k))) {	
            // 有不存在的牌	
            return false;	
        }	
        this.liPais = liPais;	
        return true;	
    }	
}	
