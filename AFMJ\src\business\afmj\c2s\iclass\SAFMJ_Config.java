package business.afmj.c2s.iclass;

import cenum.RoomTypeEnum;
import jsproto.c2s.cclass.room.BaseCreateRoom;
import jsproto.c2s.iclass.room.SBase_Config;

@SuppressWarnings("serial")
public class SAFMJ_Config extends SBase_Config {
	public static SAFMJ_Config make(BaseCreateRoom cfg, RoomTypeEnum roomTypeEnum) {
		SAFMJ_Config ret = new SAFMJ_Config();
		ret.setCfg(cfg);
		ret.setRoomType(roomTypeEnum);
		return ret;
	}
}