package business.global.pk.a3pk;	
	
import java.util.List;	
import java.util.Objects;	
	
import business.a3pk.c2s.cclass.A3PKRoomSetInfo;	
import business.global.pk.AbsPKSetRoom;	
import business.global.pk.PKRoom;	
import business.global.room.RoomRecordMgr;	
import business.global.room.base.AbsRoomPos;	
import business.global.room.base.AbsRoomPosMgr;	
import cenum.room.GaoJiTypeEnum;	
import cenum.room.XianShiConfigEnum;	
import com.ddm.server.common.utils.CommMath;	
import com.ddm.server.websocket.def.ErrorCode;	
import com.google.gson.Gson;	
import business.a3pk.c2s.iclass.CA3PK_CreateRoom;	
import business.a3pk.c2s.iclass.SA3PK_ChangePlayerNum;	
import business.a3pk.c2s.iclass.SA3PK_ChangePlayerNumAgree;	
import business.a3pk.c2s.iclass.SA3PK_ChangeRoomNum;	
import business.a3pk.c2s.iclass.SA3PK_ChatMessage;	
import business.a3pk.c2s.iclass.SA3PK_Dissolve;	
import business.a3pk.c2s.iclass.SA3PK_LostConnect;	
import business.a3pk.c2s.iclass.SA3PK_PosContinueGame;	
import business.a3pk.c2s.iclass.SA3PK_PosDealVote;	
import business.a3pk.c2s.iclass.SA3PK_PosLeave;	
import business.a3pk.c2s.iclass.SA3PK_PosReadyChg;	
import business.a3pk.c2s.iclass.SA3PK_PosUpdate;	
import business.a3pk.c2s.iclass.SA3PK_RoomEnd;	
import business.a3pk.c2s.iclass.SA3PK_RoomRecord;	
import business.a3pk.c2s.iclass.SA3PK_StartVoteDissolve;	
import business.a3pk.c2s.iclass.SA3PK_Trusteeship;	
import business.a3pk.c2s.iclass.SA3PK_Voice;	
import business.a3pk.c2s.iclass.SA3PK_XiPai;	
import cenum.ChatType;	
import cenum.ClassType;	
import core.network.http.proto.SData_Result;	
import jsproto.c2s.cclass.BaseSendMsg;	
import jsproto.c2s.cclass.RoomEndResult;	
import jsproto.c2s.cclass.room.BaseRoomConfigure;	
import jsproto.c2s.cclass.room.GetRoomInfo;	
import jsproto.c2s.cclass.room.RoomPosInfo;	
import jsproto.c2s.iclass.S_GetRoomInfo;	
import jsproto.c2s.iclass.room.SBase_Dissolve;	
import jsproto.c2s.iclass.room.SBase_PosLeave;	
	
/**	
 * 余干510K游戏房间	
 *	
 * <AUTHOR>	
 */	
public class A3PKRoom extends PKRoom {	
    // 房间配置	
    private CA3PK_CreateRoom roomCfg = null;	
    protected A3PKRoom(BaseRoomConfigure<CA3PK_CreateRoom> baseRoomConfigure, String roomKey, long ownerID) {	
        super(baseRoomConfigure, roomKey, ownerID);	
        initShareBaseCreateRoom(CA3PK_CreateRoom.class,baseRoomConfigure);;	
        this.roomCfg = (CA3PK_CreateRoom) baseRoomConfigure.getBaseCreateRoom();	
    }	
	
	
    /**	
     * 自动准备游戏 玩家加入房间时，自动进行准备。	
     */	
    @Override	
    public boolean autoReadyGame() {	
        return this.getBaseRoomConfigure().getBaseCreateRoom().getFangjian().contains(A3PKRoomEnum.A3PKGameRoomConfigEnum.ZiDongZhunBei.ordinal());	
    }	
	
    /**	
     * 房主需要准备	
     *	
     * @return T:不准备,F:默认准备	
     */	
    @Override	
    public boolean ownerNeedReady() {	
        return !this.getBaseRoomConfigure().getBaseCreateRoom().getFangjian().contains(A3PKRoomEnum.A3PKGameRoomConfigEnum.ZiDongZhunBei.ordinal());	
    }	
	
    /**	
     * 清除记录。	
     */	
    @Override	
    public void clearEndRoom() {	
        super.clear();	
        this.roomCfg = null;	
    }	
	
    @Override	
    public int getPlayingCount() {	
        return this.getPlayerNum();	
    }	
	
	
    @Override	
    public int getWanfa() {	
        return this.getRoomCfg().getWanfa();	
    }	
	
    /**	
     * 获取房间配置	
     *	
     * @return	
     */	
    public CA3PK_CreateRoom getRoomCfg() {	
        if (this.roomCfg == null) {
            initShareBaseCreateRoom(CA3PK_CreateRoom.class,getBaseRoomConfigure());;

            return (CA3PK_CreateRoom) getBaseRoomConfigure().getBaseCreateRoom();	
        }	
        return this.roomCfg;	
    }	
	
    @SuppressWarnings("unchecked")	
    @Override	
    public <T> T getCfg() {	
        return (T) getRoomCfg();	
    }	
	
    @Override	
    public String dataJsonCfg() {	
        // 获取房间配置	
        return new Gson().toJson(this.getRoomCfg());	
    }	
	
    @Override	
    public AbsRoomPosMgr initRoomPosMgr() {	
        return new A3PKRoomPosMgr(this);	
    }	
	
    @Override	
    public void startNewSet() {	
        this.setCurSetID(this.getCurSetID() + 1);	
        // / 计算庄位	
        if (this.getCurSetID() == 1) {	
            setDPos(0);	
        } else if (this.getCurSet() != null) {	
            // 根据上一局计算下一局庄家	
            A3PKRoomSet mRoomSet = (A3PKRoomSet) this.getCurSet();	
            setDPos(mRoomSet.calcNextDPos());	
            mRoomSet.clear();	
        }	
        // 每个位置，清空准备状态	
        this.getRoomPosMgr().clearGameReady();	
        // 通知局数变化	
        this.getRoomTyepImpl().roomSetIDChange();	
        this.setAutoDismiss(false);	
        this.setCurSet(this.newPKRoomSet(this.getCurSetID(), this, this.getDPos()));	
    }	
	
    @Override	
    protected AbsPKSetRoom newPKRoomSet(int curSetID, PKRoom room, int dPos) {	
        return new A3PKRoomSet(curSetID, room, dPos);	
    }	
	
    @Override	
    public <E> boolean RoomCfg(E m) {	
        A3PKRoomEnum.A3PKCfgEnum cfgEnum = (A3PKRoomEnum.A3PKCfgEnum) m;	
        int cfgInt = cfgEnum.ordinal();	
        if (this.getRoomCfg().getKexuanwanfa().contains(cfgInt)) {	
            return true;	
        }	
        return false;	
    }	
	
	
    @SuppressWarnings("rawtypes")	
    @Override	
    public GetRoomInfo getRoomInfo(long pid) {	
        S_GetRoomInfo ret = new S_GetRoomInfo();	
        // 设置房间公共信息	
        this.getBaseRoomInfo(ret);	
        if (Objects.nonNull(this.getCurSet())) {	
            ret.setSet(this.getCurSet().getNotify_set(pid));	
        } else {	
            ret.setSet(new A3PKRoomSetInfo());	
        }	
        return ret;	
    }	
	
    @Override	
    public void setEndRoom() {	
        if (null != this.getCurSet()) {	
            // 增加房局记录	
            this.getRoomPosMgr().notify2All(this.RoomEnd(this.getPKRoomRecordInfo(), this.getRoomEndResult()));	
            if (getHistorySet().size() > 0) {	
                RoomRecordMgr.getInstance().add(this);	
                refererReceiveList();	
            }	
        }	
    }	
	
    @Override	
    public BaseSendMsg Trusteeship(long roomID, long pid, int pos, boolean trusteeship) {	
        return SA3PK_Trusteeship.make(roomID, pid, pos, trusteeship);	
    }	
	
	
    @Override	
    public BaseSendMsg PosLeave(SBase_PosLeave posLeave) {	
        return SA3PK_PosLeave.make(posLeave);	
    }	
	
	
    @Override	
    public BaseSendMsg LostConnect(long roomID, long pid, boolean isLostConnect, boolean isShowLeave) {	
        return SA3PK_LostConnect.make(roomID, pid, isLostConnect, isShowLeave);	
    }	
	
    @Override	
    public BaseSendMsg PosContinueGame(long roomID, int pos) {	
        return SA3PK_PosContinueGame.make(roomID, pos);	
    }	
	
    @Override	
    public BaseSendMsg PosUpdate(long roomID, int pos, RoomPosInfo posInfo, int custom) {	
        return SA3PK_PosUpdate.make(roomID, pos, posInfo, custom);	
    }	
	
    @Override	
    public BaseSendMsg PosReadyChg(long roomID, int pos, boolean isReady) {	
        return SA3PK_PosReadyChg.make(roomID, pos, isReady);	
    }	
	
    @Override	
    public BaseSendMsg Dissolve(SBase_Dissolve dissolve) {	
        return SA3PK_Dissolve.make(dissolve);	
    }	
	
    @Override	
    public BaseSendMsg StartVoteDissolve(long roomID, int createPos, int endSec) {	
        return SA3PK_StartVoteDissolve.make(roomID, createPos, endSec);	
    }	
	
    @Override	
    public BaseSendMsg PosDealVote(long roomID, int pos, boolean agreeDissolve, int endSec) {	
        return SA3PK_PosDealVote.make(roomID, pos, agreeDissolve);	
    }	
	
    @Override	
    public BaseSendMsg Voice(long roomID, int pos, String url) {	
        return SA3PK_Voice.make(roomID, pos, url);	
    }	
	
    @Override	
    public <T> BaseSendMsg RoomRecord(List<T> records) {	
        return SA3PK_RoomRecord.make(records);	
    }	
	
    @Override	
    public <T> BaseSendMsg RoomEnd(T record, RoomEndResult<?> sRoomEndResult) {	
        return SA3PK_RoomEnd.make(record, getRoomEndResult());	
    }	
	
    @Override	
    public BaseSendMsg XiPai(long roomID, long pid, ClassType cType) {	
        return SA3PK_XiPai.make(roomID, pid, cType);	
    }	
	
    @Override	
    public BaseSendMsg ChatMessage(long pid, String name, String content, ChatType type, long toCId, int quickID) {	
        return SA3PK_ChatMessage.make(pid, name, content, type, toCId, quickID);	
    }	
	
    @Override	
    public BaseSendMsg ChangePlayerNum(long roomID, int createPos, int endSec, int playerNum) {	
        return SA3PK_ChangePlayerNum.make(roomID, createPos, endSec, playerNum);	
    }	
	
    @Override	
    public BaseSendMsg ChangePlayerNumAgree(long roomID, int pos, boolean agreeChange) {	
        return SA3PK_ChangePlayerNumAgree.make(roomID, pos, agreeChange);	
    }	
	
    @Override	
    public BaseSendMsg ChangeRoomNum(long roomID, String roomKey, int createType) {	
        return SA3PK_ChangeRoomNum.make(roomID, roomKey, createType);	
    }	
	
    /**	
     * 30秒未准备自动退出	
     *	
     * @return	
     */	
    @Override	
    public boolean is30SencondTimeOut() {	
        return this.getRoomCfg().getGaoji().contains(GaoJiTypeEnum.SECOND_TIMEOUT_30.ordinal());	
    }	
	
    /**	
     * 是否禁用魔法表情	
     *	
     * @return	
     */	
    @Override	
    public boolean isNotGift() {	
        return this.getRoomTyepImpl().getBaseCreateRoom().getGaoji()	
                .contains(GaoJiTypeEnum.NOT_GIFT.ordinal());	
	
    }	
	
    /**	
     * 是否禁止语音	
     *	
     * @return	
     */	
    @Override	
    public boolean isDisAbleVoice() {	
        return checkGaoJiXuanXiang(GaoJiTypeEnum.DISABLE_VOICE);	
    }	
	
	
    @Override	
    public void roomTrusteeship(int pos) {	
        RobotDeal(pos);	
    }	
	
    @Override	
    public void cancelTrusteeship(AbsRoomPos pos) {	
	
    }	
	
    @Override	
    public boolean isCanChangePlayerNum() {	
        return false;	
    }	
	
	
    /**	
     * 机器人处理	
     */	
    @Override	
    public void RobotDeal(int pos) {	
        if (this.getCurSet() != null) {	
            AbsPKSetRoom mSetRoom = (AbsPKSetRoom) this.getCurSet();	
            if (null != mSetRoom.getCurRound()) {	
                mSetRoom.getCurRound().RobothandCrad(pos);	
            }	
        }	
    }	
	
    /**	
     * 是否需要解散次数	
     *	
     * @return	
     */	
    @Override	
    public boolean needDissolveCount() {	
        return getRoomCfg().getFangjian().contains(A3PKRoomEnum.A3PKGameRoomConfigEnum.JieSanCishu5.ordinal());	
    }	
	
    /**	
     * 获取解散次数	
     *	
     * @return	
     */	
    @Override	
    public int getJieShanShu() {	
        return 5;	
    }	
	
	
    /**	
     * 打牌操作	
     *	
     * @param pid    用户ID	
     * @param liPais 理牌列表	
     */	
    @SuppressWarnings("rawtypes")	
    public SData_Result opLiPai(long pid, List<List<Integer>> liPais) {	
        try {	
            lock();	
            AbsPKSetRoom mCurSet = (AbsPKSetRoom) this.getCurSet();	
            if (Objects.isNull(mCurSet)) {	
                return SData_Result.make(ErrorCode.NotAllow, "not current setID:" + (mCurSet != null ? mCurSet.getSetID() : 0));	
            }	
            AbsRoomPos roomPos = this.getRoomPosMgr().getPosByPid(pid);	
            if (Objects.isNull(roomPos)) {	
                return SData_Result.make(ErrorCode.NotAllow, "not find your pos:");	
            }	
            return ((A3PKRoomSet) mCurSet).opLiPai(roomPos.getPosID(), liPais);	
        } finally {	
            unlock();	
        }	
    }	
	
    /**	
     * 独操作	
     *	
     * @param pid    用户ID	
     * @param opType 操作类型	
     */	
    @SuppressWarnings("rawtypes")	
    public SData_Result opChallengeOp(long pid,int opType) {	
        try {	
            lock();	
            AbsPKSetRoom mCurSet = (AbsPKSetRoom) this.getCurSet();	
            if (Objects.isNull(mCurSet)) {	
                return SData_Result.make(ErrorCode.NotAllow, "not current setID:" + (mCurSet != null ? mCurSet.getSetID() : 0));	
            }	
            AbsRoomPos roomPos = this.getRoomPosMgr().getPosByPid(pid);	
            if (Objects.isNull(roomPos)) {	
                return SData_Result.make(ErrorCode.NotAllow, "not find your pos:");	
            }	
            return ((A3PKRoomSet) mCurSet).opChallengeOp(roomPos.getPosID(), opType);	
        } finally {	
            unlock();	
        }	
    }


    /**
     * 断线回来清除托管
     * @return
     */
    @Override
    public boolean isConnectClearTrusteeship(){
        return false;
    }
 }	
