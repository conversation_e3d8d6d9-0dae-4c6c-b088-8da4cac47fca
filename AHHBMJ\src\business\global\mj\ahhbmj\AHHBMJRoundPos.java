package business.global.mj.ahhbmj;

import business.ahhbmj.c2s.iclass.SAHHBMJ_OpCard;
import business.global.mj.AbsMJRoundPos;
import business.global.mj.AbsMJSetRound;
import business.global.mj.MJCard;
import business.global.mj.set.MJOpCard;
import cenum.mj.MJCEnum;
import cenum.mj.MJOpCardError;
import cenum.mj.OpType;
import cenum.mj.TryEndRoundEnum;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 淮北麻将
 * 一个round回合中，可能同时等待多个pos进行操作，eg:抢杠胡
 *
 * <AUTHOR>
 */
public class AHHBMJRoundPos extends AbsMJRoundPos {
    AHHBMJRoomSet bSet = null;
    AHHBMJSetPos bSetPos = null;

    public AHHBMJRoundPos(AbsMJSetRound round, int opPos) {
        super(round, opPos);
        this.bSet = (AHHBMJRoomSet) set;
        this.bSetPos = (AHHBMJSetPos) this.pos;
    }

    @Override
    public void clear() {
        super.clear();
        bSet = null;
        bSetPos = null;

    }


    @Override
    public int op(WebSocketRequest request, OpType opType, MJOpCard mOpCard) {
        int opCardRet = -1;
        if (this.getOpType() != null && !((AHHBMJSetRound) this.getRound()).isZuoLaPao) {
            request.error(ErrorCode.NotAllow, "opPos has opered");
            return MJOpCardError.REPEAT_EXECUTE.value();
        }
        switch (opType) {
            case Pass:
                opCardRet = opPass(request, opType);
                break;
            case Out:
                opCardRet = opOutCard(request, opType, mOpCard.getOpCard());
                break;
            case Peng:
                opCardRet = opPeng(request, opType);
                break;
            case Chi:
                SAHHBMJ_OpCard opCard = (SAHHBMJ_OpCard) mOpCard;
                opCardRet = opChi(request, opType, opCard.getCardList());
                break;
            case AnGang:
                opCardRet = opAnGang(request, opType, mOpCard.getOpCard());
                break;
            case JieGang:
                opCardRet = opJieGang(request, opType, mOpCard.getOpCard());
                break;
            case Gang:
                opCardRet = opGang(request, opType, mOpCard.getOpCard());
                break;
            case BaoTing:
                opCardRet = opBaoTing(request, opType, mOpCard.getOpCard());
                break;
            case Piao_Fen:
                opCardRet = opZuo(request, opType, mOpCard.getOpCard());
                break;
            case Fan:
                opCardRet = opLa(request, opType, mOpCard.getOpCard());
                break;
            case Pao:
                opCardRet = opPao(request, opType, mOpCard.getOpCard());
                break;
            case Hu:  //自摸			
            case QiangGangHu:  //自摸	
            case JiePao:  //自摸
                opCardRet = opHuType(request, opType);
                break;
            default:
                break;
        }
        opGhkh(opType, opCardRet);
        request.response();
        return opCardRet;
    }

    private int opZuo(WebSocketRequest request, OpType opType, int opValue) {
        // 操作错误
        if (errorOpType(request, opType) <= 0) {
            return MJOpCardError.ERROR_OP_TYPE.value();
        }
        if (opValue < 0 || opValue > 2) {
            request.error(ErrorCode.NotAllow, "opValue not allow ,opValue=" + opValue);
            return MJOpCardError.CHECK_OP_TYPE_ERROR.value();
        }
        if (bSetPos.getZuoFen() > -1) {
            request.error(ErrorCode.NotAllow, "have op zuo ,not allow again");
            return MJOpCardError.CHECK_OP_TYPE_ERROR.value();
        }
        bSetPos.setZuoFen(opValue);
        setOpCard(opValue);
        getRecieveOpTypes().remove(opType);
        // 记录当前回合操作的牌
        // 执行动作
        if (getRecieveOpTypes().isEmpty()) {
            return this.exeCardAction(opType);
        }
        return opPos;
    }

    private int opLa(WebSocketRequest request, OpType opType, int opValue) {
        // 操作错误
        if (errorOpType(request, opType) <= 0) {
            return MJOpCardError.ERROR_OP_TYPE.value();
        }
        if (opValue < 0 || opValue > 2) {
            request.error(ErrorCode.NotAllow, "opValue not allow ,opValue=" + opValue);
            return MJOpCardError.CHECK_OP_TYPE_ERROR.value();
        }
        if (bSetPos.getLaFen() > -1) {
            request.error(ErrorCode.NotAllow, "have op la ,not allow again");
            return MJOpCardError.CHECK_OP_TYPE_ERROR.value();
        }
        bSetPos.setLaFen(opValue);
        setOpCard(opValue);
        getRecieveOpTypes().remove(opType);
        // 记录当前回合操作的牌
        // 执行动作
        if (getRecieveOpTypes().isEmpty()) {
            return this.exeCardAction(opType);
        }
        return opPos;
    }

    private int opPao(WebSocketRequest request, OpType opType, int opValue) {
        // 操作错误
        if (errorOpType(request, opType) <= 0) {
            return MJOpCardError.ERROR_OP_TYPE.value();
        }

        if (opValue < 0 || opValue > 2) {
            request.error(ErrorCode.NotAllow, "opValue not allow ,opValue=" + opValue);
            return MJOpCardError.CHECK_OP_TYPE_ERROR.value();
        }
        if (bSetPos.getPaoFen() > -1) {
            request.error(ErrorCode.NotAllow, "have op pao ,not allow again");
            return MJOpCardError.CHECK_OP_TYPE_ERROR.value();
        }
        bSetPos.setPaoFen(opValue);
        setOpCard(opValue);
        getRecieveOpTypes().remove(opType);
        // 记录当前回合操作的牌
        // 执行动作
        if (getRecieveOpTypes().isEmpty()) {
            return this.exeCardAction(opType);
        }
        return opPos;
    }

    /**
     * 报听
     *
     * @param request
     * @param opType
     * @return
     */
    private int opBaoTing(WebSocketRequest request, OpType opType, int cardID) {
        // 操作错误
        if (errorOpType(request, opType) <= 0) {
            return MJOpCardError.ERROR_OP_TYPE.value();
        }
        // 检查牌是否存在
        MJCard card = getCardByID(cardID);
        if (null == card) {
            request.error(ErrorCode.NotAllow, "1not find cardID:" + cardID);
            return MJOpCardError.CHECK_OP_TYPE_ERROR.value();
        }
        // 是不是自己身上的牌
        if (!outCard(card)) {
            request.error(ErrorCode.NotAllow, "2not find cardID:" + cardID);
            return MJOpCardError.CHECK_OP_TYPE_ERROR.value();
        }
        bSetPos.setTing(true);
        // 记录当前回合操作的牌
        this.setOpCard(cardID);
        // 执行动作
        return this.exeCardAction(opType);
    }


    /**
     * 杠上开花
     *
     * @param opType
     * @param opCardRet
     */
    private void opGhkh(OpType opType, int opCardRet) {
        if (opCardRet < 0) {
            return;
        }
        //杠上开花	
        if (opType == OpType.Gang || opType == OpType.JieGang || opType == OpType.AnGang) {
            bSetPos.setGSKH(true);
        } else if (opType == OpType.Out) {
            ((AHHBMJRoomSet) getPos().getSet()).setGSP(false);
            if (bSetPos.isGSKH()) {
                bSetPos.setGSKH(false);
                ((AHHBMJRoomSet) getPos().getSet()).setGSP(true);
            }
        }
    }

    /**
     * 接杠
     *
     * @param request 连接请求
     * @param opType  动作类型
     * @return
     */
    public int opJieGang(WebSocketRequest request, OpType opType, int opCard) {
        // 操作错误		
        if (errorOpType(request, opType) <= 0) {
            return MJOpCardError.ERROR_OP_TYPE.value();
        }
        // 设置动作值		
        this.setPosMgr.setOpValue(opType, this.getOpPos(), this.getLastOutCard());
        // 执行操作	
        return opErrorReturn(request, opType, this.opReturn(opType, 0, TryEndRoundEnum.ALL_AT_ONCE));
    }


    /**
     * 手上有门牌的操作。
     *
     * @param opType 操作类型
     * @param cardID 牌值
     */
    @Override
    protected int getCardOpPos(OpType opType, int cardID) {
        if (OpType.Pass.equals(opType)) {

        } else if (OpType.Hu.equals(opType)) {
            // 操作动作			
            if (!doOpType(cardID, opType)) {
                return -1;
            }
        } else {
            return -1;
        }
        // 记录操作的动作，并且尝试结束本回合			
        this.opTypeTryEndRound(this.opPos, opType, MJCEnum.OpHuType(opType), TryEndRoundEnum.ALL_WAIT);
        return this.opPos;
    }

    /**
     * 本回合位置操作 添加动作类型列表
     *
     * @param recieveOpTypes
     */
    public void addOpType(List<OpType> recieveOpTypes) {
        if (CollectionUtils.isEmpty(recieveOpTypes)) {
            return;
        }
        this.recieveOpTypes.addAll(recieveOpTypes);
        this.publicWait = this.recieveOpTypes.contains(OpType.Out);
        if (checkRecieveOpTypes(OpType.Fan) || checkRecieveOpTypes(OpType.Pao) || checkRecieveOpTypes(OpType.Piao_Fen)) {
            AHHBMJSetRound round = (AHHBMJSetRound) getRound();
            round.isZuoLaPao = true;
            this.publicWait = true;
        }
    }

    /**
     * 本回合位置操作 添加动作类型
     *
     * @param opType
     */
    public void addOpType(OpType opType) {
        if (Objects.isNull(opType)) {
            return;
        }
        this.recieveOpTypes.add(opType);
        this.publicWait = this.recieveOpTypes.contains(OpType.Out);
        if (checkRecieveOpTypes(OpType.Fan) || checkRecieveOpTypes(OpType.Pao) || checkRecieveOpTypes(OpType.Piao_Fen)) {
            AHHBMJSetRound round = (AHHBMJSetRound) getRound();
            round.isZuoLaPao = true;
            this.publicWait = true;
        }
    }

}							
