package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSCardRankEnum;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.ArrayList;	
import java.util.List;	
import java.util.Map;	
import java.util.stream.Collectors;	
	
/**	
 *三同花顺	
 */	
public class WXLSSanTongHuaShunRankingImpl_T extends WXLSAbstractRanking {	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        WXLSRankingResult result = null;	
        // 先验证是否出现三同花	
        WXLSSanTongHuaRankingImpl_T sTongHua = new WXLSSanTongHuaRankingImpl_T();	
        WXLSRankingResult sthResult = sTongHua.doResolve(player);	
        // 如果没有三同花，就不可能有三同花顺。	
        if (null == sthResult) {	
            return result;	
        }	
	
        ArrayList<WXLSCardRankEnum> cardsnew = player.getRanks();	
        ArrayList<ArrayList<WXLSCardRankEnum>> rets = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
        if (player.getCardSize() == 13) {	
                rets.addAll(checkAll(cardsnew, 5, 5, 3));	
	
        }	
	
        if (rets.size() > 2) {	
            Map<Integer, List<WXLSPockerCard>> pockerCardMap = pockerCardMap(player.getCards());	
            if (null == pockerCardMap)	
                return result;	
            if (pockerCardMap.size() == 1) {	
                result = new WXLSRankingResult();	
                result.setPockerCards(player.getCards());	
                result.setRankingEnum(WXLSRankingEnum.STongHuaShun);	
            } else if (pockerCardMap.size() == 2 || pockerCardMap.size() == 3) {	
                Map<Integer, List<WXLSPockerCard>> pockerCardMapList = null;	
                List<WXLSPockerCard> addPockerCard = new ArrayList<WXLSPockerCard>();	
                for (int j = 0; j < rets.size(); j += 3) {	
                    pockerCardMapList = pockerCardMap(player.getCards());	
                    if (null == pockerCardMapList)	
                        return result;	
	
                    int i = 0;	
                    if (equalsCardPocker(j,addPockerCard,pockerCardMapList, rets) <= 0) {	
                        addPockerCard.clear();	
                        continue;	
                    }	
                    i++;	
                    if (equalsCardPocker(j + 1,addPockerCard, pockerCardMapList, rets) <= 0) {	
                        addPockerCard.clear();	
                        continue;	
                    }	
                    i++;	
                    if (equalsCardPocker(j + 2,addPockerCard, pockerCardMapList, rets) <= 0) {	
                        addPockerCard.clear();	
                        continue;	
                    }	
                    i++;	
	
                    if (i == 3) {	
                        result = new WXLSRankingResult();	
                        result.setPockerCards(addPockerCard);	
                        result.setRankingEnum(WXLSRankingEnum.STongHuaShun);	
                        return result;	
                    }	
                }	
            }	
	
        }	
        return result;	
    }	
	
    private int equalsCardPocker(int j,	
                                 List<WXLSPockerCard> addPockerCard,	
                                 Map<Integer, List<WXLSPockerCard>> pockerCardMapList,	
                                 ArrayList<ArrayList<WXLSCardRankEnum>> rets) {	
        int i = 0;	
        for (Map.Entry<Integer, List<WXLSPockerCard>> entry : pockerCardMapList	
                .entrySet()) {	
            if (equalsCardPocker(addPockerCard,rets.get(j), entry.getValue())) {	
                i = 1;	
                break;	
            }	
        }	
        return i;	
    }	
	
    private boolean equalsCardPocker(List<WXLSPockerCard> addPockerCard,List<WXLSCardRankEnum> cRankEnums,	
                                     List<WXLSPockerCard> cPocker) {	
        int size = cRankEnums.size();	
        int i = 0;	
        for (WXLSCardRankEnum cRankEnum : cRankEnums) {	
            for (WXLSPockerCard pCard : cPocker) {	
                if (cRankEnum.equals(pCard.getRank())) {	
                    i++;	
                    break;	
                }	
            }	
        }	
	
        if (size == i) {	
            for (WXLSCardRankEnum cRankEnum : cRankEnums) {	
                for (WXLSPockerCard pCard : cPocker) {	
                    if (cRankEnum.equals(pCard.getRank())) {	
                        addPockerCard.add(pCard);	
                        cPocker.remove(pCard);	
                        break;	
                    }	
                }	
            }	
            return true;	
        }	
        return false;	
    }	
	
    private Map<Integer, List<WXLSPockerCard>> pockerCardMap(	
            List<WXLSPockerCard> pockerCards) {	
        Map<Integer, List<WXLSPockerCard>> pockerCardMap = pockerCards.stream()	
                .collect(Collectors.groupingBy(p -> p.type));	
        if (null == pockerCardMap)	
            return null;	
        // 获取分组大小	
        int sizePockerMap = pockerCardMap.size();	
        if (sizePockerMap == 4)	
            return null;	
	
        return pockerCardMap;	
    }	
	
	
	
}	
	
