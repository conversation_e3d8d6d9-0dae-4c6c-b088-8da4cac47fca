package business.global.mj.afmj.optype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCard;
import business.global.mj.afmj.AFMJRoomSet;
import business.global.mj.afmj.AFMJSetPos;
import business.global.mj.manage.OpCard;
import cenum.mj.FlowerEnum;
import cenum.mj.MJSpecialEnum;

import java.util.stream.Collectors;

/**
 * 麻将补花
 */
public class AFMJBuHuaImpl implements OpCard {

	private final int huaType = MJSpecialEnum.NOT_HUA.value();

	public boolean checkOpCard(AbsMJSetPos mSetPos, int cardID) {
		if (FlowerEnum.PRIVATE.ordinal() == cardID) {
			return this.privateCardApplique(mSetPos);
		} else if (FlowerEnum.HAND_CARD.ordinal() == cardID) {
			this.handCardApplique(mSetPos);
			return true;
		}
		return false;
	}

	@Override
	public boolean doOpCard(AbsMJSetPos mSetPos, int cardID) {
		return false;
	}


	/**
	 * 私有牌补花
	 */
	private boolean privateCardApplique(AbsMJSetPos mSetPos) {
		AFMJRoomSet set = (AFMJRoomSet) mSetPos.getSet();
		// 检查是否存花
		boolean checkExistFlower = mSetPos.allCards().stream().anyMatch(k -> k.getType() >= huaType);
		if (!checkExistFlower) {
			return false;
		}
		//设置首补花的位置
		setFirstBuHuaPos(set,mSetPos.getPosID());
		mSetPos.setPrivateCard(mSetPos.allCards().stream().map(k -> hua(mSetPos, k)).collect(Collectors.toList()));
		MJCard handCard = mSetPos.getHandCard();
		if (null != handCard && handCard.getType() >= huaType) {
			mSetPos.privateMoveHandCard();
		}
		((AFMJSetPos) mSetPos).distinct();
		mSetPos.sortCards();
		set.MJApplique(mSetPos.getPosID());
		return true;
	}

	private void setFirstBuHuaPos(AFMJRoomSet set, int posID) {
		if(set.getFirstBuHuaPos()==-1){
			set.setFirstBuHuaPos(posID);
		}
	}

	/**
	 * 花
	 *
	 * @param mSetPos 玩家信息
	 * @param mCard   牌
	 * @return 补牌
	 */
	private MJCard hua(AbsMJSetPos mSetPos, MJCard mCard) {
		if (mCard.getType() >= huaType) {
			// 记录花
			this.addHua(mSetPos, mCard);
			MJCard hCard = mSetPos.getMJSetCard().pop(false);
			hCard.setOwnnerPos(mSetPos.getPosID());
			return hCard;
		}
		return mCard;
	}

	/**
	 * 添加花
	 *
	 * @param mSetPos 玩家位置信息
	 * @param mCard   牌
	 */
	private void addHua(AbsMJSetPos mSetPos, MJCard mCard) {
		if (mCard.getType() >= huaType) {
			mSetPos.getPosOpRecord().addHua(mCard.getCardID(),huaType);
			mSetPos.addOutCardIDs(mCard.getCardID());
		}
	}

	/**
	 * 首牌补花
	 *
	 * @param mSetPos 玩家位置信息
	 */
	private void handCardApplique(AbsMJSetPos mSetPos) {
		AFMJRoomSet set = (AFMJRoomSet) mSetPos.getSet();
		MJCard handCard = mSetPos.getHandCard();
		if (null == handCard) {
			return;
		}
		if (handCard.getType() < huaType) {
			return;
		}
		MJCard mCard = mSetPos.getMJSetCard().pop(false);
		if (null == mCard) {
			mSetPos.getSet().setHuangPos(mSetPos.getPosID());
			mSetPos.getSet().endSet();
			return;
		}
		this.addHua(mSetPos, handCard);
		mSetPos.setHandCard(mCard);
		set.MJApplique(mSetPos.getPosID());
		handCardApplique(mSetPos);
	}

}
