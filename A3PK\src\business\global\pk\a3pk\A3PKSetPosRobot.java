package business.global.pk.a3pk;	
	
import business.global.pk.AbsPKSetPos;	
import business.global.pk.PKCurOutCardInfo;	
import business.global.pk.PKFactory;	
import business.global.pk.PKOpCard;	
import business.global.pk.a3pk.optype.*;	
import business.global.pk.robot.PKSetPosRobot;	
import com.ddm.server.common.utils.CommMath;	
import jsproto.c2s.cclass.pk.BasePocker;	
import org.apache.commons.collections.MapUtils;	
import org.apache.commons.lang3.ObjectUtils;	
	
import java.util.*;	
import java.util.stream.Collectors;	
	
public class A3PKSetPosRobot extends PKSetPosRobot {	
	
    public A3PKSetPosRobot(AbsPKSetPos mSetPos) {	
        super(mSetPos);	
    }	
	
    @Override	
    public PKOpCard getAutoCard() {	
        // 牌分组	
        Map<Integer, List<Integer>> map = getMSetPos().getPrivateCards().stream().collect(Collectors.groupingBy(p -> BasePocker.getCardValueEx(p)));	
        if (MapUtils.isEmpty(map)) {	
            return null;	
        }	
        PKCurOutCardInfo curOutCard = getMSetPos().getSet().getCurOutCard();	
        if (curOutCard.checkExistMustComeOutCard(this.getMSetPos().getPosID())) {	
            return ObjectUtils.firstNonNull(	
                    PKFactory.getCardType(A3PKSingleCardImpl.class).robotResultType(getMSetPos(), curOutCard, map),	
                    PKFactory.getCardType(A3PKDuiZiImpl.class).robotResultType(getMSetPos(), curOutCard, map),	
                    PKFactory.getCardType(A3PK3ZhangImpl.class).robotResultType(getMSetPos(), curOutCard, map),	
                    PKFactory.getCardType(A3PK4ZhangImpl.class).robotResultType(getMSetPos(), curOutCard, map),	
                    PKFactory.getCardType(A3PKShunZiImpl.class).robotResultType(getMSetPos(), curOutCard, map),	
                    PKFactory.getCardType(A3PKTongHuaImpl.class).robotResultType(getMSetPos(), curOutCard, map),	
                    PKFactory.getCardType(A3PK3Dai2Impl.class).robotResultType(getMSetPos(), curOutCard, map),	
                    PKFactory.getCardType(A3PK4Dai1Impl.class).robotResultType(getMSetPos(), curOutCard, map),	
                    PKFactory.getCardType(A3PKTongHuaShunZiImpl.class).robotResultType(getMSetPos(), curOutCard, map)	
            );	
        }	
        PKOpCard opCard = null;	
        switch (A3PKRoomEnum.A3PK_CARD_TYPE.valueOf(curOutCard.getOutCardType())) {	
            case A3PK_CARD_TYPE_SINGLECARD:	
                return PKFactory.getCardType(A3PKSingleCardImpl.class).robotResultType(getMSetPos(), curOutCard, map);	
            case A3PK_CARD_TYPE_DUIZI:	
                return PKFactory.getCardType(A3PKDuiZiImpl.class).robotResultType(getMSetPos(), curOutCard, map);	
            case A3PK_CARD_TYPE_3:	
                return PKFactory.getCardType(A3PK3ZhangImpl.class).robotResultType(getMSetPos(), curOutCard, map);	
            case A3PK_CARD_TYPE_4:	
                return PKFactory.getCardType(A3PK4ZhangImpl.class).robotResultType(getMSetPos(), curOutCard, map);	
            case A3PK_CARD_TYPE_SHUNZI:	
                opCard = PKFactory.getCardType(A3PKShunZiImpl.class).robotResultType(getMSetPos(), curOutCard, map);	
                break;	
            case A3PK_CARD_TYPE_TONGHUA:	
                opCard = PKFactory.getCardType(A3PKTongHuaImpl.class).robotResultType(getMSetPos(), curOutCard, map);	
                break;	
            case A3PK_CARD_TYPE_3DAI2:	
                opCard = PKFactory.getCardType(A3PK3Dai2Impl.class).robotResultType(getMSetPos(), curOutCard, map);	
                break;	
            case A3PK_CARD_TYPE_4DAI1:	
                opCard = PKFactory.getCardType(A3PK4Dai1Impl.class).robotResultType(getMSetPos(), curOutCard, map);	
                break;	
            case A3PK_CARD_TYPE_TONGHUASHUN:	
                opCard = PKFactory.getCardType(A3PKTongHuaShunZiImpl.class).robotResultType(getMSetPos(), curOutCard, map);	
                break;	
        }	
        return Objects.isNull(opCard) && CommMath.isTrueDouble(CommMath.randomInt(0, 100)) ? ObjectUtils.firstNonNull(	
                PKFactory.getCardType(A3PKShunZiImpl.class).robotResultType(getMSetPos(), curOutCard, map),	
                PKFactory.getCardType(A3PKTongHuaImpl.class).robotResultType(getMSetPos(), curOutCard, map),	
                PKFactory.getCardType(A3PK3Dai2Impl.class).robotResultType(getMSetPos(), curOutCard, map),	
                PKFactory.getCardType(A3PK4Dai1Impl.class).robotResultType(getMSetPos(), curOutCard, map),	
                PKFactory.getCardType(A3PKTongHuaShunZiImpl.class).robotResultType(getMSetPos(), curOutCard, map)	
                ) : opCard;	
    }	
}	
