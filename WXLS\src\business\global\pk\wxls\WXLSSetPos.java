package business.global.pk.wxls;	
	
	
	
import business.wxls.c2s.cclass.WXLSResults;	
import business.wxls.c2s.cclass.entity.WXLSPlayerResult;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
	
import java.util.ArrayList;	
import java.util.Collections;	
import java.util.Comparator;	
import java.util.List;	
	
/**	
 * 十三水 每个位置信息	
 * 	
 * <AUTHOR>	
 *	
 */	
public class WXLSSetPos {	
	public int posID = 0; // 1-4号位置	
	public WXLSRoomPos roomPos = null;	
	public WXLSRoomSet set = null; // 父节点	
	public List<WXLSPockerCard> privateCards = new ArrayList<>(); // 手牌	
	
		
	public void clean() {	
		this.roomPos = null;	
		this.set = null;	
		if (null != this.privateCards) {	
			this.privateCards.clear();	
			this.privateCards = null;	
		}	
	}	
	
	public WXLSSetPos(int posID, WXLSRoomPos roomPos, WXLSRoomSet set) {	
		this.posID = posID;	
		this.roomPos = roomPos;	
		this.set = set;	
	}	
	
	/**	
	 * 手牌排序	
	 */	
	private void sortCards() {	
		Collections.sort(this.privateCards, new Comparator<WXLSPockerCard>() {	
			@Override	
			public int compare(WXLSPockerCard o1, WXLSPockerCard o2) {	
				return o2.cardID - o1.cardID;	
			}	
		});	
	}	
	
	
	
	/**	
	 * 初始化手牌	
	 * 	
	 * @param cards	
	 */	
	public void init(List<WXLSPockerCard> cards) {	
		this.privateCards = new ArrayList<>(cards);	
		for (WXLSPockerCard card : this.privateCards) {	
			card.ownnerPos = posID;	
		}	
		this.sortCards();	
		getKeyCard();	
	}	
	
	private void getKeyCard() {	
		if (posID == 0) {	
			List<String> keys = new ArrayList<String>();	
			for (WXLSPockerCard pCard : this.privateCards) {	
				keys.add(pCard.toString());	
			}	
			set.room.setPlayerCard(keys);	
		}	
	}	
	
	
	
	
	@SuppressWarnings("unchecked")	
	public void calcPosEnd() {	
		WXLSResults cRecord = (WXLSResults) roomPos.getResults();	
		if (null == cRecord) {	
			cRecord = new WXLSResults();	
		}	
		cRecord.setPid(roomPos.getPid());	
		cRecord.setPosId(roomPos.getPosID());	
		for (WXLSPlayerResult pResult : set.sRankingResult.playerResults) {	
			if (pResult.getPid() == roomPos.getPid()) {	
				roomPos.calcRoomPoint(pResult.getShui());// 更新本场积分	
				cRecord.setPoint(roomPos.getPoint());	
				WXLSRoomPos WXLSRoomPos =(WXLSRoomPos)set.room.getRoomPosMgr().getPosByPosID(this.posID);	
				Double sportsPoint;	
				if(WXLSRoomPos.setSportsPoint(roomPos.getPoint())==null){	
					sportsPoint=0.0;	
				}else {	
					sportsPoint= WXLSRoomPos.setSportsPoint(roomPos.getPoint());	
				}	
				cRecord.setSportsPoint(sportsPoint);	
				if (pResult.getShui() > 0) {	
					cRecord.winCount = (cRecord.winCount + 1);	
				} else if (pResult.getShui() < 0) {	
					cRecord.loseCount = (cRecord.loseCount + 1);	
				} else if (pResult.getShui() == 0) {	
					cRecord.flatCount = (cRecord.flatCount + 1);	
				}	
			}	
		}	
		roomPos.setResults(cRecord);	
	
	}	
	
	
	
	
	
}	
