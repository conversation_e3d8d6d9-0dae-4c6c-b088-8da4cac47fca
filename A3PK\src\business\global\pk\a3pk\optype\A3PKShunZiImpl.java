package business.global.pk.a3pk.optype;	
	
import business.global.pk.AbsPKSetPos;	
import business.global.pk.PKCurOutCardInfo;	
import business.global.pk.PKOpCard;	
import business.global.pk.a3pk.A3PKRoom;	
import business.global.pk.a3pk.A3PKRoomEnum;	
import com.ddm.server.common.CommLogD;	
import jsproto.c2s.cclass.pk.BasePocker;	
import org.apache.commons.collections.CollectionUtils;	
	
import java.util.Collections;	
import java.util.Comparator;	
import java.util.List;	
import java.util.Map;	
import java.util.stream.Collectors;	
	
/**	
 * 检查顺子牌型	
 */	
public class A3PKShunZiImpl<T> extends A3PKBaseCardType<T> {	
	
    @Override	
    public boolean resultType(AbsPKSetPos mSetPos, List<Integer> privateCardList, PKOpCard opCard) {	
        A3PKRoom room = ((A3PKRoom) mSetPos.getRoom());	
        PKCurOutCardInfo curOutCard = mSetPos.getSet().getCurOutCard();	
        int outCardSize = opCard.getCardList().size();	
        if (outCardSize != 5 || checkExistA(opCard.getCardList(),room.RoomCfg(A3PKRoomEnum.A3PKCfgEnum.ShunZiBuDaiA))) {	
            // 2不能出现在顺子里；点数相连的5张或以上的牌；	
            CommLogD.error("A3PKShunZiImpl not laizi2 roomId:{},SetId:{},PosId:{},opCard:{}", mSetPos.getRoom().getRoomID(), mSetPos.getSet().getSetID(), mSetPos.getPosID(), opCard.getCardList());	
            return false;	
        }	
        Map<Integer, Long> map = opCard.getCardList().stream().collect(Collectors.groupingBy(k -> BasePocker.getCardValueEx(k), Collectors.counting()));	
        if (map.values().stream().anyMatch(k -> k != 1L)) {	
            // 存在不是对子	
            CommLogD.error("A3PKShunZiImpl not laizi3 roomId:{},SetId:{},PosId:{},opCard:{}", mSetPos.getRoom().getRoomID(), mSetPos.getSet().getSetID(), mSetPos.getPosID(), opCard.getCardList());	
            return false;	
        }	
        List<Integer> keyList = map.keySet().stream().sorted(Comparator.comparing(Integer::intValue).reversed()).collect(Collectors.toList());	
        if (!A3PKRoomEnum.existKeyList(keyList,5,5)) {	
            CommLogD.error("A3PKShunZiImpl not existKeyList roomId:{},SetId:{},PosId:{},opCard:{}", mSetPos.getRoom().getRoomID(), mSetPos.getSet().getSetID(), mSetPos.getPosID(), opCard.getCardList());	
            return false;	
        }	
        int maxKey = 0;	
        if (keyList.containsAll(A3PKRoomEnum.C23_LIST)) {	
            maxKey = keyList.stream().filter(k->!A3PKRoomEnum.CA23_LIST.contains(k)).max(Comparator.comparing(Integer::intValue)).orElse(0);	
        } else {	
            maxKey = keyList.stream().max(Comparator.comparing(Integer::intValue)).orElse(0);	
        }	
        int finalMaxKey = maxKey;	
        int compCardId = opCard.getCardList().stream().filter(k-> finalMaxKey == BasePocker.getCardValueEx(k)).max(Comparator.comparing(Integer::intValue)).orElse(0);	
        if (this.checkNotCurOutCard(A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_SHUNZI, curOutCard, mSetPos.getPosID(), opCard.getCardList()) && (A3PKRoomEnum.compLeCardId(compCardId, curOutCard.getCompValue()) || keyList.size() != curOutCard.getCompSize())) {	
            // 不符合出牌规则	
            CommLogD.error("A3PKShunZiImpl not laizi roomId:{},SetId:{},PosId:{},opCard:{}", mSetPos.getRoom().getRoomID(), mSetPos.getSet().getSetID(), mSetPos.getPosID(), opCard.getCardList());	
            return false;	
        }	
        if(A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_SHUNZI.value() < curOutCard.getOutCardType()) {	
            CommLogD.error("A3PKShunZiImpl outCardType:{}", curOutCard.getOutCardType());	
            return false;	
        }	
        return curOutCard.setCurOutCards(mSetPos.getPosID(), A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_SHUNZI.value(), opCard.getCardList(), compCardId, keyList.size());	
    }	
	
	
    @Override	
    public PKOpCard robotResultType(AbsPKSetPos mSetPos, PKCurOutCardInfo curOutCard, T item) {	
        A3PKRoom room = ((A3PKRoom) mSetPos.getRoom());	
        Map<Integer, List<Integer>> map = (Map<Integer, List<Integer>>) item;	
        int compSize = 5;	
        List<Integer> cardList = map.entrySet().stream().filter(k -> A3PKRoomEnum.isNotTrumpOrAEx(k.getKey(),room.RoomCfg(A3PKRoomEnum.A3PKCfgEnum.ShunZiBuDaiA)) && k.getValue().size() == 1).map(k -> k.getKey()).sorted(Comparator.comparing(Integer::intValue).reversed()).collect(Collectors.toList());	
        if (CollectionUtils.isEmpty(cardList) ||  cardList.size() < compSize) {	
            // 没有满足的类型	
            return null;	
        }	
        List<List<Integer>> allKeyList = A3PKRoomEnum.getKeyList(cardList,compSize,compSize);	
        if (CollectionUtils.isEmpty(allKeyList)) {	
            // key 列表	
            return null;	
        }	
        List<Integer> keyList = allKeyList.stream().filter(k->CollectionUtils.isNotEmpty(k) && k.size() == 5 && k.get(4) >curOutCard.getCompValue()).findFirst().orElse(Collections.emptyList());	
        return PKOpCard.OpCard(A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_SHUNZI.value(), keyList.stream().flatMap(k->map.get(k).stream().limit(1)).collect(Collectors.toList()));	
    }	
}	
