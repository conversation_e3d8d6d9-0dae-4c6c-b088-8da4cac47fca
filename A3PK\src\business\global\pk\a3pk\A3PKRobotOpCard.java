package business.global.pk.a3pk;	
	
import business.global.pk.*;	
import business.global.pk.robot.PKRobotOpCard;	
import cenum.PKOpType;	
import com.ddm.server.websocket.handler.requset.WebSocketRequestDelegate;	
	
import java.util.Collections;	
import java.util.List;	
import java.util.Objects;	
	
public class A3PKRobotOpCard extends PKRobotOpCard {	
    public A3PKRobotOpCard(AbsPKSetRound setRound) {	
        super(setRound);	
    }	
	
	
    /**	
     * 存在首牌	
     *	
     * @return	
     */	
    @Override	
    public int existOutCard(List<PKOpType> opTypes, AbsPKSetPos mSetPos) {	
        if (opTypes.stream().anyMatch(k -> PKOpType.Refuse.equals(k))) {	
            this.getSetRound().opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), PKOpType.Refuse, PKOpCard.OpCard(0, Collections.emptyList()));	
        } else if (opTypes.stream().anyMatch(k -> PKOpType.Pass.equals(k))) {	
            PKOpCard opCard = mSetPos.getSetPosRobot().getAutoCard();	
            if (Objects.isNull(opCard)) {	
                this.getSetRound().opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), PKOpType.Pass, PKOpCard.OpCard(0, Collections.emptyList()));	
            } else {	
                this.getSetRound().opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), PKOpType.Out, PKOpCard.OpCard(opCard.getOpValue(), opCard.getCardList()));	
            }	
        } else {	
            this.getSetRound().opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), PKOpType.Out, mSetPos.getSetPosRobot().getAutoCard());	
        }	
        return 0;	
    }	
	
	
}	
