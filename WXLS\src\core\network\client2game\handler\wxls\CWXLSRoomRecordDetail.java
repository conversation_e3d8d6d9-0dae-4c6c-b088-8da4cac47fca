package core.network.client2game.handler.wxls;	
	
import business.player.Player;	
import business.wxls.c2s.iclass.CWXLS_RoomRecordDetail;	
import com.ddm.server.websocket.handler.requset.WebSocketRequest;	
import com.google.gson.Gson;	
import core.network.client2game.handler.PlayerHandler;	
//import jsproto.c2s.iclass.wxls.CFQPLS_RoomRecordDetail;	
	
import java.io.IOException;	
	
/**	
 * 房间记录详情	
 * <AUTHOR>	
 *	
 */	
public class CWXLSRoomRecordDetail extends PlayerHandler {	
		
	
    @Override	
    public void handle(Player player, WebSocketRequest request, String message) throws IOException {	
    	final CWXLS_RoomRecordDetail req = new Gson().fromJson(message, CWXLS_RoomRecordDetail.class);	
    	long roomID = req.roomID;	
    		
//    	RoomRecord record = RoomRecordMgr.getInstance().getRoom(roomID);	
//    	if (null == record){	
//    		request.error(ErrorCode.NotAllow, "not find roomID:" + roomID);	
//    		return;	
//    	}	
//	
    	request.response();	
    }	
}	
