package business.ahhbmj.c2s.iclass;						
						
import cenum.mj.OpType;						
import jsproto.c2s.cclass.BaseSendMsg;						
						
/**						
 * 补花						
 *						
 * @param <T>						
 * <AUTHOR>						
 */						
public class SAHHBMJ_Applique<T> extends BaseSendMsg {						
    public long roomID;						
    public int pos;						
    public OpType opType;						
    public int opCard;						
    public boolean isFlash;						
    public T set_Pos;						
    public int cardRestSize;						
						
						
    public static <T> SAHHBMJ_Applique make(long roomID, int pos, OpType opType, int opCard, boolean isFlash, T set_Pos, int cardRestSize) {						
        SAHHBMJ_Applique ret = new SAHHBMJ_Applique();						
        ret.roomID = roomID;						
        ret.pos = pos;						
        ret.opType = opType;						
        ret.opCard = opCard;						
        ret.isFlash = isFlash;						
        ret.set_Pos = set_Pos;						
        ret.cardRestSize = cardRestSize;						
        return ret;						
						
						
    }						
}						
