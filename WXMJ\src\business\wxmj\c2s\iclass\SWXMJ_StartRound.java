package business.wxmj.c2s.iclass;			
import jsproto.c2s.cclass.*;			
			
			
@SuppressWarnings("serial")			
public class SWXMJ_StartRound<T> extends BaseSendMsg {			
    			
    public long roomID;			
    public T room_SetWait;			
			
			
    public static <T> SWXMJ_StartRound<T> make(long roomID, T room_SetWait) {			
    	SWXMJ_StartRound<T> ret = new SWXMJ_StartRound<T>();			
        ret.roomID = roomID;			
        ret.room_SetWait = room_SetWait;			
			
        return ret;			
    			
			
    }			
}			
