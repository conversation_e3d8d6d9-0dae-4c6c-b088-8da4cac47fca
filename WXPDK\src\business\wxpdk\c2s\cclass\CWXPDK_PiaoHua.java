package business.wxpdk.c2s.cclass;
import jsproto.c2s.cclass.BaseSendMsg;

/**
 * 莆田麻将
 * 接收客户端数据
 * 创建房间
 * <AUTHOR>
 *
 */
@SuppressWarnings("serial")
public class CWXPDK_PiaoHua extends BaseSendMsg{

	public long roomID;  
	public int piaoHua;  // 0不飘花 1飘花 2飘花
	
    public static CWXPDK_<PERSON>ao<PERSON>ua make(long roomID, int piaoFen) {
    	CWXPDK_PiaoHua ret = new CWXPDK_PiaoHua();
    	ret.roomID = roomID;
    	ret.piaoHua = piaoFen ;
        return ret;
    }
}
