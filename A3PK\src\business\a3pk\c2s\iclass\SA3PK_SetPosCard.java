package business.a3pk.c2s.iclass;	
	
import java.util.ArrayList;	
import java.util.List;	
	
import jsproto.c2s.cclass.BaseSendMsg;	
	
/**	
 * 重新设置玩家手牌	
 * <AUTHOR>	
 * @param <T>	
 *	
 */	
public class SA3PK_SetPosCard<T> extends BaseSendMsg  {	
    public long roomID;	
	// 每个玩家的牌面	
	public List<T> setPosList = new ArrayList<>();	
	
	
    public static <T>SA3PK_SetPosCard make(long roomID,List<T> setPosList) {	
    	SA3PK_SetPosCard ret = new SA3PK_SetPosCard();	
        ret.roomID = roomID;	
        ret.setPosList = setPosList;	
	
        return ret;	
    	
	
    }	
}	
