package business.global.mj.ahhbmj.optype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.ahhbmj.AHHBMJSetPos;
import business.global.mj.op.PengCardImpl;

/**
 * 检查暗杠
 *
 * <AUTHOR>
 */
public class AHHBMJPengCardImpl extends PengCardImpl {
    @Override
    public boolean checkOpCard(AbsMJSetPos mSetPos, int cardId) {
        AHHBMJSetPos setPos = (AHHBMJSetPos) mSetPos;
        if (setPos.isTing()) {
            return false;
        }
        return super.checkOpCard(mSetPos, cardId);
    }

}									
