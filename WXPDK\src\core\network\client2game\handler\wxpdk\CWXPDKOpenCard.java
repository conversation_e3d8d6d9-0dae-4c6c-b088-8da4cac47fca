package core.network.client2game.handler.wxpdk;

import business.global.pk.wxpdk.WXPDKRoom;
import business.global.pk.wxpdk.WXPDKRoomSet;
import business.global.room.RoomMgr;
import business.player.Player;
import business.wxpdk.c2s.iclass.CWXPDK_OpenCard;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.exception.WSException;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;
import core.network.client2game.handler.PlayerHandler;

import java.io.IOException;

/**
 * 明牌
 * */

public class CWXPDKOpenCard extends PlayerHandler{

	@Override
	public void handle(Player player, WebSocketRequest request, String message) throws WSException, IOException {
		final CWXPDK_OpenCard clientPack = new Gson().fromJson(message, CWXPDK_OpenCard.class);
    	
		WXPDKRoom room = (WXPDKRoom) RoomMgr.getInstance().getRoom(clientPack.roomID);
    	if (null == room){
    		request.error(ErrorCode.NotAllow, "CWXPDKOpenCard not find room:"+clientPack.roomID);
    		return;
    	}
    	WXPDKRoomSet set =  (WXPDKRoomSet) room.getCurSet();
    	if(null == set){
    		request.error(ErrorCode.NotAllow, "CWXPDKOpenCard not set room:"+clientPack.roomID);
    		return;
    	}
		request.response();
//    	set.onOpenCard(request, clientPack);
	}
}
