package business.ahmj.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;

/**
 * 莆田麻将
 * 接收客户端数据
 * 创建房间
 *
 * <AUTHOR>
 */
@SuppressWarnings("serial")
public class CAHMJ_DaNiao extends BaseSendMsg {

    public long roomID;
    public int niao;  // -1 没有操作  0不打鸟 1打鸟5

    public static CAHMJ_DaNiao make(long roomID, int niao) {
        CAHMJ_DaNiao ret = new CAHMJ_DaNiao();
        ret.roomID = roomID;
        ret.niao = niao;
        return ret;
    }
}