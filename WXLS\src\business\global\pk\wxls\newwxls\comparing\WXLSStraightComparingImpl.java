package business.global.pk.wxls.newwxls.comparing;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
	
import java.util.ArrayList;	
import java.util.List;	
	
/**	
 * 顺子的大小比较(按顺序比较)	
 */	
public class WXLSStraightComparingImpl extends WXLSAbstractComparing {	
	
    @Override	
    public int compare(WXLSPlayerDun o1, WXLSPlayerDun o2) {	
        List<WXLSPockerCard> cards = o1.getCards();	
        List<WXLSPockerCard> newcards = new ArrayList<WXLSPockerCard>();	
        newcards = cards;	
        List<WXLSPockerCard> cards2 = o2.getCards();	
        List<WXLSPockerCard> newcards2 = new ArrayList<WXLSPockerCard>();	
        newcards2 = cards2;	
        int ret;	
        if(o1.cfg.getKexuanwanfa().contains(0)){	
            ret= this.seqComparingStraight(newcards, newcards2);	
        }else {	
            ret=this.seqComparing(newcards,newcards2);	
        }
        if(ret==0){
            return  compareZeroByColor(o1,o2);
        }
        return ret;	
	
    }	
	
}	
	
