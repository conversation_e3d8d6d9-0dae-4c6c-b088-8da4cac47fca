package business.global.mj.wwmj;									
									
import business.global.room.base.AbsBaseRoom;									
import business.global.room.mj.MJRoomPos;									
									
/**									
 * 房间内每个位置信息									
 *									
 * @param <T>									
 * <AUTHOR>									
 */									
									
public class WWMJRoomPos<T> extends MJRoomPos {									
    /**					
     * 小局结算托管次数									
     */									
    private int setEndTrusteeshipCount = 0;									
    public WWMJRoomPos(int posID, AbsBaseRoom room) {									
        super(posID, room);									
    }									
									
									
    @Override									
    public void setTrusteeship(boolean isTrusteeship) {									
        if (!isTrusteeship) {									
            //小局结算托管次数									
            clearSetEndTrusteeshipCount();									
        }									
        super.setTrusteeship(isTrusteeship);									
    }									
									
    public int getSetEndTrusteeshipCount() {									
        return this.isTrusteeship() ? setEndTrusteeshipCount : 0;									
    }									
									
    public void addSetEndTrusteeshipCount() {									
        if (this.isTrusteeship()) {									
            this.setEndTrusteeshipCount++;									
        }									
    }									
									
    public void clearSetEndTrusteeshipCount() {									
        this.setEndTrusteeshipCount = 0;									
    }									
					
									
}									
