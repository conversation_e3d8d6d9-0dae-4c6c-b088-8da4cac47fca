package business.global.mj.ahhbmj.optype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCardInit;
import business.global.mj.ahhbmj.AHHBMJNormalHuCardImpl;
import business.global.mj.ahhbmj.AHHBMJSetPos;
import business.global.mj.manage.MJFactory;
import business.global.mj.op.AnGangCardImpl;
import cenum.mj.MJSpecialEnum;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**	
 * 检查暗杠	
 *	
 * <AUTHOR>	
 */	
public class AHHBMJAnGangCardImpl extends AnGangCardImpl {	
	
    @Override	
    public boolean checkOpCard(AbsMJSetPos mSetPos, int specialCard) {	
        Map<Integer, Long> map = mSetPos.allCards().stream()	
                // 筛选出所有的牌类型			
                .map(k -> k.getType())	
                // 检查等于金牌 或者 不是花牌			
                .filter(k -> this.checkFilter(mSetPos, k))	
                // 按牌类型分组			
                .collect(Collectors.groupingBy(p -> p, Collectors.counting()));	
        if (null == map || map.size() <= 0) {	
            return false;	
        }	
        AHHBMJSetPos set_pos = (AHHBMJSetPos) mSetPos;
        // 遍历出相同类型 >= 4.			
        if (!set_pos.isTing()) {
            for (Integer key : map.keySet()) {	
                Long value = map.get(key);	
                if (value.intValue() >= 4) {	
                    return true;	
                }	
            }	
            return false;	
        } else {	
            List<Integer> types = new ArrayList<>();	
            for (Integer key : map.keySet()) {	
                Long value = map.get(key);	
                if (value.intValue() < 4) {	
                    continue;	
                }	
                if (checkGangHouTing(mSetPos, mSetPos.mjCardInit(true), key)) {
                    types.add(key);	
                }	
            }	
            if (types.size() > 0) {	
                set_pos.getGangList().addAll(types);	
                return true;	
            }	
            return false;	
        }	
    }	
	
	
    /**	
     * 检查过滤器	
     */	
    @Override	
    protected boolean checkFilter(AbsMJSetPos mSetPos, int type) {	
        return !mSetPos.getSet().getmJinCardInfo().checkJinExist(type) && type < MJSpecialEnum.NOT_HUA.value();	
    }
    /**
     * 检测杠玩 是否还能听牌
     *
     * @return
     */
    public  boolean checkGangHouTing(AbsMJSetPos mSetPos, MJCardInit mCardInit, int type) {
        List<Integer> cards = new ArrayList<>();
        for (int card : mCardInit.getAllCardInts()) {
            if (card == type) {
                cards.add(card);
            }
        }
        mCardInit.getAllCardInts().removeAll(cards);
        mCardInit.getJins().add(MJSpecialEnum.NOT_JIN.value());
        if (MJFactory.getHuCard(AHHBMJNormalHuCardImpl.class).checkHuCard(mSetPos, mCardInit)) {
            return true;
        }
        return false;
    }
}									
