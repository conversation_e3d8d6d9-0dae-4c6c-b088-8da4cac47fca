package business.ahmj.c2s.iclass;	
	
import jsproto.c2s.cclass.BaseSendMsg;	
import lombok.Data;	
	
import java.util.ArrayList;	
import java.util.List;	
	
@Data	
public class SAHMJ_DiaoYu extends BaseSendMsg {	
    public long roomID;	
    /**	
     *  0：不钓鱼  1：钓鱼  2：爆炸鱼1 3：爆炸鱼2	
      */	
    public int wanFa;	
    /**	
     * 钓鱼（爆炸鱼）列表	
     */	
    public List<Integer> fanPai = new ArrayList<>();	
    /**	
     * 钓鱼中条数	
     */	
    public  int zhongTiaoShu ;	
    	
    public static SAHMJ_DiaoYu make(long roomID, List<Integer> fanPai, int wanFa,int zhongTiaoShu) {	
        SAHMJ_DiaoYu ret = new SAHMJ_DiaoYu();	
        ret.roomID = roomID;	
        ret.fanPai = fanPai;	
        ret.wanFa = wanFa;	
        ret.zhongTiaoShu = zhongTiaoShu;	
        return ret;	
    }	
}	
