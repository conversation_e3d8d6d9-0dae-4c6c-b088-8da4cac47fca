package business.wxmj.c2s.iclass;			
			
import jsproto.c2s.cclass.BaseSendMsg;			
			
/**			
 * 莆田麻将 接收客户端数据 创建房间			
 * 			
 * <AUTHOR>			
 *			
 */			
@SuppressWarnings("serial")			
public class SWXMJ_PiaoFen extends BaseSendMsg {			
			
	public long roomID;			
	public int piaoFen; // -1 没有操作 0不飘分 1飘分 2漂一分			
	public int pos;			
			
	public static SWXMJ_PiaoFen make(long roomID, int pos, int piaoFen) {			
		SWXMJ_PiaoFen ret = new SWXMJ_PiaoFen();			
		ret.roomID = roomID;			
		ret.piaoFen = piaoFen;			
		ret.pos = pos;			
		return ret;			
	}			
			
}			
