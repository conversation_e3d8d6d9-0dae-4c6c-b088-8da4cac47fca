package business.wxpdk.c2s.cclass;

/*
 * 跑得快宏定义
 * <AUTHOR>
 * **/
public class WXPDK_define {

	//跑得快的关门状态
	public static enum WXPDK_ROBCLOSE_STATUS{
		WXPDK_ROBCLOSE_STATUS_NOMAL(-1),
		WXPDK_ROBCLOSE_STATUS_FAIL(0),
		WXPDK_ROBCLOSE_STATUS_SUCCESS(1),
		WXPDK_ROBCLOSE_STATUS_REVERES(2),//反关门
		;

		private int value;
		private WXPDK_ROBCLOSE_STATUS(int value) {this.value = value;}
		public int value() {return this.value;}

		public static WXPDK_ROBCLOSE_STATUS getRobCloseStatus(String value) {
			String gameTypyName = value.toUpperCase();
			for (WXPDK_ROBCLOSE_STATUS flow : WXPDK_ROBCLOSE_STATUS.values()) {
				if (flow.toString().equals(gameTypyName)) {
					return flow;
				}
			}
			return WXPDK_ROBCLOSE_STATUS.WXPDK_ROBCLOSE_STATUS_NOMAL;
		}

		public static WXPDK_ROBCLOSE_STATUS valueOf(int value) {
			for (WXPDK_ROBCLOSE_STATUS flow : WXPDK_ROBCLOSE_STATUS.values()) {
				if (flow.value == value) {
					return flow;
				}
			}
			return WXPDK_ROBCLOSE_STATUS.WXPDK_ROBCLOSE_STATUS_NOMAL;
		}
	};

	//跑得快类型
	public static enum WXPDK_GameType{
		//		WXPDK_WXPDK(1), //跑得快
		WXPDK_FJ(2), //龙岩伏击
//		WXPDK_ZSY(3), //争上游

		;

		private int value;
		private WXPDK_GameType(int value) {this.value = value;}
		public int value() {return this.value;}

		public static WXPDK_GameType getGameType(String value) {
			String gameTypyName = value.toUpperCase();
			for (WXPDK_GameType flow : WXPDK_GameType.values()) {
				if (flow.toString().equals(gameTypyName)) {
					return flow;
				}
			}
			return WXPDK_GameType.WXPDK_FJ;
		}

		public static WXPDK_GameType valueOf(int value) {
			for (WXPDK_GameType flow : WXPDK_GameType.values()) {
				if (flow.value == value) {
					return flow;
				}
			}
			return WXPDK_GameType.WXPDK_FJ;
		}
	};


	//跑得快游戏状态
	public static enum WXPDK_GameStatus{
		WXPDK_GAME_STATUS_SENDCARD(0), //发牌
		//		WXPDK_GAME_STATUS_COMPAER_ONE(1), //比牌
		WXPDK_GAME_STATUS_COMPAER_SECOND(1), //比牌
		WXPDK_GAME_STATUS_RESULT(2), //结算
		WXPDK_GAME_STATUS_WaitingEx(3),//飘花阶段
		;

		private int value;
		private WXPDK_GameStatus(int value) {this.value = value;}
		public int value() {return this.value;}

		public static WXPDK_GameStatus getGameStatus(String value) {
			String gameTypyName = value.toUpperCase();
			for (WXPDK_GameStatus flow : WXPDK_GameStatus.values()) {
				if (flow.toString().equals(gameTypyName)) {
					return flow;
				}
			}
			return WXPDK_GameStatus.WXPDK_GAME_STATUS_SENDCARD;
		}

		public static WXPDK_GameStatus valueOf(int value) {
			for (WXPDK_GameStatus flow : WXPDK_GameStatus.values()) {
				if (flow.value == value) {
					return flow;
				}
			}
			return WXPDK_GameStatus.WXPDK_GAME_STATUS_SENDCARD;
		}
	};

	//玩法
	public static enum WXPDK_WANFA{
		WXPDK_WANFA_SHOUJUHEITAO3(0),  	//首局黑桃3先出
		WXPDK_WANFA_XIANCHUHEITAO3(1),  	//先出黑桃三 每局
		WXPDK_WANFA_FEIBICHU(2),  		//非比出
		WXPDK_WANFA_MAXZHADAN(3),  		//3炸封顶
		WXPDK_WANFA_3AZHA(4),  			//3A炸
		WXPDK_WANFA_XUEZHANDAODI(5),  	//血战到底
		WXPDK_WANFA_ZHADANBUKECHAI(6),  	//炸弹不可拆
		WXPDK_WANFA_HONGTAO10FANBEI(7),  //红桃10翻倍
		WXPDK_WANFA_QUSANZHANG(8),  		//去3张
		WXPDK_WANFA_KEMINGPAI(9),  		//可明牌
		WXPDK_WANFA_4DAIFAN(10),  		//4带翻倍
		WXPDK_WANFA_XIANSHISHOUPAI(11),  	//显示手牌
		WXPDK_WANFA_QIANGGUANMEN(12),  	//抢关门
		WXPDK_WANFA_3BUDAI(13),  			//3不带
		WXPDK_WANFA_3DAI1(14),  			//3带1
		WXPDK_WANFA_3DAI2(15),  			//3带2
		WXPDK_WANFA_4DAI1(16),  			//4带1
		WXPDK_WANFA_4DAI2(17),  			//4带2
		WXPDK_WANFA_4DAI3(18),  			//4带3
		WXPDK_WANFA_LAIZI(19),  			//赖子
		WXPDK_WANFA_KEJIABEI(20),  		//可加倍
		WXPDK_WANFA_ZHANDANFANBEI(21),  	//炸弹翻倍
		WXPDK_WANFA_ZHADANKECHAI(22),  	//炸弹可拆
		WXPDK_WANFA_3AZHAMAX(23),  	//3A炸最大
		WXPDK_WANFA_HONGTAO10ZHANIAO(24),  	//红桃十扎鸟
		WXPDK_WANFA_HEITAO3BUBI(25),  	//黑桃三不必先出
		WXPDK_WANFA_SHOUDONGGUO(26),  	//手动过
		WXPDK_WANFA_SHOUDONGZHUNBEI(27),  	//手动准备
		WXPDK_WANFA_WEIZHANGSUANFEN(28),  	//尾张算分
		WXPDK_WANFA_WUZHA(29),  	//无炸弹
		WXPDK_WANFA_NOTBELOWZERO(31),  	//不能低于0
		WXPDK_WANFA_OnlyWinRightNowPoint(32),     //每局带多少
		WXPDK_WANFA_Leavel1413(33),//剩14、15张加扣7分等等
		WXPDK_WANFA_MeiJuHeiTaoSan(34),//每局必发黑桃3
		;
		private int value;
		private WXPDK_WANFA(int value) {this.value = value;}
		public int value() {return this.value;}

		public static WXPDK_WANFA getGameType(String value) {
			String gameTypyName = value.toUpperCase();
			for (WXPDK_WANFA flow : WXPDK_WANFA.values()) {
				if (flow.toString().equals(gameTypyName)) {
					return flow;
				}
			}
			return WXPDK_WANFA.WXPDK_WANFA_XIANCHUHEITAO3;
		}

		public static WXPDK_WANFA valueOf(int value) {
			for (WXPDK_WANFA flow : WXPDK_WANFA.values()) {
				if (flow.value == value) {
					return flow;
				}
			}
			return WXPDK_WANFA.WXPDK_WANFA_XIANCHUHEITAO3;
		}
	};


	//牌类型
	public static enum WXPDK_CARD_TYPE{
		WXPDK_CARD_TYPE_NOMARL(0),			//默认状态
		WXPDK_CARD_TYPE_BUCHU(1),				//不出
		WXPDK_WANFA_SINGLECARD(2), 			//单牌
		WXPDK_CARD_TYPE_DUIZI(3),  			//对子
		WXPDK_CARD_TYPE_SHUNZI(4),  			//顺子
		WXPDK_CARD_TYPE_3BUDAI(5),  			//3不带
		WXPDK_CARD_TYPE_3DAI1(6),  			//3带1
		WXPDK_CARD_TYPE_3DAI2(7),  			//3带2
		WXPDK_CARD_TYPE_4DAI1(8),  			//4带1
		WXPDK_CARD_TYPE_4DAI2(9),  			//4带2
		WXPDK_CARD_TYPE_4DAI3(10),  			//4带3
		WXPDK_CARD_TYPE_ZHADAN(11),  			//炸弹
		WXPDK_CARD_TYPE_FEIJI3(12),  			//飞机3
		WXPDK_CARD_TYPE_FEIJI4(13),  			//飞机4
		WXPDK_WANFA_LIANDUI(14), 				//联队
		WXPDK_CARD_TYPE_FEIJI31(16), 				//飞机带1张
		WXPDK_CARD_TYPE_FEIJI32(17), 				//飞机带2张
		WXPDK_CARD_TYPE_FEIJI33(18), 				//飞机不带
		WXPDK_CARD_TYPE_4DAI2DUI(19),  			//4带2对

		;
		private int value;
		private WXPDK_CARD_TYPE(int value) {this.value = value;}
		public int value() {return this.value;}

		public static WXPDK_CARD_TYPE getGameType(String value) {
			String gameTypyName = value.toUpperCase();
			for (WXPDK_CARD_TYPE flow : WXPDK_CARD_TYPE.values()) {
				if (flow.toString().equals(gameTypyName)) {
					return flow;
				}
			}
			return WXPDK_CARD_TYPE.WXPDK_CARD_TYPE_DUIZI;
		}

		public static WXPDK_CARD_TYPE valueOf(int value) {
			for (WXPDK_CARD_TYPE flow : WXPDK_CARD_TYPE.values()) {
				if (flow.value == value) {
					return flow;
				}
			}
			return WXPDK_CARD_TYPE.WXPDK_CARD_TYPE_DUIZI;
		}
	}

	/**
	 * 炸弹算法
	 */
	public enum BombAlgorithm {
		GETROUNDALLBOMB(0),			// 炸弹归每轮最大玩家
		WINNER(1),			// 只算赢家
		PASS(2),			// 炸弹不算分
		ALWAYS(3);			// 有炸就算

		private int type;
		BombAlgorithm(int type) {
			this.type = type;
		}

		/**
		 * 类型转枚举
		 * @param type
		 * @return
		 */
		public static BombAlgorithm valueOf(int type) {
			for (BombAlgorithm bombAlgorithm : BombAlgorithm.values()) {
				if (bombAlgorithm.type == type) {
					return bombAlgorithm;
				}
			}
			throw new IllegalArgumentException("错误的炸弹算法");
		}

		/**
		 * 是否相同类型
		 * @param type
		 * @return
		 */
		public boolean has(int type) {
			return this.type == type;
		}

		public int getType() {
			return type;
		}
	}

	/**
	 * 炸弹分数计算
	 */
	public enum BombScore {
		DOUBLE_(0),			// 翻倍
		ADD_TEN(1);			// 加10分

		private int type;
		BombScore(int type) {
			this.type = type;
		}

		/**
		 * 类型转枚举
		 * @param type
		 * @return
		 */
		public static BombScore valueOf(int type) {
			for (BombScore bombScore : BombScore.values()) {
				if (bombScore.type == type) {
					return bombScore;
				}
			}
			throw new IllegalArgumentException("炸弹分数计算");
		}

		/**
		 * 是否相同炸弹计算方式
		 * @param type
		 * @return
		 */
		public boolean has(int type) {
			return this.type == type;
		}

		public int getType() {
			return type;
		}
	}


	public enum WXPDKGameRoomConfigEnum {
		/**
		 * 房间内切换人数
		 */
		FangJianQieHuanRenShu,
		/**
		 * 小局托管解散
		 */
		SetAutoJieSan,
		/**
		 * 小局托管2解散
		 */
		SetAutoJieSan2,
		;
	}

	//3带
	public static enum WXPDK_SanDai{
		WXPDK_WANFA_3DAI1(0),  			//3带1
		WXPDK_WANFA_3DAI2(1),  			//3带2
		WXPDK_WANFA_3DAI21(2),  			//3带1对
		WXPDK_WANFA_3BUDAI(3),  			//3不带
		;
		private int value;
		private WXPDK_SanDai(int value) {this.value = value;}
		public int value() {return this.value;}
	};

	public enum WXPDKCfg {
		BuPiao(0),// 不飘
		Piao(1), // 自选飘
		GDPiao1(2), // 固定飘一分
		GDPiao2(3), // 固定飘二分
		GDPiao5(4); // 固定飘4分
		private int value;

		private WXPDKCfg(int value) {
			this.value = value;
		}

		public int value() {
			return value;
		}
	}

	public enum WXPDKPiaoMoShi {
		First(0),// 首局飘
		Every(1);// 局局飘
		private int value;

		private WXPDKPiaoMoShi(int value) {
			this.value = value;
		}

		public int value() {
			return value;
		}
	}

	public enum WXPDKPiaoHua {
		NOT_OP(-1),//没有操作
		NOT_PIAO(0),//不飘花
		PIAO1(1),//飘1花
		PIAO2(2),//飘2花
		PIAO3(3),//飘3花
		PIAO4(4),//飘4花
		PIAO5(5),//飘5花

		;
		private int value;

		private WXPDKPiaoHua(int value) {
			this.value = value;
		}

		public int value() {
			return value;
		}

		public static WXPDKPiaoHua valueOf(int value) {
			for (WXPDKPiaoHua flow : WXPDKPiaoHua.values()) {
				if (flow.value == value) {
					return flow;
				}
			}
			return WXPDKPiaoHua.NOT_OP;
		}
	}

}
