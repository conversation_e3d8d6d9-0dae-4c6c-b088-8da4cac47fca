package business.rocketmq.ahmj.consumer;

import business.rocketmq.constant.MqTopic;
import business.rocketmq.consumer.BaseExitRoomConsumer;
import com.ddm.server.annotation.Consumer;
import com.ddm.server.common.rocketmq.MqConsumerHandler;
import core.server.ahmj.AHMJAPP;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
 * create at:  2020-09-08  10:00
 * @description: 退出房间
 */
@Consumer(topic = MqTopic.BASE_EXIT_ROOM, id = AHMJAPP.gameTypeId)
public class AHMJExitRoomConsumer extends BaseExitRoomConsumer implements MqConsumerHandler {


    @Override
    public void action(Object body) {
        super.action(body, AHMJAPP.GameType().getId());
    }
}
