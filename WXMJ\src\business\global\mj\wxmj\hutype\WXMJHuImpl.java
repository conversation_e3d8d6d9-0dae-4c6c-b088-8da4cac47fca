package business.global.mj.wxmj.hutype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCardInit;
import business.global.mj.hu.PPHuCardImpl;
import business.global.mj.wxmj.WXMJRoomEnum;
import business.global.mj.hu.BaseHuCard;
import business.global.mj.hu.DDHuCardImpl;
import business.global.mj.hu.NormalHuCardImpl;
import business.global.mj.manage.MJFactory;
import business.global.mj.util.HuUtil;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class WXMJHuImpl extends BaseHuCard {

    @Override
    public boolean checkHuCard(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
        if (null == mCardInit) {
            return false;
        }
        if (MJFactory.getHuCard(PPHuCardImpl.class).checkHuCard(mSetPos, mCardInit)) {
            if (mSetPos.getPrivateCard().size() == 1) {//单吊可以
                mSetPos.getPosOpRecord().getOpHuList().add(WXMJRoomEnum.WXMJOpPoint.DDC);
            }
            mSetPos.getPosOpRecord().getOpHuList().add(WXMJRoomEnum.WXMJOpPoint.DDH);
            return true;
        }
        if (MJFactory.getHuCard(NormalHuCardImpl.class).checkHuCard(mSetPos, mCardInit)) {
            if (mSetPos.getPrivateCard().size() == 1) {//单吊可以
                mSetPos.getPosOpRecord().getOpHuList().add(WXMJRoomEnum.WXMJOpPoint.DDC);
            }
            return true;
        }
        return false;
    }

}
