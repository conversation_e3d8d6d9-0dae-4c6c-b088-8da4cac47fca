package business.afmj.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;


public class SAFMJ_ChangeRoomNum extends BaseSendMsg {
    
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	public long roomID;
    public String roomKey;
    public int createType;
    public static SAFMJ_ChangeRoomNum make(long roomID, String roomKey, int createType) {
    	SAFMJ_ChangeRoomNum ret = new SAFMJ_ChangeRoomNum();
        ret.roomID = roomID;
        ret.roomKey = roomKey;
        ret.createType = createType;
        return ret;
    }
}