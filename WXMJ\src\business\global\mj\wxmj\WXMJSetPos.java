package business.global.mj.wxmj;

import business.global.mj.AbsMJSetPos;
import business.global.mj.AbsMJSetRoom;
import business.global.mj.MJCard;
import business.global.mj.MJCardInit;
import business.global.mj.hu.NormalHuCardImpl;
import business.global.mj.manage.MJFactory;
import business.global.mj.wxmj.WXMJRoomEnum.WXMJOpPoint;
import business.global.mj.wxmj.hutype.WXMJTingImpl;
import business.global.room.mj.MJRoomPos;
import business.wxmj.c2s.cclass.WXMJResults;
import business.wxmj.c2s.cclass.WXMJSet_Pos;
import cenum.PrizeType;
import cenum.mj.HuType;
import cenum.mj.MJHuOpType;
import cenum.mj.OpPointEnum;
import cenum.mj.OpType;
import com.sun.org.apache.bcel.internal.generic.RET;
import jsproto.c2s.cclass.mj.BaseMJRoom_PosEnd;
import jsproto.c2s.cclass.mj.BaseMJSet_Pos;
import jsproto.c2s.cclass.room.AbsBaseResults;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 麻将 每一局每个位置信息
 *
 * <AUTHOR>
 */

public class WXMJSetPos extends AbsMJSetPos {

    public int piaofen = -1;
    public int jiePaoFen = -1;
    public boolean isTing = false;
    public boolean beiYongTing = false;
    public List<Integer> tingGang = new ArrayList<Integer>();

    public List<Integer> getTingGang() {
        return tingGang;
    }

    public void setTingGang(List<Integer> tingGang) {
        this.tingGang = tingGang;
    }

    public boolean isBeiYongTing() {
        return beiYongTing;
    }

    public void setBeiYongTing(boolean beiYongTing) {
        this.beiYongTing = beiYongTing;
    }

    public boolean isTing() {
        return isTing;
    }

    public void setTing(boolean ting) {
        isTing = ting;
    }

    public int getJiePaoFen() {
        return jiePaoFen;
    }

    public void setJiePaoFen(int jiePaoFen) {
        this.jiePaoFen = jiePaoFen;
    }

    public int getPiaofen() {
        return piaofen;
    }

    public void setPiaofen(int piaofen) {
        this.piaofen = piaofen;
    }

    public WXMJSetPos(int posID, MJRoomPos roomPos, AbsMJSetRoom set) {
        super(posID, roomPos, set, WXMJTingImpl.class);
        this.setMSetOp(new WXMJSetOp(this));
        this.setCalcPosEnd(new WXMJCalcPosEnd(this));
    }


    /**
     * 清空操作状态
     */
    public void clearOp() {
        ((WXMJSetOp) this.getmSetOp()).clearOp();
        this.getPosOpNotice().clearBuNengChuList();
    }


    /**
     * 获取手牌通知信息
     */
    @Override
    public BaseMJSet_Pos getNotify(boolean isSelf) {
        if (isRevealCard()) {
            isSelf = true;
        }
        WXMJSet_Pos ret = this.newMJSetPos();
        // 玩家位置		
        ret.setPosID(this.getPosID());
        ret.setPiaoFen(this.piaofen);
        // 是自己		
        int length = sizePrivateCard();
        for (int i = 0; i < length; i++) {
            ret.getShouCard().add(isSelf ? getPCard(i).cardID : 0);
        }
        // 可胡的牌		
        ret.setHuCard(isSelf ? this.getHuCardTypes() : null);
        if (this.getHandCard() != null) {
            // 首牌		
            ret.setHandCard(isSelf ? this.getHandCard().getCardID() : 5000);
        }
        ret.setTing(this.isTing);
        // 打出的牌		
        ret.setOutCard(this.getOutCardIDs());
        // 公共牌		
        ret.setPublicCardList(this.getPublicCardList());
        // 掉线连接		
        ret.setIsLostConnect(null);
        // 获取手牌通知信息		
        return ret;
    }

    /**
     * 新一局中各位置的信息
     *
     * @return
     */
    @Override
    protected WXMJSet_Pos newMJSetPos() {
        return new WXMJSet_Pos();
    }

    /**
     * 结算信息
     *
     * @return
     */
    protected AbsBaseResults mResultsInfo() {
        WXMJResults mResults = (WXMJResults) this.getResults();
        if (null == mResults) {
            // new 总结算		
            mResults = (WXMJResults) this.newResults();
            // 用户PID		
            mResults.setPid(this.getPid());
            // 位置		
            mResults.setPosId(this.getPosID());
            // 是否房主		
            mResults.setOwner(this.getPid() == this.getRoom().getOwnerID());
        }
        // 记录胡牌次数		
        if (!(HuType.NotHu.equals(this.getHuType()) || HuType.DianPao.equals(this.getHuType()))) {
            mResults.setHuCnt(this.pidHuCntEnd());
            mResults.addHuTypes(this.getHuType());
        }
        // 总分数
        mResults.setPoint(this.pidSumPointEnd());
        // 总竞技点分数		
        mResults.setSportsPoint(this.getRoomPos().sportsPoint());
        // 点炮次数		
        mResults.addDianPaoPoint(this.getHuType());
        // 接炮次数		
        mResults.addJiePaoPoint(this.getHuType());
        // 自摸次数		
        mResults.addZimoPoint(this.getHuType());
        return mResults;
    }

    /**
     * 计算位置小局分数
     */
    @Override
    public void calcPosPoint() {
        this.getCalcPosEnd().calcPosPoint(this);
    }

    /**
     * 操作类型
     */
    @Override
    public boolean doOpType(int cardID, OpType opType) {
        return this.getmSetOp().doOpType(cardID, opType);
    }


    /**
     * 检测类型
     */
    @Override
    public boolean checkOpType(int cardID, OpType opType) {
        return this.getmSetOp().checkOpType(cardID, opType);
    }


    /**
     * 玩家总分和胡牌次数
     */
    public void pidPointEnd() {
        this.getRoomPos().calcRoomPoint(this.getEndPoint());
        this.getRoomPos().setTempPoint(this.getRoomPos().getPoint());
        if (PrizeType.RoomCard.equals(getRoom().getBaseRoomConfigure().getPrizeType())) {
            this.getRoomPos().addCountPoint(this.getEndPoint());
        }
        if (this.getHuType() != HuType.NotHu && this.getHuType() != HuType.DianPao) {
            this.getRoomPos().setHuCnt(this.getRoomPos().getHuCnt() + 1);
        }
    }

    /**
     * 检测自摸胡
     */
    @Override
    public List<OpType> recieveOpTypes() {
        this.getPosOpRecord().clearOpCardType();
        List<OpType> opTypes = new ArrayList<OpType>();
        this.clearOutCard();
        this.getTingGang().clear();
        this.setJiePaoFen(-1);
        if (checkOpType(0, OpType.Hu)) {
            opTypes.add(OpType.Hu);
        }
        if (this.getHandCard() != null) {
            this.getPosOpRecord().setHuCardType(this.getHandCard().getType());
        }

        if (opTypes.size() > 0) {
            this.setmHuOpType(MJHuOpType.ZiMo);
        }
        if (this.opSize() >= 1 && opTypes.size() > 0) {
            this.getPosOpRecord().getOpHuList().add(WXMJOpPoint.GSH);
        }
        if (checkOpType(0, OpType.Ting) && !this.isTing) {
            opTypes.add(OpType.Ting);
        }
        if (checkOpType(0, OpType.Gang)) {
            opTypes.add(OpType.Gang);
        }
        if (this.isTing) {
            boolean nengangang = false;
            Map<Integer, List<MJCard>> cardTypeList = this.allCards().stream().collect(Collectors.groupingBy(MJCard::getType, Collectors.toList()));
            for (Map.Entry<Integer, List<MJCard>> ccc : cardTypeList.entrySet()) {
                if (ccc.getValue().size() == 4) {
                    List<MJCard> cards = new ArrayList<>(this.allCards());
                    cards.removeAll(ccc.getValue());
                    MJCardInit mmm = mCardInit(cards, 0, true);
                    mmm.addJins(60);
                    if (MJFactory.getHuCard(NormalHuCardImpl.class).checkHuCard(this, mmm)) {
                        this.getTingGang().add(ccc.getKey());
                        nengangang = true;
                    }
                }
            }
            if (nengangang) {
                opTypes.add(OpType.AnGang);
            }
        } else {
            if (checkOpType(0, OpType.AnGang)) {
                opTypes.add(OpType.AnGang);
            }

        }

        opTypes.add(OpType.Out);
        return opTypes;
    }


    /**
     * 操作
     *
     * @return
     */
    public int opSize() {
        return ((WXMJSetOp) this.getmSetOp()).opSize();
    }


    /**
     * 统计本局分数
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    @Override
    public BaseMJRoom_PosEnd calcPosEnd() {
        // 玩家当局分数结算		
        this.getCalcPosEnd().calcPosEnd(this);
        // 位置结算信息		
        BaseMJRoom_PosEnd ret = this.posEndInfo();
        ret.setEndPoint(((WXMJCalcPosEnd) this.getCalcPosEnd()).getCalcPosEnd());
        return ret;
    }

    /**
     * 计算总结算信息
     */
    @Override
    public void calcResults() {
        WXMJResults mResultsInfo = (WXMJResults) this.mResultsInfo();
        WXMJRoomPos roomPos = (WXMJRoomPos) this.getRoomPos();
        this.setResults(mResultsInfo);
    }

    /**
     * 检测平胡
     */
    @Override
    public OpType checkPingHu(int curOpPos, int cardID) {
        // 清空操作牌初始		
        List<Integer> hCardTypes = this.getHuCardTypes();
        this.clearPaoHu();
        int fen = 0;
        if (checkOpType(cardID, OpType.JiePao)) {
            for (Object ooo : this.getPosOpRecord().getOpHuList()) {
                fen += huPaiFen((WXMJOpPoint) ooo);
            }
            if (fen < this.getJiePaoFen()) {
                return OpType.Not;
            }
            this.setJiePaoFen(fen);
            this.setmHuOpType(MJHuOpType.JiePao);
            this.getPosOpRecord().addOpHuList(WXMJOpPoint.PH);
            return OpType.JiePao;
        }
        return OpType.Not;
    }

    public int huPaiFen(WXMJOpPoint opPoint) {
        switch (opPoint) {
            case DDC:
                return 2;
            case DDH:
                return 4;
        }
        return 0;
    }

    /**
     * 计算动作分数
     *
     * @param count
     */
    @Override
    public <T> void calcOpPointType(T opType, int count) {
        this.getCalcPosEnd().calcOpPointType(opType, count);
    }

    /**
     * 新结算
     */
    @Override
    protected AbsBaseResults newResults() {
        return new WXMJResults();
    }


}		
