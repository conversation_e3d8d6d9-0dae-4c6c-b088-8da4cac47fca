package business.ahhbmj.c2s.iclass;						
						
import jsproto.c2s.cclass.BaseSendMsg;						
						
import java.util.ArrayList;						
						
public class CAHHBMJ_OpCard extends BaseSendMsg {						
						
    public long roomID;						
    public int setID;						
    public int roundID;						
    public ArrayList<Integer> cardList;  //牌											
						
    public static CAHHBMJ_OpCard make(long roomID, int setID, int roundID, ArrayList<Integer> cardList) {						
        CAHHBMJ_OpCard ret = new CAHHBMJ_OpCard();						
        ret.roomID = roomID;						
        ret.setID = setID;						
        ret.roundID = roundID;						
        ret.cardList = cardList;						
        return ret;						
						
						
    }						
}											
