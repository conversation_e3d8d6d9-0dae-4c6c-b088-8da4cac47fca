package business.global.mj.wwmj;

import business.global.mj.AbsMJSetRoom;
import business.global.mj.template.MJTemplateRoom;
import business.global.mj.template.MJTemplateRoomEnum;
import business.global.room.mj.MahjongRoom;
import business.wwmj.c2s.cclass.WWMJResults;
import business.wwmj.c2s.iclass.CWWMJ_CreateRoom;
import com.google.gson.Gson;
import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.RoomEndResult;
import jsproto.c2s.cclass.room.BaseRoomConfigure;
import jsproto.c2s.iclass.mj._RoomEnd;

import java.util.Comparator;


/**
 * 模板麻将游戏房间
 *
 * <AUTHOR>
 */
public class WWMJRoom extends MJTemplateRoom {
    /**
     * 房间配置
     */
    private CWWMJ_CreateRoom roomCfg = null;

    protected WWMJRoom(BaseRoomConfigure<CWWMJ_CreateRoom> baseRoom<PERSON>onfigure, String roomKey, long ownerID) {
        super(baseRoom<PERSON>onfigure, roomKey, ownerID);
        initShareBaseCreateRoom(CWWMJ_CreateRoom.class, baseRoomConfigure);
        this.roomCfg = (CWWMJ_CreateRoom) baseRoomConfigure.getBaseCreateRoom();
    }

    /**
     * 房间内每个位置信息 管理器
     */
    @Override
    public WWMJRoomMgr initRoomPosMgr() {
        return new WWMJRoomMgr(this);
    }


    @Override
    public int getWanfa() {
        return this.getRoomCfg().getWanfa();
    }

    @Override
    public boolean isWanFaShowTingHuPoint() {
        return true;
    }


    /**
     * 获取房间配置
     *
     * @return
     */
    public CWWMJ_CreateRoom getRoomCfg() {
        if (this.roomCfg == null) {
            initShareBaseCreateRoom(CWWMJ_CreateRoom.class, getBaseRoomConfigure());
            return (CWWMJ_CreateRoom) getBaseRoomConfigure().getBaseCreateRoom();
        }
        return this.roomCfg;
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> T getCfg() {
        return (T) getRoomCfg();
    }

    @Override
    public String dataJsonCfg() {
        // 获取房间配置											
        return new Gson().toJson(this.getRoomCfg());
    }

    @Override
    public <E> boolean RoomCfg(E m) {
        return getRoomCfg().getKexuanwanfa().contains(((WWMJRoomEnum.KeXuanWanFa) m).ordinal());
    }

    @Override
    protected AbsMJSetRoom newMJRoomSet(int curSetID, MahjongRoom room, int dPos) {
        return new WWMJRoomSet(curSetID, (WWMJRoom) room, dPos);
    }


    /**
     * 是否需要解散次数
     *
     * @return
     */
    @Override
    public boolean needDissolveCount() {
        return getRoomCfg().getFangjian().contains(WWMJRoomEnum.WWMJGameRoomConfigEnum.JieSanCishu5.ordinal()) || getRoomCfg().getFangjian().contains(WWMJRoomEnum.WWMJGameRoomConfigEnum.JieSanCishu3.ordinal());
    }

    /**
     * 获取解散次数
     *
     * @return
     */
    @Override
    public int getJieShanShu() {
        if (getRoomCfg().getFangjian().contains(WWMJRoomEnum.WWMJGameRoomConfigEnum.JieSanCishu3.ordinal())) {
            return 3;
        }
        if (getRoomCfg().getFangjian().contains(WWMJRoomEnum.WWMJGameRoomConfigEnum.JieSanCishu5.ordinal())) {
            return 5;
        }
        return 3;
    }

    /**
     * 自动准备游戏 玩家加入房间时，自动进行准备。
     */
    @Override
    public boolean autoReadyGame() {
        return this.getBaseRoomConfigure().getBaseCreateRoom().getFangjian()
                .contains(WWMJRoomEnum.WWMJGameRoomConfigEnum.ZiDongZhunBei.ordinal());
    }

    @Override
    public <T> BaseSendMsg RoomEnd(T record, RoomEndResult<?> sRoomEndResult) {
        RoomEndResult roomEndResult = this.getRoomEndResult();
        roomEndResult.getResultsList().stream().max(Comparator.comparingInt(WWMJResults::getPoint)).ifPresent(n -> ((WWMJResults) n).setWinner(true));
        roomEndResult.getResultsList().stream().max(Comparator.comparingInt(WWMJResults::getDianPaoPoint)).ifPresent(n -> ((WWMJResults) n).setDianPaoWang(true));
        return _RoomEnd.make(this.getMJRoomRecordInfo(), roomEndResult, getGameName());
    }

    /**
     * 是否能切换人数
     *
     * @return boolean
     */
    @Override
    public boolean isCanChangePlayerNum() {
        return this.getBaseRoomConfigure().getBaseCreateRoom().getFangjian().contains(WWMJRoomEnum.WWMJGameRoomConfigEnum.FangJianQieHuanRenShu.ordinal());
    }

    @Override
    public MJTemplateRoomEnum.HuCardLunZhuang wanFa_huCardLunZhuang() {
        return MJTemplateRoomEnum.HuCardLunZhuang.ZHUANGHU_LIANZHUANG_ZHUANGBUHU_XIAJIA_ZHUANG;
    }

    @Override
    public MJTemplateRoomEnum.LiuJuLunZhuang wanFa_liuJuLunZhuang() {
        return MJTemplateRoomEnum.LiuJuLunZhuang.XIA_JIA_ZHUANG;
    }

    @Override
    public boolean isOnlyWinRightNowPoint() {
        return RoomCfg(WWMJRoomEnum.KeXuanWanFa.ZhiYingDangQianFen);
    }

    /**
     * 竞技点能否低于零
     *
     * @return
     */
    @Override
    public boolean isRulesOfCanNotBelowZero() {
        return RoomCfg(WWMJRoomEnum.KeXuanWanFa.BiShaiFenBuNengDiYu0);
    }
    /**
     * 是否需要摸打
     *
     * @return
     */
    public boolean isMoDa() {
        return getRoomCfg().tuoguan == 1;
    }
}
