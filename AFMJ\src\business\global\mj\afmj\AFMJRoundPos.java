package business.global.mj.afmj;

import business.global.mj.AbsMJRoundPos;
import business.global.mj.AbsMJSetPos;
import business.global.mj.AbsMJSetRound;
import business.global.mj.MJCard;
import business.global.mj.set.MJOpCard;
import cenum.mj.*;
import com.ddm.server.common.CommLogD;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import business.global.mj.afmj.AFMJRoomEnum.*;
import jsproto.c2s.cclass.mj.OpTypeInfo;

import java.util.List;
import java.util.Objects;

/**
 * 细化setop
 */
public class AFMJRoundPos extends AbsMJRoundPos {
	AFMJRoomSet bSet;
	AFMJSetPos bSetPos;

	public AFMJRoundPos(AbsMJSetRound round, int opPos) {
		super(round, opPos);
		this.bSet = (AFMJRoomSet) set;
		this.bSetPos = (AFMJSetPos) this.pos;
	}

	@Override
	public int op(WebSocketRequest request, OpType opType, MJOpCard mOpCard) {
		int opCardRet = -1;  //操作者位置
		if (this.getOpType() != null) {
			request.error(ErrorCode.NotAllow, "opPos has opered");
			return MJOpCardError.REPEAT_EXECUTE.value(); // 回合并发
		}
		switch (opType) {
		case Out:
			opCardRet = op_OutCard(request, opType, mOpCard.getOpCard());
			break;
		case AnGang:
			opCardRet = op_AnGang(request, opType, mOpCard.getOpCard());
			break;
		case JieGang:
			opCardRet = op_JieGang(request, opType);
			break;
		case Gang:
			opCardRet = op_Gang(request, opType, mOpCard.getOpCard());
			break;
		case Peng:
			opCardRet = op_Peng(request, opType);
			break;
		case Pass:
			opCardRet = op_Pass(request, opType);
			break;
		case Chi:
			opCardRet = op_Chi(request, opType, mOpCard.getOpCard());
			break;
		case TianHu:
		case Hu:
		case QiangGangHu:
		case JiePao:
			opCardRet = op_HuType(request, opType);
			break;
		default:
			break;
		}
		request.response();
		return opCardRet;
	}


	// / ==================================================
	// / 2 操作手牌
	// 2.1打牌
	public int op_OutCard(WebSocketRequest request, OpType opType, int cardID) {
		// 操作错误
		if (errorOpType(request, opType) <= 0)
			return -1;

		// 操作错误
		if (errorOpType(request, opType) <= 0) {
			return -1;
		}

		// pos 出牌
		MJCard card = getCardByID(cardID);
		if (null == card) {
			request.error(ErrorCode.NotAllow, "1not find cardID:" + cardID);
			return -1;
		}

		if (!outCard(card)) {
			request.error(ErrorCode.NotAllow, "2not find cardID:" + cardID);
			return -1;
		}
		// =====================================
        this.set.getLastOpInfo().setLastOpPos(this.bSetPos.getPosID());
		this.bSetPos.cleanOp();
		// 飘精
		if(((AFMJRoom)bSet.getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BoJing)
		|| ((AFMJRoom)bSet.getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoYiJing)
		|| ((AFMJRoom)bSet.getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoZhengJingHuoLiangFu)){
			if(bSet.getmJinCardInfo().checkJinExist(cardID / 100)){
				bSetPos.setPiaoJing(true);
			}
		}
		// 冲一包三
		// 冲一包三：最后一个打精的玩家A，打出的精被其他玩家B得到，且B玩家冲关或霸王，A玩家包出精分，其他玩家不用出；
		if(bSet.getmJinCardInfo().checkJinExist(cardID / 100)){
			bSet.getDaJingPosList().add(bSetPos.getPosID());
			bSet.getDaJingCardIdList().add(cardID);
		}
		// 记录当前回合操作的牌
		this.setOpCard(cardID);
		// 设置动作
		this.setOpType(opType, HuType.NotHu);
		// 执行动作
		return exeCardAction(opType);

	}

	/**
	 * 设置动作
	 *
	 * @param opType 操作类型
	 * @param huType 胡类型
	 */
	public void setOpType(OpType opType, HuType huType) {
		this.round.setOpType(opType);
		this.opType = opType;
		if (!huType.equals(HuType.NotHu)) {
			round.setSetHuEnd(true);
			this.pos.setHuCardType(huType, this.opPos,this.round.getRoundID());
			this.round.tryEndRound(true);
		}
	}


	// 2.3暗杠
	public int op_AnGang(WebSocketRequest request, OpType opType, int cardID) {
		// 操作错误
		if (errorOpType(request, opType) <= 0) {
			return -1;
		}
		// pos 碰牌
		if (!doOpType(cardID, opType)) {
			request.error(ErrorCode.NotAllow, "not op_AnGang");
			return -1;
		}
		// 记录操作的牌ID
		this.setOpCard(cardID);
		// 记录操作的动作，并且尝试结束本回合
		this.opNotHu(this.opPos, opType, TryEndRoundEnum.ALL_WAIT);
		return this.opPos;
	}

	// 2.4明杠
	public int op_Gang(WebSocketRequest request, OpType opType, int cardID) {
		// 操作错误
		if (errorOpType(request, opType) <= 0) {
			return -1;
		}
//		int ret = pos.getOpCardId();
		if (!doOpType(cardID, opType)) {
			request.error(ErrorCode.NotAllow, "not op_Gang");
			return -1;
		}
		// 记录操作的牌ID
		this.setOpCard(cardID);
		// 记录操作的动作，并且尝试结束本回合
		return this.exeCardAction(opType);
	}

	// 3.0接杠
	public int op_JieGang(WebSocketRequest request, OpType opType) {

		// 操作错误
		if (errorOpType(request, opType) <= 0)
			return -1;
		// 设置动作值
		// 设置动作值
		this.opCard = this.getLastOutCard();
		this.setPosMgr.setOpValue(opType, this.getOpPos(), this.getLastOutCard());
		// 执行操作
		int ret = this.opReturn(opType, 0, TryEndRoundEnum.ALL_AT_ONCE);
		if (ret < 0) {
			request.error(ErrorCode.NotAllow, "op :{%s},ret :{%d}", opType.toString(), ret);
			return -1;
		}
		return ret;
	}

	// 3 接打牌
	// 3.1过
	public int op_Pass(WebSocketRequest request, OpType opType) {
		// 操作错误
		if (errorOpType(request, opType) <= 0)
			return -1;
		this.pos.getPosOpNotice().clearBuNengChuList();
		// 执行操作
		int ret = this.opReturn(opType, 0, TryEndRoundEnum.ALL_AT_ONCE);
		if (ret < 0) {
			request.error(ErrorCode.NotAllow, "op :{%s},ret :{%d}", opType.toString(), ret);
			return -1;
		}
		return ret;
	}

	// 3.2碰
	public int op_Peng(WebSocketRequest request, OpType opType) {
		// 操作错误
		if (errorOpType(request, opType) <= 0)
			return -1;
		// 设置动作值
		this.setPosMgr.setOpValue(opType, this.getOpPos(), this.getLastOutCard());
		// 执行操作
		int ret = this.opReturn(opType, 0, TryEndRoundEnum.ALL_AT_ONCE);
		if (ret < 0) {
			request.error(ErrorCode.NotAllow, "op :{%s},ret :{%d}", opType.toString(), ret);
			return -1;
		}
		return ret;
	}

	// 3.2吃
	public int op_Chi(WebSocketRequest request, OpType opType, int cardID) {
		// 操作错误
		if (errorOpType(request, opType) <= 0)
			return -1;
		// pos 吃牌
		int size = this.pos.getPosOpNotice().getChiList().size();
		if (size <= 0) {
			request.error(ErrorCode.NotAllow, "not chi");
			return -1;
		}
		boolean isRet = false;
		for (List<Integer> chis : this.pos.getPosOpNotice().getChiList()) {
			if (chis.contains(cardID)) {
				isRet = true;
				break;
			}
		}
		if (!isRet) {
			request.error(ErrorCode.NotAllow, "isRet not chi");
			return -1;
		}
		// 记录操作值
		this.setPosMgr.setOpValue(opType, this.getOpPos(), cardID);
		// 执行操作
		int ret = this.opReturn(opType, 0, TryEndRoundEnum.ALL_AT_ONCE);
		if (ret < 0) {
			request.error(ErrorCode.NotAllow, "op :{%s},ret :{%d}", opType.toString(), ret);
			return -1;
		}
		return ret;
	}

//	/**
//	 * 吃
//	 *
//	 * @param request 连接请求
//	 * @param opType  动作类型
//	 * @param chiList 吃牌列表
//	 * @return
//	 */
	/*
	// 3.3吃
	public int op_Chi(WebSocketRequest request, OpType opType, List<Integer> chiList) {
		// 操作错误
		if (errorOpType(request, opType) <= 0) {
			return MJOpCardError.ERROR_OP_TYPE.value();
		}
		// 检查是否有吃牌列表
		if (CollectionUtils.isEmpty(this.getPos().getPosOpNotice().getChiList()) || CollectionUtils.isEmpty(chiList)) {
			request.error(ErrorCode.NotAllow, "not chi");
			return MJOpCardError.CHECK_OP_TYPE_ERROR.value();
		}
		// 检查是否有指定要吃的牌
		if (!this.getPos().getPosOpNotice().getChiList().stream().anyMatch(k -> k.contains(chiList.get(0)))) {
			request.error(ErrorCode.NotAllow, "isRet not chi");
			return MJOpCardError.CHECK_OP_TYPE_ERROR.value();
		}
		// 记录操作值
		this.getSetPosMgr().setOpValue(opType, this.getOpPos(), chiList.get(0));
		// 设置吃牌列表
		((AFMJSetPos)this.getPos()).setChiCardList(chiList);
		// 操作返回
		return opErrorReturn(request, opType, this.opReturn(opType, 0, TryEndRoundEnum.ALL_AT_ONCE));
	}*/

	// 补花
	public int op_buhua(AbsMJSetPos mSetPos, int cardID){
		if(this.bSetPos.checkBuHuaSuccess(mSetPos,cardID)){
			return 1;// 补花成功
		}else{
			return -1; // 补花失败
		}
	}

	/**
	 * 麻将胡
	 *
	 * @param request 客户端请求
	 * @param opType 操作类型
	 * @return -1:操作错误; >= 0 操作玩家的位置
	 */
	public int op_HuType(WebSocketRequest request, OpType opType) {
		// 操作错误
		if (errorOpType(request, opType) <= 0)
			return -1;
		// 执行操作
		int ret = this.opReturn(opType, 0, TryEndRoundEnum.ALL_AT_ONCE);
		if (ret < 0) {
			request.error(ErrorCode.NotAllow, "op :{%s},ret :{%d}", opType.toString(), ret);
			return -1;
		}
		return ret;
	}

	/**
	 * 手上有门牌的操作。
	 * 
	 * @param opType 操作类型
	 * @param cardID 牌ID
	 */
	@Override
	protected int getCardOpPos(OpType opType, int cardID) {
		if (OpType.Pass.equals(opType)) {

		} else if (OpType.Hu.equals(opType)) {
			// 操作动作
			if (!doOpType(cardID, opType)) {
				return -1;
			}
		} else {
			return -1;
		}
		// 记录操作的动作，并且尝试结束本回合
		this.opTypeTryEndRound(this.opPos, opType, MJCEnum.OpHuType(opType), TryEndRoundEnum.ALL_WAIT);
		return this.opPos;
	}


	/**
	 * 检查操作返回
	 *
	 * @param opType      动作类型
	 * @param cardID      牌ID
	 * @param tryEndRound T:顺序胡，如果顺序最高的点胡，则马上结束本回合 ; F:一炮多响，所有可胡的玩家都操作了，才结束本回合。
	 * @return 操作位置
	 */
	protected int opReturn(OpType opType, int cardID, TryEndRoundEnum tryEndRound) {
		// 执行动作类型信息
		OpTypeInfo oInfo = this.set.getSetPosMgr().exeOpTypeInfo(this.opPos, opType);
		if (Objects.isNull(oInfo)) {
			// 如果返回 -3,执行其他动作
			return this.getCardOpPos(opType, cardID);
		}

		if (OpType.Not.equals(oInfo.getOpType())) {
			// 不执行动作
			this.opNotHu(this.opPos, opType, TryEndRoundEnum.ALL_WAIT);
			if (this.tryEndRound(false)) {
				CommLogD.error("RoomID:{},opPos:{},RecieveOpTypes:{}", this.getSet().getRoom().getRoomID(), this.opPos, this.getRecieveOpTypes().toString());
			}
			return MJOpCardError.LINE_UP.value();
		}

		if (OpType.Pass.equals(oInfo.getOpType())) {
			// 过
			this.opNotHu(oInfo.getPosId(), opType, TryEndRoundEnum.ALL_AT_ONCE);
			if (this.tryEndRound(true)) {
				// 记录操作位置
				this.getRound().setExeOpPos(oInfo.getPosId());
			}
			this.setPosMgr.clearOpTypeInfoList();
			return oInfo.getPosId();
		}
		if (this.setPosMgr.checkNotExistOpTypeInfoList()) {
			// 如果返回 -3,执行其他动作
			return this.getCardOpPos(opType, cardID);
		}

		// 动作操作结果
		boolean isRes = this.doOpType(oInfo.getPosId(), this.setPosMgr.opValue(oInfo.getOpType(), oInfo.getPosId()), oInfo.getOpType());
		if (!isRes) {
			// 玩家执行动作有误。
			return MJOpCardError.ERROR_EXEC_OP_TYPE.value();
		}
		// 操作动作类型,并且尝试结束本回合.
		this.opTypeTryEndRound(oInfo.getPosId(), oInfo.getOpType(), MJCEnum.OpHuType(oInfo.getOpType()), tryEndRound);
		if (this.setPosMgr.checkHuEnd()) {
			this.setPosMgr.clearOpTypeInfoList();
		}
		return oInfo.getPosId();
	}

}
