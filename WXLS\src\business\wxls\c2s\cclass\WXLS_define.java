package business.wxls.c2s.cclass;	
	
/*	
 * 跑得快宏定义	
 * <AUTHOR>	
 * **/	
public class WXLS_define {	
	
	//跑得快游戏状态	
	public static enum WXLS_GameStatus{	
//		WXLS_GAME_STATUS_WAIT_START(0), //牌局等待开始阶段	
		WXLS_GAME_STATUS_DEAL(0), //发牌阶段	
		WXLS_GAME_STATUS_CONFIRMBanker(1), //抢庄状态	
		WXLS_GAME_STATUS_ADDDOUBLE(2), //加倍阶段	
		WXLS_GAME_STATUS_PLAYING(3), //比牌阶段	
		WXLS_GAME_STATUS_RESULT(4), //结束阶段	
		;	
	
		private int value;	
		private WXLS_GameStatus(int value) {this.value = value;}	
		public int value() {return this.value;}	
	
		public static WXLS_GameStatus getGameStatus(String value) {	
			String gameTypeName = value.toUpperCase();	
			for (WXLS_GameStatus flow : WXLS_GameStatus.values()) {	
				if (flow.toString().equals(gameTypeName)) {	
					return flow;	
				}	
			}	
			return WXLS_GameStatus.WXLS_GAME_STATUS_DEAL;	
		}	
	
		public static WXLS_GameStatus valueOf(int value) {	
			for (WXLS_GameStatus flow : WXLS_GameStatus.values()) {	
				if (flow.ordinal() == value) {	
					return flow;	
				}	
			}	
			return WXLS_GameStatus.WXLS_GAME_STATUS_DEAL;	
		}	
	};	
	
	
	/**	
	 * 位置	
	 *	
	 * <AUTHOR>	
	 *	
	 */	
	public static enum WXLS_WEIZI_TYPE {	
		SuiJi, // 随机	
		KeXuan,;// 可选	
	
	}	
	/**	
	 * 位置	
	 *	
	 * <AUTHOR>	
	 *	
	 */	
	public static enum WXLS_GuiNum {	
		two(1,2),	
		three(2,3),	
		four(3,4);	
	
		private int value;	
		private int value2;	
	
		private WXLS_GuiNum(int value,int value2) {this.value = value;this.value2=value2;}	
		public static WXLS_GuiNum valueOf(int value) {	
			for (WXLS_GuiNum flow : WXLS_GuiNum.values()) {	
				if (flow.value() == value) {	
					return flow;	
				}	
			}	
			return WXLS_GuiNum.two;	
		}	
	
		public int value() {return this.value;}	
		public int value2() {return this.value2;}	
	}	
	
	
}	
