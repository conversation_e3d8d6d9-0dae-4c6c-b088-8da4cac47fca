package core.network.client2game.handler.a3pk;	
	
import java.io.IOException;	
	
import business.global.pk.a3pk.A3PKRoomEnum;	
import com.ddm.server.websocket.def.ErrorCode;	
import com.ddm.server.websocket.handler.requset.WebSocketRequest;	
import com.google.gson.Gson;	
	
import business.a3pk.c2s.iclass.CA3PK_CreateRoom;	
import business.player.Player;	
import business.player.feature.PlayerRoom;	
import cenum.PrizeType;	
import core.network.client2game.handler.PlayerHandler;	
import core.network.http.proto.SData_Result;	
import core.server.a3pk.A3PKAPP;	
import jsproto.c2s.cclass.room.BaseRoomConfigure;	
	
/**	
 * 创建房间	
 *	
 * <AUTHOR>	
 */	
public class CA3PKCreateRoom extends PlayerHandler {	
	
    @SuppressWarnings("rawtypes")	
    @Override	
    public void handle(Player player, WebSocketRequest request, String message)	
            throws IOException {	
	
        final CA3PK_CreateRoom clientPack = new Gson().fromJson(message,	
                CA3PK_CreateRoom.class);	
	
	
	
	
        // 公共房间配置	
        BaseRoomConfigure<CA3PK_CreateRoom> configure = new BaseRoomConfigure<CA3PK_CreateRoom>(	
                PrizeType.RoomCard,	
                A3PKAPP.GameType(),	
                clientPack.clone());	
        SData_Result resule = player.getFeature(PlayerRoom.class).createRoomAndConsumeCard(configure);	
        if (ErrorCode.Success.equals(resule.getCode())) {	
            request.response(resule.getData());	
        } else {	
            request.error(resule.getCode(), resule.getMsg());	
        }	
    }	
}	
