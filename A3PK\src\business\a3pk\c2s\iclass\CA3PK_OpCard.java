package business.a3pk.c2s.iclass;	
	
import com.ddm.server.common.utils.Lists;	
import jsproto.c2s.cclass.pk.base.CPK_OpCard;	
	
import java.util.List;	
	
public class CA3PK_OpCard {	
    public long roomID;	
    public int setID;	
    public int roundID;	
    public int opType;	
    public int cardType;	
    public List<Integer> cardList = null;//黑风列表	
    public List<Integer> substituteCard = Lists.newArrayList();	
}	
