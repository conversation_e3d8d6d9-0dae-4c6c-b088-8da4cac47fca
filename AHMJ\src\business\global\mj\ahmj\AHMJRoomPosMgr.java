package business.global.mj.ahmj;

import business.global.room.base.AbsBaseRoom;
import business.global.room.mj.MJRoomPosMgr;

import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 红中麻将 房间内每个位置信息
 *
 * <AUTHOR>
 */

public class AHMJRoomPosMgr extends MJRoomPosMgr {

	public AHMJRoomPosMgr(AbsBaseRoom room) {
		super(room);
	}

	@Override protected void initPosList() {
		// 初始化房间位置
		for (int posID = 0; posID < this.getPlayerNum(); posID++) {
			this.posList.add(new AHMJRoomPos<>(posID, room));
		}
	}

	/**
	 * 上火是否都有操作
	 * */
	public boolean checkAllOpNiao() {
		return !this.getPosList().stream().filter(roomPos->((AHMJRoomPos) roomPos).getDaNiao().equals(AHMJRoomEnum.AHMJDaNiao.NOT_OP)).findAny().isPresent();
	}

	/**
	 * 获取飘分列表
	 * */
	public ArrayList<Integer> getDaNiaoList() {
		ArrayList<Integer> daNiaoList = new ArrayList<>(this.getPosList().stream().map(roomPos -> ((AHMJRoomPos) roomPos).getDaNiao().index).collect(Collectors.toList()));
		return daNiaoList;
	}
}
