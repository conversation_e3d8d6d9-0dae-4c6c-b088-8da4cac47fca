package core.network.client2game.handler.a3pk;	
	
import java.io.IOException;	
import java.util.Objects;	
	
import business.a3pk.c2s.iclass.CA3PK_OpCard;	
import business.global.pk.PKOpCard;	
import business.global.pk.PKRoom;	
import cenum.PKOpType;	
import com.ddm.server.websocket.def.ErrorCode;	
import com.ddm.server.websocket.handler.requset.WebSocketRequest;	
import com.google.gson.Gson;	
	
import business.global.room.RoomMgr;	
import business.player.Player;	
import core.network.client2game.handler.PlayerHandler;	
import core.network.http.proto.SData_Result;	
	
/**	
 * 打牌	
 * 	
 * <AUTHOR>	
 *	
 */	
public class CA3PKOpCard extends PlayerHandler {	
	
	@SuppressWarnings("rawtypes")	
	@Override	
	public void handle(Player player, WebSocketRequest request, String message) throws IOException {	
		final CA3PK_OpCard req = new Gson().fromJson(message, CA3PK_OpCard.class);	
		long roomID = req.roomID;	
		PKOpType opType = PKOpType.valueOf(req.opType);	
		PKRoom room = (PKRoom) RoomMgr.getInstance().getRoom(roomID);	
		if (Objects.isNull(room)) {	
			request.error(ErrorCode.NotAllow, "CA3PKOpCard not find room:" + roomID);	
			return;	
		}	
		if (PKOpType.JiangPai.equals(opType)|| PKOpType.MeiJiang.equals(opType)) {	
			request.error(ErrorCode.NotAllow, "CA3PKOpCard not find room:{%d} opType:{%s}",roomID,opType);	
			return;	
		}	
		SData_Result result = room.opCard(request, player.getId(), req.setID, req.roundID, opType, PKOpCard.OpCard(req.cardType,req.cardList));	
		if (!ErrorCode.Success.equals(result.getCode())) {	
    		request.error(result.getCode(),result.getMsg());	
    	}	
	}	
}	
