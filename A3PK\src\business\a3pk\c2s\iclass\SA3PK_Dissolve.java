package business.a3pk.c2s.iclass;	
	
import jsproto.c2s.iclass.room.SBase_Dissolve;	
	
/**	
 * 房间解散通知	
 * 	
 * <AUTHOR>	
 *	
 */	
public class SA3PK_Dissolve extends SBase_Dissolve {	
	
	/**	
	 * 	
	 */	
	private static final long serialVersionUID = 1L;	
	
	public static SA3PK_Dissolve make(SBase_Dissolve dissolve) {	
		SA3PK_Dissolve ret = new SA3PK_Dissolve();	
		ret.setOwnnerForce(dissolve.isOwnnerForce());	
		ret.setRoomID(dissolve.getRoomID());	
		ret.setDissolveNoticeType(dissolve.getDissolveNoticeType());	
		ret.setMsg(dissolve.getMsg());	
		return ret;	
	}	
}	
