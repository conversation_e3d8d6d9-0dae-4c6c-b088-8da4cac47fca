package business.wxpdk.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;

/**
 * <AUTHOR>
 * @date 2022-04-22 10:11
 */
public class SWXPDK_GetTime extends BaseSendMsg {
    // 房间ID
    private long roomID;
    // 玩家PID
    private long pid;
    // 位置
    private int pos;
    public int secTotal = -1;

    public static SWXPDK_GetTime make(long roomID, long pid, int pos) {
        SWXPDK_GetTime ret = new SWXPDK_GetTime();
        ret.setRoomID(roomID);
        ret.setPid(pid);
        ret.setPos(pos);
        return ret;
    }

    public long getRoomID() {
        return roomID;
    }

    public void setRoomID(long roomID) {
        this.roomID = roomID;
    }

    public long getPid() {
        return pid;
    }

    public void setPid(long pid) {
        this.pid = pid;
    }

    public int getPos() {
        return pos;
    }

    public void setPos(int pos) {
        this.pos = pos;
    }

}
