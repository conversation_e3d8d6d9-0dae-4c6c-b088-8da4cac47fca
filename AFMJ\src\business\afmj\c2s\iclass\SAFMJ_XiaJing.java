package business.afmj.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;

import java.util.HashMap;

@SuppressWarnings("serial")
public class SAFMJ_XiaJing extends BaseSendMsg {
	public long roomID;
	public int jin; // 上正精 , 客户端显示动画
	public int jin2; // 上副精 , 客户端显示动画
	public int xiaZhengJing; // 下正精
	public int xiaFuJing; // 下副精
	public HashMap<Integer, Integer> xiaJingFenMap = new HashMap<>(); // 下精分map：<玩家位置 , 玩家的下精分>
	public int xiaBaWangJingPos = -1; // 下霸王精玩家位置
	public int normalMoCnt = 0; // 普通摸牌数量
	public int gangMoCnt = 0; // 杠后摸牌数量
	public static SAFMJ_XiaJing make(long roomID, int jin , int jin2 ,int xiaZhengJing, int xiaFuJing, HashMap<Integer, Integer> xiaJingFenMap , int xiaBaWangJingPos , int normalMoCnt, int gangMoCnt) {
		SAFMJ_XiaJing ret = new SAFMJ_XiaJing();
    	ret.roomID = roomID;
		ret.jin = jin;
		ret.jin2 = jin2;
		ret.xiaZhengJing = xiaZhengJing;
		ret.xiaFuJing = xiaFuJing;
		ret.xiaJingFenMap = xiaJingFenMap;
		ret.xiaBaWangJingPos = xiaBaWangJingPos;
    	ret.normalMoCnt = normalMoCnt;
    	ret.gangMoCnt = gangMoCnt;

        return ret;
	    
	}
}
