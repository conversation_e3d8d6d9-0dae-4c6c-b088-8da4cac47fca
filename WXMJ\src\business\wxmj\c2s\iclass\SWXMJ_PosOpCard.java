package business.wxmj.c2s.iclass;

import java.util.List;
import java.util.Map;

import cenum.mj.OpType;
import jsproto.c2s.cclass.BaseSendMsg;


@SuppressWarnings("serial")
public class SWXMJ_PosOpCard<T> extends BaseSendMsg {

    public long roomID;
    public int pos;
    public T set_Pos;
    public OpType opType;
    public int opCard;
    public boolean isFlash;
    public boolean shifoutianting;

    public static <T> SWXMJ_PosOpCard<T> make(long roomID, int pos, T set_Pos, OpType opType, int opCard, boolean isFlash, boolean shifoutianting) {
        SWXMJ_PosOpCard<T> ret = new SWXMJ_PosOpCard<T>();
        ret.roomID = roomID;
        ret.pos = pos;
        ret.set_Pos = set_Pos;
        ret.opType = opType;
        ret.opCard = opCard;
        ret.isFlash = isFlash;
        ret.shifoutianting = shifoutianting;
        return ret;


    }
}			
