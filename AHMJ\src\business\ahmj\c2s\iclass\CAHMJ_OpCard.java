package business.ahmj.c2s.iclass;					
					
import jsproto.c2s.cclass.BaseSendMsg;					
					
import java.util.ArrayList;					
					
public class CAHMJ_OpCard extends BaseSendMsg {					
					
    public long roomID;					
    public int setID;					
    public int roundID;					
    public ArrayList<Integer> cardList;  //牌					
					
    public static CAHMJ_OpCard make(long roomID, int setID, int roundID, ArrayList<Integer> cardList) {					
        CAHMJ_OpCard ret = new CAHMJ_OpCard();					
        ret.roomID = roomID;					
        ret.setID = setID;					
        ret.roundID = roundID;					
        ret.cardList = cardList;					
        return ret;					
					
					
    }					
}					
