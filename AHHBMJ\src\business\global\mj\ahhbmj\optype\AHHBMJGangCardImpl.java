package business.global.mj.ahhbmj.optype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCardInit;
import business.global.mj.ahhbmj.AHHBMJNormalHuCardImpl;
import business.global.mj.ahhbmj.AHHBMJSetPos;
import business.global.mj.manage.MJFactory;
import business.global.mj.op.GangCardImpl;
import business.global.mj.util.HuUtil;
import cenum.mj.MJSpecialEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * 检查暗杠
 *
 * <AUTHOR>
 */
public class AHHBMJGangCardImpl extends GangCardImpl {

    @Override
    public boolean checkOpCard(AbsMJSetPos mSetPos, int cardID) {
        if (super.checkOpCard(mSetPos, cardID)) {
            AHHBMJSetPos set_pos = (AHHBMJSetPos) mSetPos;
            if (set_pos.isTing()) {
                if (checkGangHouTing(mSetPos, mSetPos.mjCardInit(false), mSetPos.getHandCard().type)) {
                    set_pos.getGangList().add(mSetPos.getHandCard().type);
                    return true;
                }
            } else {
                return true;
            }
        }
        return false;
    }
    /**
     * 检测杠玩 是否还能听牌
     *
     * @return
     */
    public  boolean checkGangHouTing(AbsMJSetPos mSetPos, MJCardInit mCardInit, int type) {
        List<Integer> cards = new ArrayList<>();
        for (int card : mCardInit.getAllCardInts()) {
            if (card == type) {
                cards.add(card);
            }
        }
        mCardInit.getAllCardInts().removeAll(cards);
        mCardInit.getJins().add(MJSpecialEnum.NOT_JIN.value());
        return HuUtil.getInstance().checkHu(mCardInit.getAllCardInts(), mCardInit.sizeJin());

    }
}									
