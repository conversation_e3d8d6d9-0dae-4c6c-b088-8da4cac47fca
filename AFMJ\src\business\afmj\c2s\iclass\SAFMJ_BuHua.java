package business.afmj.c2s.iclass;

import cenum.mj.OpType;
import jsproto.c2s.cclass.BaseSendMsg;

/**
 * 补花
 * <AUTHOR>
 * @param <T>
 *
 */
public class SAFMJ_BuHua extends BaseSendMsg {
    public long roomID;
    public OpType opType;

    
    public static <T> SAFMJ_BuHua make(long roomID, OpType opType) {
    	SAFMJ_BuHua ret = new SAFMJ_BuHua();
        ret.roomID = roomID;
        ret.opType = opType;
        return ret;
    

    }
}
