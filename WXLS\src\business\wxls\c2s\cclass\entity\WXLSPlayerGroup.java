package business.wxls.c2s.cclass.entity;	
	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
	
import java.util.ArrayList;	
	
	
/**	
 * 玩家组	
 * Created by Huaxing on 2017/5/11.	
 */	
public class WXLSPlayerGroup {	
    private ArrayList<WXLSPockerCard> gamePlayerNum;	
	private long roomID;	
	private long pid;	
	private int posIdx;	
    public WXLSPlayerGroup() {	
    }	
	
    public WXLSPlayerGroup(ArrayList<WXLSPockerCard> gamePlayerNum) {	
        this.gamePlayerNum = gamePlayerNum;	
    }	
    	
	
	
    public WXLSPlayerGroup(ArrayList<WXLSPockerCard> gamePlayerNum, long roomID,	
						   long pid, int posIdx) {	
		super();	
		this.gamePlayerNum = gamePlayerNum;	
		this.roomID = roomID;	
		this.pid = pid;	
		this.posIdx = posIdx;	
	}	
	
	public long getRoomID() {	
		return roomID;	
	}	
	
	public void setRoomID(long roomID) {	
		this.roomID = roomID;	
	}	
	
	public long getPid() {	
		return pid;	
	}	
	
	public void setPid(long pid) {	
		this.pid = pid;	
	}	
	
	public int getPosIdx() {	
		return posIdx;	
	}	
	
	public void setPosIdx(int posIdx) {	
		this.posIdx = posIdx;	
	}	
	
	public ArrayList<WXLSPockerCard> getGamePlayerNum() {	
        return gamePlayerNum;	
    }	
	
    public void setGamePlayerNum(ArrayList<WXLSPockerCard> gamePlayerNum) {	
        this.gamePlayerNum = gamePlayerNum;	
    }	
	
	@Override	
	public String toString() {	
		return "WXLSPlayerGroup [gamePlayerNum=" + gamePlayerNum + ", roomID="	
				+ roomID + ", pid=" + pid + ", posIdx=" + posIdx + "]";	
	}	
	
	
}	
