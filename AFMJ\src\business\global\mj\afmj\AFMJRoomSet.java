package business.global.mj.afmj;

import java.util.*;
import java.util.stream.Collectors;

import business.afmj.c2s.cclass.AFMJRoomSetEnd;
import business.afmj.c2s.cclass.AFMJRoomSetInfo;
import business.afmj.c2s.iclass.*;
import business.global.room.base.AbsRoomPos;
import com.ddm.server.common.CommLogD;
import com.ddm.server.common.utils.CommTime;
import business.global.mj.AbsMJSetPos;
import business.global.mj.AbsMJSetPosMgr;
import business.global.mj.AbsMJSetRoom;
import business.global.mj.AbsMJSetRound;
import business.global.mj.MJCard;
import business.global.room.mj.MJRoomPos;
import business.global.room.mj.MahjongRoom;
import cenum.mj.OpType;
import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.mj.BaseMJSet_Pos;
import business.global.mj.afmj.AFMJRoomEnum.*;

/**
 * 一局游戏逻辑
 */
public class AFMJRoomSet extends AbsMJSetRoom {
	private final AFMJRoom mjRoom;
	private int jinJin = 0;
	private boolean isLastCard = false; // 是否最后的牌
	private int firstBuHuaPos = -1; // 第一个补花的人
	private Boolean endFlag = true; // 游戏结束标志  正常游戏结束的 值为true  解散为false
	private MJCard zhengJing= new MJCard(6001); // 正精
	private MJCard fuJing = new MJCard(6002); // 副精
	private MJCard XiaZhengJingCard = null;  // 下正精牌
	private MJCard XiaFuJingCard = null;  // 下副精牌
	private boolean isDaHu = false; // 是否大胡，true: 胡大胡 , false: 平胡
	private final List<Integer> daJingPosList = new ArrayList<>(); // 打精的玩家列表
	private final List<Integer> daJingCardIdList= new ArrayList<>(); // 打精的精牌cardId列表
	private final HashMap<Integer, Integer> xiaJingFenMap = new HashMap<>(); // 下精分map：<玩家位置 , 玩家的下精分>
	private int xiaBaWangJingPos = -1; // 下霸王精玩家位置
	public boolean isPlayJingAnimation = true; // 是否播放翻精动画 , true：播放动画，false：不播放翻精动画
	private int jiePaoCardId = -1; // 接炮的牌的ID

	public int getJiePaoCardId() {
		return jiePaoCardId;
	}

	public void setJiePaoCardId(int jiePaoCardId) {
		this.jiePaoCardId = jiePaoCardId;
	}

	public boolean isPlayJingAnimation() {
		return isPlayJingAnimation;
	}

	public void setPlayJingAnimation(boolean playJingAnimation) {
		isPlayJingAnimation = playJingAnimation;
	}

	public MJCard getZhengJing() {
		return zhengJing;
	}

	public void setZhengJing(MJCard zhengJing) {
		this.zhengJing = zhengJing;
	}

	public MJCard getFuJing() {
		return fuJing;
	}

	public void setFuJing(MJCard fuJing) {
		this.fuJing = fuJing;
	}

	public int getXiaBaWangJingPos() {
		return xiaBaWangJingPos;
	}

	public void setXiaBaWangJingPos(int xiaBaWangJingPos) {
		this.xiaBaWangJingPos = xiaBaWangJingPos;
	}

	public HashMap<Integer, Integer> getXiaJingFenMap() {
		return xiaJingFenMap;
	}

	public List<Integer> getDaJingPosList() {
		return daJingPosList;
	}

	public List<Integer> getDaJingCardIdList() {
		return daJingCardIdList;
	}

	public boolean isDaHu() {
		return isDaHu;
	}

	public void setDaHu(boolean daHu) {
		this.isDaHu = daHu;
	}

	public MJCard getXiaZhengJingCard() {
		return XiaZhengJingCard;
	}

	public void setXiaZhengJingCard(MJCard xiaZhengJingCard) {
		XiaZhengJingCard = xiaZhengJingCard;
	}

	public MJCard getXiaFuJingCard() {
		return XiaFuJingCard;
	}

	public void setXiaFuJingCard(MJCard xiaFuJingCard) {
		XiaFuJingCard = xiaFuJingCard;
	}

	public Boolean getEndFlag() {
		return endFlag;
	}

	public void setEndFlag(Boolean endFlag) {
		this.endFlag = endFlag;
	}

	public int getFirstBuHuaPos() {
		return firstBuHuaPos;
	}

	public void setFirstBuHuaPos(int firstBuHuaPos) {
		this.firstBuHuaPos = firstBuHuaPos;
	}

	public AFMJRoomSet(int setID, MahjongRoom room, int dPos) {
		super(setID, room, dPos);
		this.mjRoom = (AFMJRoom) room;
		this.startMS = CommTime.nowMS();
		// 回放记录添加游戏配置
		this.addGameConfig();
		this.startSet();
	}

	/**
	 * 摸牌
	 */
	public MJCard getCard(int opPos, boolean isNormalMo) {
		AbsMJSetPos setPos = this.posDict.get(opPos);
		// 随机摸牌
		MJCard card = this.setCard.pop(isNormalMo, this.getGodInfo().godHandCard(setPos));
		if (null == card) {
			// 黄庄位置
			this.getMHuInfo().setHuangPos(opPos);
			return null;
		}
		// 设置牌
		setPos.getCard(card); // 摸到的牌放到手牌
		BaseMJSet_Pos posInfoOther = setPos.getNotify(false);
		BaseMJSet_Pos posInfoSelf = setPos.getNotify(true);
		// 通知房间内的所有玩家，指定玩家摸牌了。
		this.roomPlayBack.playBack2Pos(opPos,
				this.posGetCard(this.room.getRoomID(), opPos, this.setCard.getRandomCard().getNormalMoCnt(),
						this.setCard.getRandomCard().getGangMoCnt(), posInfoSelf),
				this.setPosMgr.getAllPlayBackNotify());
		this.room.getRoomPosMgr().notify2ExcludePosID(opPos,
				this.posGetCard(this.room.getRoomID(), opPos, this.setCard.getRandomCard().getNormalMoCnt(),
						this.setCard.getRandomCard().getGangMoCnt(), posInfoOther));

		/*// 摸牌补花
		if (card != null) {
			if (card.type >= MJSpecialEnum.NOT_HUA.value()) {
				if (MJFactory.getOpCard(AFMJBuHuaImpl.class).checkOpCard(setPos, FlowerEnum.HAND_CARD.ordinal())) {
					((AFMJSetOp) setPos.getmSetOp()).addOp(setPos, true, OpType.BuHua);//操作链增加补花
				}
			}
		}*/
		return card;
	}

	@Override
	protected <T> BaseSendMsg posGetCard(long roomID, int pos, int normalMoCnt, int gangMoCnt, T set_Pos) {
		return SAFMJ_PosGetCard.make(roomID, pos, normalMoCnt, gangMoCnt, set_Pos);
	}

	/**
	 * 麻将当局结算
	 */
	@Override
	protected AFMJRoomSetEnd newMJRoomSetEnd() {
		return new AFMJRoomSetEnd();
	}

	/**
	 * 开局补花
	 */
/*	public void startSetApplique(AFMJSetPos setPos, boolean isFlash) {
		boolean isBuHua = false;
		AFMJBuHuaImpl buHua = (AFMJBuHuaImpl) MJFactory.getOpCard(AFMJBuHuaImpl.class);
		if (buHua.checkOpCard(setPos, FlowerEnum.CHECK_FLOWER.ordinal(), isFlash)) {
			buHua.checkOpCard(setPos, FlowerEnum.PRIVATE.ordinal(), isFlash);
			isBuHua = true;
		}
		if (isBuHua) {
			startSetApplique(setPos, isFlash);
		} else {
			return;
		}
	}*/

	@Override
	public void startSet() {

		CommLogD.info("startSet id:{}", getSetID());
		// 洗底牌
		this.absMJSetCard();
		// 初始化本局位置管理器
		this.setSetPosMgr(this.absMJSetPosMgr());
		// 初始化玩家手上的牌
		this.initSetPosCard();
		// 通知本局开始
		this.notify2SetStart();
		// 一些基本数据初始，无需理会。
		exeStartSet();
	}

	@Override
	protected AbsMJSetPos absMJSetPos(int posID) {
		return new AFMJSetPos(posID, (MJRoomPos) this.room.getRoomPosMgr().getPosByPosID(posID), this);

	}

	@Override
	protected void absMJSetCard() {
		this.setSetCard(new AFMJSetCard(this));
	}

	@Override
	protected AbsMJSetPosMgr absMJSetPosMgr() {
		return new AFMJSetPosMgr(this);
	}

	@Override
	protected <T> BaseSendMsg setStart(long roomID, T setInfo) {
		return SAFMJ_SetStart.make(roomID, setInfo);
	}

	@Override
	protected AbsMJSetRound nextSetRound(int roundID) {
		return new AFMJSetRound(this, roundID);
	}

	/**
	 * 配置文件是否需要游戏名
	 *
	 * @return T:需要,F:不需要
	 */
	public boolean isConfigName() {
		return true;
	}

	@Override
	public int kaiJinNum() {
		return 1;
	}

	@Override
	public boolean isBaiBanTiJin() {
		return false;
	}

	@Override
	public int cardSize() {
		return 13;
	}

	/**
	 * 庄家胡牌、流局，庄家坐庄
	 */
	@Override
	public int calcNextDPos() {
		/*if(((AFMJSetPosMgr)this.setPosMgr).isExistYPDX()){ // 一炮多响，庄家的下家坐庄
			if((this.dPos+1)%this.getPlayerNum() == 0){ // 下一个玩家坐庄,再次轮到房主坐庄
				mjRoom.setOwnerDposCount(mjRoom.getEmptyPosCount() + 1);
			}
			return (this.dPos+1)%this.getPlayerNum(); // 下一个玩家坐庄
		}*/
		if (this.getMHuInfo().isHuNotEmpty()) { // 玩家胡
			if(mjRoom.RoomCfgWanFa(AFMJWanFa.TuiJing)) { // 推精
				mjRoom.setShangJuLiuJu(false); // 设置上局不是流局
				mjRoom.setTuiJingZhengJing(getmJinCardInfo().getJin(1)); // 设置推精正精牌
				mjRoom.setTuiJingFuJing(getmJinCardInfo().getJin(2)); // 设置推精副精牌
			}
			if (this.getMHuInfo().getHuPos()==this.dPos) { // 庄家胡牌连庄
				return this.dPos;
			}
			if((this.dPos+1)%this.getPlayerNum() == 0){ // 下一个玩家坐庄,再次轮到房主坐庄
				mjRoom.setOwnerDposCount(mjRoom.getEmptyPosCount() + 1);
			}
			return (this.dPos+1)%this.getPlayerNum(); // 下一个玩家坐庄
			// return this.getMHuInfo().getHuPos(); // 胡的玩家坐庄
		} else { // 流局
			if(mjRoom.RoomCfgWanFa(AFMJWanFa.TuiJing)){ // 推精
				mjRoom.setShangJuLiuJu(true); // 设置上局流局
				mjRoom.setTuiJingZhengJing(null); // 设置推精正精牌
				mjRoom.setTuiJingFuJing(null); // 设置推精副精牌
			}
			return this.dPos; // 流局时，庄家连庄；
		}
	}

	@Override
	public void addGameConfig() {
		this.getRoomPlayBack().addPlaybackList(
				SAFMJ_Config.make(this.mjRoom.getRoomCfg(), this.mjRoom.getRoomTyepImpl().getRoomTypeEnum()),
				null);

	}

	@Override
	protected <T> BaseSendMsg setEnd(long roomID, T setEnd) {
		return SAFMJ_SetEnd.make(roomID, setEnd);
	}

	/**
	 * 一局结束的信息
	 */
	@Override
	public AFMJRoomSetEnd getNotify_setEnd() {
		AFMJRoomSetEnd setEndInfo = (AFMJRoomSetEnd) this.mRoomSetEnd();
		setEndInfo.setJin(this.getmJinCardInfo().getJin(1).getCardID());
		setEndInfo.setJin2(this.getmJinCardInfo().getJin(2).getCardID());
		return setEndInfo;
	}

	/**
	 *
	 * @param jinCard  金1 : 正精
	 * @param jinCard2 金2 ：副精
	 */
	@Override
	public void kaiJinNotify(MJCard jinCard, MJCard jinCard2) {
		this.jinJin = jinCard2.getCardID();
		getRoomPlayBack().playBack2All(SAFMJ_Jin.make(getRoom().getRoomID(), jinCard.getCardID(), jinCard2.getCardID(),0,
				getMJSetCard().getRandomCard().getNormalMoCnt(),
				getMJSetCard().getRandomCard().getGangMoCnt(),
				this.isPlayJingAnimation()));
	}

	/**
	 * 下精通知
	 * @param jin 上正精
	 * @param jin2 上副精
	 * @param xiaZhengJingCard 下正精
	 * @param xiaFuJingCard 下副精
	 */
	public void xiaJingNotify(int jin , int jin2 , MJCard xiaZhengJingCard, MJCard xiaFuJingCard,HashMap<Integer, Integer> xiaJingFenMap,int xiaBaWangJingPos) {
		getRoomPlayBack().playBack2All(SAFMJ_XiaJing.make(
				getRoom().getRoomID(),
				jin,
				jin2,
				xiaZhengJingCard.getCardID(),
				xiaFuJingCard.getCardID(),
				xiaJingFenMap,
				xiaBaWangJingPos,
				getMJSetCard().getRandomCard().getNormalMoCnt(),
				getMJSetCard().getRandomCard().getGangMoCnt()));
	}


	@Override
	protected void calcCurSetPosPoint() {
		// 计算位置小局分数
		/* if (!this.getMHuInfo().isHuEmpty()) { */
		this.getPosDict().values().forEach(AbsMJSetPos::calcPosPoint);
		// 所有玩家客户端显示模块分数，模块分数显示在胡牌类型后面
		for(int i = 0; i < this.getPlayerNum(); i++){
			AFMJSetPos afmjSetPos = (AFMJSetPos)this.getPosDict().get(i);
			// 胡牌分
			((AFMJCalcPosEnd)afmjSetPos.getCalcPosEnd()).getAfmjHuTypeMap().put(AFMJOpPoint.HuPaiPoint,afmjSetPos.getHuPaiPoint());
			// 上精分
			((AFMJCalcPosEnd)afmjSetPos.getCalcPosEnd()).getAfmjHuTypeMap().put(AFMJOpPoint.ShangJingPoint,afmjSetPos.getShangJingPoint());
			// 下精分
			((AFMJCalcPosEnd)afmjSetPos.getCalcPosEnd()).getAfmjHuTypeMap().put(AFMJOpPoint.XiaJingPoint,afmjSetPos.getXiaJingPoint());
			// 博精
			((AFMJCalcPosEnd)afmjSetPos.getCalcPosEnd()).getAfmjHuTypeMap().put(AFMJOpPoint.BoJingPoint,afmjSetPos.getBoJingPoint());
			// 杠分
			((AFMJCalcPosEnd)afmjSetPos.getCalcPosEnd()).getAfmjHuTypeMap()
					.put(AFMJOpPoint.GangPoint,afmjSetPos.getAnGangPoint() + afmjSetPos.getPengGangPoint() + afmjSetPos.getZhiGangPoint());
		}
		// 连庄数结算，牌局是否结束，黄庄
		calcOtherPoint();

	}


	/**
	 * 其他特殊结算
	 */
	public void calcOtherPoint() {
		// 庄家胡牌、流局，庄家坐庄
		int evenDposCount;
		if (this.getMHuInfo().getHuPos() != -1) {
			if (this.getMHuInfo().getHuPos() == this.dPos) {
				evenDposCount = this.room.getEvenDpos() + 1;
				addLianZhuang(this.dPos, evenDposCount);
			}
		} else {
			evenDposCount = this.room.getEvenDpos() + 1;
			addLianZhuang(this.dPos, evenDposCount);
		}
	}

	@Override
	public void MJApplique(int pos) {
		AbsMJSetPos setPos = posDict.get(pos);
		BaseMJSet_Pos posInfoOther = setPos.getNotify(false);
		BaseMJSet_Pos posInfoSelf = setPos.getNotify(true);
		getRoomPlayBack().playBack2Pos(pos,
				SAFMJ_Applique.make(getRoom().getRoomID(), pos, OpType.Out, 0, false, posInfoSelf,
						getMJSetCard().getRandomCard().getNormalMoCnt(),
						getMJSetCard().getRandomCard().getGangMoCnt()), null);
		for (int i = 0; i < getRoom().getPlayerNum(); i++) {
			if (i == pos)
				continue;
			getRoom().getRoomPosMgr().notify2Pos(i,
					SAFMJ_Applique.make(getRoom().getRoomID(), pos, OpType.Out, 0, false, posInfoOther,
							getMJSetCard().getRandomCard().getNormalMoCnt(),
							getMJSetCard().getRandomCard().getGangMoCnt()));
		}
	}

	/**
	 * 发送设置位置的牌
	 */
	@Override
	public void sendSetPosCard() {

		for (int i = 0; i < room.getPlayerNum(); i++) {
			AbsMJSetPos setPos = posDict.get(i);
			setPos.sortCards();
		}
		for (int i = 0; i < room.getPlayerNum(); i++) {
			long pid = this.room.getRoomPosMgr().getPosByPosID(i).getPid();
			if (i == 0) {
				this.getRoomPlayBack().playBack2Pos(
						i,
						SAFMJ_SetPosCard.make(this.room.getRoomID(),this.setPosCard(pid)), null);
			} else {
				this.getRoom().getRoomPosMgr().notify2Pos(
						i,
						SAFMJ_SetPosCard.make(this.room.getRoomID(),
								this.setPosCard(pid)));
			}
		}
	}

	/**
	 * 结算每个玩家的下精分
	 */
	public void calcXiaJing(){
		List<Integer> jingList = new ArrayList<>(); // 有精玩家列表
		for (int i = 0; i < room.getPlayerNum(); i++) {
			AbsMJSetPos setPos = posDict.get(i);
			List<MJCard> allCards= setPos.allCards();
			if(XiaZhengJingCard != null && XiaFuJingCard != null){
				for(MJCard mjCard : allCards){
					if(mjCard.type == XiaZhengJingCard.type){ // 正精
						if(!jingList.contains(i))jingList.add(i); // 有精玩家列表
						((AFMJSetPos)(setPos)).setXiaZhengJingFen(
								((AFMJSetPos)(setPos)).getXiaZhengJingFen() + AFMJOpPoint.ZhengJing.value());
					}
					if(mjCard.type == XiaFuJingCard.type){ // 副精
						if(!jingList.contains(i))jingList.add(i); // 有精玩家列表
						((AFMJSetPos)(setPos)).setXiaFuJingFen(
								((AFMJSetPos)(setPos)).getXiaFuJingFen() + AFMJOpPoint.FuJing.value());
					}
				}
			}

			// 冲关
			switch (((AFMJSetPos)(setPos)).getXiaZhengJingFen() + ((AFMJSetPos)(setPos)).getXiaFuJingFen()){
				case 5:((AFMJSetPos)(setPos)).setXiaJingChongGuan(2);break;
				case 6:((AFMJSetPos)(setPos)).setXiaJingChongGuan(3);break;
				case 7:((AFMJSetPos)(setPos)).setXiaJingChongGuan(4);break;
				case 8:((AFMJSetPos)(setPos)).setXiaJingChongGuan(5);break;
				case 9:((AFMJSetPos)(setPos)).setXiaJingChongGuan(6);break;
				case 10:((AFMJSetPos)(setPos)).setXiaJingChongGuan(7);break;
				case 11:((AFMJSetPos)(setPos)).setXiaJingChongGuan(8);break;
				case 12:((AFMJSetPos)(setPos)).setXiaJingChongGuan(9);break;
				default:((AFMJSetPos)(setPos)).setXiaJingChongGuan(1);break;
			}

			// 下精总分
			((AFMJSetPos)(setPos)).setXiaJingFen(
					(((AFMJSetPos)(setPos)).getXiaZhengJingFen() + ((AFMJSetPos)(setPos)).getXiaFuJingFen()) *
							((AFMJSetPos)(setPos)).getXiaJingChongGuan());
		}

		// 霸王精
		// 霸王精：所有玩家中仅有一个玩家有精，称为霸王精；
		// 霸王精玩家精分翻倍；
		if(jingList.size() == 1){
			setXiaBaWangJingPos(jingList.get(0)); // 设置下霸王精玩家位置
			AbsMJSetPos setPos = posDict.get(jingList.get(0));
			((AFMJSetPos)(setPos)).setXiaBaWangJing(true);
			((AFMJSetPos)(setPos)).setXiaJingFen(
					((AFMJSetPos)(setPos)).getXiaJingFen() * AFMJOpPoint.BaWangJing.value());
		}

		// 结算每个玩家的下精分，下精分每家付
		AFMJSetPos selfSetPos; // 当前玩家
		AFMJSetPos otherSetPos; // 其他玩家
		for (int i = 0; i < getRoom().getPlayerNum();i++) {
			int xiaJingFen; // 当前玩家的下精分
			selfSetPos = (AFMJSetPos)getMJSetPos(i); // 当前玩家
			if (null == selfSetPos) continue;
			// 当前玩家加的下精分
			xiaJingFen = selfSetPos.getXiaJingFen() * (getRoom().getPlayerNum() - 1 );
			// 当前玩家扣的下精分
			for(int j = 0; j < getRoom().getPlayerNum();j++){
				if(j == i)continue;
				otherSetPos = (AFMJSetPos)getMJSetPos(j); // 其他玩家
				if (null == otherSetPos) continue;
				xiaJingFen -= otherSetPos.getXiaJingFen(); // 当前玩家扣下精分
			}
			// 设置下精分map
			xiaJingFenMap.put(i,xiaJingFen);
		}

	}

	/**
	 * 是否最后的牌
	 */
	public boolean isLastCard() {
		return isLastCard;
	}

	/**
	 * 设置是否最后的牌
	 */
	public void setLastCard(boolean isLastCard) {
		this.isLastCard = isLastCard;
	}

	public void setHuaCount() {
	}

	/**
	 * 获取通知当局信息
	 */
	@Override
	public AFMJRoomSetInfo getNotify_set(long pid) {
		AFMJRoomSetInfo ret = (AFMJRoomSetInfo) this.getMJRoomSetInfo(pid);
		// 金
		ret.setJin(this.getmJinCardInfo().getJin(1).getCardID()); // 正精
		ret.setJin2(this.getmJinCardInfo().getJin(2).getCardID()); // 副精
		ret.setJinJin(this.jinJin);
		return ret;
	}

	/**
	 * 创建新的当局麻将信息
	 */
	protected AFMJRoomSetInfo newMJRoomSetInfo() {
		return new AFMJRoomSetInfo();
	}

	/**
	 * 检查小局托管自动解散
	 */
	public boolean checkSetEndTrusteeshipAutoDissolution() {
		if(mjRoom.getRoomCfg().getFangjian().contains(AFMJFangJian.XiaoJuTuoGuanJieSan.ordinal())){
			return true;
		}else if(mjRoom.getRoomCfg().getFangjian().contains(AFMJFangJian.TuoGuan2XiaoJuJieSan.ordinal())){
			// 有玩家连续2局托管
			// 获取托管玩家pid列表
			List<Long> trusteeshipPlayerList = getRoom().getRoomPosMgr().getRoomPosList().stream()
					.filter(n -> n.isTrusteeship() && ((AFMJRoomPos)n).getTuoGuanSetCount() >= 2).map(AbsRoomPos::getPid).collect(Collectors.toList());
			return trusteeshipPlayerList.size() > 0;
		}
		return false;
	}


	/**
	 * 小局托管自动解散回放记录 注意：需要自己重写
	 *
	 * @param roomId 房间id
	 * @param pidList 托管玩家Pid
	 * @param sec 记录时间
	 * @return 小局托管自动解散回放记录
	 */
	public BaseSendMsg DissolveTrusteeship(long roomId, List<Long> pidList, int sec) {
		return SAFMJ_DissolveTrusteeship.make(roomId,pidList,sec);
	}


}
