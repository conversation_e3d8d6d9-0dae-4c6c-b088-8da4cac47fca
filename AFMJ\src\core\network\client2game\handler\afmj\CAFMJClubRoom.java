package core.network.client2game.handler.afmj;

import java.io.IOException;

import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;

import business.player.Player;
import business.player.feature.PlayerClubRoom;
import business.afmj.c2s.iclass.CAFMJ_CreateRoom;
import cenum.PrizeType;
import core.network.client2game.handler.PlayerHandler;
import core.server.afmj.AFMJAPP;
import jsproto.c2s.cclass.room.BaseRoomConfigure;

/**
 * 亲友圈房间
 * 
 * <AUTHOR>
 *
 */
public class CAFMJClubRoom extends PlayerHandler {

	@Override
	public void handle(Player player, WebSocketRequest request, String message)
			throws IOException {

		final CAFMJ_CreateRoom clientPack = new Gson().fromJson(message,
				CAFMJ_CreateRoom.class);

		// 公共房间配置
		BaseRoomConfigure<CAFMJ_CreateRoom> configure = new BaseRoomConfigure<CAFMJ_CreateRoom>(
				PrizeType.RoomCard,
				AFMJAPP.GameType(),
				clientPack.clone());
		player.getFeature(PlayerClubRoom.class).createNoneClubRoom(request,configure);
	}
}
