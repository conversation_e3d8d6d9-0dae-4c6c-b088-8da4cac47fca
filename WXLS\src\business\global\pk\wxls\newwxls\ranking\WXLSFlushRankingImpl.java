package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.ArrayList;	
import java.util.List;	
	
/**	
 *  解析玩家手中的牌是不是同花(花色一样)	
 */	
public class WXLSFlushRankingImpl extends WXLSAbstractRanking {	
	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        WXLSRankingResult result = null;	
        if (player.getCardSize() == 5) {	
            List<WXLSPockerCard> cards = player.getCards();	
            List<WXLSPockerCard> newcards = new ArrayList<WXLSPockerCard>();	
            for (int i = 0; i < cards.size(); i++) {	
                    newcards.add(cards.get(i));	
            }	
            if (this.isSameSuit(newcards)) { // 如果是同色	
                result = new WXLSRankingResult();	
                result.setRankingEnum(WXLSRankingEnum.FLUSH);	
            }	
        }	
	
        return result;	
    }	
	
}	
