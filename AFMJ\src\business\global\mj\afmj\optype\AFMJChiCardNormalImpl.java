package business.global.mj.afmj.optype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCard;
import business.global.mj.MJCardInit;
import business.global.mj.manage.OpCard;
import business.global.mj.set.LastOpTypeItem;
import cenum.mj.OpType;
import com.ddm.server.common.utils.CommMath;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 正常的牌吃牌动作
 * 无白板替金，无吃幅打幅，可吃字牌
 * 东南西北只能吃东南西北牌，中发白只能吃中发白牌
 * 东南西北4张牌任意3张都是顺子
 */
public class AFMJChiCardNormalImpl implements OpCard {

	@Override
	public boolean checkOpCard(AbsMJSetPos mSetPos, int cardId) {
		int cardType = cardId / 100;
		MJCardInit mjCardInit = mSetPos.mjCardInit(true);
		if (null == mjCardInit) {
			return false;
		}
		// 操作：正副精都可以打出，可以当本身进行吃、碰、杠
//		if (mSetPos.getSet().getmJinCardInfo().checkJinExist(cardType)) {
//			return false;
//		}
		List<List<Integer>> chiList = new ArrayList<>();
		// 东南西北只能吃东南西北牌
		if(cardType >= 41 && cardType <= 44){
			chiList = chiFengCard(new ArrayList<>(mjCardInit.getAllCardInts()), cardType,  chiList);
		}else {
			chiList = chiCard(new ArrayList<>(mjCardInit.getAllCardInts()), cardType, 0, chiList);
		}
		for (int i = 0; i < chiList.size(); i++) {
			List<Integer> chis = chiList.get(i);
			chis = chiListCarId(chis, mSetPos.getPrivateCard());
			if (chis.size() < 3) {
				chis.add(cardId);
			}
			chiSort(chis);
			chiList.set(i, chis);
		}

		if (chiList.size() <= 0) {
			return false;
		}
		// 吃牌列表升序
		List<Integer> chiListFirstCardTypeList = new ArrayList<>(); // 所有吃牌列表的第1张牌的牌型列表
		List<List<Integer>> sortChiList = new ArrayList<>(); // 升序的吃牌列表
		for(List<Integer> chiCardIdList : chiList){
			chiListFirstCardTypeList.add(chiCardIdList.get(0) / 100);
		}
		// 升序
		Collections.sort(chiListFirstCardTypeList);
		for(Integer chiListFirstCardType : chiListFirstCardTypeList){
			for(List<Integer> chiCardIdList : chiList){
				if(chiListFirstCardType == chiCardIdList.get(0) / 100){
					sortChiList.add(chiCardIdList);
				}
			}
		}
		mSetPos.getPosOpNotice().setChiList(sortChiList);
		return true;
	}

	private List<Integer> chiListCarId(List<Integer> chis, List<MJCard> privateCards) {
		List<Integer> chiCardIds = new ArrayList<>();
		List<Integer> chiCardCount = new ArrayList<>();

		for (MJCard privateCard : privateCards) {
			if (chis.contains(privateCard.type)) {
				int cardType = privateCard.type;
				if (!chiCardCount.contains(cardType)) {
					chiCardCount.add(cardType);
					chiCardIds.add(privateCard.cardID);
				}

			}
			if (chiCardCount.size() >= 3) {
				break;
			}
		}
		return chiCardIds;

	}

	private void chiSort(List<Integer> chis) {
		chis.sort((o1, o2) -> {
			int oType1 = o1 / 100;
			int oType2 = o2 / 100;
			return oType1 - oType2;
		});
	}

	@Override
	public boolean doOpCard(AbsMJSetPos mSetPos, int cardID) {
		boolean ret = false;
		int lastOutCard = mSetPos.getSet().getLastOpInfo().getLastOutCard();
		int fromPos = mSetPos.getMJSetCard().getCardByID(lastOutCard).ownnerPos;

		List<Integer> publicCard = new ArrayList<>();
		publicCard.add(OpType.Chi.value());
		publicCard.add(fromPos);
		publicCard.add(lastOutCard);
		List<Integer> chiTmps = new ArrayList<>();
		for (int i = 0; i < mSetPos.getPosOpNotice().getChiList().size(); i++) {
			List<Integer> chis = mSetPos.getPosOpNotice().getChiList().get(i);
			for (int j = 0; j < chis.size(); j++) {
				if (chis.get(0) == cardID) {
					chiTmps = chis;
					break;
				}
			}

		}

		int lastOutCardType = lastOutCard / 100;
		for (int i = 0; i < chiTmps.size(); i++) {
			if (lastOutCardType == chiTmps.get(i) / 100) {
				chiTmps.set(i, lastOutCard);
			}
		}

		// 搜集牌
		List<MJCard> tmp = new ArrayList<>();
		for (int i = 0; i < mSetPos.getPrivateCard().size(); i++) {
			if (chiTmps.contains(mSetPos.getPrivateCard().get(i).cardID)) {
				if (!tmp.contains(mSetPos.getPrivateCard().get(i))) {
					tmp.add(mSetPos.getPrivateCard().get(i));
				}
				if (tmp.size() >= 2) {
					ret = true;
					break;
				}
			}
		}

		if (ret) {
			publicCard.add(chiTmps.get(0));
			publicCard.add(chiTmps.get(1));
			publicCard.add(chiTmps.get(2));

			mSetPos.addPublicCard(publicCard);
			mSetPos.removeAllPrivateCard(tmp);
			mSetPos.getSet().getSetPosMgr().clearChiList();
			mSetPos.privateMoveHandCard();
			mSetPos.getSet().getLastOpInfo().addLastOpItem(OpType.Chi,new LastOpTypeItem(mSetPos.getPosID(),lastOutCard));

		}
		return ret;
	}

	/**
	 * 获取所有的吃牌
	 *
	 * @param privateCards 私有牌
	 * @param cardType 牌类型
	 * @param idx 位置
	 * @param chiList 吃列表
	 * @return 所有的吃牌
	 */
	public List<List<Integer>> chiCard(List<Integer> privateCards, int cardType, int idx, List<List<Integer>> chiList) {
		List<Integer> cardInts = new ArrayList<>();
		// 如果 下标 和 手牌长度一致
		if (idx == privateCards.size()) {
			return chiList;
		}
		// 从指定的下标开始，遍历出所有手牌
		for (int i = idx, size = privateCards.size(); i < size; i++) {
			// 如果 手牌中的类型 == 牌的类型
			if (privateCards.get(i) == cardType || privateCards.get(i) > 47) {
				continue;
			}
			// 中发白只能吃中发白牌
			if(cardType >= 45 && cardType <= 47){
				if(privateCards.get(i) < 45 || privateCards.get(i) > 47)continue;
			}
			// 如果 手牌中类型 不出现重复 并且 记录的牌数 < 2
			if (!cardInts.contains(privateCards.get(i)) && cardInts.size() < 2) {
				// 添加不重复的牌
				cardInts.add(privateCards.get(i));
				// 如果 记录牌数 == 2 结束循环
			} else if (cardInts.size() == 2) {
				break;
			}
		}
		idx++;
		// 如果 记录牌数 == 2
		if (cardInts.size() == 2) {
			// 添加牌
			cardInts.add(cardType);
			// 判断是否顺子
			if (CommMath.isContinuous(cardInts)) {
				// 如果是否有重复的顺子
				if (!chiList.contains(cardInts)) {
					chiList.add(cardInts);
				}
				return chiCard(privateCards, cardType, idx, chiList);
			}
		}
		return chiCard(privateCards, cardType, idx, chiList);
	}


	/**
	 * 获取东南西北风牌的所有的吃牌
	 * 东南西北4张牌任意3张都是顺子
	 *
	 * @param privateCards 私有牌
	 * @param cardType 牌类型：东南西北
	 * @param chiList 吃列表
	 * @return  不重复的风牌顺子
	 */
	public List<List<Integer>> chiFengCard(List<Integer> privateCards, int cardType, List<List<Integer>> chiList) {
		List<Integer> cardInts = new ArrayList<>(); // 私有牌的所有风牌
		// 遍历私有牌
		for(Integer integer : privateCards){
			if(integer >= 41 && integer <= 44){
				cardInts.add(integer);
			}
		}
		// 遍历私有牌的所有风牌
		for(int i = 0; i < cardInts.size(); i++){
			List<Integer> fengShunZiTemp = new ArrayList<>(); // 风牌顺子
			// 风牌顺子添加不重复的牌型
			if(cardInts.get(i) == cardType){
				continue;
			}else {
				fengShunZiTemp.add(cardInts.get(i));
				fengShunZiTemp.add(cardType);
			}
			// 遍历私有牌的所有风牌
			for(int j = 0; j < cardInts.size(); j++){
				if(cardInts.get(j) != cardInts.get(i) && cardInts.get(j) != cardType){
					List<Integer> fengShunZi = new ArrayList<>(fengShunZiTemp); // 风牌顺子
					// 风牌顺子添加不重复的牌型
					fengShunZi.add(cardInts.get(j));
					// 风牌顺子牌型排序
					fengShunZi.sort((o1, o2)->o1.compareTo(o2));
					//是否有重复的风牌顺子
					if(!chiList.contains(fengShunZi)){
						chiList.add(fengShunZi);
					}
				}
			}
		}
		return chiList;
	}


}
