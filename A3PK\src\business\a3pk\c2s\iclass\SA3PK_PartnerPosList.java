package business.a3pk.c2s.iclass;	
	
import jsproto.c2s.cclass.BaseSendMsg;	
	
import java.util.List;	
import java.util.Set;	
	
	
public class SA3PK_PartnerPosList extends BaseSendMsg {	
    /**	
     * 房间id	
     */	
    public long roomID;	
    /**	
     * 独打位置	
     */	
    public Integer challengePos = -1;	
    /**	
     * 显示牌列表	
     */	
    public Set<Integer> showCardList = null;	
	
    public static SA3PK_PartnerPosList make(long roomID, Integer challengePos,Set<Integer> showCardList) {	
        SA3PK_PartnerPosList ret = new SA3PK_PartnerPosList();	
        ret.setChallengePos(challengePos);	
        ret.setShowCardList(showCardList);	
        return ret;	
	
    }	
	
    public long getRoomID() {	
        return roomID;	
    }	
	
    public void setRoomID(long roomID) {	
        this.roomID = roomID;	
    }	
	
    public Integer getChallengePos() {	
        return challengePos;	
    }	
	
    public void setChallengePos(Integer challengePos) {	
        this.challengePos = challengePos;	
    }	
	
    public void setShowCardList(Set<Integer> showCardList) {	
        this.showCardList = showCardList;	
    }	
}	
