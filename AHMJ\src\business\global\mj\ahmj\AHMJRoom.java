package business.global.mj.ahmj;

import business.global.mj.AbsMJSetRoom;
import business.global.mj.ahmj.AHMJRoomEnum.AHMJCfg;
import business.global.room.ContinueRoomInfoMgr;
import business.global.room.base.AbsRoomPosMgr;
import business.global.room.base.DissolveRoom;
import business.global.room.mj.MahjongRoom;
import business.ahmj.c2s.cclass.*;
import business.ahmj.c2s.iclass.*;
import cenum.ChatType;
import cenum.ClassType;
import cenum.room.DissolveType;
import cenum.room.GaoJiTypeEnum;
import com.ddm.server.common.CommLogD;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;
import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.RoomEndResult;
import jsproto.c2s.cclass.room.BaseRoomConfigure;
import jsproto.c2s.cclass.room.ContinueRoomInfo;
import jsproto.c2s.cclass.room.GetRoomInfo;
import jsproto.c2s.cclass.room.RoomPosInfo;
import jsproto.c2s.iclass.S_GetRoomInfo;
import jsproto.c2s.iclass.room.SBase_Dissolve;
import jsproto.c2s.iclass.room.SBase_PosLeave;

import java.util.List;

/**
 * 安化麻将 房间逻辑
 *
 * @param <T>
 * <AUTHOR>
 */

public class AHMJRoom<T> extends MahjongRoom {
	public CAHMJ_CreateRoom cfg;// 开房配置

	/**
	 * 房间内每个位置信息 管理器
	 */
	@Override public AbsRoomPosMgr initRoomPosMgr() {
		return new AHMJRoomPosMgr(this);
	}

	/**
	 * @param baseRoomConfigure
	 * @param roomKey
	 * @param ownerID
	 */
	protected AHMJRoom(BaseRoomConfigure<CAHMJ_CreateRoom> baseRoomConfigure, String roomKey, long ownerID) {
		super(baseRoomConfigure, roomKey, ownerID);
		initShareBaseCreateRoom(CAHMJ_CreateRoom.class, baseRoomConfigure);
		this.cfg = (CAHMJ_CreateRoom) this.getBaseRoomConfigure().getBaseCreateRoom();

	}

	@Override public void clear() {
		super.clear();
		cfg = null;
	}

	@Override public <E> boolean RoomCfg(E m) {
		AHMJCfg cfgEnum = (AHMJCfg) m;
		int cfgInt = cfgEnum.value();
		if (this.getRoomCfg().getKexuanwanfa().contains(cfgInt)) {
			return true;
		}
		return false;
	}
	@Override public int getWanfa() {
		return 0;
	}

	@Override protected AbsMJSetRoom newMJRoomSet(int curSetID, MahjongRoom room, int dPos) {
		return new AHMJRoomSet(curSetID, room, dPos);
	}

	@SuppressWarnings("rawtypes") @Override public GetRoomInfo getRoomInfo(long pid) {
		S_GetRoomInfo ret = new SAHMJ_GetRoomInfo();
		// 设置房间公共信息
		this.getBaseRoomInfo(ret);
		if (null != this.getCurSet()) {
			ret.setSet(this.getCurSet().getNotify_set(pid));
		} else {
			ret.setSet(new AHMJRoomSetInfo());
		}
		return ret;
	}

	@Override public <T> BaseSendMsg RoomEnd(T record, RoomEndResult<?> sRoomEndResult) {
		return SAHMJ_RoomEnd.make(this.getMJRoomRecordInfo(), this.getRoomEndResult());
	}

	@Override public BaseSendMsg ChangePlayerNum(long roomID, int createPos, int endSec, int playerNum) {
		return SAHMJ_ChangePlayerNum.make(roomID, createPos, endSec, playerNum);
	}

	@Override public BaseSendMsg ChangePlayerNumAgree(long roomID, int pos, boolean agreeChange) {
		return SAHMJ_ChangePlayerNumAgree.make(roomID, pos, agreeChange);
	}

	@Override public BaseSendMsg ChangeRoomNum(long roomID, String roomKey, int createType) {
		return SAHMJ_ChangeRoomNum.make(roomID, roomKey, createType);
	}

	@Override public BaseSendMsg Trusteeship(long roomID, long pid, int pos, boolean trusteeship) {
		return SAHMJ_Trusteeship.make(roomID, pid, pos, trusteeship);
	}

	@Override public BaseSendMsg PosLeave(SBase_PosLeave posLeave) {
		return SAHMJ_PosLeave.make(posLeave);
	}

	@Override public BaseSendMsg LostConnect(long roomID, long pid, boolean isLostConnect, boolean isShowLeave) {
		return SAHMJ_LostConnect.make(roomID, pid, isLostConnect, isShowLeave);
	}

	@Override public BaseSendMsg PosContinueGame(long roomID, int pos) {
		return SAHMJ_PosContinueGame.make(roomID, pos);
	}

	@Override public BaseSendMsg PosUpdate(long roomID, int pos, RoomPosInfo posInfo, int custom) {
		return SAHMJ_PosUpdate.make(roomID, pos, posInfo, custom);
	}

	@Override public BaseSendMsg PosReadyChg(long roomID, int pos, boolean isReady) {
		return SAHMJ_PosReadyChg.make(roomID, pos, isReady);
	}

	@Override public BaseSendMsg Dissolve(SBase_Dissolve dissolve) {
		return SAHMJ_Dissolve.make(dissolve);
	}

	@Override public DissolveRoom initDissolveRoom(int posID, int WaitSec) {
		return new DissolveRoom(this, posID, WaitSec);
	}

	@Override public BaseSendMsg StartVoteDissolve(long roomID, int createPos, int endSec) {
		return SAHMJ_StartVoteDissolve.make(roomID, createPos, endSec);
	}

	@Override public BaseSendMsg PosDealVote(long roomID, int pos, boolean agreeDissolve, int endSec) {
		return SAHMJ_PosDealVote.make(roomID, pos, agreeDissolve);
	}

	@Override public BaseSendMsg Voice(long roomID, int pos, String url) {
		return SAHMJ_Voice.make(roomID, pos, url);
	}

	@Override public BaseSendMsg XiPai(long roomID, long pid, ClassType cType) {
		return SAHMJ_XiPai.make(roomID, pid, cType);
	}

	@Override public BaseSendMsg ChatMessage(long pid, String name, String content, ChatType type, long toCId,
			int quickID) {
		return SAHMJ_ChatMessage.make(pid, name, content, type, toCId, quickID);
	}

	@Override public <T> BaseSendMsg RoomRecord(List<T> records) {
		return SAHMJ_RoomRecord.make(records);
	}

	@Override public String dataJsonCfg() {
		return new Gson().toJson(this.getRoomCfg());
	}

	/**
	 * 获取房间配置
	 *
	 * @return
	 */
	public CAHMJ_CreateRoom getRoomCfg() {
		if (this.cfg == null) {
			initShareBaseCreateRoom(CAHMJ_CreateRoom.class, getBaseRoomConfigure());
			return (CAHMJ_CreateRoom) getBaseRoomConfigure().getBaseCreateRoom();
		}
		return this.cfg;
	}

	/**
	 * 清除记录。
	 */
	@Override public void clearEndRoom() {
		super.clear();
		cfg = null;// 开房配置
	}

	@SuppressWarnings("unchecked") @Override public T getCfg() {
		return (T) getRoomCfg();
	}

	/**
	 * 检查是否解散房间
	 *
	 * @return T:解散,F:不解散
	 */
	public boolean checkDissolveRoom(int curSec) {
		return checkDissolveRoom(curSec, DissolveType.ALL);
	}

	/**
	 * 机器人处理
	 */
	@Override public void RobotDeal(int pos) {
		if (this.getCurSet() != null) {
			AbsMJSetRoom mSetRoom = (AbsMJSetRoom) this.getCurSet();
			if (null != mSetRoom.getCurRound()) {
				mSetRoom.getCurRound().RobothandCrad(pos);
			}
		}
	}

	/**
	 * 继续房间功能 如果有需要的话去子游戏那边处理
	 */
	@Override protected void continueRoom() {
		ContinueRoomInfo continueRoomInfo = new ContinueRoomInfo();
		continueRoomInfo.setRoomID(this.getRoomID());
		continueRoomInfo.setBaseRoomConfigure(this.getBaseRoomConfigure().deepClone());
		continueRoomInfo.setRoomEndTime(this.getGameRoomBO().getEndTime());
		continueRoomInfo.setPlayerIDList(this.getRoomPidAll());
		ContinueRoomInfoMgr.getInstance().putContinueRoomInfo(continueRoomInfo);
	}

	/**
	 * 30秒未准备自动退出
	 *
	 * @return
	 */
	@Override public boolean is30SencondTimeOut() {
		return checkGaoJiXuanXiang(GaoJiTypeEnum.SECOND_TIMEOUT_30);
	}


	/**
	 * 是否禁止语音
	 *
	 * @return
	 */
	@Override
	public boolean isDisAbleVoice() {
		return checkGaoJiXuanXiang(GaoJiTypeEnum.DISABLE_VOICE);
	}


	/**
	 * 是否能切换人数
	 *
	 * @return boolean
	 */
	@Override
	public boolean isCanChangePlayerNum() {
		return this.getBaseRoomConfigure().getBaseCreateRoom().getFangjian().contains(AHMJRoomEnum.AHMJGameRoomConfigEnum.FangJianQieHuanRenShu.ordinal());
	}

	/**
	 * 自动准备游戏 玩家加入房间时，自动进行准备。
	 */
	@Override
	public boolean autoReadyGame() {
		return this.getBaseRoomConfigure().getBaseCreateRoom().getFangjian().contains(AHMJRoomEnum.AHMJGameRoomConfigEnum.ZiDongZhunBei.ordinal());
	}

	/**
	 * 房主需要准备
	 *
	 * @return T:不准备,F:默认准备
	 */
	@Override
	public boolean ownerNeedReady() {
		return !this.getBaseRoomConfigure().getBaseCreateRoom().getFangjian().contains(AHMJRoomEnum.AHMJGameRoomConfigEnum.ZiDongZhunBei.ordinal());
	}

	/**
	 * 是否小局自动解散
	 *
	 * @return boolean
	 */
	public boolean isSetAutoJieSan() {
		return this.getBaseRoomConfigure().getBaseCreateRoom().getFangjian().contains(AHMJRoomEnum.AHMJGameRoomConfigEnum.SetAutoJieSan.ordinal());
	}
	
	/**
	 * 七王玩法
	 *
	 * @return
	 */
	public boolean isKingsOfSeven() {
		//Todo 七王玩法
		return AHMJRoomEnum.WangShu.Seven.ordinal() == getRoomCfg().getWangshu();
	}

	/**
	 * 摸牌打牌的机器人
	 * @return
	 */
	public boolean isMoDa(){
		return true;
	}


	/**
	 * 获得翻牌个数
	 *
	 * @return boolean
	 */
	public int getFlipCardNum() {
		//Todo 翻牌个数
		return AHMJRoomEnum.AHMJMJGangPai.valueOf(getRoomCfg().getGangpai()).value();
	}

	/**
	 * 杠上炮算大胡
	 *
	 * @return boolean
	 */
	public boolean gangShangPaoHuDa() {
		return RoomCfg(AHMJCfg.GangShangPaoDaHu);
	}

	/**
	 * 3王或者以上的胡抢杠胡或者杠上炮必须有玩法
	 *
	 * @return boolean
	 */
	public boolean kingHuFromQGH() {
		return RoomCfg(AHMJCfg.NotJiePaoSanWang);
	}

	/**
	 * 硬庄
	 *
	 * @return boolean
	 */
	public boolean yingZhuang() {
		return RoomCfg(AHMJCfg.DaiWangYing);
	}


	/**
	 * 获取抓鸟数量
	 *
	 * @return int
	 */
	public int getZhuaNiaoNum() {
		if(getRoomCfg().getZhuaniaomoshi() == AHMJRoomEnum.ZhuaNiaoMoShi.ZHONG159.ordinal()){
			return AHMJRoomEnum.NiaoShu.getNiaoShu(getRoomCfg().getNiaoshu());
		}
		return 0;
	}

	/**
	 * 打鸟
	 */
	public void opDaNiao(WebSocketRequest request, long pid, CAHMJ_DaNiao data) {
		try {
			lock();
			if (null == this.getCurSet()) {
				request.error(ErrorCode.NotAllow, "");
				return;
			}
			AHMJRoomSet roomSet = (AHMJRoomSet) this.getCurSet();
			roomSet.opDaNiao(request, pid, data);
		}catch (Exception e){
			CommLogD.error(e.getMessage());
		}finally {
			unlock();
		}
	}

	/**
	 * 新一局
	 */
	@Override
	public void startNewSet() {
		// 更新连续托管局数
		for(int i = 0; i < getPlayerNum(); i++){
			AHMJRoomPos ahmjRoomPos = (AHMJRoomPos)getRoomPosMgr().getPosByPosID(i);
			if(ahmjRoomPos.isTrusteeship()){ // 托管
				ahmjRoomPos.addTuoGuanSetCount();
			}
		}
		this.setCurSetID(this.getCurSetID() + 1);
		// / 计算庄位
		if (this.getCurSetID() == 1) {
			setDPos(0);
		} else if (this.getCurSet() != null) {
			AbsMJSetRoom mRoomSet = (AbsMJSetRoom) this.getCurSet();
			// 根据上一局计算下一局庄家
			setDPos(mRoomSet.calcNextDPos());
			mRoomSet.clear();
		}
		getRoomPosMgr().getRoomPosList().forEach(n->{
			((AHMJRoomPos)n).setDaNiao(AHMJRoomEnum.AHMJDaNiao.NOT_OP);
		});
		// 每个位置，清空准备状态
		this.getRoomPosMgr().clearGameReady();
		// 通知局数变化
		this.getRoomTyepImpl().roomSetIDChange();
		this.setCurSet(this.newMJRoomSet(this.getCurSetID(), this, this.getDPos()));
	}

	/**
	 * 不能低于零规则
	 *
	 * @return boolean
	 */
	public boolean isRulesOfCanNotBelowZero(){
		return RoomCfg(AHMJCfg.BiSaiFenBuDiYuLing);
	}

	/**
	 * 是否需要解散次数
	 * @return
	 */
	@Override
	public boolean needDissolveCount(){
		return AHMJRoomEnum.AHMJGameRoomConfigEnum.getJieSanCiShu(getRoomCfg().getFangjian())>0;
	}

	/**
	 * 获取解散次数
	 * @return
	 */
	@Override
	public int getJieShanShu(){
		return AHMJRoomEnum.AHMJGameRoomConfigEnum.getJieSanCiShu(getRoomCfg().getFangjian());
	}



	/**
	 * 断线回来清除托管
	 * @return
	 */
	public boolean isConnectClearTrusteeship(){
		return false;
	}

	/**
	 * 有没有带多少赢多少
	 * @return
	 */
	public  boolean checkTakeLose(){
		return RoomCfg(AHMJCfg.TakeLose);
	}
}
