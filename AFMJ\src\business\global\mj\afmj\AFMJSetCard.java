package business.global.mj.afmj;

import business.global.mj.AbsMJSetCard;
import business.global.mj.MJCard;
import business.global.mj.RandomCard;
import cenum.mj.MJCardCfg;
import com.ddm.server.common.utils.Random;

import java.util.ArrayList;
import java.util.List;

/**
 * 每一局麻将底牌信息 抓牌人是逆时针出手 牌是顺时针被抓
 */
public class AFMJSetCard extends AbsMJSetCard {
	public AFMJRoomSet set;
	private int kaiJinHuaNum;

	public AFMJSetCard(AFMJRoomSet set) {
		this.set = set;
		this.room = set.getRoom();
		this.kaiJinHuaNum = 0;
		this.randomCard();
	}

	/**
	 * 洗牌
	 * 用牌：万、筒、条、东、南、西、北、中、发、白，共计136张牌。
	 * 字牌：（合计28张）——任意三张不同的风牌或三元牌各一张，可组成顺子；
	 */
	public void randomCard() {
		List<MJCardCfg> mCfgs = new ArrayList<>();
		// 序数牌
		mCfgs.add(MJCardCfg.WANG); // 有万牌,1~9万,各4张，共36张
		mCfgs.add(MJCardCfg.TIAO); // 有条牌,1~9条,各4张，共36张
		mCfgs.add(MJCardCfg.TONG); // 有筒牌,1~9筒,各4张，共36张

		// 字牌
		mCfgs.add(MJCardCfg.FENG); // 有风牌,东南西北风,各4张，共计16张
		mCfgs.add(MJCardCfg.JIAN); // 有箭牌,中发,各4张，共计8张
		mCfgs.add(MJCardCfg.BAI); //  有箭牌,白,共计4张


//		mCfgs.add(MJCardCfg.HUA); //  有花牌,春、夏、秋、冬、梅、兰、竹、菊,各1张，共计8张
//		this.setHuaType(5 * 10);
//		this.setBaiJin(false);
//		this.addJinCard(new MJCard(6001));
		baseRandomCard(mCfgs); // 洗牌,摇骰子
		this.initDPos(this.set); // 第一局随机庄家
	}

	public void baseRandomCard(List<MJCardCfg> mCfgs) {
		this.randomCard = new RandomCard(mCfgs, this.room.getPlayerNum(), this.room.getXiPaiList().size());
		this.room.getXiPaiList().clear();
	}

	@Override
	public MJCard pop(boolean isNormalMo, int cardType) {
		int sizeCard = this.randomCard.getSize();
		// 留牌：不留牌；
		// 最后1张牌摸完后没有胡牌则为流局
		// 最后1张能摸的牌摸完后打出一张后，没有人胡牌则为流局。
		if (sizeCard <= 1) {
			this.set.setLastCard(true);
		}
		if (sizeCard <= 0) { // 流局：摸完最后一张能摸的牌后且打出一张牌后，没有人胡牌则为流局。
			return null;
		}

		MJCard ret = this.getGodCard(cardType);
		ret = null != ret ? ret : this.randomCard.removeLeftCards(0);
		if (isNormalMo)
			this.randomCard.setNormalMoCnt(this.randomCard.getNormalMoCnt() + 1);
		else
			this.randomCard.setGangMoCnt(this.randomCard.getGangMoCnt() + 1);
		return ret;
	}

	/**
	 * @return true:开局是随机庄家 , false: 开局不是随机庄家
	 */
	@Override
	protected boolean firstRandomDPos() {
		return true;
	}

	/**
	 * 开金 不开花
	 */
	public MJCard popKaiJin() {
		MJCard card = this.randomCard.getLeftCards().get(kaiJinHuaNum);
		// 如果是花的话重新开一张
		if (card.getType() > 50) {
			kaiJinHuaNum++;
			this.popKaiJin();
		}
		this.randomCard.getLeftCards().remove(card);
		return card;
	}


	/**
	 *	翻开的正精和副精都有四张，且翻开的牌不能是花牌；
	 *  没有花牌
	 */
	public MJCard popJinCard(int cardType) {

		int sizeCard = this.randomCard.getSize();

		// 不留牌
		// 最后1张牌摸完后没有胡牌则为流局
		if (sizeCard <= 0) {
			this.set.setLastCard(true);
			return null;
		}

		MJCard ret = this.getGodCard(cardType);
		// 翻开的牌不能是花牌
		MJCard jinCard;
		// 随机开金 , 没有花牌
		int index = Random.nextInt(this.randomCard.getLeftCards().size());
		jinCard = new MJCard(this.randomCard.getLeftCards().get(index).cardID);

		ret = null != ret ? ret : jinCard;
		return ret;

	}

	

}
