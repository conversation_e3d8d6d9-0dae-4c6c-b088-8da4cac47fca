package business.global.mj.ahmj;

import business.global.mj.AbsMJSetPos;
import business.global.mj.AbsMJSetRoom;
import business.global.mj.MJCard;
import business.global.mj.ahmj.ting.AHMJTingImpl;
import business.global.mj.manage.MJFactory;
import business.global.room.mj.MJRoomPos;
import business.ahmj.c2s.cclass.AHMJResults;
import business.ahmj.c2s.cclass.AHMJRoom_PosEnd;
import business.ahmj.c2s.cclass.AHMJSet_Pos;
import cenum.mj.MJHuOpType;
import cenum.mj.OpType;
import jsproto.c2s.cclass.mj.BaseMJRoom_PosEnd;
import jsproto.c2s.cclass.mj.BaseMJSet_Pos;
import jsproto.c2s.cclass.room.AbsBaseResults;

import java.util.ArrayList;
import java.util.List;

/**
 * 青岛 每一局每个位置信息
 *
 * <AUTHOR>
 */
public class AHMJSetPos extends AbsMJSetPos {
    //杠列表
    private List<Integer> anGangList = new ArrayList<>();
    //杠列表
    private List<Integer> buGangList = new ArrayList<>();
    //中鸟列表
    private List<Integer> zhongNiaoList = new ArrayList<>();
    /**
     * 扣听
     */
    private boolean isTing = false;
    /**
     * 抢杠胡标志
     */
    private boolean qiangGangHuFlag = false;
    /**
     * 杠上炮标志
     */
    private boolean gangShangPaoFlag = false;
    /**
     * 杠上花标志
     */
    private boolean gangShangHuaFlag = false;
    /**
     * @param posID
     * @param roomPos
     * @param set
     */
    public AHMJSetPos(int posID, MJRoomPos roomPos, AbsMJSetRoom set) {
        super(posID, roomPos, set, AHMJTingImpl.class);
        this.setOpHuType(null);
        this.setMSetOp(new AHMJSetOp(this));
        this.setCalcPosEnd(new AHMJCalcPosEnd(this));
    }

    @Override
    public void calcPosPoint() {
        this.getCalcPosEnd().calcPosPoint(this);
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    @Override
    public BaseMJRoom_PosEnd<AHMJCalcPosEnd> calcPosEnd() {
        this.getCalcPosEnd().calcPosEnd(this);
        AHMJRoom_PosEnd ret = (AHMJRoom_PosEnd)this.posEndInfo();
        ret.setEndPoint(this.getCalcPosEnd().getCalcPosEnd());
        ret.setZhongNiaoList(getZhongNiaoList());
        ret.setDaNiao(((AHMJRoomPos)getRoomPos()).getDaNiao().index);
        return ret;
    }

    /**
     * 新位置结算信息
     *
     * @return
     */
    @SuppressWarnings("rawtypes")
    protected AHMJRoom_PosEnd newMJSetPosEnd() {
        return new AHMJRoom_PosEnd();
    }

    @Override
    @SuppressWarnings("rawtypes")
    public AHMJSet_Pos getNotify(boolean isSelf) {
        AHMJSet_Pos ret = (AHMJSet_Pos) super.getNotifyInfo(isSelf);
        ret.setYaoGang(isTing);
        ret.setNiao(((AHMJRoomPos)getRoomPos()).getDaNiao().index);
        return ret;
    }

    /**
     * 新一局中各位置的信息
     *
     * @return
     */
    @Override
    protected AHMJSet_Pos newMJSetPos() {
        return new AHMJSet_Pos();
    }

    @Override
    public boolean doOpType(int cardID, OpType opType) {
        return this.getmSetOp().doOpType(cardID, opType);
    }

    @Override
    public boolean checkOpType(int cardID, OpType opType) {
        return this.getmSetOp().checkOpType(cardID, opType);
    }

    /**
     * 检查胡
     *
     * @param
     * @param cardID
     * @return
     */
    @Override
    public OpType checkPingHu(int curOpPos, int cardID) {
        // 清空操作牌初始
        this.clearPaoHu();
        OpType opType = isQiangGangHuFlag()?OpType.QiangGangHu:OpType.JiePao;
        if (!this.getPosOpRecord().isHuCardType(cardID / 100) && checkOpType(cardID, opType)) {
            this.setmHuOpType(MJHuOpType.JiePao);
            // 可胡牌，下一轮检测是不是漏胡用
            this.getPosOpRecord().setHuCardType(cardID / 100);
            // 设置炮胡类型
            return OpType.JiePao;
        }
        return OpType.Not;
    }

    /**
     * 操作
     *
     * @return
     */
    public int isOpSize() {
        return ((AHMJSetOp) this.getmSetOp()).isOpSize();
    }

    /**
     * 接收
     *
     * @return {@link List<OpType>}
     */
    @Override
    public List<OpType> recieveOpTypes() {
        // 清空记录数据
        this.clearOutCard();
        OpType opType = OpType.Not;
        List<OpType> opTypes = new ArrayList<>();
        if (checkOpType(0, OpType.Hu)) {
            opType = OpType.Hu;
        }
        if (!OpType.Not.equals(opType)) {
            this.getPosOpRecord().setHuCardType(1000);
            this.setmHuOpType(MJHuOpType.ZiMo);
            opTypes.add(OpType.Hu);
        }
        if (checkOpType(0, OpType.Ting)) {
            opTypes.add(OpType.Ting);
        }
        if (checkOpType(0, OpType.AnGang)) {
            opTypes.add(OpType.AnGang);
        }
        if (checkOpType(0, OpType.Gang)) {
            opTypes.add(OpType.Gang);
        }
        opTypes.add(OpType.Out);
        return opTypes;
    }

    @Override
    protected AbsBaseResults newResults() {
        return new AHMJResults();
    }

    @Override
    public <T> void calcOpPointType(T opType, int count) {
        getCalcPosEnd().calcOpPointType(opType, count);
    }

    /**
     * 检查报听
     *
     * @param key
     * @return
     */
    public boolean checkBaotingGang(Integer key) {
        List<MJCard> allCardInts = new ArrayList<>();
        allCardInts.addAll(allCards());
        allCardInts.removeIf(b -> b.getType() == key);
        AHMJTingImpl tImpl = (AHMJTingImpl) MJFactory.getTingCard(AHMJTingImpl.class);
        if (tImpl.checkTingCardList(allCardInts, this)) {
            return true;
        }
        return false;
    }


    public List<Integer> getAnGangList() {
        return anGangList;
    }

    public List<Integer> getBuGangList() {
        return buGangList;
    }


    /**
     * 结算分数
     */
    public void setPosEnd() {
        getCalcPosEnd().calcPosPoint(this);
    }

    public boolean isTing() {
        return isTing;
    }

    public void setTing(boolean ting) {
        isTing = ting;
    }

    /**
     * 抢杠胡
     *
     * @return boolean
     */
    public boolean isQiangGangHuFlag() {
        return qiangGangHuFlag;
    }

    /**
     * 抢杠胡
     *
     * @param qiangGangHuFlag 抢杠胡
     */
    public void setQiangGangHuFlag(boolean qiangGangHuFlag) {
        this.qiangGangHuFlag = qiangGangHuFlag;
    }

    /**
     * 设置杠上炮
     *
     * @param gangShangPaoFlag 杠上炮
     */
    public void setGangShangPaoFlag(boolean gangShangPaoFlag) {
        this.gangShangPaoFlag = gangShangPaoFlag;
    }

    /**
     * 是否杠上炮
     *
     * @return boolean
     */
    public boolean isGangShangPaoFlag() {
        return gangShangPaoFlag;
    }

    public void setGangShangHuaFlag(boolean gangShangHuaFlag) {
        this.gangShangHuaFlag = gangShangHuaFlag;
    }

    public boolean isGangShangHuaFlag() {
        return gangShangHuaFlag;
    }

    public void setZhongNiaoList(List<Integer> zhongNiaoList) {
        this.zhongNiaoList = zhongNiaoList;
    }

    public List<Integer> getZhongNiaoList() {
        return zhongNiaoList;
    }

    public AHMJSetPosRobot getSetPosRobot() {
        return new AHMJSetPosRobot(this);
    }

    @Override
    public void clearOpHuType() {
        this.setOpHuType(null);
    }


    @Override
    public void calcResults() {
        AHMJResults lResults = (AHMJResults) this.mResultsInfo();
        lResults.setAnGangPoint(lResults.getAnGangPoint()+getGangCountByType(1));
        lResults.setGangPoint(lResults.getGangPoint()+getGangCountByType(0));
        this.setResults(lResults);
    }

    /**
     * 获取杠的次数
     *
     * @param type 类型 0明杠 1暗杠
     * @return int
     */
    public int getGangCountByType(int type){
        Long gangNum = 0L;
        if(type==0){
            gangNum = getPublicCardList().stream().filter(n->OpType.JieGang.value()==n.get(0)||OpType.Gang.value()==n.get(0)).count();
        }else if(type==1){
            gangNum = getPublicCardList().stream().filter(n->OpType.AnGang.value()==n.get(0)).count();
        }
        return gangNum.intValue();
    }

    @Override
    public BaseMJSet_Pos getPlayBackNotify() {
        AHMJSet_Pos setPos = (AHMJSet_Pos) super.getPlayBackNotify();
        setPos.setNiao(((AHMJRoomPos)getRoomPos()).getDaNiao().index);
        setPos.setYaoGang(isTing);
        return setPos;
    }

}
