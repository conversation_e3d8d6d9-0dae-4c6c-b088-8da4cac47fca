package core.network.client2game.handler.ahlxmj;										
										
import business.ahlxmj.c2s.iclass.CAHLXMJ_CreateRoom;							
import business.player.Player;							
import business.player.feature.PlayerRoom;							
import cenum.PrizeType;							
import com.ddm.server.websocket.def.ErrorCode;							
import com.ddm.server.websocket.handler.requset.WebSocketRequest;							
import com.google.gson.Gson;							
import core.network.client2game.handler.PlayerHandler;							
import core.network.http.proto.SData_Result;							
import core.server.ahlxmj.AHLXMJAPP;							
import jsproto.c2s.cclass.room.BaseRoomConfigure;							
							
import java.io.IOException;							
										
/**										
 * 创建房间										
 * 										
 * <AUTHOR>										
 *										
 */										
public class CAHLXMJCreateRoom extends PlayerHandler {										
							
	@SuppressWarnings("rawtypes")							
	@Override										
	public void handle(Player player, WebSocketRequest request, String message)										
			throws IOException {										
										
		final CAHLXMJ_CreateRoom clientPack = new Gson().from<PERSON>son(message,										
				CAHLXMJ_CreateRoom.class);										
		// 公共房间配置										
		BaseRoomConfigure<CAHLXMJ_CreateRoom> configure = new BaseRoomConfigure<CAHLXMJ_CreateRoom>(										
				PrizeType.RoomCard,										
				AHLXMJAPP.GameType(),										
				clientPack.clone());										
		SData_Result resule = player.getFeature(PlayerRoom.class).createRoomAndConsumeCard(configure);										
		if (ErrorCode.Success.equals(resule.getCode())) {										
			request.response(resule.getData());										
		} else {										
			request.error(resule.getCode(),resule.getMsg());										
		}										
	}										
}										
