package business.global.mj.ahmj;

import business.ahmj.c2s.cclass.AHMJPointItem;
import business.global.mj.AbsMJSetOp;
import business.global.mj.AbsMJSetPos;
import business.global.mj.AbsMJSetRoom;
import business.global.mj.MJCard;
import business.global.mj.ahmj.optype.*;
import business.global.mj.ahmj.ting.AHMJTingImpl;
import business.global.mj.hu.QiangGangHuCardImpl;
import business.global.mj.manage.MJFactory;
import cenum.mj.MJHuOpType;
import cenum.mj.OpType;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * 安化麻将
 *
 * <AUTHOR>
 */
public class AHMJSetOp extends AbsMJSetOp {
    // 操作
    private List<OpType> opTypes = new ArrayList<>();
    // 玩家信息
    private AHMJSetPos mSetPos;

    public AHMJSetOp(AHMJSetPos mSetPos) {
        super();
        this.mSetPos = mSetPos;
    }

    /**
     * 添加动作
     *
     * @param doOpType 是否操作成功
     * @param opType   动作类型
     */
    private void addOp(AbsMJSetPos mSetPos, boolean doOpType, OpType opType) {
        if (doOpType) {
            mSetPos.calcHuFan();
            this.opTypes.add(opType);
        }
    }

    /**
     * 执行动作
     *
     * @param cardID 牌ID
     * @param opType 动作类型
     * @return
     */
    @Override
    public boolean doOpType(int cardID, OpType opType) {
        boolean doOpType = false;
        switch (opType) {
            case AnGang:
                doOpType = ((AHMJAnGangImpl) MJFactory.getOpCard(AHMJAnGangImpl.class)).doOpCard(mSetPos, cardID, opType);
                this.addOp(mSetPos, doOpType, OpType.AnGang);
                break;
            case Gang:
                doOpType = MJFactory.getOpCard(AHMJBuGangCardImpl.class).doOpCard(mSetPos, cardID);
                this.addOp(mSetPos, doOpType, OpType.Gang);
                break;
            case JieGang:
                doOpType = MJFactory.getOpCard(AHMJJieGangCardImpl.class).doOpCard(mSetPos, cardID);
                this.addOp(mSetPos, doOpType, OpType.JieGang);
                break;
            case Peng:
                doOpType = MJFactory.getOpCard(AHMJPengCardImpl.class).doOpCard(mSetPos, cardID);
                break;
            case Chi:
                doOpType = MJFactory.getOpCard(AHMJChiCardImpl.class).doOpCard(mSetPos, cardID);
                break;
            case QiangGangHu:
                doOpType = MJFactory.getHuCard(QiangGangHuCardImpl.class).checkHuCard(mSetPos.getMJSetPos());
                if (doOpType) {
                    mSetPos.setmHuOpType(MJHuOpType.QGHu);
                }
                break;
            case TianHu:
            case JiePao:
            case Hu:
                //杠上炮或者杠上花
            case YaoGangHu:
                doOpType = doPingHu(mSetPos,opType);
                break;
        }
        return doOpType;
    }

    /**
     * 做平胡
     *
     * @param mSetPos m集pos
     * @return boolean 平胡结果
     */
    private boolean doPingHu(AbsMJSetPos mSetPos,OpType opType) {
        if(OpType.JiePao.equals(opType)){
            int lastOutCard = mSetPos.getSet().getLastOpInfo().getLastOutCard();
            if (lastOutCard > 0) {
                mSetPos.setHandCard(new MJCard(lastOutCard));
            }
            mSetPos.setmHuOpType(MJHuOpType.JiePao);
        }else if(OpType.Hu.equals(opType)){
            mSetPos.setmHuOpType(MJHuOpType.ZiMo);
        }else if(OpType.YaoGangHu.equals(opType)){
            AHMJPointItem item = (AHMJPointItem)mSetPos.getOpHuType();
            if(Objects.nonNull(item)){
                if(item.getAHMJOpPoints().contains(AHMJRoomEnum.AHMJOpPoint.GSKH)){
                    //杠上花
                    mSetPos.setmHuOpType(MJHuOpType.ZiMo);
                }else{
                    //杠上炮
                    mSetPos.setmHuOpType(MJHuOpType.JiePao);
                }
                mSetPos.setHandCard(new MJCard(item.getCardID()));
                AHMJRoomSet set = (AHMJRoomSet)mSetPos.getSet();
                for(Integer card:set.getFlipCardList()){
                    //剩余牌加入打牌区域
                    if(card!=item.getCardID()){
                        int ownnerPos = mSetPos.getMJSetCard().getCardByID(card).ownnerPos;
                        AHMJSetPos mjSetPos = (AHMJSetPos)set.getMJSetPos(ownnerPos);
                        mjSetPos.addOutCardIDs(card);
                    }
                }
                //清空翻牌列表
                set.clearFlipCardList();
            }
        }
        return true;
    }

    /**
     * 检查胡返回
     *
     * @param mSetPos m集pos
     * @param cardID
     * @return boolean
     */
    private boolean checkHuCardReturn(AbsMJSetPos mSetPos, OpType optype, AHMJPointItem item, int cardID) {
        if (Objects.isNull(item)) {
            return false;
        }
        //杠上炮和杠上开花需要记录胡的牌
        if(OpType.YaoGangHu.equals(optype) || OpType.GSKH.equals(optype)){
            item.setCardID(cardID);
        }
        mSetPos.setOpHuType(item);
        return true;
    }

    /**
     * 检查动作类型
     * q
     *
     * @param cardID 牌ID
     * @param opType 动作类型
     * @return
     */
    @Override
    public boolean checkOpType(int cardID, OpType opType) {
        int cardType = cardID / 100;
        boolean isOpType = false;
        switch (opType) {
            case AnGang:
                if (this.mSetPos.getSet().getSetCard().getRandomCard().getSize() == 0) {
                    break;
                }
                isOpType = MJFactory.getOpCard(AHMJAnGangImpl.class).checkOpCard(mSetPos, cardID);
                break;
            case Ting:
                isOpType = MJFactory.getTingCard(AHMJTingImpl.class).checkTingList(mSetPos);
                break;
            case JieGang:
                if (this.mSetPos.getSet().getSetCard().getRandomCard().getSize() == 0) {
                    break;
                }
                isOpType = MJFactory.getOpCard(AHMJJieGangCardImpl.class).checkOpCard(mSetPos, cardID);
                break;
            case Gang:
                if (this.mSetPos.getSet().getSetCard().getRandomCard().getSize() == 0) {
                    break;
                }
                isOpType = MJFactory.getOpCard(AHMJBuGangCardImpl.class).checkOpCard(mSetPos, cardID);
                break;
            case Peng:
                isOpType = MJFactory.getOpCard(AHMJPengCardImpl.class).checkOpCard(mSetPos, cardID);
                break;
            case Chi:
                isOpType = MJFactory.getOpCard(AHMJChiCardImpl.class).checkOpCard(mSetPos, cardID);
                break;
            case Hu:
            case JiePao:
            case TianHu:
                //杠上花
            case GSKH:
                //杠上炮
            case QiangGangHu:
                //杠上炮
            case YaoGangHu:
                if(OpType.GSKH.equals(opType)){
                    mSetPos.setGangShangHuaFlag(true);
                }
                if(OpType.YaoGangHu.equals(opType)){
                    mSetPos.setGangShangPaoFlag(true);
                }
                boolean isZiMo = OpType.QiangGangHu.equals(opType) || OpType.GSKH.equals(opType) || OpType.YaoGangHu.equals(opType) || cardType==0;
                boolean isHu = this.checkHuCardReturn(mSetPos, opType, ((AHMJTingImpl) MJFactory.getTingCard(AHMJTingImpl.class)).getPointItem(mSetPos, mSetPos.mCardInit(cardType, true), isZiMo), cardID);
                mSetPos.setGangShangHuaFlag(false);
                mSetPos.setGangShangPaoFlag(false);
                return isHu;
            //检测烂胡
            case SanJinDao:
                return this.checkHuCardReturn(mSetPos, opType, ((AHMJTingImpl) MJFactory.getTingCard(AHMJTingImpl.class)).checkLanhu(mSetPos),cardID);
            default:
                break;
        }
        return isOpType;
    }

    @Override
    public void clear() {
        this.mSetPos = null;
    }

    /**
     * 检查动作
     *
     * @return
     */
    public int isOpSize() {
        return this.opTypes.size();
    }

}
