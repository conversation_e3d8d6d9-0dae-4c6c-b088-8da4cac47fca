package business.global.mj.ahhbmj;


/**
 * 淮北麻将 枚举
 *
 * <AUTHOR>
 */

public class AHHBMJRoomEnum {


    /**
     * 可选玩法：坐拉跑,不可点炮,一炮多响；带8花,不留牌
     */
    public enum KeXuanWanFa {
        ZUO_LA_PAO,
        BU_KE_DIANPAO,
        YPDX,
        SSY,
        Dai8Hua,
        BuLiuPai,
    }

    /**
     * 加番：报胡,夹子,缺一；
     */
    public enum JiaFan {
        BAO_HU,
        JIA_ZI,
        QUE_YI,
    }

    /**
     * 炮胡不加分,炮胡1分
     */
    public enum PaoHu {
        PaoHuBuJiaFen,
        PaoHu1Fen,
    }

    /**
     * 自摸1分,自摸2分
     */
    public enum ZiMoFen {
        ZiMo1Fen,
        ZiMo2Fen,
    }
   /**
     * 抢杠包三家,抢杠付自己
     */
    public enum QiangGangHu {
        BaoSanJia,
        ZiJiFu,
    }

    /**
     * 七对×2,杠放/抢杠/杠开×2,十三不靠×2；
     */
    public enum FanBei {
        QIDUI_X2,
        GANGHU_X2,
        SSL_X2,
        QXSSL_X4,

    }

    /**
     * 牌数：去万牌,去字牌,去风牌；
     */
    public enum PaiShu {
        QU_WAN,
        QU_ZI,
        QU_FENG,
    }

    /**
     * 杠牌算分：杠了就有,杠随胡走；；
     */
    public enum GangPai {
        YOU_GANG_JIU_SUAN,
        GANG_SUI_HU_ZOU,
    }


    public enum AHHBMJGameRoomConfigEnum {

        /**
         * RENSHU
         */
        FANGJIANNEIQIEHUANRENSHU,
        /**
         * 自动准备
         */
        ZiDongZhunBei,
        /**
         * 小局托管解散
         */
        TuoGuanJieSan,

    }


}												
