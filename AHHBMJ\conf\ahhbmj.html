<h2 id="淮北麻将新增协议接口">淮北麻将新增协议接口</h2>
<h3 id="胡分">胡分</h3>
<pre><code>GSKH,// 杠上开花    
GSP,// 杠放
ZiMo, // 自摸
QGH, // 抢杠胡
DiHu, // 地胡
TianHu, // 天胡
SSL,    //十三不靠
QDHu,    //七对胡
BianJiaDiao,//夹子
DanTing,       //报胡
QueYiSe,//缺一
JieGang, // 直杠
Gang, // 续杠
AnGang, // 暗杠
JiePao,// 炮胡
PaoFen,//跑分</code></pre>
<h3 id="操作">操作</h3>
<pre><code><PERSON>(6), // 吃    
Out(7),// 出牌    
Peng(2),//碰 
Gang(3),//|碰杠   
JieGang(4),//直杠 
AnGang(5),//暗杠  
Ting(27),//听    
Hu(1) ,//胡  
QiangGangHu(9), // 抢杠胡  
BaoTing(146),//报听
Piao_Fen(129), //下坐 ：按钮“不下坐”、“1坐”、“2坐” 点的时候值发 0，1，2
Fan(95), // 下拉 按钮“不下拉”、“1拉”、“2拉”  点的时候值发 0，1，2
Pao(132), // 下跑 按钮“不下跑”、“1跑”、“2跑” 点的时候值发 0，1，2</code></pre>
<h3 id="ahhbmjresults-总结算">AHHBMJResults 总结算</h3>
<pre><code>private int zhuangPoint;//坐庄次数                      
private boolean DianPaoWang = false;//点炮王                       
private boolean winner = false;//大赢家                        
// 胡次数                  
private int huCnt = 0;                  
// 胡类型列表                    
private List&lt;HuType&gt; huTypes = new ArrayList&lt;HuType&gt;();                 
// 点炮                   
private int dianPaoPoint = 0;                   
// 接炮                   
private int jiePaoPoint = 0;                    
// 自摸                   
private int ziMoPoint = 0;  </code></pre>
<h3 id="ahhbmjset_pos-每局的玩家位置信息">AHHBMJSet_Pos 每局的玩家位置信息</h3>
<pre><code>public boolean isTing;//报听
private int zuoFen=-1; //-1:不显示 0：“不下坐”、1：“1坐”、2：“2坐
private int laFen=-1;//-1:不显示 0：“不下拉”、1：“1拉”、2：“2拉
private int paoFen=1;//-1:不显示 0：“不下跑”、1：“1跑”、2：“2跑</code></pre>
<h3 id="ahhbmjroom_posend-每局的玩家位置结算信息">AHHBMJRoom_PosEnd 每局的玩家位置结算信息</h3>
<pre><code>public boolean isTing;//报听
private int zuoFen=-1; //-1:不显示 0：“不下坐”、1：“1坐”、2：“2坐
private int laFen=-1;//-1:不显示 0：“不下拉”、1：“1拉”、2：“2拉
private int paoFen=1;//-1:不显示 0：“不下跑”、1：“1跑”、2：“2跑</code></pre>
