package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSCardRankEnum;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.ArrayList;	
	
/**	
 *三顺子	
 */	
public class WXLSSShunZiRankingImpl_T extends WXLSAbstractRanking {	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        WXLSRankingResult result = null;	
        ArrayList<WXLSCardRankEnum> cardsnew = player.getRanks();	
        ArrayList<ArrayList<WXLSCardRankEnum>> rets = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
        if (player.getCardSize() == 13) {	
                rets.addAll(check(cardsnew, 5, 5, 3));	
        }	
	
        if (rets.size() > 2) {	
            result = new WXLSRankingResult();	
            result.setPockerCards(player.getCards());	
            result.setRankingEnum(WXLSRankingEnum.SShunZi);	
        }	
        return result;	
    }	
	
	
}	
