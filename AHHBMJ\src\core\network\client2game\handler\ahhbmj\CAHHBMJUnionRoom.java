package core.network.client2game.handler.ahhbmj;						
						
import business.ahhbmj.c2s.iclass.CAHHBMJ_CreateRoom;						
import business.player.Player;						
import business.player.feature.PlayerUnionRoom;						
import cenum.PrizeType;						
import com.ddm.server.websocket.handler.requset.WebSocketRequest;						
import com.google.gson.Gson;						
import core.network.client2game.handler.PlayerHandler;						
import core.server.ahhbmj.AHHBMJAPP;						
import jsproto.c2s.cclass.room.BaseRoomConfigure;						
						
import java.io.IOException;						
						
/**						
 * 赛事房间						
 *						
 * <AUTHOR>						
 */						
public class CAHHBMJUnionRoom extends PlayerHandler {						
						
	@Override						
	public void handle(Player player, WebSocketRequest request, String message)						
			throws IOException {						
						
		final CAHHBMJ_CreateRoom clientPack = new Gson().fromJson(message,						
				CAHHBMJ_CreateRoom.class);						
						
		// 公共房间配置												
		BaseRoomConfigure<CAHHBMJ_CreateRoom> configure = new BaseRoomConfigure<CAHHBMJ_CreateRoom>(						
				PrizeType.RoomCard,						
				AHHBMJAPP.GameType(),						
				clientPack.clone());						
		player.getFeature(PlayerUnionRoom.class).createNoneUnionRoom(request, configure);						
	}						
}												
