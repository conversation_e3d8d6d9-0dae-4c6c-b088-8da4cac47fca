package business.global.mj.afmj.hutype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCard;
import business.global.mj.MJCardInit;
import business.global.mj.hu.BaseHuCard;
import business.global.mj.util.HuZiUtil;
import business.global.mj.afmj.AFMJRoomEnum.AFMJOpPoint;

import java.util.List;

public class AFMJHuImpl extends BaseHuCard {

	@Override
	public boolean checkHuCard(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
		if (null == mCardInit) {
			return false;
		}
		if(HuZiUtil.getInstance().checkHu(mCardInit.getAllCardInts(), mCardInit.sizeJin())){
			return true;
		}
		return false;
	}

	@Override
	public <T> Object checkHuCardReturn(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
		if (null == mCardInit) {
			return AFMJOpPoint.Not;
		}
		if (HuZiUtil.getInstance().checkHu(mCardInit.getAllCardInts(), mCardInit.sizeJin())) {
			return AFMJOpPoint.Hu;
		}
		return AFMJOpPoint.Not;
	}



	/**
	 * 获取玩家牌型数据和金数量
	 * @param mSetPos 玩家
	 * @param allCardList 手上牌
	 * @param cardType  头牌
	 * @param isJin 是否金
	 */
	public MJCardInit mCardInit(AbsMJSetPos mSetPos, List<MJCard> allCardList,int cardType, boolean isJin) {
		MJCardInit mCardInit = mSetPos.mjCardInit(allCardList, isJin);
		if (null == mCardInit) {
			return null;
		}
		// 牌类型大于 0
		if (cardType > 0) {
			// 是否有金
			if (isJin) {
				// 检查牌类型是否金
				if (mSetPos.getSet().getmJinCardInfo().getJinKeys().contains(cardType)) {
					mCardInit.addJins(cardType);
				} else {
					mCardInit.addCardInts(cardType);
				}
			} else {
				mCardInit.addCardInts(cardType);
			}
		}
		return mCardInit;
	}






}
