package business.wxmj.c2s.iclass;			
			
import java.util.ArrayList;			
			
import cenum.room.SetState;			
import jsproto.c2s.cclass.BaseSendMsg;			
			
/**			
 * 接收客户端数据			
 * 状态改变			
 * <AUTHOR>			
 *			
 */			
			
public class SWXMJ_ChangeStatus extends BaseSendMsg {			
			
	/**			
	 * 			
	 */			
	private static final long serialVersionUID = 1L;			
	public long roomID;			
	public int  setID;//局数			
    public SetState state;  //位置			
    public ArrayList<Integer> piaoFenList; //飘分			
    public int dPos = 0; // 庄家位置			
    public static SWXMJ_ChangeStatus make(long roomID, int  setID, SetState state, int dPos,ArrayList<Integer> piaoFenList) {			
    	SWXMJ_ChangeStatus ret = new SWXMJ_ChangeStatus();			
        ret.roomID = roomID;			
        ret.setID = setID;			
        ret.state = state;			
        ret.piaoFenList = piaoFenList;			
        ret.dPos = dPos;			
        return ret;			
    }			
}			
