package business.global.pk.wxls.newwxls.comparing;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSCardRankEnum;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;
import jsproto.c2s.cclass.pk.BasePocker;

import java.util.*;	
	
public abstract class WXLSAbstractComparing implements WXLSIComparing {	
	
    /**	
     * 按照三条或者或者四条的比较	
     *	
     * @param map1	
     * @param map2	
     * @param pair	
     * @return	
     */	
    protected int multiComparing(Map<Integer, Integer> map1, Map<Integer, Integer> map2, int pair) {	
	
        int p1Number = -1;	
        int p2Number = -1;	
	
        Iterator<Map.Entry<Integer, Integer>> it = map1.entrySet().iterator();	
        while (it.hasNext()) {	
            Map.Entry<Integer, Integer> next = it.next();	
            if (next.getValue() .equals( pair)) {	
                p1Number = next.getKey();	
            }	
        }	
	
        Iterator<Map.Entry<Integer, Integer>> it2 = map2.entrySet().iterator();	
        while (it2.hasNext()) {	
            Map.Entry<Integer, Integer> next = it2.next();	
            if (next.getValue() .equals( pair)) {	
                p2Number = next.getKey();	
            }	
        }	
	
        if (p1Number > p2Number) {	
            return -1;	
        }	
        if (p1Number < p2Number) {	
            return 1;	
        }	
	
        return 0;	
    }	
	
    /**	
     * 按照顺序的比较	
     *	
     * @param p1	
     * @param p2	
     * @return	
     */	
    protected int seqComparing(WXLSPlayerDun p1, WXLSPlayerDun p2) {	
        List<WXLSPockerCard> p1Cards = p1.getCards();	
        List<WXLSPockerCard> p2Cards = p2.getCards();	
	
        int size = p1.getCardSize();	
	
        for (int i = 0; i < size; i++) {	
            if (p1Cards.get(i).getRank().getNumber() < p2Cards.get(i).getRank().getNumber()) {	
                return 1;	
            }	
            if (p1Cards.get(i).getRank().getNumber() > p2Cards.get(i).getRank().getNumber()) {	
                return -1;	
            }	
            if (p1Cards.get(i).getRank().getNumber() .equals( p2Cards.get(i).getRank().getNumber())) {	
                continue;	
            }	
        }	
        return 0;	
    }	
	
    /**	
     * 按照顺序的比较	
     *	
     * @param	
     * @param	
     * @return	
     */	
    protected int seqComparing(List<WXLSPockerCard> p1Cards, List<WXLSPockerCard> p2Cards) {	
        int size = p1Cards.size();	
	
        for (int i = 0; i < size; i++) {	
            if (p1Cards.get(i).getRank().getNumber() < p2Cards.get(i).getRank().getNumber()) {	
                return 1;	
            }	
            if (p1Cards.get(i).getRank().getNumber() > p2Cards.get(i).getRank().getNumber()) {	
                return -1;	
            }	
            if (p1Cards.get(i).getRank().getNumber() .equals( p2Cards.get(i).getRank().getNumber())) {	
                continue;	
            }	
        }	
        return 0;	
    }	
    /**	
     * 顺子顺序的比较	
     *	
     * @param	
     * @param	
     * @return	
     */	
    protected int seqComparingStraight(List<WXLSPockerCard> p1Cards, List<WXLSPockerCard> p2Cards) {	
        int size = p1Cards.size();	
        boolean AShun1=checkAShun(p1Cards);	
        boolean AShun2=checkAShun(p2Cards);	
	
        if(AShun1||AShun2){	
            if(AShun1&&AShun2){	
                return 0;	
            }	
            if(AShun1){	
                return 1;	
            }	
            if(AShun2){	
                return -1;	
            }	
	
        }	
	
        for (int i = 0; i < size; i++) {	
            if (p1Cards.get(i).getRank().getNumber() < p2Cards.get(i).getRank().getNumber()) {	
                return 1;	
            }	
            if (p1Cards.get(i).getRank().getNumber() > p2Cards.get(i).getRank().getNumber()) {	
                return -1;	
            }	
            if (p1Cards.get(i).getRank().getNumber() .equals( p2Cards.get(i).getRank().getNumber())) {	
                continue;	
            }	
        }	
        return 0;	
    }	
	
    private boolean checkAShun(List<WXLSPockerCard> p1Cards) {	
        Collections.sort(p1Cards);	
        if(p1Cards.get(0).getRank().equals( WXLSCardRankEnum.CARD_ACE)&&p1Cards.get(1).getRank().equals( WXLSCardRankEnum.CARD_FIVE)){	
            return true;	
        }	
        return false;	
    }	
	
    /**	
     * @param map1	
     * @param map2	
     * @param pair	
     *            对子的数量	
     * @param maxPairLoopAddOne	
     *            对子最大的循环数量+1	
     * @return	
     */	
    protected int pairComparing(Map<Integer, Integer> map1, Map<Integer, Integer> map2, int pair,	
                                int maxPairLoopAddOne) {	
        if (maxPairLoopAddOne - 1 == 0) {	
            pair = 1;	
        }	
        int p1MaxNum = this.findMaxNumber(map1, pair);	
        int p2MaxNum = this.findMaxNumber(map2, pair);	
	
        if (p1MaxNum < p2MaxNum) {	
            return 1;	
        }	
        if (p1MaxNum > p2MaxNum) {	
            return -1;	
        }	
        if (p1MaxNum == p2MaxNum) {
            map1.remove(p1MaxNum);	
            map2.remove(p2MaxNum);	
            if (map1.size() == 0 && map2.size() == 0) {	
                return 0;	
            }	
            if (map1.size() == map2.size() && 0 == maxPairLoopAddOne - 1) {	
                return this.pairComparing(map1, map2, pair - 1, 1);	
            }	
            return this.pairComparing(map1, map2, pair, maxPairLoopAddOne - 1);	
        }	
        return 0;	
    }	
	
    public Map<Integer, Integer> getCardsRankCountMap(List<WXLSPockerCard> cards) {	
        Map<Integer, Integer> rankCount = new HashMap<Integer, Integer>();	
        for (WXLSPockerCard WXLSPockerCard : cards) {	
            Integer number = new Integer(WXLSPockerCard.getRank().getNumber());	
            if (!rankCount.containsKey(number)) {	
                rankCount.put(number, 1);	
            } else {	
                rankCount.put(number, rankCount.get(number) + 1);	
            }	
        }	
        return rankCount;	
    }	
	
    public int findMaxNumber(Map<Integer, Integer> map, int pair) {	
        int p1Number = -1;	
	
        Iterator<Map.Entry<Integer, Integer>> it = map.entrySet().iterator();	
        while (it.hasNext()) {	
            Map.Entry<Integer, Integer> next = it.next();	
            if (next.getValue() .equals( pair)) {	
                int number = next.getKey();	
                if (number > p1Number) {	
                    p1Number = number;	
                }	
            }	
        }	
        return p1Number;	
	
    }

    public  int compareZeroByColor( WXLSPlayerDun o1, WXLSPlayerDun o2){
        return  o2.getCards().get(0).getSuit().getNumber()-o1.getCards().get(0).getSuit().getNumber()>0?1:-1;
    }
	
}	
	
