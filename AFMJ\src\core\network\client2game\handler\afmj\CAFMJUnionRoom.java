package core.network.client2game.handler.afmj;

import business.player.Player;
import business.player.feature.PlayerUnionRoom;
import business.afmj.c2s.iclass.CAFMJ_CreateRoom;
import cenum.PrizeType;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;
import core.network.client2game.handler.PlayerHandler;
import core.server.afmj.AFMJAPP;
import jsproto.c2s.cclass.room.BaseRoomConfigure;

import java.io.IOException;

/**
 * 亲友圈房间
 * 
 * <AUTHOR>
 *
 */
public class CAFMJUnionRoom extends PlayerHandler {

	@Override
	public void handle(Player player, WebSocketRequest request, String message)
			throws IOException {

		final CAFMJ_CreateRoom clientPack = new Gson().fromJson(message,
				CAFMJ_CreateRoom.class);

		// 公共房间配置
		BaseRoomConfigure<CAFMJ_CreateRoom> configure = new BaseRoomConfigure<CAFMJ_CreateRoom>(
				PrizeType.RoomCard,
				AFMJAPP.GameType(),
				clientPack.clone());
		player.getFeature(PlayerUnionRoom.class).createNoneUnionRoom(request,configure);
	}
}
