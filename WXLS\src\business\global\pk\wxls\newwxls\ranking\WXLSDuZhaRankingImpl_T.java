package business.global.pk.wxls.newwxls.ranking;

import business.global.pk.wxls.newwxls.WXLSPlayerDun;
import business.wxls.c2s.cclass.newwxls.WXLSCardRankEnum;
import business.wxls.c2s.cclass.newwxls.WXLSCardSuitEnum;
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;

import java.util.*;

/**
 *十三张牌有且只有一个铁支。。。赢每家4岛
 */
public class WXLSDuZhaRankingImpl_T extends WXLSAbstractRanking {
    @Override
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {
        WXLSRankingResult result = null;
        if (player.getCardSize() == 13) {
            boolean flag = false;
            Map<Integer, Integer> rankCount = player.getCardsRankCountMap();
            int four = 0;
            int two=0;
            int three=0;
            ArrayList<WXLSCardRankEnum> newcards = new ArrayList<WXLSCardRankEnum>();
            List<WXLSPockerCard> cards=new ArrayList<>();
            Iterator<Map.Entry<Integer, Integer>> it = rankCount.entrySet().iterator();
            while (it.hasNext()) {
                Map.Entry<Integer, Integer> entry = it.next();
                int value = entry.getValue();
                if ( value == 4) {
                    four++;
                }
                if(value==2){
                    two++;
                }
                if(value==3){
                    three++;
                }
                if(value==1){
                    WXLSPockerCard card= player.getCards().stream().filter(k->k.getRank().getNumber()==entry.getKey()).findFirst().orElse(null);
                    if(Objects.nonNull(card)){
                        newcards.add(card.getRank());
                        cards.add(card);
                    }
                }
            }
            if(four==1&&two==0&&three==0){
                boolean isShunZi=checkShunZi(newcards);
                boolean isTongHua=checkTongHua(cards);
                if(!isShunZi&&!isTongHua){
                    flag=true;
                }
            }
            if (flag) {
                result = new WXLSRankingResult();
                result.setPockerCards(player.getCards());
                result.setRankingEnum(WXLSRankingEnum.DuZha);
                return result;
            }
        }
        return result;
    }

    private boolean checkTongHua(List<WXLSPockerCard> newcards) {
        Map<WXLSCardSuitEnum, List<WXLSPockerCard>> suitCount = this.getCardsSuitCountMap(newcards);
        return suitCount.entrySet().stream().anyMatch(k->k.getValue().size()>=5);
    }

    private boolean checkShunZi(ArrayList<WXLSCardRankEnum> newcards) {
        for (int i = 0; i < WXLSAbstractRanking.fiveCars.size(); i++) {
            if (newcards.containsAll(WXLSAbstractRanking.fiveCars.get(i))) {
                return true;
            }
        }
        return false;
    }
    public Map<WXLSCardSuitEnum, List<WXLSPockerCard>> getCardsSuitCountMap(List<WXLSPockerCard> newcards) {
        List<WXLSPockerCard> cards =newcards;
        Map<WXLSCardSuitEnum, List<WXLSPockerCard>> SuitCount = new HashMap<WXLSCardSuitEnum, List<WXLSPockerCard>>();
        for (WXLSPockerCard WXLSPockerCard : cards) {
            WXLSCardSuitEnum Suit = WXLSPockerCard.getSuit();
            if (!SuitCount.containsKey(Suit)) {
                List<WXLSPockerCard> list = new ArrayList<WXLSPockerCard>();
                list.add(WXLSPockerCard);
                SuitCount.put(Suit, list);
            } else {
                SuitCount.get(Suit).add(WXLSPockerCard);
            }
        }
        return SuitCount;
    }
}
