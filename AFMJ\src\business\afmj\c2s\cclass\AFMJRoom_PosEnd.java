package business.afmj.c2s.cclass;

import jsproto.c2s.cclass.mj.BaseMJRoom_PosEnd;

public class AFMJRoom_PosEnd extends BaseMJRoom_PosEnd {

    // 单局结算的模块分数：胡牌分、上精分、下精分、飘精、杠分
    private int huPaiPoint = 0; // 胡牌分
    private int shangJingPoint = 0; // 上精分
    private int xiaJingPoint = 0; // 下精分
    private int boJingPoint = 0; // 博精倍数
    private int gangPoint = 0; // 杠分

    public void setHuPaiPoint(int huPaiPoint) {
        this.huPaiPoint = huPaiPoint;
    }

    public void setShangJingPoint(int shangJingPoint) {
        this.shangJingPoint = shangJingPoint;
    }

    public void setXiaJingPoint(int xiaJingPoint) {
        this.xiaJingPoint = xiaJingPoint;
    }

    public void setBoJingPoint(int boJingPoint) {
        this.boJingPoint = boJingPoint;
    }

    public void setGangPoint(int gangPoint) {
        this.gangPoint = gangPoint;
    }
}
