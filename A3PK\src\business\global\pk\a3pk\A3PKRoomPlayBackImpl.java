package business.global.pk.a3pk;	
	
import business.global.room.base.AbsBaseRoom;	
import business.global.room.base.RoomPlayBackImplAbstract;	
import jsproto.c2s.cclass.BaseSendMsg;	
	
public class A3PKRoomPlayBackImpl extends RoomPlayBackImplAbstract {	
	
    private final static String PosOpCard = "PosOpCard";	
    private final static String SetStart = "SetStart";	
    private final static String StartVoteDissolve = "StartVoteDissolve";	
    private final static String OpCardEX = "OpCardEX";	
	
    public A3PKRoomPlayBackImpl(AbsBaseRoom room) {	
        super(room);	
    }	
	
    @Override	
    public boolean isOpCard(BaseSendMsg msg) {	
        return msg.getOpName().indexOf(OpCardEX) > 0 || msg.getOpName().indexOf(PosOpCard) > 0 || msg.getOpName().indexOf(SetStart) > 0 || msg.getOpName().indexOf(StartVoteDissolve) > 0;	
    }	
}	
