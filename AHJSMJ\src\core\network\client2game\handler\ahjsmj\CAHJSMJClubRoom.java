package core.network.client2game.handler.ahjsmj;												
												
import java.io.IOException;												
												
import com.ddm.server.websocket.handler.requset.WebSocketRequest;												
import com.google.gson.Gson;												
												
import business.ahjsmj.c2s.iclass.CAHJSMJ_CreateRoom;												
import business.player.Player;												
import business.player.feature.PlayerClubRoom;												
import cenum.PrizeType;												
import core.network.client2game.handler.PlayerHandler;												
import core.server.ahjsmj.AHJSMJAPP;												
import jsproto.c2s.cclass.room.BaseRoomConfigure;												
												
/**												
 * 亲友圈房间												
 * 												
 * <AUTHOR>												
 *												
 */												
public class CAHJSMJClubRoom extends PlayerHandler {												
												
	@Override												
	public void handle(Player player, WebSocketRequest request, String message)												
			throws IOException {												
												
		final CAHJSMJ_CreateRoom clientPack = new Gson().fromJson(message,												
				CAHJSMJ_CreateRoom.class);														
												
		// 公共房间配置												
		BaseRoomConfigure<CAHJSMJ_CreateRoom> configure = new BaseRoomConfigure<CAHJSMJ_CreateRoom>(												
				PrizeType.RoomCard,												
				AHJSMJAPP.GameType(),												
				clientPack.clone());												
		player.getFeature(PlayerClubRoom.class).createNoneClubRoom(request,configure);												
	}												
}												
