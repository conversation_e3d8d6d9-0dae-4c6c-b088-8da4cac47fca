package business.ahmj.c2s.iclass;	
import jsproto.c2s.cclass.BaseSendMsg;	
	
/**	
 * 莆田麻将	
 * 接收客户端数据	
 * 创建房间	
 * <AUTHOR>	
 *	
 */	
@SuppressWarnings("serial")	
public class SAHMJ_PiaoFen extends BaseSendMsg{	
	
	public long roomID;  	
	public int  piaoFen;  // -1 没有操作  0不飘分 1飘分	
	public int  shangHuo;  // -1 没有操作  0不上火 1上火	
	public int  pos;	
		
    public static SAHMJ_PiaoFen make(long roomID,int pos,  int piaoFen,int  shangHuo) {	
		SAHMJ_PiaoFen ret = new SAHMJ_PiaoFen();	
    	ret.roomID = roomID;	
    	ret.piaoFen = piaoFen ;	
    	ret.shangHuo = shangHuo;	
    	ret.pos = pos;	
        return ret;	
    }	
}	
