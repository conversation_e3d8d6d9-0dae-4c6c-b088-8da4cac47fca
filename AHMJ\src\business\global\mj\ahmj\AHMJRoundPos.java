package business.global.mj.ahmj;

import business.global.mj.AbsMJRoundPos;
import business.global.mj.AbsMJSetRound;
import business.global.mj.MJCard;
import business.global.mj.set.MJOpCard;
import cenum.mj.MJCEnum;
import cenum.mj.MJOpCardError;
import cenum.mj.OpType;
import cenum.mj.TryEndRoundEnum;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import core.db.persistence.BaseDao;

/**
 * 靖州麻将
 * 一个round回合中，可能同时等待多个pos进行操作，eg:抢杠胡
 *
 * <AUTHOR>
 */
public class AHMJRoundPos extends AbsMJRoundPos {
	AHMJRoomSet bSet = null;
	AHMJSetPos bSetPos = null;

	public AHMJRoundPos(AbsMJSetRound round, int opPos) {
		super(round, opPos);
		this.bSet = (AHMJRoomSet) set;
		this.bSetPos = (AHMJSetPos) this.pos;
	}

	@Override public void clear() {
		super.clear();
		bSet = null;
		bSetPos = null;

	}

	/**
	 * 记录当前回合操作的牌
	 *
	 * @param opCard
	 */
	public void setOpCard(int opCard) {
		AHMJRoomSet set = (AHMJRoomSet) getSet();
		bSetPos.getPosOpRecord().setOpCardType(opCard / 100);
		super.setOpCard(opCard);
	}

	/**
	 * 打牌
	 *
	 * @param request 连接请求
	 * @param opType  动作类型
	 * @param cardID  牌值
	 * @return
	 */
	public int opOutCard(WebSocketRequest request, OpType opType, int cardID) {
		// 操作错误
		if (errorOpType(request, opType) <= 0) {
			return MJOpCardError.ERROR_OP_TYPE.value();
		}
		// 检查牌是否存在
		MJCard card = getCardByID(cardID);
		if (null == card) {
			request.error(ErrorCode.NotAllow, "1not find cardID:" + cardID);
			return MJOpCardError.CHECK_OP_TYPE_ERROR.value();
		}
		// 不能打金(手中全是王牌不能放弃胡牌；（即选则不胡，需要弹窗提示只能选择胡牌）)
		if(this.set.getmJinCardInfo().getJinMap().containsKey(cardID/100)) {
			request.error(ErrorCode.NotAllow, "jin find cardID:" + cardID);
			return -1;
		}
		if (this.bSetPos.isTing()) {
			if (null != this.bSetPos.getHandCard()) {
				if (this.bSetPos.getHandCard().getCardID() != cardID) {
					request.error(ErrorCode.NotAllow, "4not find cardID:" + cardID);
					return -1;
				}
			} else {
				request.error(ErrorCode.NotAllow, "5not find cardID:" + cardID);
				return -1;
			}
		}
		// 是不是自己身上的牌
		if (!outCard(card)) {
			request.error(ErrorCode.NotAllow, "2not find cardID:" + cardID);
			return MJOpCardError.CHECK_OP_TYPE_ERROR.value();
		}
		// =====================================
		// 记录当前回合操作的牌
		this.setOpCard(cardID);
		// 执行动作
		return this.exeCardAction(opType);
	}


	@Override public int op(WebSocketRequest request, OpType opType, MJOpCard mOpCard) {
		int opCardRet = -1;
		if (this.getOpType() != null) {
			request.error(ErrorCode.NotAllow, "opPos has opered");
			return MJOpCardError.REPEAT_EXECUTE.value();
		}
		switch (opType) {
		case Pass:
		case SQPass:
			opCardRet = opPass(request, opType);
			break;
		case Out:
			opCardRet = opOutCard(request, opType, mOpCard.getOpCard());
			break;
		case Chi:
			opCardRet = opChi(request, opType, mOpCard.getOpCard());
			break;
		case Peng:
			opCardRet = opPeng(request, opType);
			break;
		case AnGang:
			opCardRet = opAnGang(request, opType, mOpCard.getOpCard());
			break;
		case JieGang:
			opCardRet = opJieGang(request, opType);
			break;
		case Gang:
			opCardRet = opGang(request, opType, mOpCard.getOpCard());
			break;
		case YaoGangHu:
		case QiangGangHu:
		case JiePao:
		case Hu:  //自摸
			opCardRet = opHuType(request, opType);
			break;
		default:
			break;
		}
		request.response();
		return opCardRet;
	}

	/**
	 * 过
	 *
	 * @param request 连接请求
	 * @param opType  动作类型
	 * @return
	 */
	public int opPass(WebSocketRequest request, OpType opType) {
		// 操作错误
		if (errorOpType(request, opType) <= 0) {
			return MJOpCardError.ERROR_OP_TYPE.value();
		}
		// 执行操作
		return opErrorReturn(request, opType, this.opReturn(opType, 0, TryEndRoundEnum.ALL_AT_ONCE));
	}

	/**
	 * 手上有门牌的操作。
	 *
	 * @param opType 操作类型
	 * @param cardID 牌值
	 */
	@Override
	protected int getCardOpPos(OpType opType, int cardID) {
		if (OpType.Pass.equals(opType)) {

		} else if (OpType.Hu.equals(opType)||OpType.YaoGangHu.equals(opType)) {
			// 操作动作
			if (!doOpType(cardID, opType)) {
				return -1;
			}
		} else {
			return -1;
		}
		// 记录操作的动作，并且尝试结束本回合
		this.opTypeTryEndRound(this.opPos, opType, MJCEnum.OpHuType(opType), TryEndRoundEnum.ALL_WAIT);
		return this.opPos;
	}
}						
