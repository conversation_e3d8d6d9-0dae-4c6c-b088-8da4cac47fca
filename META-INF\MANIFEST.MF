Manifest-Version: 1.0
Ant-Version: Apache Ant 1.10.15
Created-By: 1.8.0_452-b09 (Amazon.com Inc.)
Main-Class: core.server.GameServer
Class-Path: lib/org-netbeans-modules-java-j2seproject-copylibstask.jar
  lib/gson-2.8.6-javadoc.jar lib/gson-2.8.6-sources.jar lib/gson-2.8.6
 .jar lib/mysql-connector-java-5.1.23-bin.jar lib/ant-contrib-1.0b3.ja
 r lib/commons-codec-1.9-javadoc.jar lib/commons-codec-1.9-sources.jar
  lib/commons-codec-1.9.jar lib/commons-collections-3.2.2.jar lib/comm
 ons-logging-1.1.1.jar lib/httpasyncclient-4.0.2.jar lib/httpclient-4.
 4.1.jar lib/httpcore-4.4.1.jar lib/httpcore-nio-4.4.1.jar lib/apns-0.
 2.3.jar lib/asm-6.2.jar lib/bonecp-0.7.1.RELEASE.jar lib/bsh-2.0b5.ja
 r lib/c3p0-0.9.5-pre7-javadoc.jar lib/c3p0-0.9.5-pre7-sources.jar lib
 /c3p0-0.9.5-pre7.jar lib/mchange-commons-java-*******-javadoc.jar lib
 /mchange-commons-java-*******-sources.jar lib/mchange-commons-java-0.
 2.6.5.jar lib/cglib-3.2.7.jar lib/commons-beanutils-1.9.3-javadoc.jar
  lib/commons-beanutils-1.9.3-sources.jar lib/commons-beanutils-1.9.3.
 jar lib/commons-cli-1.2.jar lib/commons-lang3-3.7-javadoc.jar lib/com
 mons-lang3-3.7-sources.jar lib/commons-lang3-3.7.jar lib/disruptor-3.
 4.2.jar lib/ehcache-3.8.1.jar lib/fastjson-1.2.31.jar lib/guava-10.0.
 1.jar lib/guava-17.0-sources.jar lib/guava-17.0.jar lib/ip2region-2.6
 .4.jar lib/jackson-annotations-2.1.4.jar lib/jackson-core-2.1.4.jar l
 ib/jackson-databind-2.1.4.jar lib/javax.servlet-3.0.0.v201112011016.j
 ar lib/javolution-core-java-6.0.0-javadoc.jar lib/javolution-core-jav
 a-6.0.0-sources.jar lib/javolution-core-java-6.0.0.jar lib/jsch-0.1.5
 1.jar lib/commons-dbutils-1.7.jar lib/druid-1.1.10.jar lib/jfinal-3.2
 .jar lib/jfinal-weixin-1.9-sources.jar lib/jfinal-weixin-1.9.jar lib/
 org.eclipse.jgit-3.3.2.************-r-javadoc.jar lib/org.eclipse.jgi
 t-3.3.2.************-r-sources.jar lib/org.eclipse.jgit-3.3.2.2014041
 71909-r.jar lib/joda-time-2.9.9.jar lib/jsc.jar lib/junit-4.10-javado
 c.jar lib/junit-4.10-sources.jar lib/junit-4.10.jar lib/javassist-3.2
 5.0-GA.jar lib/junit-4.12.jar lib/mockito-all-2.0.2-beta.jar lib/powe
 rmock-api-mockito-1.6.3.jar lib/powermock-api-support-1.6.3.jar lib/p
 owermock-core-1.6.3.jar lib/powermock-module-junit4-1.6.1.jar lib/pow
 ermock-module-junit4-common-1.6.1.jar lib/powermock-reflect-1.6.3.jar
  lib/kernel.jar lib/kryo-4.0.2.jar lib/kryo-serializers-0.45.jar lib/
 minlog-1.3.0.jar lib/reflectasm-1.07.jar lib/log4j-1.2.17.jar lib/log
 4j-1.2.17-javadoc.jar lib/log4j-1.2.17-sources.jar lib/log4j-1.2.17.j
 ar lib/logback-classic-1.1.3.jar lib/logback-core-1.1.3.jar lib/lombo
 k-1.18.8.jar lib/mina-core-2.0.9.jar lib/bson-3.4.2.jar lib/mongodb-d
 river-3.4.2.jar lib/mongodb-driver-core-3.4.2.jar lib/atmosphere-runt
 ime-2.6.4.jar lib/geronimo-servlet_3.0_spec-1.0.jar lib/nettosphere-3
 .2.2.jar lib/netty-all-4.1.51.Final.jar lib/netty-all-4.1.6.Final.jar
  lib/protobuf-java-2.5.0-javadoc.jar lib/protobuf-java-2.5.0-sources.
 jar lib/protobuf-java-2.5.0.jar lib/quartz-2.3.0.jar lib/commons-pool
 -1.5.4.jar lib/commons-pool2-2.4.2.jar lib/jedis-2.9.0.jar lib/common
 s-validator-1.6.jar lib/rocketmq-client-4.7.1.jar lib/rocketmq-common
 -4.7.1.jar lib/rocketmq-logging-4.7.1.jar lib/rocketmq-remoting-4.7.1
 .jar lib/libfb303.jar lib/libthrift.jar lib/ApiGateway-HMAC_2.0.0.jar
  lib/slf4j-api-1.7.12.jar

