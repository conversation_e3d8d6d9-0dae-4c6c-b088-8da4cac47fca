package business.afmj.c2s.iclass;
import jsproto.c2s.cclass.*;


@SuppressWarnings("serial")
public class SAFMJ_SetEnd<T> extends BaseSendMsg {
    
    public long roomID;
    public T setEnd;


    public static <T> SAFMJ_SetEnd<T> make(long roomID, T setEnd) {
    	SAFMJ_SetEnd<T> ret = new SAFMJ_SetEnd<T>();
        ret.roomID = roomID;
        ret.setEnd = setEnd;

        return ret;
    

    }
}