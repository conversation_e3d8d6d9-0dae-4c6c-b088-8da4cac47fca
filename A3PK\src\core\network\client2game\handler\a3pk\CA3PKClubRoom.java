package core.network.client2game.handler.a3pk;	
	
import java.io.IOException;	
	
import business.global.pk.a3pk.A3PKRoomEnum;	
import com.ddm.server.websocket.def.ErrorCode;	
import com.ddm.server.websocket.handler.requset.WebSocketRequest;	
import com.google.gson.Gson;	
	
import business.a3pk.c2s.iclass.CA3PK_CreateRoom;	
import business.player.Player;	
import business.player.feature.PlayerClubRoom;	
import cenum.PrizeType;	
import core.network.client2game.handler.PlayerHandler;	
import core.server.a3pk.A3PKAPP;	
import jsproto.c2s.cclass.room.BaseRoomConfigure;	
	
/**	
 * 亲友圈房间	
 * 	
 * <AUTHOR>	
 *	
 */	
public class CA3PKClubRoom extends PlayerHandler {	
	
	@Override	
	public void handle(Player player, WebSocketRequest request, String message)	
			throws IOException {	
	
		final CA3PK_CreateRoom clientPack = new Gson().fromJson(message,	
				CA3PK_CreateRoom.class);	
		// 公共房间配置	
		BaseRoomConfigure<CA3PK_CreateRoom> configure = new BaseRoomConfigure<CA3PK_CreateRoom>(	
				PrizeType.RoomCard,	
				A3PKAPP.GameType(),	
				clientPack.clone());	
		player.getFeature(PlayerClubRoom.class).createNoneClubRoom(request,configure);	
	}	
}	
