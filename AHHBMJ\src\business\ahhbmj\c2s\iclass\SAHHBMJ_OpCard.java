package business.ahhbmj.c2s.iclass;						
						
import business.global.mj.set.MJOpCard;						
						
						
import java.util.List;						
						
						
public class SAHHBMJ_OpCard extends MJOpCard {						
						
    private List<Integer> cardList;						
						
    public SAHHBMJ_OpCard(int opCard, List<Integer> cardList) {						
        super(opCard);						
        this.cardList = cardList;						
    }						
						
    public List<Integer> getCardList() {						
        return cardList;						
    }						
						
    public void setCardList(List<Integer> cardList) {						
        this.cardList = cardList;						
    }						
						
    public static MJOpCard OpCard(int opCard, List<Integer> cardList) {						
        return new SAHHBMJ_OpCard(opCard, cardList);						
    }						
}											
