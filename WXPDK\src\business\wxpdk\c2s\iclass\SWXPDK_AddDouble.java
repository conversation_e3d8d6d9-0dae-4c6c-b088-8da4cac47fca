package business.wxpdk.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;

/**
 * 接收客户端数据
 * 加倍
 * <AUTHOR>
 *
 */

@SuppressWarnings("serial")
public class SWXPDK_AddDouble extends BaseSendMsg {

	public long roomID;
    public int pos;  //位置
    public int addDouble;


    public static SWXPDK_AddDouble make(long roomID,int pos,int addDouble) {
    	SWXPDK_AddDouble ret = new SWXPDK_AddDouble();
        ret.roomID = roomID;
        ret.pos = pos;
        ret.addDouble = addDouble;
        return ret;
    }
}
