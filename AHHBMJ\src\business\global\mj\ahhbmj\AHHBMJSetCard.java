package business.global.mj.ahhbmj;


import business.global.mj.AbsMJSetCard;
import business.global.mj.AbsMJSetRoom;
import business.global.mj.MJCard;
import business.global.mj.RandomCard;
import cenum.mj.MJCardCfg;
import lombok.Data;

import java.util.*;

/**
 * 淮北麻将 每一局麻将底牌信息 抓牌人是逆时针出手 牌是顺时针被抓
 *
 * <AUTHOR>
 */
@Data
public class AHHBMJSetCard extends AbsMJSetCard {
    private AHHBMJRoomSet set;
    private int liuPai = 14;

    public AHHBMJSetCard(AHHBMJRoomSet set) {
        this.set = set;
        this.room = set.getRoom();
        this.randomCard();
        if (getSet().getRoom().RoomCfg(AHHBMJRoomEnum.KeXuanWanFa.BuLiuPai)) {
            liuPai = 0;
        }
    }


    /**
     * 洗牌
     */
    @Override
    @SuppressWarnings("rawtypes")
    public void randomCard() {
        List<MJCardCfg> mCfgs = new ArrayList<MJCardCfg>();
        mCfgs.add(MJCardCfg.TIAO);
        mCfgs.add(MJCardCfg.TONG);
        if (!getSet().isQU_WAN()) {
            mCfgs.add(MJCardCfg.WANG);
        }
        if (!getSet().isQU_ZI()) {
            mCfgs.add(MJCardCfg.ZHONG);
            mCfgs.add(MJCardCfg.FA);
            mCfgs.add(MJCardCfg.BAI);
            if (!getSet().isQU_FENG()) {
                mCfgs.add(MJCardCfg.FENG);
            }
        }
        if (getSet().getRoom().RoomCfg(AHHBMJRoomEnum.KeXuanWanFa.Dai8Hua)) {
            //花牌：8张；——2022.06.14增加
            mCfgs.add(MJCardCfg.HUA);
        }
        baseRandomCard(this.set, mCfgs);
        this.initDPos(this.set);
    }

    public void baseRandomCard(AbsMJSetRoom set, List<MJCardCfg> mCfgs) {
        this.randomCard = new RandomCard(mCfgs, this.room.getPlayerNum(), 0);
    }

    /**
     * 摸牌
     *
     * @param isNormalMo T:正常摸牌，F:杠牌摸牌
     *                   * @param cardType 牌类型（默认：0）
     * @return
     */
    @Override
    public MJCard pop(boolean isNormalMo, int cardType) {
        int sizeCard = this.randomCard.getSize();

        if (sizeCard <= liuPai) {
            return null;
        }

        MJCard ret = this.getGodCard(cardType);
        ret = null != ret ? ret : this.randomCard.removeLeftCards(0);
        if (isNormalMo) {
            this.randomCard.setNormalMoCnt(this.randomCard.getNormalMoCnt() + 1);
        } else {
            this.randomCard.setGangMoCnt(this.randomCard.getGangMoCnt() + 1);
        }

        return ret;
    }

    @Override
    protected boolean firstRandomDPos() {
        return false;
    }

    /**
     * 剩余牌堆有没有这张牌
     *
     * @param cardID
     * @return
     */
    public boolean getLeftCard(int cardID) {
        return randomCard.leftCards.stream().anyMatch(n -> n.type == cardID / 100);
    }

    public boolean isCardNull() {
        return liuPai == randomCard.getSize();
    }

}							
