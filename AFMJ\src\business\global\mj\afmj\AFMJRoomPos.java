package business.global.mj.afmj;

import business.global.room.base.AbsBaseRoom;
import business.global.room.mj.MJRoomPos;

/**
 * 房间内每个位置信息
 *
 * <AUTHOR>
 */
public class AFMJRoomPos extends MJRoomPos {

    private int tuoGuanSetCount = 0; // 连续托管局数

    public int getTuoGuanSetCount() {
        return tuoGuanSetCount;
    }

    public void setTuoGuanSetCount(int tuoGuanSetCount) {
        this.tuoGuanSetCount = tuoGuanSetCount;
    }

    public AFMJRoomPos(int posID, AbsBaseRoom room) {
        super(posID, room);
    }


    /**
     * 增加托管局数
     */
    public void addTuoGuanSetCount(){
        tuoGuanSetCount += 1;
    }

    /**
     * 连续托管局数清零
     */
    public void clearTuoGuanSetCount(){
        tuoGuanSetCount = 0;
    }


    @Override
    public void setGameReady(boolean isGameReady) {
        super.setGameReady(isGameReady);
    }

}
