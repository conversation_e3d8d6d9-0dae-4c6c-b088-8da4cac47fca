package business.ahmj.c2s.cclass;

import business.global.mj.ahmj.AHMJRoomEnum.AHMJOpPoint;
import com.ddm.server.common.CommLogD;
import com.ddm.server.common.utils.Lists;
import com.ddm.server.common.utils.Maps;

import java.io.*;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class AHMJPointItem implements Cloneable, Serializable {
    /**
     * 分数
     */
    private int point = 0;
    /**
     * 牌值
     */
    private int cardID = 0;
    /**
     * 牌型
     */
    private List<AHMJOpPoint> AHMJOpPoints = Lists.newArrayList();
    /**
     * 牌型
     */
    private Map<AHMJOpPoint, Integer> map = Maps.newMap();

    public AHMJPointItem(int point, List<AHMJOpPoint> AHMJOpPoints, Map<AHMJOpPoint, Integer> map) {
        this.point = point;
        this.AHMJOpPoints = AHMJOpPoints;
        this.map = map;
    }

    public AHMJPointItem() {
    }

    public int getPoint() {
        return point;
    }

    public int getAllPoint() {
        return this.point ;
    }

    public List<AHMJOpPoint> getAHMJOpPoints() {
        return AHMJOpPoints;
    }

    public Map<AHMJOpPoint, Integer> getMap() {
        return map;
    }

    public void setMap(Map<AHMJOpPoint, Integer> map) {
        this.map = map;
    }

    public void addAHMJOpPoint(AHMJOpPoint oEnum, int value) {
        this.map.put(oEnum, value);
        this.point += value;
        this.AHMJOpPoints.add(oEnum);
    }

    public void removeAHMJOpPoint(AHMJOpPoint oEnum, int value) {
        this.map.remove(oEnum);
        this.point -= value;
        this.AHMJOpPoints.remove(oEnum);
    }


    /**
     * 实现对象间的深度克隆【从外形到内在细胞，完完全全深度copy】
     *
     * @return
     */
    public AHMJPointItem deepClone() {
        // Anything 都是可以用字节流进行表示，记住是任何！
        AHMJPointItem cookBook = null;
        try {

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(baos);
            // 将当前的对象写入baos【输出流 -- 字节数组】里
            oos.writeObject(this);

            // 从输出字节数组缓存区中拿到字节流
            byte[] bytes = baos.toByteArray();

            // 创建一个输入字节数组缓冲区
            ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
            // 创建一个对象输入流
            ObjectInputStream ois = new ObjectInputStream(bais);
            // 下面将反序列化字节流 == 重新开辟一块空间存放反序列化后的对象
            cookBook = (AHMJPointItem) ois.readObject();

        } catch (Exception e) {
            CommLogD.error(e.getClass() + ":" + e.getMessage());
        }
        return cookBook;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AHMJPointItem item = (AHMJPointItem) o;
        return point == item.point &&
                Objects.equals(AHMJOpPoints, item.AHMJOpPoints) &&
                Objects.equals(map, item.map);
    }

    public void setCardID(int cardID) {
        this.cardID = cardID;
    }

    public int getCardID() {
        return cardID;
    }

    @Override
    public int hashCode() {
        return Objects.hash(point, AHMJOpPoints, map);
    }


    @Override
    public String toString() {
        return "AHMJPointItem{" +
                "point=" + point +
                ", AHMJOpPoints=" + AHMJOpPoints +
                ", map=" + map +
                '}';
    }
}
