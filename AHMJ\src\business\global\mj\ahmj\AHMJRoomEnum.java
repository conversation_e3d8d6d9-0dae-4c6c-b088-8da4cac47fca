package business.global.mj.ahmj;

import java.util.ArrayList;

/**
 * 靖州麻将 枚举
 *
 * <AUTHOR>
 */

public class AHMJRoomEnum {
	public enum AHMJCfg {
		Not(-1),
		//代王硬
		DaiWangYing(0),
		//杠上炮算单大胡
		GangShangPaoDaHu(1),
		//三王或以上代王硬不可接普通炮
		NotJiePaoSanWang(2),
		//比赛分不能低于0
		BiSaiFenBuDiYuLing(3),
		//带多赢少
        TakeLose(4);
        private int value;
		AHMJCfg(int value) {this.value = value;}
		public int value(){return this.value;}
		public static int valueOf(int value) {
			for (AHMJCfg huPai : AHMJCfg.values()) {
				if (huPai.ordinal() == value) {
					return huPai.value;
				}
			}
			return AHMJCfg.Not.value;
		}
	}

	public enum NiaoShu {
		NAIO1(1), NAIO2(2), NAIO3(3), NAIO4(4),
		;
		public int value;

		NiaoShu(int value) {
			this.value = value;
		}

		public static int getNiaoShu(int odinal) {
			for (NiaoShu niaoShu : values()) {
				if (niaoShu.ordinal() == odinal) {
					return niaoShu.value;
				}
			}
			return NAIO1.value;
		}
	}

	public enum AHMJGameRoomConfigEnum {
		/**
		 * 房间内切换人数
		 */
		FangJianQieHuanRenShu,
		/**
		 * 自动准备
		 */
		ZiDongZhunBei,
		/**
		 * 小局托管解散
		 */
		SetAutoJieSan,
		/**
		 * 解散次数不超过5次
		 */
		JieSanCiShu5,
		/**
		 * 解散次数不超过3次
		 */
		JieSanCiShu3,
		/**
		 * 托管3小局解散
		 */
		TuoGuan3XiaoJuJieSan,
		;

		/**
		 * 获取解散次数
		 *
		 * @param jieSanList 洁圣列表
		 * @return int
		 */
		public static int getJieSanCiShu(ArrayList<Integer> jieSanList){
			if(jieSanList.contains(AHMJGameRoomConfigEnum.JieSanCiShu3.ordinal())){
				return 3;
			}else if(jieSanList.contains(AHMJGameRoomConfigEnum.JieSanCiShu5.ordinal())){
				return 5;
			}
			return 0;
		}
	}

	/**
	 * 玩法
	 *
	 * <AUTHOR>
	 */
	public enum AHMJDaNiao {
		//没打鸟
		NOT_OP(-1,-1),
		// 不打鸟
		NOT(0,0),
		// 鸟5
		FiVe(5,1),
		;

		int value;
		int index;

		AHMJDaNiao(int value, int index) {
			this.value = value;
			this.index = index;
		}

		public int getValue() {
			return value;
		}

		public static AHMJDaNiao valueOf(int value) {
			for (AHMJDaNiao flow : AHMJDaNiao.values()) {
				if (flow.index == value) {
					return flow;
				}
			}
			return AHMJDaNiao.NOT_OP;
		}

	}


	/**
	 * 动作分数
	 *
	 * <AUTHOR>
	 */
	public enum AHMJOpPoint {
		Not(0),
		//小七对
		QDHu(4),
		//平胡
		PingHu(2),
		//抢杠胡
		QiangGangHu(4),
		//杠上开花
		GSKH(4),
		//硬庄
		YZ(4),
		//杠上炮
		GSP(4),
		//2王
		King_2(0),
		//3王
		King_3(4),
		//4王
		King_4(8),
		//6王
		King_6(8),
		//7王
		King_7(8),
		//烂胡
		LanHu(2),
		//清一色
		QYS(4),
		//中鸟分
		ZhongNiao(0),
		//打鸟分
		DaNiao(0),
		//牌型分
		HuPoint(0),
		;
		private int value;

		private AHMJOpPoint(int value) {
			this.value = value;
		}

		public int value() {
			return this.value;
		}
	}


	/**
	 * <AUTHOR>
	 */
	public enum AHMJEndType {
		/**
		 * 不特殊操作
		 */
		NOT,
		/**
		 * 加
		 */
		PLUS,
		/**
		 * 乘
		 */
		MULTIPLY,
		;
	}


	/**
	 * 抓鸟模式:
	 */
	public enum ZhuaNiaoMoShi {
		ZHONG159, BUZHUA,
	}

	/**
	 * 王数:
	 */
	public enum WangShu {
		Seven, Four,
	}

	/**
	 * 打鸟:
	 */
	public enum DaNaio {
		Not, Five,
	}
	/**
	 * 杠牌，3张，2张
	 *
	 * <AUTHOR>
	 */
	public enum AHMJMJGangPai {
		Three(3), // 3张
		Two(2), // 2张

		;

		private int value;

		private AHMJMJGangPai(int value) {
			this.value = value;
		}

		public int value() {
			return this.value;
		}

		public static AHMJMJGangPai valueOf(int value) {
			for (AHMJMJGangPai gangPai : AHMJMJGangPai.values()) {
				if (gangPai.ordinal() == value) {
					return gangPai;
				}
			}
			return AHMJMJGangPai.Three;
		}
	}
}						
