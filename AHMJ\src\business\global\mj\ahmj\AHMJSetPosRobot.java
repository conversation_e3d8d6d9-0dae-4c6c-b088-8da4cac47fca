package business.global.mj.ahmj;

import java.util.Collections;
import java.util.List;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCard;
import business.global.mj.robot.MJSetPosRobot;

/**
 * 机器人位置操作
 *
 * <AUTHOR>
 */
public class AHMJSetPosRobot extends MJSetPosRobot {
    private AHMJSetPos pxPos;

    public AHMJSetPosRobot(AbsMJSetPos mSetPos) {
        super(mSetPos);
        this.pxPos = (AHMJSetPos) mSetPos;
    }

    /**
     * 只能吃打的机器人
     * @return
     */
    public int getAutoCard2(){
        List<MJCard> allCards = mSetPos.allCards();
        Collections.reverse(allCards);
        for (MJCard mCard : allCards) {
            if (mSetPos.getSet().getmJinCardInfo().checkJinExist(mCard.getType())) {
                continue;
            }
            return mCard.cardID;
        }
        return 0;
    }

}
