package business.wxpdk.c2s.iclass;
import com.google.gson.Gson;
import jsproto.c2s.cclass.BaseSendMsg;

/**
 * 莆田麻将
 * 接收客户端数据
 * 创建房间
 * <AUTHOR>
 *
 */
@SuppressWarnings("serial")
public class SWXPDK_PiaoHua extends BaseSendMsg{

	public long roomID;  
	public int piaoHua;
	public int  pos;
	
    public static SWXPDK_PiaoHua make(long roomID, int pos, int piaoHua) {
    	SWXPDK_PiaoHua ret = new SWXPDK_PiaoHua();
    	ret.roomID = roomID;
    	ret.piaoHua = piaoHua ;
    	ret.pos = pos;
        return ret;
    }

    public static void main(String args[]){
    	System.out.println(new Gson().toJson(new SWXPDK_PiaoHua()));
	}
}
