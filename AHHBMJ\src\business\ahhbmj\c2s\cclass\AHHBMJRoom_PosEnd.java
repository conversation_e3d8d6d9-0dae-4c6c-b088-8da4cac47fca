package business.ahhbmj.c2s.cclass;

import jsproto.c2s.cclass.mj.BaseMJRoom_PosEnd;
import lombok.Data;


@Data
public class AHHBMJRoom_PosEnd extends BaseMJRoom_PosEnd {
    public boolean isTing;//报听
    private int zuoFen = -1; //-1:不显示 0：“不下坐”、1：“1坐”、2：“2坐
    private int laFen = -1;//-1:不显示 0：“不下拉”、1：“1拉”、2：“2拉
    private int paoFen = 1;//-1:不显示 0：“不下跑”、1：“1跑”、2：“2跑
    private int zuoFenPoint ; //坐分
    private int laFenPoint ;//拉分
    private int paoFenPoint ;//跑分
    private int gangPoint ;//杠分
    private int jiaPoint ;//加分
    private int huCardPoint ;//胡牌分


}							
