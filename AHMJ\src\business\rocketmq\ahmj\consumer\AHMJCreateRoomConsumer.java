package business.rocketmq.ahmj.consumer;

import BaseCommon.CommLog;
import business.ahmj.c2s.iclass.CAHMJ_CreateRoom;
import business.global.sharegm.ShareNodeServerMgr;
import business.rocketmq.bo.MqAbsRequestBo;
import business.rocketmq.constant.MqTopic;
import business.rocketmq.consumer.BaseCreateRoomConsumer;
import cenum.PrizeType;
import com.ddm.server.annotation.Consumer;
import com.ddm.server.common.rocketmq.MqConsumerHandler;
import com.google.gson.Gson;
import core.server.ahmj.AHMJAPP;
import jsproto.c2s.cclass.room.BaseRoomConfigure;

/**
 * <AUTHOR> xush<PERSON><PERSON>
 * create at:  2020-08-19  11:17
 * @description: 红中麻将创建房间
 */
@Consumer(topic = MqTopic.BASE_CREATE_ROOM, id = AHMJAPP.gameTypeId)
public class AHMJCreateRoomConsumer extends BaseCreateRoomConsumer implements MqConsumerHandler {


    @Override
    public void action(Object body) throws ClassNotFoundException {
        MqAbsRequestBo mqAbsRequestBo = (MqAbsRequestBo) body;
        //判断游戏和请求创建节点一致
        if (mqAbsRequestBo.getGameTypeId() == AHMJAPP.GameType().getId() && ShareNodeServerMgr.getInstance().checkCurrentNode(mqAbsRequestBo.getShareNode().getIp(), mqAbsRequestBo.getShareNode().getPort())) {
//            CommLog.info("创建房间[{}]", mqAbsRequestBo.getGameTypeName());
            final CAHMJ_CreateRoom clientPack = new Gson().fromJson(mqAbsRequestBo.getBody(),
                    CAHMJ_CreateRoom.class);
            // 公共房间配置
            BaseRoomConfigure<CAHMJ_CreateRoom> configure = new BaseRoomConfigure<>(
                    PrizeType.RoomCard,
                    AHMJAPP.GameType(),
                    clientPack.clone());
            super.action(body, AHMJAPP.GameType().getId(), configure);
        }
    }
}
