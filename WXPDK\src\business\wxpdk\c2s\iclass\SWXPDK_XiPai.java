package business.wxpdk.c2s.iclass;

import cenum.ClassType;
import jsproto.c2s.cclass.BaseSendMsg;


public class SWXPDK_XiPai extends BaseSendMsg {
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	public long roomID;
    public long pid;
    public ClassType cType;
    public static SWXPDK_Xi<PERSON>ai make(long roomID, long pid,ClassType cType) {
    	SWXPDK_XiPai ret = new SWXPDK_XiPai();
        ret.roomID = roomID;
        ret.pid = pid;
        ret.cType = cType;
        return ret;
    

    }
}
