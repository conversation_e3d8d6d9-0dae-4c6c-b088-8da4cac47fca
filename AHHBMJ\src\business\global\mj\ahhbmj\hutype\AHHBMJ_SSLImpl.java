package business.global.mj.ahhbmj.hutype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCardInit;
import business.global.mj.hu.abs.AbsSSBK;
import com.ddm.server.common.utils.CommMath;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 十三不靠：由147、258、369三组花色不同的序数牌和东南西北中发白7张牌中任意不同的5张牌组成的14张的牌型；
 */
public class AHHBMJ_SSLImpl extends AbsSSBK {


    @Override
    public boolean checkHuCard(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
        if (null == mCardInit) {
            return false;
        }
        if (mSetPos.sizePublicCardList() > 0) {
            return false;
        }
        return checkSSBK(mCardInit, mSetPos);

    }

    /**
     * 检查用户是否胡
     *
     * @param allCards
     * @param totalJin
     * @return
     */
    @Override
    protected boolean checkSSBK(List<Integer> allCards, int totalJin) {
        Map<Boolean, List<Integer>> partitioned = allCards
                .stream().collect(Collectors.partitioningBy(e -> e >= 40));
        if (null == partitioned) {
            return false;
        }
        List<Integer> fengList = partitioned.get(true);
        List<Integer> cardList = partitioned.get(false);
        return checkFeng(fengList, totalJin) && checkCard(cardList, totalJin);
    }

    private boolean checkCard(List<Integer> cardList, int totalJin) {
        Map<Integer, Long> result = cardList.stream().collect(Collectors.groupingBy(k -> k % 10, Collectors.counting()));
        long max = result.values().stream().mapToLong((x) -> x).summaryStatistics().getMax();
        if (max != 1) {
            return false;
        }
        CommMath.getSort(cardList, false);
        for (int i = 0, sizeI = cardList.size(); i < sizeI; i++) {
            for (int j = i + 1, sizeJ = cardList.size(); j < sizeJ; j++) {
                if (cardList.get(i) / 10 != cardList.get(j) / 10) {
                    continue;
                }
                if (cardList.get(i) - cardList.get(j) != 3 && cardList.get(i) - cardList.get(j) != 6) {
                    return false;
                }
                break;
            }
        }

        return true;
    }


    /**
     * @param cardList
     * @param totalJin,
     * @return
     */
    @Override
    public boolean checkFeng(List<Integer> cardList, int totalJin) {

        // 分组，计数	
        Map<Integer, Long> result = cardList.stream()
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        long max = result.values().stream().mapToLong((x) -> x).summaryStatistics().getMax();
        return max == 1 && cardList.size() + totalJin >= 5;
    }
}	
