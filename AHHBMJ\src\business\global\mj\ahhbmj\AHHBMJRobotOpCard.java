package business.global.mj.ahhbmj;

import business.global.mj.AbsMJSetPos;
import business.global.mj.AbsMJSetRound;
import business.global.mj.robot.MJRobotOpCard;
import business.global.mj.set.MJOpCard;
import cenum.mj.HuType;
import cenum.mj.MJCEnum;
import cenum.mj.MJOpCardError;
import cenum.mj.OpType;
import com.ddm.server.common.utils.CommMath;
import com.ddm.server.websocket.handler.requset.WebSocketRequestDelegate;

import java.util.List;

/**
 * 麻将机器人打牌
 *
 * <AUTHOR>
 */
public class AHHBMJRobotOpCard extends MJRobotOpCard {

    public AHHBMJRobotOpCard(AbsMJSetRound setRound) {
        super(setRound);

    }

    /**
     * 存在首牌
     *
     * @return
     */
    public int existHandCard(List<OpType> opTypes, AbsMJSetPos mSetPos) {
        OpType opType = opTypes.stream().filter(k -> !HuType.NotHu.equals(MJCEnum.OpHuType(k))).findAny()
                .orElse(opTypes.get(CommMath.randomInt(0, opTypes.size() - 1)));
        if (HuType.NotHu.equals(MJCEnum.OpHuType(opType))) {
            if (opTypes.contains(OpType.Out)) {
                // 打牌			
                return this.getSetRound().opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), OpType.Out,
                        MJOpCard.OpCard(mSetPos.getHandCard().cardID));
            } else if (opTypes.contains(OpType.Pass)) {
                // 过操作			
                return this.getSetRound()
                        .opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), OpType.Pass, MJOpCard.OpCard(0));
            }
            return MJOpCardError.ROBOT_OP_ERROR.value();
        } else {
            // 存在自摸胡牌			
            return this.getSetRound()
                    .opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), opType, MJOpCard.OpCard(0));
        }
    }

    /**
     * 不存在首牌
     *
     * @return
     */
    public int notExistHandCard(List<OpType> opTypes, AbsMJSetPos mSetPos) {
        OpType opType = opTypes.stream().filter(k -> !HuType.NotHu.equals(MJCEnum.OpHuType(k))).findAny()
                .orElse(opTypes.get(CommMath.randomInt(0, opTypes.size() - 1)));
        if (HuType.NotHu.equals(MJCEnum.OpHuType(opType))) {
            if (opTypes.contains(OpType.Piao_Fen)) {
                // 没有相应的动作直接过
                return this.getSetRound()
                        .opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), OpType.Piao_Fen, MJOpCard.OpCard(0));
            } else if (opTypes.contains(OpType.Fan)) {
                // 没有相应的动作直接过
                return this.getSetRound()
                        .opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), OpType.Fan, MJOpCard.OpCard(0));
            } else if (opTypes.contains(OpType.Pao)) {
                // 没有相应的动作直接过
                return this.getSetRound()
                        .opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), OpType.Pao, MJOpCard.OpCard(0));
            } else if (opTypes.contains(OpType.Pass)) {
                // 没有相应的动作直接过			
                return this.getSetRound()
                        .opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), OpType.Pass, MJOpCard.OpCard(0));
            } else if (opTypes.contains(OpType.Out)) {
                return this.getSetRound().opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), OpType.Out,
                        MJOpCard.OpCard(mSetPos.getHandCard().cardID));
            }
            return MJOpCardError.ROBOT_OP_ERROR.value();
        } else {
            // 点炮胡或者抢杠胡			
            return this.getSetRound()
                    .opCard(new WebSocketRequestDelegate(), mSetPos.getPosID(), opType, MJOpCard.OpCard(0));
        }
    }

}											
