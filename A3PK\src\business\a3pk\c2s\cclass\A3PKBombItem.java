package business.a3pk.c2s.cclass;	
	
import business.global.pk.a3pk.A3PKRoomEnum;	
import lombok.Builder;	
import lombok.Data;	
import lombok.ToString;	
	
import java.util.List;	
	
@Data	
@Builder	
@ToString	
public class A3PKBombItem {	
    /**	
     * 比较值	
     */	
    private int compValue;	
	
    /**	
     * 比较数量	
     */	
    private int compSize;	
	
    /**	
     * 牌列表	
     */	
    private List<Integer> cardList;	
    /**	
     * 同色奖	
     */	
    private int sameColor;	
    /**	
     * 奖励分数	
     */	
    private int prizePoint;	
	
    public void addPrizePoint(int prizePoint) {	
        this.prizePoint += prizePoint;	
    }	
	
    public int getCompValueEx() {	
        return compValue;	
    }	
}	
