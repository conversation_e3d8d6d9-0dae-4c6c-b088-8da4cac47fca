package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSCardSuitEnum;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.Iterator;	
import java.util.List;	
import java.util.Map;	
	
/**	
 * 全黑一点红	
 */	
public class WXLSQuanHeiYiDianHongRankingImpl_T  extends WXLSAbstractRanking {	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        WXLSRankingResult result = null;	
        if (player.getCardSize() == 13) {	
            Map<WXLSCardSuitEnum, List<WXLSPockerCard>> suitCount = player.getCardsSuitCountMap();	
            Iterator<Map.Entry<WXLSCardSuitEnum, List<WXLSPockerCard>>> it = suitCount.entrySet().iterator();	
            int hong=0;	
            int hei=0;	
            while (it.hasNext()) {	
                Map.Entry<WXLSCardSuitEnum, List<WXLSPockerCard>> entry = it.next();	
                if (entry.getKey() == WXLSCardSuitEnum.HEARTS||entry.getKey()==WXLSCardSuitEnum.DIAMONDS) {	
                    hong+=entry.getValue().size();	
                }	
                if (entry.getKey() == WXLSCardSuitEnum.SPADES||entry.getKey()==WXLSCardSuitEnum.CLUBS) {	
                    hei+=entry.getValue().size();	
                }	
            }	
            if (hong==1&&hei==12) {	
                result = new WXLSRankingResult();	
                result.setPockerCards(player.getCards());	
                result.setRankingEnum(WXLSRankingEnum.QUANHEIYIDIANHONG);	
                return result;	
            }	
        }	
        return result;	
    }	
}	
	
