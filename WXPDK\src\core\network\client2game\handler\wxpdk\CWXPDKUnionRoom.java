package core.network.client2game.handler.wxpdk;

import business.wxpdk.c2s.cclass.WXPDK_define;
import business.wxpdk.c2s.iclass.CWXPDK_CreateRoom;
import business.player.Player;
import business.player.feature.PlayerUnionRoom;
import cenum.PrizeType;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;
import core.network.client2game.handler.PlayerHandler;
import core.server.wxpdk.WXPDKAPP;
import jsproto.c2s.cclass.room.BaseRoomConfigure;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 亲友圈房间
 * 
 * <AUTHOR>
 *
 */
public class CWXPDKUnionRoom extends PlayerHandler {

	@Override
	public void handle(Player player, WebSocketRequest request, String message)
			throws IOException {

		final CWXPDK_CreateRoom clientPack = new Gson().from<PERSON>son(message,
				CWXPDK_CreateRoom.class);
		// 公共房间配置
		BaseRoomConfigure<CWXPDK_CreateRoom> configure = new BaseRoomConfigure<CWXPDK_CreateRoom>(
				PrizeType.RoomCard,
				WXPDKAPP.GameType(),
				clientPack.clone());
		player.getFeature(PlayerUnionRoom.class).createNoneUnionRoom(request,configure);
	}
}
