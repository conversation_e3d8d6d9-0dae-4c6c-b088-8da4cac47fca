package business.global.mj.ahmj.hutype;

import business.ahmj.c2s.cclass.AHMJPointItem;
import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCardInit;
import business.global.mj.ahmj.AHMJRoomEnum;
import business.global.mj.hu.BaseHuCard;
import business.global.mj.util.HuUtil;

public class AHMJNormalHuCardImpl extends BaseHuCard {
    @Override
    public boolean checkHuCard(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
        if (null == mCardInit) {
            return false;
        }
        return HuUtil.getInstance().checkHu(mCardInit.getAllCardInts(), mCardInit.sizeJin());
    }

    @Override
    public <T> Object checkHuCardReturn(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
        if (checkHuCard(mSetPos, mCardInit)) {
            AHMJPointItem item = new AHMJPointItem();
            item.addAHMJOpPoint(AHMJRoomEnum.AHMJOpPoint.PingHu, AHMJRoomEnum.AHMJOpPoint.PingHu.value());
            return item;
        }
        // 检查胡类型
        return null;
    }
}					
