package business.global.mj.ahhbmj;

import business.global.mj.AbsMJSetOp;
import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCard;
import business.global.mj.MJCardInit;
import business.global.mj.ahhbmj.optype.AHHBMJAnGangCardImpl;
import business.global.mj.ahhbmj.optype.AHHBMJGangCardImpl;
import business.global.mj.ahhbmj.optype.AHHBMJJieGangCardImpl;
import business.global.mj.ahhbmj.optype.AHHBMJPengCardImpl;
import business.global.mj.ahhbmj.ting.AHHBMJTingImpl;
import business.global.mj.hu.QiangGangHuCardImpl;
import business.global.mj.manage.MJFactory;
import cenum.mj.HuType;
import cenum.mj.MJHuOpType;
import cenum.mj.OpType;

import java.util.ArrayList;
import java.util.List;

/**
 * 淮北麻将
 *
 * <AUTHOR>
 */
public class AHHBMJSetOp extends AbsMJSetOp {
    // 操作												
    private List<OpType> opTypes = new ArrayList<>();
    // 玩家信息						
    private AHHBMJSetPos mSetPos;


    public AHHBMJSetOp(AHHBMJSetPos mSetPos) {
        super();
        this.mSetPos = mSetPos;
    }

    /**
     * 执行动作
     *
     * @param cardID 牌ID
     * @param opType 动作类型
     * @return
     */
    @Override
    public boolean doOpType(int cardID, OpType opType) {
        boolean doOpType = false;
        MJHuOpType huOpType = null;
        int lastOutCard;
        switch (opType) {
            case QiangGangHu:
                doOpType = MJFactory.getHuCard(QiangGangHuCardImpl.class).checkHuCard(mSetPos.getMJSetPos());
                huOpType = MJHuOpType.QGHu;
                if (doOpType && mSetPos.getHandCard() == null) {
                    mSetPos.setHandCard(new MJCard(mSetPos.getSet().getLastOpInfo().getLastOpCard()));
                }
                break;
            case JiePao:
                lastOutCard = mSetPos.getHandCard() == null ? mSetPos.getSet().getLastOpInfo().getLastOutCard() : 0;

                huOpType = MJHuOpType.JiePao;
                doOpType = checkOpType(lastOutCard, opType);
                if (doOpType && mSetPos.getHandCard() == null) {
                    mSetPos.setHandCard(new MJCard(lastOutCard));
                }
                break;
            case Hu:
                huOpType = MJHuOpType.ZiMo;
                doOpType = checkOpType(cardID, opType);
                break;
            default:
                //吃。碰。杠。暗杠。接杠											
                doOpType = doOtherOpType(opType, cardID);
                break;
        }
        if (doOpType && huOpType != null) {
            mSetPos.setmHuOpType(huOpType);
            dianPao(huOpType);
        }

        this.addOp(doOpType, opType);
        return doOpType;
    }


    /**
     * @param huOpType
     */
    private void dianPao(MJHuOpType huOpType) {
        if (huOpType != null && huOpType != MJHuOpType.ZiMo) {
            int lastOpPos = mSetPos.getSet().getLastOpInfo().getLastOpPos();
            AbsMJSetPos pos = mSetPos.getMJSetPos(lastOpPos);
            pos.setHuType(HuType.DianPao);
            AHHBMJRoomSet set = (AHHBMJRoomSet) mSetPos.getSet();

        }
    }

    /**
     * 扣听后不能吃、碰、杠，只能打出本轮摸到的牌，直至胡牌
     *
     * @param opType
     * @param cardID
     * @return
     */
    private boolean doOtherOpType(OpType opType, int cardID) {

        boolean doOpType = false;
        switch (opType) {
            case AnGang:
                doOpType = MJFactory.getOpCard(AHHBMJAnGangCardImpl.class).doOpCard(mSetPos, cardID);
                break;
            case Gang:
                doOpType = MJFactory.getOpCard(AHHBMJGangCardImpl.class).doOpCard(mSetPos, cardID);
                break;
            case JieGang:
                doOpType = MJFactory.getOpCard(AHHBMJJieGangCardImpl.class).doOpCard(mSetPos, cardID);
                break;
            case Peng:
                doOpType = MJFactory.getOpCard(AHHBMJPengCardImpl.class).doOpCard(mSetPos, cardID);
                break;
            case Chi:
                break;

            default:
                break;
        }
        return doOpType;
    }

    /**
     * 检查动作类型
     * q
     *
     * @param cardID 牌ID
     * @param opType 动作类型
     * @return
     */
    @Override
    public boolean checkOpType(int cardID, OpType opType) {
        int cardType = cardID / 100;
        boolean isOpType = false;
        MJCardInit initCard = null;
        switch (opType) {
            case AnGang:
                isOpType = MJFactory.getOpCard(AHHBMJAnGangCardImpl.class).checkOpCard(mSetPos, cardID);
                break;
            case Ting:
                isOpType = MJFactory.getTingCard(AHHBMJTingImpl.class).checkTingList(mSetPos);
                break;
            case JieGang:
                isOpType = MJFactory.getOpCard(AHHBMJJieGangCardImpl.class).checkOpCard(mSetPos, cardID);
                break;
            case Gang:
                isOpType = MJFactory.getOpCard(AHHBMJGangCardImpl.class).checkOpCard(mSetPos, cardID);
                break;
            case Peng:
                isOpType = MJFactory.getOpCard(AHHBMJPengCardImpl.class).checkOpCard(mSetPos, cardID);
                break;
            case Chi:
                break;

            case Hu:// 胡					
            case JiePao:// 胡
            case QiangGangHu:// 胡
                initCard = mSetPos.mjCardInit(true);
                initCard.addCardInts(cardType);
                isOpType = MJFactory.getHuCard(AHHBMJNormalHuCardImpl.class).checkHuCard(mSetPos, initCard);
                break;
            default:
                break;
        }
        return isOpType;
    }


    @Override
    public void clear() {
        this.mSetPos = null;
    }

    /**
     * 添加动作
     *
     * @param doOpType 是否操作成功
     * @param opType   动作类型
     */
    public void addOp(boolean doOpType, OpType opType) {
        if (doOpType) {
            this.opTypes.add(opType);
        }
    }

    /**
     * 检查动作
     *
     * @return
     */
    public int isOpSize() {
        return this.opTypes.size();
    }


}											
