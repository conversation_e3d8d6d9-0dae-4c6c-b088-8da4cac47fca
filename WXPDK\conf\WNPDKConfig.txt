#手牌handleCard=[12,15,16,24];#去掉的牌deleteCard={[0x0E, 0x0F, 0x1F, 0x2F],,[0x0E, 0x0F, 0x1F, 0x2F, 0x1E, 0x2E, 0x0D],,[0x0E, 0x0F, 0x1F, 0x2F],,[0x0E, 0x0F, 0x1F, 0x2F]};#结算时索引值对应的结算方式#记牌分jiPaiFen = 0;#牌多通输   paiDuoTongShu = 1; #固定分guDingFen = 2;	#机器人明牌概率aiOpenCard = 5#机器人翻倍概率aiAddDouble = 10#加倍倍数addDoubleList = [1,2,5];#抢关门分数2*16robClosePointByNotGuDingFen = 32;#抢关门分数2 固定分robClosePointByGuDingFen = 2;#关门*2倍robCloseAddDouble = 2;#房间加倍限制maxRoomAddDouble = 3;#上庄分数backerPointList = [0, 100, 150, 200];#翻倍上限maxAddDoubleList = [0, 5, 10, 20];#神牌模式(0:关,1:开)God_Card  = 1;//0x1E,0x2E,0x3E,0x3F//0x03-0x3E 0x13-0x1E 0x23-0x2E 0x33-0x3F#玩家一Private_Card1 = [];#玩家二Private_Card2 = [];#玩家三Private_Card3 = [];#玩家四Private_Card4 = [];