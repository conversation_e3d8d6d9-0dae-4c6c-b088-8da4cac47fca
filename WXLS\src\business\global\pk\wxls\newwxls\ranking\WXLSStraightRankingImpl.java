package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSCardRankEnum;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.ArrayList;	
import java.util.List;	
	
/**	
 *  解析玩家手中的牌是不是顺子	
 */	
public class WXLSStraightRankingImpl extends WXLSAbstractRanking {	
	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        WXLSRankingResult result = null;	
        List<WXLSPockerCard> cards = player.getCards();	
        ArrayList<WXLSCardRankEnum> newcards = new ArrayList<WXLSCardRankEnum>();	
        if (5 == cards.size()) {	
            for (int i = 0; i < cards.size(); i++) {	
                newcards.add(cards.get(i).getRank());	
            }	
            for (int i = 0; i < WXLSAbstractRanking.fiveCars.size(); i++) {	
                if (newcards.containsAll(WXLSAbstractRanking.fiveCars.get(i))) {	
                    result = new WXLSRankingResult();	
                    result.setRankingEnum(WXLSRankingEnum.STRAIGHT);	
                    break;	
                }	
            }	
        }	
        return result;	
    }	
	
	
}	
