package business.a3pk.c2s.cclass;	
	
import business.global.pk.PKCurOutCardInfo;	
import cenum.room.SetState;	
import com.ddm.server.common.utils.Lists;	
import jsproto.c2s.cclass.mj.BaseMJRoom_SetEnd;	
import jsproto.c2s.cclass.mj.BaseMJRoom_SetRound;	
import jsproto.c2s.cclass.pk.base.BasePKRoom_SetEnd;	
import jsproto.c2s.cclass.pk.base.BasePKRoom_SetRound;	
import jsproto.c2s.cclass.pk.base.BasePKSet_Pos;	
import jsproto.c2s.cclass.room.RoomSetInfo;	
	
import java.util.ArrayList;	
import java.util.List;	
	
/**	
 * 当局信息	
 */	
public class A3PKRoomSetInfo extends RoomSetInfo {	
    /**	
     * 庄家位置	
     */	
    private int dPos = 0;	
    /**	
     * 最后操作时间	
     */	
    private int lastShotTime = 0;	
    /**	
     * 当前时间	
     */	
    private int setCurrentTime;	
    /**	
     * 最近一个出牌，等待被人操作接手的CardID， 用于箭头标识	
     */	
    private int waitReciveCard = 0;	
	
    /**	
     * 当前出牌	
     */	
    private PKCurOutCardInfo curOutCardInfo = new PKCurOutCardInfo();	
	
    /**	
     * 每个玩家的牌面	
     */	
    private List<BasePKSet_Pos> setPosList = new ArrayList<>();	
    /**	
     * 当前局状态 Init；Wait；End； Playing不需要信息	
     */	
    private SetState state = SetState.Init;	
    /**	
     * 当前等待信息 Wait	
     */	
    private BasePKRoom_SetRound setRound = new BasePKRoom_SetRound();	
    /**	
     * 结束状态	
     */	
    private BasePKRoom_SetEnd setEnd = new BasePKRoom_SetEnd();	
	
    /**	
     * 独打的位置	
     */	
    private int challengePos = -1;	
	
    /**	
     * 独打表态列表 0未表态 1同意 2拒绝	
     */	
    private List<Integer> posAgreeList = null;	
	
    /**	
     * 看谁的牌	
     */	
    private int seePosId;	
	
    public int getSeePosId() {	
        return seePosId;	
    }	
	
    public void setSeePosId(int seePosId) {	
        this.seePosId = seePosId;	
    }	
	
    public int getLastShotTime() {	
        return lastShotTime;	
    }	
	
    public void setLastShotTime(int lastShotTime) {	
        this.lastShotTime = lastShotTime;	
    }	
	
    public int getSetCurrentTime() {	
        return setCurrentTime;	
    }	
	
    public void setSetCurrentTime(int setCurrentTime) {	
        this.setCurrentTime = setCurrentTime;	
    }	
	
	
    public int getWaitReciveCard() {	
        return waitReciveCard;	
    }	
	
    public void setWaitReciveCard(int waitReciveCard) {	
        this.waitReciveCard = waitReciveCard;	
    }	
	
    public List<BasePKSet_Pos> getSetPosList() {	
        return setPosList;	
    }	
	
    public void setSetPosList(List<BasePKSet_Pos> setPosList) {	
        this.setPosList = setPosList;	
    }	
	
    public void addSetPosList(BasePKSet_Pos setPos) {	
        this.setPosList.add(setPos);	
    }	
	
    public SetState getState() {	
        return state;	
    }	
	
    public void setState(SetState state) {	
        this.state = state;	
    }	
	
    public void setSetRound(BasePKRoom_SetRound setRound) {	
        this.setRound = setRound;	
    }	
	
    public void setSetEnd(BasePKRoom_SetEnd setEnd) {	
        this.setEnd = setEnd;	
    }	
	
    public BasePKRoom_SetRound getSetRound() {	
        return setRound;	
    }	
	
    public BasePKRoom_SetEnd getSetEnd() {	
        return setEnd;	
    }	
	
	
    public PKCurOutCardInfo getCurOutCardInfo() {	
        return curOutCardInfo;	
    }	
	
    public void setCurOutCardInfo(PKCurOutCardInfo curOutCardInfo) {	
        this.curOutCardInfo = curOutCardInfo;	
    }	
	
    public int getdPos() {	
        return dPos;	
    }	
	
    public void setdPos(int dPos) {	
        this.dPos = dPos;	
    }	
	
    public List<Integer> getPosAgreeList() {	
        return posAgreeList;	
    }	
	
    public void setPosAgreeList(List<Integer> posAgreeList) {	
        this.posAgreeList = posAgreeList;	
    }	
	
    public int getChallengePos() {	
        return challengePos;	
    }	
	
    public void setChallengePos(int challengePos) {	
        this.challengePos = challengePos;	
    }	
}	
