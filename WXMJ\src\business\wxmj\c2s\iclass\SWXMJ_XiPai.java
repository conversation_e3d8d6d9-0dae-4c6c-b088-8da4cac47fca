package business.wxmj.c2s.iclass;			
			
import cenum.ClassType;			
import jsproto.c2s.cclass.BaseSendMsg;			
			
			
public class SWXMJ_XiPai extends BaseSendMsg {			
    /**			
	 * 			
	 */			
	private static final long serialVersionUID = 1L;			
	public long roomID;			
    public long pid;			
    public ClassType cType;			
    public static SWXMJ_Xi<PERSON>ai make(long roomID, long pid, ClassType cType) {			
    	SWXMJ_XiPai ret = new SWXMJ_XiPai();			
        ret.roomID = roomID;			
        ret.pid = pid;			
        ret.cType = cType;			
        return ret;			
    			
			
    }			
}			
