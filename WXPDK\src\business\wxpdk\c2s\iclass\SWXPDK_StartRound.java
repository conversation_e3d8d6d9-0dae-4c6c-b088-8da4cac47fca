package business.wxpdk.c2s.iclass;
import jsproto.c2s.cclass.*;


@SuppressWarnings("serial")
public class SWXPDK_StartRound<T> extends BaseSendMsg {
    
    public long roomID;
    public T room_SetWait;


    public static <T>SWXPDK_StartRound<T> make(long roomID, T room_SetWait) {
    	SWXPDK_StartRound<T> ret = new SWXPDK_StartRound<T>();
        ret.roomID = roomID;
        ret.room_SetWait = room_SetWait;

        return ret;
    

    }
}
