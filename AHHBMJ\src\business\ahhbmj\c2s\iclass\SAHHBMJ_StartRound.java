package business.ahhbmj.c2s.iclass;						
						
import jsproto.c2s.cclass.BaseSendMsg;						
						
						
public class SAHHBMJ_StartRound<T> extends BaseSendMsg {						
						
    public long roomID;						
    public T room_SetWait;						
						
    public static <T> SAHHBMJ_StartRound make(long roomID, T room_SetWait) {						
        SAHHBMJ_StartRound ret = new SAHHBMJ_StartRound();						
        ret.roomID = roomID;						
        ret.room_SetWait = room_SetWait;						
        return ret;						
						
						
    }						
}												
