package business.afmj.c2s.iclass;

import jsproto.c2s.iclass.room.SBase_Dissolve;

/**
 * 房间解散通知
 * 
 * <AUTHOR>
 *
 */
public class SAFMJ_Dissolve extends SBase_Dissolve {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public static SAFMJ_Dissolve make(SBase_Dissolve dissolve) {
		SAFMJ_Dissolve ret = new SAFMJ_Dissolve();
		ret.setRoomID(dissolve.getRoomID());
		ret.setOwnnerForce(dissolve.isOwnnerForce());
		ret.setDissolveNoticeType(dissolve.getDissolveNoticeType());
		ret.setMsg(dissolve.getMsg());
		return ret;
	}
}