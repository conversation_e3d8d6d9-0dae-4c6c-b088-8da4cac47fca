package business.ahmj.c2s.iclass;						
import jsproto.c2s.cclass.*;						
						
						
public class SAHMJ_Config<T> extends BaseSendMsg {						
    public T cfg;						
    public boolean isClub;						
						
    public static <T> SAHMJ_Config make(T cfg,boolean isClub) {						
    	SAHMJ_Config ret = new SAHMJ_Config();						
        ret.cfg = cfg;						
        ret.isClub = isClub;						
        return ret;						
    						
						
    }						
}						
