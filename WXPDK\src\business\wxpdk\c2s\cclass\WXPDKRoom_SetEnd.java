package business.wxpdk.c2s.cclass;

import jsproto.c2s.cclass.room.RoomSetEndInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * 跑得快 配置
 * <AUTHOR>
 *
 */


// 一局结束的信息
public class WXPDKRoom_SetEnd extends RoomSetEndInfo{
	public int endTime = 0;
	public List<Integer> 	roomDoubleList = new ArrayList<>();			//房间倍数
	public List<WXPDKRoom_PosEnd> posResultList = new ArrayList<>(); // 每个玩家的结算
}

