package business.ahhbmj.c2s.iclass;						
						
import jsproto.c2s.cclass.BaseSendMsg;						
						
public class SAHHBMJ_ChangePlayerNumAgree extends BaseSendMsg {						
						
    private static final long serialVersionUID = 1L;						
    public long roomID;						
    public int pos;						
    public boolean agreeChange;						
						
    public static SAHHBMJ_ChangePlayerNumAgree make(long roomID, int pos, boolean agreeChange) {						
        SAHHBMJ_ChangePlayerNumAgree ret = new SAHHBMJ_ChangePlayerNumAgree();						
        ret.roomID = roomID;						
        ret.pos = pos;						
        ret.agreeChange = agreeChange;						
        return ret;						
						
						
    }						
}												
