package business.global.pk.a3pk.optype;	
	
import business.global.pk.AbsPKSetPos;	
import business.global.pk.PKCurOutCardInfo;	
import business.global.pk.PKOpCard;	
import business.global.pk.a3pk.A3PKRoomEnum;	
import com.ddm.server.common.CommLogD;	
import jsproto.c2s.cclass.pk.BasePocker;	
import org.apache.commons.collections.CollectionUtils;	
import org.apache.commons.collections.MapUtils;	
	
import java.util.Comparator;	
import java.util.List;	
import java.util.Map;	
import java.util.stream.Collectors;	
	
/**	
 * 检查同花牌型	
 */	
public class A3PKTongHuaImpl<T> extends A3PKBaseCardType<T> {	
	
    @Override	
    public boolean resultType(AbsPKSetPos mSetPos, List<Integer> privateCardList, PKOpCard opCard) {	
        PKCurOutCardInfo curOutCard = mSetPos.getSet().getCurOutCard();	
        if (opCard.getCardList().size() != 5) {	
            return false;	
        }	
        // 是否有重复的牌	
	
        Map<Integer, Long> map = opCard.getCardList().stream().collect(Collectors.groupingBy(k -> BasePocker.getCardColor(k), Collectors.counting()));	
        if (MapUtils.isEmpty(map) || map.size() != 1) {	
            CommLogD.error("A3PKTongHuaImpl map:{}",map.toString() );	
            return false;	
        }	
        // 比较颜色	
        int compCardId = BasePocker.getCardColor(opCard.getCardList().get(0));	
        if (checkNotCurOutCard(A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_TONGHUA, curOutCard, mSetPos.getPosID(), opCard.getCardList()) && (compCardId <= curOutCard.getCompValue() || opCard.getCardList().size() != curOutCard.getCurOutCards().size())) {	
            // 不符合出牌规则	
            return false;	
        }	
        if(A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_TONGHUA.value() < curOutCard.getOutCardType()) {	
            CommLogD.error("A3PKTongHuaImpl outCardType:{}", curOutCard.getOutCardType());	
            return false;	
        }	
        return curOutCard.setCurOutCards(mSetPos.getPosID(), A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_TONGHUA.value(), opCard.getCardList(),compCardId);	
    }	
	
    @Override	
    public PKOpCard robotResultType(AbsPKSetPos mSetPos, PKCurOutCardInfo curOutCard, T item) {	
        Map<Integer, List<Integer>> map = mSetPos.getPrivateCards().stream().collect(Collectors.groupingBy(k -> BasePocker.getCardColor(k)));	
        List<Integer> cardList = map.entrySet().stream().filter(k -> k.getKey() > curOutCard.getOutCardType() && k.getValue().size() <= 6).map(k -> k.getKey()).sorted(Comparator.comparing(Integer::intValue).reversed()).limit(5).collect(Collectors.toList());	
        return CollectionUtils.isNotEmpty(cardList) ? PKOpCard.OpCard(A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_TONGHUA.value(), cardList) : null;	
    }	
}	
