package business.global.mj.afmj;

import business.global.room.base.AbsBaseRoom;
import business.global.room.base.Trusteeship;
import cenum.room.RoomState;
import com.ddm.server.websocket.def.ErrorCode;
import core.network.http.proto.SData_Result;


public class AFMJTrusteeship extends Trusteeship {
    AFMJRoom AFMJRoom;
    public AFMJTrusteeship(AbsBaseRoom room) {
        super(room);
        this.AFMJRoom = (AFMJRoom) room;
    }

    /**
     * 房间托管
     */
    @SuppressWarnings("rawtypes")
    @Override
    public SData_Result roomTrusteeship(long pid, boolean trusteeship, boolean isOwn) {
        AFMJRoomPos pos = (AFMJRoomPos)this.AFMJRoom.getRoomPosMgr().getPosByPid(pid);
        if (pos == null) {
            return SData_Result.make(ErrorCode.NotAllow, "not in pos");
        }
        if (!RoomState.Playing.equals(this.AFMJRoom.getRoomState())) {
            return SData_Result.make(ErrorCode.NotAllow, "roomState:{%s}", this.AFMJRoom.getRoomState());
        }
        // 如果玩家进入托管，启动定时器。
        if (trusteeship) {
            startTrusteeShipTime();
        }
        // 托管2小局解散：连续2局托管
        if(!trusteeship){ // 玩家取消托管，连续托管局数清零
            pos.clearTuoGuanSetCount();
        }
        pos.setTrusteeship(trusteeship, isOwn);
        if (!trusteeship) {
            // 如果所有玩家取消托管，关闭定时器
            if (!this.AFMJRoom.getRoomPosMgr().checkExistTrusteeship()) {
                cancelTimer();
            }
            this.AFMJRoom.cancelTrusteeship(pos);
        }
        return SData_Result.make(ErrorCode.Success);
    }

}
