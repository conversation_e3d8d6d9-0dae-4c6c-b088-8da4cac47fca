package business.global.pk.wxls;

import BaseCommon.CommLog;
import business.global.pk.wxls.newwxls.WXLSDealer;
import business.global.pk.wxls.newwxls.WXLSPlayerData;
import business.global.pk.wxls.newwxls.WXLSPlayerDun;
import business.global.pk.wxls.newwxls.ranking.WXLSRankingFacade;
import business.global.pk.wxls.utlis.WXLSRoomEnum;
import business.global.room.base.AbsRoomPos;
import business.global.room.base.AbsRoomSet;
import business.wxls.c2s.cclass.*;
import business.wxls.c2s.cclass.entity.WXLSPlayerResult;
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;
import business.wxls.c2s.iclass.*;
import cenum.PrizeType;
import cenum.room.SetState;
import cenum.room.TrusteeshipState;
import com.ddm.server.common.CommLogD;
import com.ddm.server.common.utils.CommTime;
import com.ddm.server.websocket.handler.requset.WebSocketRequestDelegate;
import com.google.gson.Gson;
import core.db.entity.clarkGame.GameSetBO;
import core.db.service.clarkGame.GameSetBOService;
import core.ioc.ContainerMgr;
import jsproto.c2s.cclass.BaseSendMsg;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 十三水一局游戏逻辑
 */
public class WXLSRoomSet extends AbsRoomSet {
    public Hashtable<Integer, WXLSSetPos> posDict = new Hashtable<>(); // 每个位置信息
    public WXLSRoom room = null;
    public long startMS = 0;
    public SetState state = SetState.Waiting;// 牌局状态
    public WXLSDealer dealer = new WXLSDealer();
    public WXLSRoomSet_End setEnd = new WXLSRoomSet_End();
    public SWXLS_RankingResult sRankingResult;
    public WXLSPlayerResult fourbagger;// 全垒打
    public GameSetBO bo = null;
    // 参与的玩家人数
    public int playerNum;
    Map<Long, Integer> beishulist = new HashMap<Long, Integer>();
    Timer timer = new Timer();// 倍率计时
    HashMap<Integer, List<WXLSPockerCard>> hMap = new HashMap<Integer, List<WXLSPockerCard>>();
    private ArrayList<String> first = null;
    private ArrayList<String> second = null;
    private ArrayList<String> third = null;
    private ArrayList<WXLSPockerCard> firsts = null;
    private ArrayList<WXLSPockerCard> seconds = null;
    private ArrayList<WXLSPockerCard> thirds = null;
    private WXLSPockerCard publicCard = null;
    private long setStartTime = 0;
    private int playerMaxNum = 8;

    @SuppressWarnings("rawtypes")
    public WXLSRoomSet(WXLSRoom room) {
        super(room.getCurSetID());
        this.startMS = CommTime.nowMS();
        this.room = room;
        this.playerNum = room.getSSSPlayerNum();
        this.playerMaxNum = room.getPlayerNum();
        this.startSet();

    }

    public WXLSPockerCard getPublicCard() {
        return publicCard;
    }

    public void setPublicCard(WXLSPockerCard publicCard) {
        this.publicCard = publicCard;
    }

    /**
     * 删除牌组信息
     *
     * @return
     */
    public static boolean deleteCard(List<WXLSPockerCard> cardList, List<WXLSPockerCard> deleteCardList) {
        boolean flag = true;
        List<WXLSPockerCard> notRepeat = cardList.stream().distinct().collect(Collectors.toList());
        //检测牌的合理性 是否重复
        if (notRepeat.size() != cardList.size()) {
            return false;
        }
        //判断要删除的牌是否都在
        for (WXLSPockerCard byte1 : deleteCardList) {
            if (!cardList.contains(byte1)) {
                flag = false;
            }
        }
        if (flag) {//牌都在的话删除
            cardList.removeAll(deleteCardList);
        }
        return flag;
    }

    /**
     * 标识Id
     *
     * @return
     */
    @Override
    public int getTabId() {
        return this.room.getTabId();
    }

    public void clear() {
        if (null != this.posDict) {
            this.posDict.forEach((key, value) -> {
                if (null != value) {
                    value.clean();
                }
            });
            this.posDict.clear();
            this.posDict = null;
        }
        if (null != this.dealer) {
            this.dealer.clean();
            this.dealer = null;
        }

        if (null != this.sRankingResult) {
            this.sRankingResult.clean();
            this.sRankingResult = null;
        }
        if (null != this.timer) {
            timer.cancel();
            timer = null;
        }
        this.room = null;
        this.setEnd = null;
        this.fourbagger = null;
        this.bo = null;
        this.beishulist = null;
        this.hMap = null;
        this.first = null;
        this.second = null;
        this.third = null;
        this.seconds = null;
        this.thirds = null;
        this.publicCard=null;


    }

    /**
     * 开始设置
     */
    public void startSet() {
        // 庄家处理
//		if (this.room.isZJModel()) {
//			zjDeal();
//		}
//		if (this.room.cfg.zhuangjiaguize == 4) {
//			this.room.notify2All(SWXLS_BeiShuSelect.make(this.room.getRoomID()));
//			timer.schedule(this, 10 * 1000);
//		} else {
        startGame();
//		}

    }

    public void setBeiShu(long pid, int pos, int beishu) {
        if (!SetState.Waiting.equals(this.state)) {
            return;
        }
        if (!this.beishulist.containsKey(pid)) {
            this.beishulist.put(pid, beishu);
            if (beishulist.size() == this.playerNum) {
                timer.cancel();
                timer = null;
                // 确认庄家，广播
                beishuD();
                startGame();
            }
        }
    }

    @SuppressWarnings("rawtypes")
    private void beishuD() {
        int max = 1;
        if (beishulist.size() != 0) {

            for (Integer value : beishulist.values()) {
                if (max < value) {
                    max = value;
                }
            }
            if (max == 1) {
                // 随机
                Random random = new Random();
                int ran = random.nextInt(playerNum);
                this.room.setZJ(getPid(ran));
                this.room.setBS(max);
                this.room.getRoomPosMgr().notify2All(SWXLS_ZJBeiShu.make(this.room.getRoomID(), ran, max));
            } else {
                List<Long> list = new ArrayList<Long>();
                Iterator iter = beishulist.entrySet().iterator();
                while (iter.hasNext()) {
                    Map.Entry entry = (Map.Entry) iter.next();
                    long key = (long) entry.getKey();
                    int val = (int) entry.getValue();
                    if (max == val) {
                        list.add(key);
                    }
                }
                if (list.size() > 0) {
                    Random random = new Random();
//					int ran = random.nextInt(playerNum);
                    Long pid = list.get(random.nextInt(list.size()));
                    this.room.setZJ(pid);
                    this.room.setBS(max);
                    this.room.getRoomPosMgr().notify2All(SWXLS_ZJBeiShu.make(this.room.getRoomID(),
                            this.room.getRoomPosMgr().getPosByPid(this.room.getZJ()).getPosID(), max));
                }
            }
        } else {
            // 随机
            Random random = new Random();
            int ran = random.nextInt(playerNum);
            this.room.setZJ(getPid(ran));
            this.room.setBS(max);
            this.room.getRoomPosMgr().notify2All(SWXLS_ZJBeiShu.make(this.room.getRoomID(), ran, max));
        }

    }

    private void startGame() {
        //设置参与游戏的玩家

        for (int i = 0; i < playerMaxNum; i++) {
            WXLSRoomPos WXLSRoomPos = (WXLSRoomPos) this.room.getRoomPosMgr().getPosByPosID(i);
            if (WXLSRoomPos == null || WXLSRoomPos.getPid() == 0)
                continue;
            if(WXLSRoomPos.isCardReady()){
                if(Objects.nonNull(this.dealer)&&this.dealer.PlayerList.containsKey(WXLSRoomPos.getPid())){
                    WXLSPlayerData playerdata = this.dealer.PlayerList.get(WXLSRoomPos.getPid());
                    CommLogD.error(" playerCardReady is true {}",playerdata.toString());
                }
            }
            if (WXLSRoomPos.isReady() && this.room.getCurSetID() == 1 || (WXLSRoomPos.isGameReady() && this.room.getCurSetID() > 1 && WXLSRoomPos.getPid() != 0)) {
                WXLSRoomPos.setPlayTheGame(true);
            }

        }
        this.room.getRoomPosMgr().clearGameReady();
        WXLSRoomPosMgr roomPos = (WXLSRoomPosMgr) this.room.getRoomPosMgr();
        roomPos.clearCardReady();
        this.dealer.init(this.room);
        //是否开启神牌模式
        if (isGodCard()) {
            godCard();
        }
        // 对每个位置的人设置牌
//		int randomPos = WXLSRandomPos.randomSaizi(playerNum);
        for (int idx = 0; idx < playerMaxNum; idx++) {
            int i = (0 + idx) % playerMaxNum;
            WXLSRoomPos wxlsRoomPos = (WXLSRoomPos) this.room.getRoomPosMgr().getPosByPosID(i);
            if (wxlsRoomPos == null || wxlsRoomPos.getPid() == 0 || !wxlsRoomPos.isPlayTheGame()) {
                continue;
            }
            WXLSSetPos setPos = new WXLSSetPos(i, wxlsRoomPos, this);
            posDict.put(i, setPos);
            // 如果是神牌模式发送神牌
            if (isGodCard()) {
                setPos.init(getGodCardSet(new WXLSPlayerData(this.room.getRoomID(), setPos.roomPos.getPid(), setPos.posID,room.getRoomCfg()), idx));
            } else {
                List<WXLSPockerCard> cards = this.dealer
                        .dispatch(new WXLSPlayerData(this.room.getRoomID(), setPos.roomPos.getPid(), setPos.posID,room.getRoomCfg()), playerNum, this.room.roomCfg);
                setPos.init(cards);
            }
        }
        if (playerNum == 3) {
            if (Objects.isNull(this.publicCard)) {
                this.publicCard =dealer.getPoker().dispatch();
            }
        }
        this.state = SetState.Init;// 牌局状态
        // 开始发牌
        this.setStartTime = CommTime.nowMS();
        for (int i = 0; i < playerMaxNum; i++) {
            WXLSRoomPos WXLSRoomPos = (WXLSRoomPos) this.room.getRoomPosMgr().getPosByPosID(i);
            if (WXLSRoomPos == null || WXLSRoomPos.getPid() == 0 || !WXLSRoomPos.isPlayTheGame())
                continue;
            long pid = WXLSRoomPos.getPid();
            this.room.getRoomPosMgr().notify2Pos(i, SWXLS_SetStart.make(this.room.getRoomID(), this.getNotify_set(pid)));
        }

        room.getRoomPosMgr().setAllLatelyOutCardTime();
        this.room.getTrusteeship().setTrusteeshipState(TrusteeshipState.Normal);
    }

    //是否开启神牌
    private boolean isGodCard() {
        return this.room.getConfigMgr().getGodCard() == 1;
    }

    /**
     * 设置神牌
     */
//	0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D,  0x0E,	0x0F, //方块3~2
//	0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D,  0x1E,	0x1F, //梅花3~2
//	0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C, 0x2D,  0x2E,	0x2F, //红桃3~2
//	0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C, 0x3D,  0x3E,	0x3F, //黑桃3~2
    private void godCard() {
        if (!isGodCard()) return;

        deleteCard(this.dealer.getPoker().getCards(), this.room.getConfigMgr().getPrivateCard1());
        ArrayList<WXLSPockerCard> card1 = getGodCard(this.room.getConfigMgr().getPrivateCard1());
        ArrayList<WXLSPockerCard> card2=new ArrayList<>();
        ArrayList<WXLSPockerCard> card3=new ArrayList<>();
        ArrayList<WXLSPockerCard> card4=new ArrayList<>();
        if (playerNum==4) {
            deleteCard(this.dealer.getPoker().getCards(), this.room.getConfigMgr().getPrivateCard2());
            deleteCard(this.dealer.getPoker().getCards(), this.room.getConfigMgr().getPrivateCard3());
            deleteCard(this.dealer.getPoker().getCards(), this.room.getConfigMgr().getPrivateCard4());
             card2 = getGodCard(this.room.getConfigMgr().getPrivateCard2());
             card3 = getGodCard(this.room.getConfigMgr().getPrivateCard3());
             card4 = getGodCard(this.room.getConfigMgr().getPrivateCard4());
        }else if(playerNum==3){
            deleteCard(this.dealer.getPoker().getCards(), this.room.getConfigMgr().getPublicCard());
            this.publicCard=CollectionUtils.isNotEmpty(this.room.getConfigMgr().getPublicCard())?this.room.getConfigMgr().getPublicCard().get(0):null;
            deleteCard(this.dealer.getPoker().getCards(), this.room.getConfigMgr().getPrivateCard2());
            deleteCard(this.dealer.getPoker().getCards(), this.room.getConfigMgr().getPrivateCard3());
            card2 = getGodCard(this.room.getConfigMgr().getPrivateCard2());
            card3 = getGodCard(this.room.getConfigMgr().getPrivateCard3());
        }else if(playerNum==2){
            deleteCard(this.dealer.getPoker().getCards(), this.room.getConfigMgr().getPrivateCard2());
            card2 = getGodCard(this.room.getConfigMgr().getPrivateCard2());
        }



        hMap.put(0, card1);
        hMap.put(1, card2);
        hMap.put(2, card3);
        hMap.put(3, card4);
    }

    /**
     * 获取牌
     */
    public ArrayList<WXLSPockerCard> getGodCard(List<WXLSPockerCard> list) {
        if (!isGodCard()) return new ArrayList<WXLSPockerCard>();
        int cardNum = this.room.getConfigMgr().getHandleCard();
        ArrayList<WXLSPockerCard> cardList = new ArrayList<WXLSPockerCard>(cardNum);
        cardList.addAll(list);
        int count = cardNum - cardList.size();
        List<WXLSPockerCard> tempList = this.dealer.getPoker().dispatch(count);
        cardList.addAll(tempList);
        return cardList;
    }

    private List<WXLSPockerCard> getGodCardSet(WXLSPlayerData zjplsPlayerData, int idx) {
        List<WXLSPockerCard> card = hMap.get(idx);
        zjplsPlayerData.setPlayerCards(card, this.room.roomCfg,this.playerNum);
        this.dealer.addPlayer(zjplsPlayerData.pid, zjplsPlayerData);
        return card;
    }

    /**
     * 庄家设置
     */
    private void zjDeal() {
//		// 0:房主坐庄 1:随机坐庄 2轮流坐庄 3赢分坐庄 4倍率抢庄	
//		switch (this.room.cfg.zhuangjiaguize) {	
//		case 0:	
//			this.room.setZJ(this.room.getControllerID());	
//			break;	
//		case 1:	
//			Random random = new Random();	
//			int ran = random.nextInt(playerNum);	
//			this.room.setZJ(getPid(ran));	
//			break;	
//		case 2:	
//			if (this.room.getZJ() == -1) {	
//				this.room.setZJ(this.room.getControllerID());	
//			} else {	
//				long zhuanjia = this.room.getZJ();	
//				int pos = this.room.getRoomPosMgr().getPosByPid(zhuanjia).posID;	
//				pos++;	
//				if (pos >= playerNum) {	
//					pos = 0;	
//				}	
//				this.room.setZJ(this.room.getRoomPosMgr().getPosByPosID(pos).getPid());	
//			}	
//			break;	
//		case 3:	
//			if (this.room.getZJ() == -1) {	
//				this.room.setZJ(this.room.getControllerID());	
//			}	
//			// else {	
//			// long zjid = this.dealer.getWinMaxPid();	
//			// if(zjid !=-1)	
//			// {	
//			// this.room.setZJ(zjid);	
//			// }	
//			// }	
//			break;	
//		case 4:	
//			break;	
//		}	
    }

    @Override
    public boolean checkExistPrizeType(PrizeType prizeType) {
        return false;
    }

    /**
     * 每200ms更新1次 秒
     *
     * @param sec
     * @return T 是 F 否
     */
    public boolean update(int sec) {
        boolean isClose = false;
        if (this.state == SetState.Init) {
            // if (CommTime.nowMS() > this.startMS + InitTime) {
            this.state = SetState.Playing;
            if (!this.startNewRound()) {
                this.endSet();
            }
            // }
        } else if (this.state == SetState.Playing) {
            boolean isStartNewRound = !this.startNewRound();
            if (isStartNewRound) {
                this.endSet();
            }
        } else if (this.state == SetState.End) {
            isClose = true;
        }
        return isClose;
    }


    @Override
    public void clearBo() {
        this.setBo(null);
    }

    /**
     * 局结束
     */
    public void endSet() {
        if (this.state == SetState.End)
            return;
        setEnd(true);
        this.state = SetState.End;
        this.calcPoint();
        // 赢分最多做庄处理
//		if (this.room.cfg.zhuangjiaguize == 3) {	
//			if (this.dealer.winMaxPid != -1) {	
//				this.room.setZJ(this.dealer.winMaxPid);	
//			}	
//		}	
        this.room.getRoomPosMgr().notify2All(SWXLS_Result.make(this.room.getRoomID(), this.getSRankingResult()));
        this.room.getRoomPosMgr().notify2All(SWXLS_SetEnd.make(this.room.getRoomID(), this.state));
        // 小局托管自动解散
        this.setTrusteeshipAutoDissolution();
    }

    @Override
    public void addDissolveRoom(BaseSendMsg baseSendMsg) {

    }

    @Override
    public void addGameConfig() {

    }

    /**
     * 设置玩家的牌序
     */
    public boolean setRankeds(CWXLS_Ranked cRanked, int posIndex, boolean isSpecial) {
        if (isSpecial) {
            this.dealer.setSpecialRanked(cRanked);
            return this.dealer.getPlayer(this.posDict.get(posIndex).roomPos.getPid()).getSpecial();
        } else {
            return this.dealer.setRanked(cRanked);

        }
    }

    public boolean checkRanked(long pid) {
        return this.dealer.getPlayer(pid).checkCards(this.getPublicCard());
    }

    /**
     * 获取所有人的牌序列表
     */
    @SuppressWarnings("rawtypes")
    public List<CWXLS_Ranked> getAllRankeds() {
        List<CWXLS_Ranked> rankeds = new ArrayList<CWXLS_Ranked>();
        Iterator iter = this.dealer.PlayerList.entrySet().iterator();
        while (iter.hasNext()) {
            Map.Entry entry = (Map.Entry) iter.next();
            rankeds.add(((WXLSPlayerData) entry.getValue()).getRanked());
        }
        return rankeds;
    }

    /**
     * 牌准备的托管
     */
    public void roomTrusteeship(int posID) {

        // 判断玩家是否处于配牌阶段
        if (this.state != SetState.Playing) {
            return;
        }
        for (int num = 0; num < playerMaxNum; num++) {
            WXLSRoomPos WXLSRoomPos = (WXLSRoomPos) this.room.getRoomPosMgr().getPosByPosID(num);
            if (WXLSRoomPos == null || WXLSRoomPos.isCardReady()) {
                continue;
            }
            if (!posDict.containsKey(WXLSRoomPos.getPosID())) {
                continue;
            }
            WXLSSetPos WXLSSetPos = posDict.get(WXLSRoomPos.getPosID());
            if (posID > -1) {
                if (WXLSSetPos.posID != posID) {
                    continue;
                }
            } else if (!WXLSSetPos.roomPos.isRobot() && !WXLSSetPos.roomPos.isTrusteeship()) {
                continue;
            }

            List<WXLSPockerCard> shouCard = WXLSSetPos.privateCards;
            sortCards(shouCard);
            CWXLS_PlayerRanked dunPos = new CWXLS_PlayerRanked();
            for (int j = 0; j < shouCard.size(); j++) {
                first = new ArrayList<String>();
                firsts = new ArrayList<WXLSPockerCard>();
                second = new ArrayList<String>();
                seconds = new ArrayList<WXLSPockerCard>();
                third = new ArrayList<String>();
                thirds = new ArrayList<WXLSPockerCard>();
                List<Integer> containsCardId = new ArrayList<Integer>();
                for (int k = 0, size = shouCard.size(); k < size; k++) {
                    int i = (k + j) % shouCard.size();
                    if (!containsCardId.contains(shouCard.get(i).cardID) && containsCardId.size() < 3) {
                        containsCardId.add(shouCard.get(i).cardID);
                        first.add(shouCard.get(i).toString());
                        firsts.add(new WXLSPockerCard(shouCard.get(i).toString()));
                    } else if (seconds.size() < 5) {
                        second.add(shouCard.get(i).toString());
                        seconds.add(new WXLSPockerCard(shouCard.get(i).toString()));
                    } else if (thirds.size() < 5) {
                        third.add(shouCard.get(i).toString());
                        thirds.add(new WXLSPockerCard(shouCard.get(i).toString()));
                    }
                }
                // 判断中墩和前墩墩的大小是否倒水。
                WXLSPlayerDun firstdun = new WXLSPlayerDun(this.room.roomCfg,playerNum);
                firstdun.addData(firsts);
                WXLSRankingFacade.getInstance().resolve(firstdun);
                WXLSPlayerDun seconddun = new WXLSPlayerDun(this.room.roomCfg,playerNum);
                seconddun.addData(seconds);
                WXLSRankingFacade.getInstance().resolve(seconddun);
                // 判断中墩和后墩的大小是否倒水。
                WXLSPlayerDun thirddun = new WXLSPlayerDun(this.room.roomCfg,playerNum);
                thirddun.addData(thirds);
                WXLSRankingFacade.getInstance().resolve(thirddun);
                if (firstdun.compareTo(seconddun) == 1 && firstdun.compareTo(thirddun) == 1) {
                    if (seconddun.compareTo(thirddun) == -1) {
                        dunPos.first = first;
                        dunPos.second = third;
                        dunPos.third = second;
                    } else {
                        dunPos.first = first;
                        dunPos.second = second;
                        dunPos.third = third;
                    }
                    CWXLS_Ranked cRanked = new CWXLS_Ranked(room.getRoomID(), WXLSSetPos.roomPos.getPid(), WXLSSetPos.roomPos.getPosID(),
                            dunPos);
                    WebSocketRequestDelegate request = new WebSocketRequestDelegate();
                    room.cardReady(request, true, WXLSSetPos.roomPos.getPid(), WXLSSetPos.roomPos.getPosID(), cRanked, false);
                    break;
                }

            }

        }
    }

    /**
     * 手牌排序
     */
    private void sortCards(List<WXLSPockerCard> shouCard) {
        Collections.sort(shouCard, new Comparator<WXLSPockerCard>() {
            @Override
            public int compare(WXLSPockerCard o1, WXLSPockerCard o2) {
                return o1.cardID - o2.cardID;
            }
        });
    }


    @SuppressWarnings("rawtypes")
    public long getPid(int idx) {
        List<Long> list = new ArrayList<Long>();
        List<WXLSRoomPos> rPos = this.room.getAllWXLSRoomPosList();
        for (WXLSRoomPos sRoomPos : rPos) {
            list.add(sRoomPos.getPid());
        }
        if (list.size() == idx || idx < 0)
            return list.get(0);
        return list.get(idx);

    }


    /**
     * 结算积分
     */
    public void calcPoint() {
        GameSetBOService gameSetBoService = ContainerMgr.get().getComponent(GameSetBOService.class);
        GameSetBO gameSetBO = gameSetBoService.findOne(room.getRoomID(), this.room.getCurSetID());
        this.bo = gameSetBO == null ? new GameSetBO() : gameSetBO;
        if (gameSetBO == null) {
            bo.setRoomID(room.getRoomID());
            bo.setSetID(this.getSetID());
        }

        fourbagger = this.getFourbagger();
        sRankingResult = this.getSRankingResult();
        List<WXLSPlayerResult> allWXLSPlayerResults = sRankingResult.playerResults;
        WXLSRoomPosMgr roomPos = (WXLSRoomPosMgr) this.room.getRoomPosMgr();
        roomPos.clearCardReady();
        room.playerResult.add(allWXLSPlayerResults);
        room.rankingResult = sRankingResult;
        for (WXLSPlayerResult pResult : allWXLSPlayerResults) {
            posDict.get(pResult.getPosIdx()).calcPosEnd();
        }

        // 总结每个人积分
        for (WXLSPlayerResult pResult : allWXLSPlayerResults) {
            setEnd.pResults.add(pResult);
            goldEnd(pResult.getPosIdx(), pResult.getShui());
        }
        room.getRoomPosMgr().setAllLatelyOutCardTime();
        setEnd.endTime = CommTime.nowSecond();
        WXLSSetEndResult setEndResult;
        //解散的时候手动加入当局手牌信息
        if (sRankingResult.playerResults.size() == 0) {
            List<CWXLS_Ranked> rankeds = new ArrayList<>();
            List<WXLSPlayerResult> playerResults = new ArrayList<>();
            for (int i = 0; i < room.getSSSPlayerNum(); i++) {
                WXLSSetPos setPos = this.posDict.get(i);
                if (setPos == null) continue;
                CWXLS_PlayerRanked playerRanked = new CWXLS_PlayerRanked();
                WXLSPlayerResult playerResult = new WXLSPlayerResult(setPos.roomPos.getPid(), setPos.posID, 0);
                playerRanked.first = getList(setPos.privateCards.subList(0, 3));
                playerRanked.second = getList(setPos.privateCards.subList(3, 8));
                playerRanked.third = getList(setPos.privateCards.subList(8, 13));
                CWXLS_Ranked ranked = new CWXLS_Ranked(room.getRoomID(), setPos.roomPos.getPid(), setPos.posID, playerRanked);
                rankeds.add(ranked);
                playerResult.setPoint(0);
                playerResults.add(playerResult);
            }
            setEndResult = new WXLSSetEndResult(rankeds, playerResults,getPublicCardStr());
        } else {
            setEndResult = new WXLSSetEndResult(sRankingResult.rankeds, this.getWXLSPlayerResults(),
                    this.room.getZJ(), 1,getPublicCardStr());
        }
        String dataJsonRes = new Gson().toJson(setEndResult);
        bo.setDataJsonRes(dataJsonRes);
        bo.setEndTime(setEnd.endTime);
//		if (this.checkExistPrizeType(PrizeType.Gold)) {	
//            return;	
//        }	
        gameSetBoService.saveOrUpDate(bo);
    }

    public List<String> getList(List<WXLSPockerCard> wxlsPockerCards) {
        List<String> list = new ArrayList<>();
        for (WXLSPockerCard con : wxlsPockerCards) {
            list.add(con.getKey());
        }
        return list;
    }

    public List<WXLSPlayerResult> getWXLSPlayerResults() {
        List<WXLSPlayerResult> playerResults = sRankingResult.playerResults;

        for (AbsRoomPos pos : this.room.getRoomPosMgr().posList) {
            if (pos.getPid() <= 0L || !pos.isPlayTheGame()) {
                continue;
            }
            boolean addFlag = true;
            for (WXLSPlayerResult playerResult : playerResults) {
                playerResult.setPoint(playerResult.getShui());
                if (playerResult.getPid() == pos.getPid()) {
                    addFlag = false;
                    break;
                }
            }
            if (addFlag) {
                WXLSPlayerResult playerResult = new WXLSPlayerResult();
                playerResult.setPid(pos.getPid());
                playerResult.setPosIdx(pos.getPosID());
                playerResults.add(playerResult);
            }
        }
//		this.room.	
        return playerResults;
    }

    /**
     * 练习场结算
     */
    private void goldEnd(int posID, int shui) {
//		if (room.getPrizeType() == PrizeType.Gold) {	
//			WXLSRoomPos<?> pos = (WXLSRoomPos<?>) room.getRoomPosMgr().getPosByPosID(posID);	
//			if (!RobotMgr.getInstance().isRobot((int) pos.getPid())) {	
//				Player player = PlayerMgr.getInstance().getPlayer(pos.getPid());	
//				player.getFeature(PlayerCurrency.class).goldRoomEnd(shui, room.getBaseMark(), ItemFlow.CreateRoom_WXLS,	
//						GameType.WXLS.value());	
//			}	
//	
//		}	
    }

    /**
     * 开启新的回合
     */
    public boolean startNewRound() {
        WXLSRoomPosMgr roomPosMgr = (WXLSRoomPosMgr) this.room.getRoomPosMgr();
        if (roomPosMgr.isAllCardReady()) {
            // if(this.dealer.checkPlayerList())
            // {
            // this.room.curSet.dealer.Compare(this.room.getZJ());
            // return false;
            // }
            if (this.dealer.checkAllPlayerCard()) {
                this.dealer.Compare(this.room.getZJ());
            } else {
                CommLog.error("checkAllPlayerCard not false");
                return true;
            }
            return false;
        }
        return true;
    }

    /**
     * 获取通知设置
     *
     * @param pid 用户ID
     * @return
     */
    @SuppressWarnings("unused")
    public WXLSRoomSetInfo getNotify_set(long pid) {
        WXLSRoomSetInfo ret = new WXLSRoomSetInfo();
        ret.setSetID(this.room.getCurSetID());
        ret.setPublicCard(getPublicCardStr());
        ret.setStartTime = this.setStartTime;
        ret.mapai = this.room.mapai == null ? "" : this.room.mapai.toString();
        ret.beishu = 1;
        if (this.room.isZJModel()) {
            AbsRoomPos roomPosDelegateAbstract = this.room.getRoomPosMgr().getPosByPid(this.room.getZJ());
            if (roomPosDelegateAbstract != null) {
                ret.backerPos = roomPosDelegateAbstract.getPosID();
            }
        } else {
            ret.backerPos = -1;
        }

        ret.setCurrentTime = CommTime.nowMS();
        // 每个玩家的牌面
        // ret.posInfo = new ArrayList<>();
        List<WXLSRoomSet_Pos> posInfo = new ArrayList<WXLSRoomSet_Pos>();
        for (int i = 0; i < playerMaxNum; i++) {
            WXLSRoomPos fqplsRoomPos = (WXLSRoomPos) this.room.getRoomPosMgr().getPosByPosID(i);
            if (fqplsRoomPos == null || fqplsRoomPos.getPid() == 0) {
                continue;
            }
            if (pid == fqplsRoomPos.getPid()) {
                WXLSPlayerData player = this.dealer.getPlayer(fqplsRoomPos.getPid());
                if (player == null) {
                    continue;
                }
                ret.isXiPai = player.isXiPai();
                ret.setPosList.add(player.getSetPosInfo());
                ret.isPlaying = fqplsRoomPos.isPlayTheGame();
                if (fqplsRoomPos.isCardReady()) {
                    WXLSPlayerData playerData = this.dealer.PlayerList.get(fqplsRoomPos.getPid());
                    CWXLS_Ranked ranked = playerData.getRanked();
                    ret.ranked = ranked;
                }
            }
            WXLSSetPos setPos = posDict.get(i);
            if (setPos != null) {
                posInfo.add(new WXLSRoomSet_Pos(fqplsRoomPos.getPosID(), fqplsRoomPos.getPid(), fqplsRoomPos.isPlayTheGame()));
            }
        }
        if (this.room.getZJ() != -1) {
            ret.zhuangJia = this.room.getRoomPosMgr().getPosByPid(this.room.getZJ()).getPosID();
        }
        ret.posInfo = posInfo;
        ret.state = this.state == SetState.Init ? SetState.Init : this.state;// 牌局状态
        // ret.state = this.state; // 当前局状态 Init；End； Playing不需要信息
        // 如果是等待状态： waiting；
        if (this.state == SetState.Playing) {
            int pos = this.room.getRoomPosMgr().getPosByPid(pid).getPosID();

        }
        return ret;
    }
    public String getPublicCardStr(){
        return new String(Objects.isNull(this.publicCard)?"":this.publicCard.getKey());
    }
    public void setXiPai(long pid) {
        if (this.dealer.PlayerList.containsKey(pid)) {
            this.dealer.getPlayer(pid).setXiPai(true);
        }
    }


    /**
     * 获取通知设置结束
     *
     * @return
     */
    @Override
    public WXLSRoomSet_End getNotify_setEnd() {
        return setEnd;
    }

    public void run() {
        beishuD();
        startGame();
    }

    // =================================================================
    // 对比最终结果
    private WXLSPlayerResult getFourbagger() {
        return ((WXLSRoomSet) room.getCurSet()).dealer.getFourbagger();
    }

    // 对比所有结果
    private SWXLS_RankingResult getSRankingResult() {
        SWXLS_RankingResult ret = ((WXLSRoomSet) room.getCurSet()).dealer.getSRankingResult();
        for (WXLSPlayerResult con : ret.playerResults) {
            WXLSRoomPos sssRoomPos = (WXLSRoomPos) this.room.getRoomPosMgr().getPosByPosID(con.getPosIdx());
            con.setSportsPoint(sssRoomPos.setSportsPoint(con.getPoint()));
        }
        ret.setPublicCard(this.getPublicCardStr());
        return ret;
    }

    /**
     * 检查小局托管自动解散
     */
    public boolean checkSetEndTrusteeshipAutoDissolution() {
        if (room.getRoomCfg().getFangjian().contains(WXLSRoomEnum.WXLSGameRoomConfigEnum.AuToEnd.ordinal())) {
            return true;
        } else if (room.getRoomCfg().getFangjian().contains(WXLSRoomEnum.WXLSGameRoomConfigEnum.TuoGuan2XiaoJuJieSan.ordinal())) {
            // 有玩家连续2局托管
            // 获取托管玩家pid列表
            List<Long> trusteeshipPlayerList = room.getRoomPosMgr().getRoomPosList().stream()
                    .filter(n -> n.isTrusteeship() && ((WXLSRoomPos) n).getTuoGuanSetCount() >= 2).map(n -> n.getPid()).collect(Collectors.toList());
            if (trusteeshipPlayerList.size() > 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * 小局托管自动解散
     */
    public void setTrusteeshipAutoDissolution() {
        // 检查小局托管自动解散
        if (checkSetEndTrusteeshipAutoDissolution()) {
            // 获取托管玩家pid列表
            List<Long> trusteeshipPlayerList = room.getRoomPosMgr().getRoomPosList().stream()
                    .filter(n -> n.isTrusteeship()).map(n -> n.getPid()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(trusteeshipPlayerList)) {
                // 记录回放中
                room.getRoomPosMgr().notify2All(DissolveTrusteeship(this.room.getRoomID(), trusteeshipPlayerList, CommTime.nowSecond()));
                room.setTrusteeshipDissolve(true);
            }
        }
    }

    /**
     * 小局托管自动解散回放记录 注意：需要自己重写
     *
     * @param roomId  房间id
     * @param pidList 托管玩家Pid
     * @param sec     记录时间
     * @return 小局托管解散
     */
    public BaseSendMsg DissolveTrusteeship(long roomId, List<Long> pidList, int sec) {
        return SWXLS_DissolveTrusteeship.make(roomId, pidList, sec);
    }

}	
