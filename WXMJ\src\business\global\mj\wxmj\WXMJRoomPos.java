package business.global.mj.wxmj;

import business.global.mj.wxmj.WXMJRoomEnum.WXMJPiaoFen;
import business.global.room.base.AbsBaseRoom;
import business.global.room.mj.MJRoomPos;

/**
 * 房间内每个位置信息
 *
 * <AUTHOR>
 */
public class WXMJRoomPos extends MJRoomPos {

    protected WXMJPiaoFen m_piaoFenEnum = WXMJPiaoFen.NOT_OP;// 飘分

    public WXMJRoomPos(int posID, AbsBaseRoom room) {
        super(posID, room);
        this.setPlayTheGame(true);
    }

    /**
     * @return m_piaoFenEnum
     */
    public WXMJPiaoFen getPiaoFenEnum() {
        return m_piaoFenEnum;
    }

    public void setPiaoFenEnum(WXMJPiaoFen piaoFenEnum) {
        this.m_piaoFenEnum = piaoFenEnum;
    }

    public void clearPiaoHua() {
        this.m_piaoFenEnum = m_piaoFenEnum.NOT_OP;
    }

    @Override
    public void setGameReady(boolean isGameReady) {
        super.setGameReady(isGameReady);
        if (!isGameReady) {
            m_piaoFenEnum = WXMJPiaoFen.NOT_OP;
        }
    }
}			
