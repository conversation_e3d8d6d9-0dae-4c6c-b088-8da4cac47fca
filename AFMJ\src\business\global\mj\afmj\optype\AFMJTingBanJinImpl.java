package business.global.mj.afmj.optype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCard;
import business.global.mj.MJCardInit;
import business.global.mj.afmj.hutype.AFMJDDHuImpl;
import business.global.mj.afmj.hutype.AFMJHuImpl;
import business.global.mj.afmj.hutype.AFMJPPImpl;
import business.global.mj.manage.MJFactory;
import business.global.mj.ting.AbsTing;
import cenum.mj.MJSpecialEnum;

import java.util.List;

public class AFMJTingBanJinImpl extends AbsTing {

/*	@Override
	public boolean tingHu(AbsMJSetPos mSetPos,MJCardInit mCardInit) {
		if(MJFactory.getHuCard(BTJHuCardImpl.class).checkHuCard(mSetPos, mCardInit)) {
			return true;
		}
		return false;
	}*/
	@Override
	public boolean tingHu(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
		if (MJFactory.getHuCard(AFMJPPImpl.class).checkHuCard(mSetPos, mCardInit)) {
			return true;
		}
		if (MJFactory.getHuCard(AFMJDDHuImpl.class).checkHuCard(mSetPos, mCardInit)) {
			return true;
		}
		if (MJFactory.getHuCard(AFMJHuImpl.class).checkHuCard(mSetPos, mCardInit)) {
			return true;
		}
		return false;
	}

	/**
	 * 检测抢金的听牌列表
	 *
	 * @param mSetPos 玩家位置信息
	 * @param idx 下标
	 * @param isDPos 是否庄家
	 * @return 抢金的听牌列表
	 */
	@Override
	public List<MJCard> qangJinTingList(AbsMJSetPos mSetPos, int idx, boolean isDPos) {
		// 获取所有牌
		List<MJCard> allCards = mSetPos.allCards();
		// 如果牌的下标 == 所有牌 -1
		if (allCards.size() == idx) {
			return null;
		}
		// 开局后庄家、闲家也可以飞听
//		// 庄家多一张牌，所有需移除一张做检查
//		if (isDPos) {
//			// 移除一张牌
//			allCards.remove(idx);
//		}
		// 听牌
		boolean checkErgodicExistHuCard = checkErgodicExistHuCard(mSetPos, allCards);
		idx++;
		// 判断听牌数
		if (checkErgodicExistHuCard) {
			return allCards;
		} else {
			if (isDPos) {
				// 庄家可以尝试将所有牌都打出一遍，试试有没有听牌
				return qangJinTingList(mSetPos, idx,isDPos);
			} else {
				// 闲家只能检查一次。
				return null;
			}
		}
	}


	/**
	 * 遍历检查是否存在胡牌
	 *
	 * @param mSetPos 玩家位置信息
	 * @param allCardList 牌列表
	 * @return true: 存在胡牌; false: 不存在胡牌
	 */
	public boolean checkErgodicExistHuCard(AbsMJSetPos mSetPos, List<MJCard> allCardList) {
		// 麻将牌的初始信息
		MJCardInit mInit = mSetPos.mjCardInit(allCardList, true);
		if (null == mInit) {
			return false;
		}
		// 添加一张任意牌，进行测试是否能胡
		return tingHu(mSetPos, this.newMJCardInit(mInit.getAllCardInts(), mInit.getJins(), MJSpecialEnum.NOT_JIN.value()));
	}




}
