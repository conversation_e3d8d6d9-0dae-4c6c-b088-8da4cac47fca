package business.wxpdk.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;


public class SWXPDK_ChangePlayerNumAgree extends BaseSendMsg {
    
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	public long roomID;
    public int pos;
    public boolean agreeChange;
    public static SWXPDK_ChangePlayerNumAgree make(long roomID, int pos, boolean agreeChange) {
    	SWXPDK_ChangePlayerNumAgree ret = new SWXPDK_ChangePlayerNumAgree();
        ret.roomID = roomID;
        ret.pos = pos;
        ret.agreeChange = agreeChange;
        return ret;
    

    }
}
