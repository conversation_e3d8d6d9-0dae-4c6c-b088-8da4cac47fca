package business.afmj.c2s.iclass;

import cenum.mj.OpType;
import jsproto.c2s.cclass.BaseSendMsg;

/**
 * 补花
 * <AUTHOR>
 * @param <T>
 *
 */
@SuppressWarnings("serial")
public class SAFMJ_Applique<T> extends BaseSendMsg {
    public long roomID;
    public int pos;
    public OpType opType;
    public int opCard;
    public boolean isFlash;
    public T set_Pos;
    public int normalMoCnt = 0; // 普通摸牌数量
    public int gangMoCnt = 0; // 杠后、补花后摸牌数量
    @SuppressWarnings("rawtypes")
    public static <T>SAFMJ_Applique make(long roomID,int pos,OpType opType,int opCard,boolean isFlash,T set_Pos,int normalMoCnt, int gangMoCnt) {
        SAFMJ_Applique ret = new SAFMJ_Applique();
        ret.roomID = roomID;
        ret.pos = pos;
        ret.opType = opType;
        ret.opCard = opCard;
        ret.isFlash = isFlash;
        ret.set_Pos = set_Pos;
        ret.normalMoCnt = normalMoCnt;
        ret.gangMoCnt = gangMoCnt;

        return ret;


    }
}
