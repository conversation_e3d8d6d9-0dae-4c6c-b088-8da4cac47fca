package business.global.mj.ahhbmj;

import business.ahhbmj.c2s.cclass.AHHBMJResults;
import business.ahhbmj.c2s.cclass.AHHBMJRoom_PosEnd;
import business.ahhbmj.c2s.cclass.AHHBMJSet_Pos;
import business.global.mj.AbsMJSetPos;
import business.global.mj.AbsMJSetRoom;
import business.global.mj.MJCard;
import business.global.mj.ahhbmj.ting.AHHBMJTingImpl;
import business.global.room.mj.MJRoomPos;
import cenum.mj.HuType;
import cenum.mj.MJHuOpType;
import cenum.mj.OpPointEnum;
import cenum.mj.OpType;
import jsproto.c2s.cclass.mj.BaseMJRoom_PosEnd;
import jsproto.c2s.cclass.room.AbsBaseResults;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 青岛 每一局每个位置信息
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class AHHBMJSetPos extends AbsMJSetPos {
    /**
     * 杠上开花
     */
    private boolean isGSKH;
    private boolean isTing;
    private int zuoFen = -1; //-1:不显示 0：“不下坐”、1：“1坐”、2：“2坐
    private int laFen = -1;//-1:不显示 0：“不下拉”、1：“1拉”、2：“2拉
    private int paoFen = -1;//-1:不显示 0：“不下跑”、1：“1跑”、2：“2跑
    /**
     * 能摇杠的牌
     */
    private List<Integer> gangList = new ArrayList<>();

    /**
     * @param posID
     * @param roomPos
     * @param set
     */
    public AHHBMJSetPos(int posID, MJRoomPos roomPos, AbsMJSetRoom set) {
        super(posID, roomPos, set, AHHBMJTingImpl.class);
        this.setMSetOp(new AHHBMJSetOp(this));
        this.setCalcPosEnd(new AHHBMJCalcPosEnd(this));
        AHHBMJRoom room = (AHHBMJRoom) getRoom();
        //@TODO 2022.08.09	改为每局都要重新选择坐拉跑，不受有没有换庄的影响了
//        Map<Integer, List<Integer>> zuoLaPaoList = room.getZuoLaPaoList();
//        if (zuoLaPaoList.size() > 0) {
//            updateZuoLaPaoList(zuoLaPaoList.get(getPosID()));
//            if (room.getLastDPos() != room.getDPos()) {
//                int dPos = getSet().getDPos();
//                if (getPosID() == room.getLastDPos() || getPosID() == dPos) {
//                    this.laFen = -1;
//                    this.zuoFen = -1;
//                }
//            }
//        }

    }


    @Override
    public void clear() {
        super.clear();
    }

    /**
     * 计算总结算信息
     */
    @Override
    public void calcResults() {
        // 获取总结算信息											
        AHHBMJResults ahhbmjResults = (AHHBMJResults) this.mResultsInfo();
        AHHBMJCalcPosEnd calcPosEnd = (AHHBMJCalcPosEnd) getCalcPosEnd();
        ahhbmjResults.addZhuangPoint(getPosID() == getSet().getDPos() ? 1 : 0);
        ahhbmjResults.addAnGangPoint(calcPosEnd.getAnGangCount());
        ahhbmjResults.addMingGangPoint(calcPosEnd.getMingGangCount());
        ahhbmjResults.addBuGangPoint(calcPosEnd.getBuGangCount());
        // 并且设置覆盖						
        this.setResults(ahhbmjResults);
    }

    @Override
    public void calcPosPoint() {
        this.getCalcPosEnd().calcPosPoint(this);
    }


    /**
     * 统计本局分数
     *
     * @return
     */
    @Override
    public BaseMJRoom_PosEnd<?> calcPosEnd() {
        // 玩家当局分数结算											
        this.getCalcPosEnd().calcPosEnd(this);
        // 位置结算信息											
        AHHBMJRoom_PosEnd ret = (AHHBMJRoom_PosEnd) this.posEndInfo();
        ret.setEndPoint(this.getCalcPosEnd().getCalcPosEnd());
        ret.setTing(isTing);
        ret.setZuoFen(zuoFen);
        ret.setLaFen(laFen);
        ret.setPaoFen(paoFen);
        Map<OpPointEnum, Integer> huTypeMap = getCalcPosEnd().getHuTypeMap();
        //胡牌分
        if (huTypeMap.containsKey(OpPointEnum.Hu)) {
            ret.setHuCardPoint(huTypeMap.get(OpPointEnum.Hu));
        }
        //杠分
        if (huTypeMap.containsKey(OpPointEnum.GangNum)) {
            ret.setGangPoint(huTypeMap.get(OpPointEnum.GangNum));
        }
        //加分
        if (huTypeMap.containsKey(OpPointEnum.JiaPai)) {
            ret.setJiaPoint(huTypeMap.get(OpPointEnum.JiaPai));
        }
        //坐分
        if (huTypeMap.containsKey(OpPointEnum.KanFen)) {
            ret.setZuoFenPoint(huTypeMap.get(OpPointEnum.KanFen));
        }
        //拉分
        if (huTypeMap.containsKey(OpPointEnum.PiaoFen)) {
            ret.setLaFenPoint(huTypeMap.get(OpPointEnum.PiaoFen));
        }
        //跑分
        if (huTypeMap.containsKey(OpPointEnum.PaoFen)) {
            ret.setPaoFenPoint(huTypeMap.get(OpPointEnum.PaoFen));
        }
        ret.setHuCardPoint(ret.getHuCardPoint()+ret.getJiaPoint()+ret.getLaFenPoint()+ret.getZuoFenPoint()+ret.getPaoFenPoint());
        return ret;
    }

    /**
     * 新位置结算信息
     *
     * @return
     */
    @SuppressWarnings("rawtypes")
    protected AHHBMJRoom_PosEnd newMJSetPosEnd() {
        return new AHHBMJRoom_PosEnd();
    }

    @Override
    public AHHBMJSet_Pos getNotify(boolean isSelf) {
        AHHBMJSet_Pos setPos = (AHHBMJSet_Pos) this.getNotifyInfo(isSelf);
        setPos.setLaFen(laFen);
        setPos.setPaoFen(paoFen);
        setPos.setTing(isTing);
        setPos.setZuoFen(zuoFen);
        if (Objects.nonNull(getSet().getCurRound())) {
            AHHBMJSetRound curRound = (AHHBMJSetRound) getSet().getCurRound();
            setPos.setZuoLaPao(curRound.isZuoLaPao);
        }
        return setPos;
    }

    @Override
    public AHHBMJSet_Pos getPlayBackNotify() {
        AHHBMJSet_Pos setPos = (AHHBMJSet_Pos) super.getPlayBackNotify();
        setPos.setLaFen(laFen);
        setPos.setPaoFen(paoFen);
        setPos.setTing(isTing);
        setPos.setZuoFen(zuoFen);
        setPos.setZuoLaPao(getSet().isAtFirstHu());
        return setPos;

    }

    /**
     * 新一局中各位置的信息
     *
     * @return
     */
    @Override
    protected AHHBMJSet_Pos newMJSetPos() {
        return new AHHBMJSet_Pos();
    }

    @Override
    public boolean doOpType(int cardID, OpType opType) {
        return this.getmSetOp().doOpType(cardID, opType);
    }

    @Override
    public boolean checkOpType(int cardID, OpType opType) {
        return this.getmSetOp().checkOpType(cardID, opType);
    }

    /**
     * 检查胡
     *
     * @param
     * @param cardID
     * @return
     */
    @Override
    public OpType checkPingHu(int curOpPos, int cardID) {
        return checkPaoHu(curOpPos, cardID, false);

    }

    public OpType checkPaoHu(int curOpPos, int cardID, boolean qgh) {
        // 清空操作牌初始						
        this.clearPaoHu();
        OpType opType = OpType.Not;
        boolean isLouHu = this.getPosOpRecord().getHuCardTypeList().contains(cardID / 100);
        if (isLouHu) {
            return opType;
        }
        //漏胡：如果玩家漏掉炮胡，则该玩家摸牌前禁止炮胡这张漏掉的牌，其他牌可正常胡牌					
        if (checkOpType(cardID, OpType.Hu)) {
            this.setmHuOpType(MJHuOpType.JiePao);
            // 可胡牌，下一轮检测是不是漏胡用						
            this.getPosOpRecord().setHuCardType(cardID / 100);
            // 设置炮胡类型						
            opType = OpType.JiePao;
        }
        return opType;
    }

    /**
     * 可接受操作
     *
     * @return
     */
    @Override
    public List<OpType> recieveOpTypes() {
        this.clearOutCard();
        this.gangList.clear();
        List<OpType> opTypes = new ArrayList<OpType>();
        if (checkOpType(0, OpType.Ting)) {
            opTypes.add(OpType.Ting);
            if (!isTing) {
                opTypes.add(OpType.BaoTing);
            }
        }
        if (checkOpType(0, OpType.Hu)) {
            opTypes.add(OpType.Hu);
        }
        AHHBMJSetCard setCard = (AHHBMJSetCard) getSet().getSetCard();
        if (!setCard.isCardNull()) {
            if (checkOpType(0, OpType.AnGang)) {
                opTypes.add(OpType.AnGang);
            }
            if (checkOpType(0, OpType.Gang)) {
                opTypes.add(OpType.Gang);
            }
        }
        opTypes.add(OpType.Out);
        if (isTing){
            getPosOpNotice().getBuNengChuList().addAll(getPrivateCards().stream().map(k->k.cardID).collect(Collectors.toList()));
        }
        return opTypes;
    }


    @Override
    protected AbsBaseResults newResults() {
        return new AHHBMJResults();
    }

    @Override
    public <T> void calcOpPointType(T opType, int count) {
        ((AHHBMJCalcPosEnd) getCalcPosEnd()).calcOpPointType(opType, count);
    }

    @Override
    public boolean outCard(MJCard card) {
        if (getPosOpNotice().getBuNengChuList().contains(card.type)) {
            return false;
        }
        return super.outCard(card);

    }


    public int point(OpPointEnum opPointEnum) {
        int point = 1;
        AHHBMJRoomSet set = (AHHBMJRoomSet) getSet();
        switch (opPointEnum) {
            case DanTing:
                point = 2;
                break;
            case QDHu:
                point = set.isQIDUI_X2() ? 2 : 1;
                break;
            case SSL:
                point = set.isSSL_X2() ? 2 : 1;
                break;
            case GSKH:
            case QGH:
            case GSP:
                point = set.isGANGHU_X2() ? 2 : 1;
                break;
            case BianJiaDiao:
            case QueYiSe:
                point = isZimo() ? 2 : 1;
                break;
            case JiePao:
            case PingHu:
                point = 1;
                break;
            case QXSSL:
                point = set.isQXSSL_X4() ? 4 : set.isSSL_X2() ? 2 : 1;

                break;
            case ZiMo:
                point = ((AHHBMJRoom) getRoom()).getCfg().getZimofen() + 1;
                break;
            default:
                point = 0;

        }
        return point;
    }

    public boolean isZimo() {
        return getHuType().equals(HuType.ZiMo) || getHuType().equals(HuType.GSKH);
    }

    public boolean opZuoLaPao() {
        if (getSet().getDPos() == getPosID()) {
            return zuoFen >= 0 && paoFen >= 0;
        }
        return laFen >= 0 && paoFen >= 0;

    }

    public List<Integer> zuoLaPaoList() {
        List<Integer> list = new ArrayList<>();
        list.add(zuoFen);
        list.add(laFen);
        list.add(paoFen);
        return list;
    }

    public void updateZuoLaPaoList(List<Integer> list) {
        this.zuoFen = list.get(0);
        this.laFen = list.get(1);
        this.paoFen = list.get(2);
    }
}	
						
						
