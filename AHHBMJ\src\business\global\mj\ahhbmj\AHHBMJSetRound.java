package business.global.mj.ahhbmj;

import business.ahhbmj.c2s.cclass.AHHBMJRoom_RoundPos;
import business.ahhbmj.c2s.cclass.AHHBMJRoom_SetRound;
import business.ahhbmj.c2s.iclass.SAHHBMJ_PosOpCard;
import business.ahhbmj.c2s.iclass.SAHHBMJ_StartRound;
import business.global.mj.AbsMJRoundPos;
import business.global.mj.AbsMJSetRoom;
import business.global.mj.AbsMJSetRound;
import business.global.mj.set.MJOpCard;
import cenum.mj.MJOpCardError;
import cenum.mj.OpType;
import com.ddm.server.common.utils.CommTime;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.ddm.server.websocket.handler.requset.WebSocketRequestDelegate;
import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.mj.BaseMJRoom_SetRound;

import java.util.ArrayList;
import java.util.List;

/**
 * 淮北麻将 回合逻辑 每一次等待操作，都是一个round
 *
 * <AUTHOR>
 */

public class AHHBMJSetRound extends AbsMJSetRound {

    public boolean isZuoLaPao;

    public AHHBMJSetRound(AbsMJSetRoom set, int roundID) {
        super(set, roundID);
    }


    @Override
    public int opCard(WebSocketRequest request, int opPos, OpType opType, MJOpCard mOpCard) {
        return opCard(request, opPos, opType, mOpCard, false);
    }


    /**
     * @param request
     * @param opPos
     * @param opType
     * @param mOpCard
     * @param isFlash
     * @return
     */
    public synchronized int opCard(WebSocketRequest request, int opPos, OpType opType, MJOpCard mOpCard, boolean isFlash) {
        if (this.getEndTime() > 0) {
            request.error(ErrorCode.NotAllow, "end Time opPos has no round power");
            return MJOpCardError.ERROR_OP_TYPE.value();
        }
        AbsMJRoundPos pos = this.roundPosDict.get(opPos);
        if (null == pos) {
            request.error(ErrorCode.NotAllow, "opPos has no round power");
            return MJOpCardError.ROUND_POS_ERROR.value();
        }
        int opCardRet = pos.op(request, opType, mOpCard);
        if (opCardRet >= 0) {
            // 位置操作牌													
            this.posOpCardRet(opCardRet, isFlash);
        }
        return opCardRet;
    }

    /**
     * 自动出牌
     *
     * @param sec
     * @return
     */
    @Override
    protected boolean autoOutCard(int sec) {

        if (sec - this.startTime < 1) {
            return false;
        }
        AHHBMJRoundPos roundPos = null;
        AHHBMJSetPos sPos = null;
        AHHBMJRoomSet roomSet = null;
        AHHBMJSetCard card = null;
        int cardID;
        for (int posID = 0; posID < this.room.getPlayerNum(); posID++) {
            roundPos = (AHHBMJRoundPos) this.roundPosDict.get(posID);
            if (null == roundPos) {
                continue;
            }
            sPos = (AHHBMJSetPos) roundPos.getPos();
            if (null == sPos) {
                continue;
            }
            roomSet = (AHHBMJRoomSet) sPos.getSet();
            if (null == roomSet) {
                continue;
            }
            List<OpType> opList = roundPos.getRecieveOpTypes();
            if (null == opList || opList.size() <= 0) {
                continue;
            }
            if (!sPos.isTing()) {
                continue;
            }
            if (opList.contains(OpType.Gang) || opList.contains(OpType.JieGang) || opList.contains(OpType.AnGang) || opList.contains(OpType.Hu)) {
                continue;
            }
            if (opList.contains(OpType.Out)) {
                if (null != sPos.getHandCard()) {
                    cardID = sPos.getHandCard().cardID;
                } else {
                    continue;
                }
                this.opCard(new WebSocketRequestDelegate(), roundPos.getOpPos(), OpType.Out, MJOpCard.OpCard(cardID), true);
            }
        }

        return false;
    }


    @Override
    protected AbsMJRoundPos nextRoundPos(int pos) {
        return new AHHBMJRoundPos(this, pos);
    }

    /**
     * 尝试其他回合
     *
     * @param preRound 上回合
     * @return
     */
    @Override
    protected boolean tryStartRoundOther(AbsMJSetRound preRound) {
        // 上一轮接牌， 本轮继续出牌							
        if (preRound.getOpType() == OpType.BaoTing) {
            return tryStartRoundOut(preRound);
        }
        if (preRound.getOpType() == OpType.Piao_Fen) {
            return tryStartRounPiao_Fen();
        }
        if (preRound.getOpType() == OpType.Fan) {
            return tryStartRounPiao_Fen();
        }
        if (preRound.getOpType() == OpType.Pao) {
            return tryStartRounPiao_Fen();
        }
        return false;
    }

    private boolean tryStartRounPiao_Fen() {
        if (!atFirstZuoLaPao()) {
            ((AHHBMJRoomSet) getSet()).initSetPosCard1();
            getSet().getSetPosMgr().startSetApplique();
            set.setAtFirstHu(false);
            int dPos = getSet().getDPos();
            if (null == this.set.getMJSetPos(dPos).getHandCard()) {
                if (null == this.set.getCard(dPos, true)) {
                    return false;
                }
            }
            MJRoundPos(dPos);
        }
        notifyStart();
        return true;

    }

    private boolean atFirstZuoLaPao() {
        for (int i = 0; i < this.set.getRoom().getPlayerNum(); i++) {
            AHHBMJSetPos setPos = (AHHBMJSetPos) getSet().getMJSetPos(i);
            if (setPos.opZuoLaPao()) {
                continue;
            }
            AbsMJRoundPos nextPos = this.nextRoundPos(i);
            if (i == set.getDPos()) {
                if (setPos.getZuoFen() == -1) {
                    nextPos.addOpType(OpType.Piao_Fen);
                }
            } else {
                if (setPos.getLaFen() == -1) {
                    nextPos.addOpType(OpType.Fan);
                }
            }
            if (setPos.getPaoFen() == -1) {
                nextPos.addOpType(OpType.Pao);
            }
            this.roundPosDict.put(nextPos.getOpPos(), nextPos);
        }
        if (this.roundPosDict.size() > 0) {
            return true;
        }
        return false;

    }


    @Override
    protected boolean checkExistClearPass() {
        return false;
    }


    @Override
    protected <T> BaseSendMsg startRound(long roomID, T room_SetWait) {
        return SAHHBMJ_StartRound.make(roomID, room_SetWait);
    }

    @Override
    protected <T> BaseSendMsg posOpCard(long roomID, int pos, T set_Pos, OpType opType, int opCard, boolean isFlash) {
        return SAHHBMJ_PosOpCard.make(roomID, pos, set_Pos, opType, opCard, isFlash);
    }


/**
 * 机器人操作============================================================================================
 * <p>
 * 机器人操作
 *
 * @param posID
 */
    /**
     * 机器人操作
     *
     * @param posID
     */
    @Override
    public void RobothandCrad(int posID) {

        if (this.getEndTime() > 0) {
            return;
        }
        if (this.getRoundPosDict().containsKey(posID)) {
            new AHHBMJRobotOpCard(this).RobothandCrad(posID);
        }
    }


    /**
     * 下位置操作类型
     *
     * @param nextPos
     * @return
     */
    @Override
    public AbsMJRoundPos nextPosOpType(AbsMJRoundPos nextPos) {
        if (nextPos.getPos().checkOpType(0, OpType.TingYouJin)) {
            nextPos.addOpType(OpType.TingYouJin);
        } else {
            if (nextPos.getPos().checkOpType(0, OpType.Ting)) {
                nextPos.addOpType(OpType.Ting);
                if (!((AHHBMJSetPos) nextPos.getPos()).isTing()) {
                    nextPos.addOpType(OpType.BaoTing);
                }
            }
        }
        nextPos.addOpType(OpType.Out);
        return nextPos;
    }

    /**
     * 开始本回合,并摸牌
     *
     * @param pos
     * @param isNormalMo
     * @return
     */
    @Override
    public boolean startWithGetCard(int pos, boolean isNormalMo) {
        // 抓牌
        // 作弊情况下，已经有手牌
        AHHBMJRoomSet set = (AHHBMJRoomSet) getSet();
        if (set.isAtFirstHu()) {
            if (set.getRoom().RoomCfg(AHHBMJRoomEnum.KeXuanWanFa.ZUO_LA_PAO)) {
                if (atFirstZuoLaPao()) {
                    return true;
                }
            }
            ((AHHBMJRoomSet) getSet()).initSetPosCard1();
            getSet().getSetPosMgr().startSetApplique();
            set.setAtFirstHu(false);

        }
        if (null == this.set.getMJSetPos(pos).getHandCard()) {
            if (null == this.set.getCard(pos, isNormalMo)) {
                return false;
            }
        }
        return MJRoundPos(pos);

    }

    /**
     * 获取本轮信息
     *
     * @param pos 位置
     * @return
     */
    @Override
    public BaseMJRoom_SetRound getNotify_RoundInfo(int pos) {
        ret = new AHHBMJRoom_SetRound();
        ret.setWaitID(this.roundID);
        ret.setStartWaitSec(this.startTime);
        ret.setRunWaitSec(CommTime.nowSecond() - this.startTime);
        for (AbsMJRoundPos roundPos : this.roundPosDict.values()) {
            if (roundPos.getOpType() != null) {
                continue;
            }
            // 自己 或 公开
            ;
            if (pos == roundPos.getOpPos() || roundPos.isPublicWait()) {
                AHHBMJRoom_RoundPos data = new AHHBMJRoom_RoundPos();
                boolean isSelf = pos == roundPos.getOpPos();
                data.setOpList(roundPos.getRecieveOpTypes());
                data.setChiList(isSelf ? roundPos.getPos().getPosOpNotice().getChiList() : null);
                data.setLastOpCard(roundPos.checkRecieveOpTypes(OpType.QiangGangHu) ?
                        roundPos.getLastOpCard() : roundPos.getLastOutCard());
                data.setWaitOpPos(roundPos.getOpPos());
                data.setTingCardMap(isSelf ? roundPos.getPos().getPosOpNotice().getTingCardMap() : null);
                data.setBuChuList(isSelf ? roundPos.getPos().getPosOpNotice().getBuNengChuList() : null);
                AHHBMJSetPos setPos = (AHHBMJSetPos) roundPos.getPos();
                data.setGangList(isSelf ? new ArrayList<>(setPos.getGangList()) : null);
                int value = 4 - ((AHHBMJRoomPos) setPos.getRoomPos()).getPaoFen();
                data.setXiaPao(value >= 2 ? 2 : 1);
                ret.addOpPosList(data);
                // 设置动作列表
                roundPos.getPos().getPosOpRecord().setOpList(data.getOpList());
                if (room.isConnectClearTrusteeship()) {
                    // 重新记录打牌时间
                    roundPos.getPos().getRoomPos().setLatelyOutCardTime(CommTime.nowMS());
                }
                // 设置最后操作时间
                this.set.getLastOpInfo().setLastShotTime(CommTime.nowSecond());

            }
        }
        return ret;
    }

    /**
     * 过
     *
     * @param preRound
     * @return
     */
    @Override
    protected boolean tryStartRoundPass(AbsMJSetRound preRound) {
        if (preRound.getWaitDealRound() != null) {
            preRound = preRound.getWaitDealRound();
        }
        // 上次的出牌，需要继续处理
        if (preRound.checkExistNextRoundOp()) {
            // 检查下回合操作位置
            return this.checkNextRoundOpPos(preRound);
        } else {
            //其他操作
            if (passOther()) {
                return true;
            }
            // 检查是否直接过
            if (this.checkPass()) {
                return true;
            } else if (preRound.getOpType() == OpType.Out || preRound.getOpType() == OpType.BaoTing) {
                // 无法再处理了，下家抓牌
                // 获取用户位置ID
                return checkQtherPing();
            } else if (preRound.getOpType() == OpType.Gang) {
                return checkQtherQiang();
            }
            return true;
        }
    }

}
													
												
												
