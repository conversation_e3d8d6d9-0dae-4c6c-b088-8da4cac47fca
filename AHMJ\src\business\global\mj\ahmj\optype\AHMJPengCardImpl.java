package business.global.mj.ahmj.optype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCard;
import business.global.mj.ahmj.AHMJRoomSet;
import business.global.mj.ahmj.AHMJSetPos;
import business.global.mj.op.PengCardImpl;
import java.util.List;

/**
 * 检查碰
 *
 * <AUTHOR>
 */
public class AHMJPengCardImpl extends PengCardImpl {

	@Override public boolean checkOpCard(AbsMJSetPos mSetPos, int cardId) {
		if (((AHMJSetPos) mSetPos).isTing()) {
			return false;
		}
		boolean existPeng = super.checkOpCard(mSetPos, cardId);
		if(existPeng){
			List<MJCard> cardList = mSetPos.allCards();
			AHMJRoomSet set = (AHMJRoomSet)mSetPos.getSet();
			boolean existNonJin = cardList.stream().anyMatch(n -> cardId/100!=n.type && !set.getmJinCardInfo().checkJinExist(n.type));
			boolean isCanGang = cardList.stream().filter(n -> cardId/100==n.type).count() > 2;
			if(existNonJin||isCanGang){
				return true;
			}
			return false;
		}
		return false;
	}
}
