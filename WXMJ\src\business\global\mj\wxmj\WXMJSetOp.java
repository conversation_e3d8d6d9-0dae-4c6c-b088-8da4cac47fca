package business.global.mj.wxmj;

import business.global.mj.AbsMJSetOp;
import business.global.mj.MJCard;
import business.global.mj.wxmj.hutype.WXMJHuImpl;
import business.global.mj.hu.DDHuCardImpl;
import business.global.mj.hu.NormalHuCardImpl;
import business.global.mj.hu.QiangGangHuCardImpl;
import business.global.mj.wxmj.WXMJRoomEnum.WXMJOpPoint;
import business.global.mj.wxmj.hutype.WXMJTingImpl;
import business.global.mj.manage.MJFactory;
import business.global.mj.op.*;
import cenum.mj.MJHuOpType;
import cenum.mj.OpType;

import java.util.ArrayList;
import java.util.List;

public class WXMJSetOp extends AbsMJSetOp {
    // 操作列表		
    private List<OpType> opTypes = new ArrayList<>();
    // 玩家信息		
    private WXMJSetPos mSetPos;

    public WXMJSetOp(WXMJSetPos mSetPos) {
        super();
        this.mSetPos = mSetPos;
    }

    @Override
    public boolean doOpType(int cardID, OpType opType) {
        boolean doOpType = false;
        switch (opType) {
            case AnGang:
                mSetPos.getPosOpNotice().clearBuNengChuList();
                doOpType = MJFactory.getOpCard(AnGangCardImpl.class).doOpCard(mSetPos, cardID);
                this.addOp(doOpType, OpType.AnGang, cardID / 100);
                break;
            case Gang:
                mSetPos.getPosOpNotice().clearBuNengChuList();
                doOpType = MJFactory.getOpCard(GangCardImpl.class).doOpCard(mSetPos, cardID);
                this.addOp(doOpType, OpType.AnGang, cardID / 100);
                break;
            case JieGang:
                mSetPos.getPosOpNotice().clearBuNengChuList();
                doOpType = MJFactory.getOpCard(JieGangCardImpl.class).doOpCard(mSetPos, cardID);
                this.addOp(doOpType, OpType.AnGang, cardID / 100);
                break;
            case Peng:
                mSetPos.getPosOpNotice().clearBuNengChuList();
                doOpType = MJFactory.getOpCard(PengCardImpl.class).doOpCard(mSetPos, cardID);
                break;
            case Chi:
                doOpType = MJFactory.getOpCard(ChiCardNormalImpl.class).doOpCard(mSetPos, cardID);
                mSetPos.getPosOpNotice().getBuNengChuList().add(mSetPos.getSet().getLastOpInfo().getLastOutCard() / 100);
                break;
            case JiePao:
            case Hu:
                doOpType = doPingHu();
                break;
            case QiangGangHu:
                doOpType = MJFactory.getHuCard(QiangGangHuCardImpl.class).checkHuCard(mSetPos.getMJSetPos());
                if (doOpType)
                    mSetPos.setmHuOpType(MJHuOpType.QGHu);
                break;
            case TianHu:
                doOpType = true;
                break;
            default:
                break;
        }
        return doOpType;
    }

    @Override
    public boolean checkOpType(int cardID, OpType opType) {
        int cardType = cardID / 100;
        boolean isOpType = false;
        switch (opType) {
            case AnGang:
                isOpType = MJFactory.getOpCard(AnGangCardImpl.class).checkOpCard(mSetPos, 0);
                break;
            case Gang:
                isOpType = MJFactory.getOpCard(GangCardImpl.class).checkOpCard(mSetPos, cardID);
                break;
            case JieGang:
                isOpType = MJFactory.getOpCard(JieGangCardImpl.class).checkOpCard(mSetPos, cardID);
                break;
            case Peng:
                isOpType = MJFactory.getOpCard(PengCardImpl.class).checkOpCard(mSetPos, cardID);
                break;
            case Ting:
                isOpType = MJFactory.getTingCard(WXMJTingImpl.class).checkTingList(mSetPos);
                break;
            case Chi:
                isOpType = MJFactory.getOpCard(ChiCardNormalImpl.class).checkOpCard(mSetPos, cardID);
                break;
            case JiePao:
                return MJFactory.getHuCard(WXMJHuImpl.class).checkHuCard(mSetPos, mSetPos.mCardInit(cardType, true));
            case TianHu:
            case Hu:
                if (MJFactory.getHuCard(WXMJHuImpl.class).checkHuCard(mSetPos, mSetPos.mCardInit(cardType, true))) {
                    return true;
                }
                return false;
            default:
                break;
        }
        return isOpType;
    }


    /**
     * 清除所有动作
     */
    public void clearOp() {
        this.opTypes.clear();
    }

    /**
     * 添加动作
     *
     * @param doOpType 是否操作成功
     * @param opType   动作类型
     */
    public void addOp(boolean doOpType, OpType opType, int cardID) {
        if (doOpType) {
            this.opTypes.add(opType);
        }
    }

    public boolean doPingHu() {
        if (MJHuOpType.JiePao.equals(mSetPos.getmHuOpType())) {
            int lastOutCard = mSetPos.getSet().getLastOpInfo().getLastOutCard();
            if (lastOutCard > 0) {
                mSetPos.setHandCard(new MJCard(lastOutCard));
            }
        }
        return true;
    }

    public int opSize() {
        return this.opTypes.size();
    }


    /**
     * 检查动作
     *
     * @param opType
     * @return
     */
    public boolean isOp(OpType opType) {
        return this.opTypes.contains(opType);
    }

    @Override
    public void clear() {
        this.mSetPos = null;
    }

}			
