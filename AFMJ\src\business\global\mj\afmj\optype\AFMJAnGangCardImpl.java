package business.global.mj.afmj.optype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.op.BaseAnGang;
import cenum.mj.MJSpecialEnum;

/**
 * 检查暗杠
 */
public class AFMJAnGangCardImpl extends BaseAnGang {
	
	/**
	 * 检查过滤器
	 */
	@Override
	protected boolean checkFilter(AbsMJSetPos mSetPos, int type) {
//		return !mSetPos.getSet().getmJinCardInfo().checkJinExist(type) && type < MJSpecialEnum.NOT_HUA.value();
		// 操作：正副精都可以打出，可以当本身进行吃、碰、杠
		return type < MJSpecialEnum.NOT_HUA.value();
	}



}
