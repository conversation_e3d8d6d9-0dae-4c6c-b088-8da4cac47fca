package core.network.client2game.handler.wxmj;			
			
import java.io.IOException;			
			
import com.ddm.server.websocket.def.ErrorCode;			
import com.ddm.server.websocket.handler.requset.WebSocketRequest;			
import com.google.gson.Gson;			
			
import business.global.mj.wxmj.WXMJRoom;			
import business.global.room.RoomMgr;			
import business.player.Player;			
import business.wxmj.c2s.iclass.CWXMJ_PiaoFen;			
import core.network.client2game.handler.PlayerHandler;			
			
/**			
 * 打牌			
 * 			
 * <AUTHOR>			
 *			
 */			
public class CWXMJPiaoFen extends PlayerHandler {			
			
	@Override			
	public void handle(Player player, WebSocketRequest request, String message) throws IOException {			
		final CWXMJ_PiaoFen req = new Gson().fromJson(message, CWXMJ_PiaoFen.class);			
		long roomID = req.roomID;			
			
		WXMJRoom room = (WXMJRoom) RoomMgr.getInstance().getRoom(roomID);			
		if (null == room) {			
			request.error(ErrorCode.NotAllow, "CNAMJOpCard not find room:" + roomID);			
			return;			
		}			
			
		room.opPiaoFen(request, player.getId(), req);			
	}			
}			
