package business.global.mj.afmj;

import business.global.mj.AbsMJSetPos;
import business.global.mj.AbsMJSetPosMgr;
import business.global.mj.AbsMJSetRoom;
import business.global.mj.MJCard;
import business.global.mj.afmj.optype.AFMJBuHuaImpl;
import business.global.mj.manage.MJFactory;
import cenum.mj.*;
import jsproto.c2s.cclass.mj.NextOpType;
import jsproto.c2s.cclass.mj.OpTypeInfo;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import business.global.mj.afmj.AFMJRoomEnum.*;


public class AFMJSetPosMgr extends AbsMJSetPosMgr {
	private final AFMJRoomSet set;
	private boolean isExistYPDX;


	public AFMJSetPosMgr(AbsMJSetRoom set) {
		super(set);
		this.set = (AFMJRoomSet) set;
	}

	/**
	 * 开局补花
	 */
	@Override
	public void startSetApplique() {
		// 标识是否有开局补花
		boolean isStartSetApplique = false;
		AbsMJSetPos mPos = null;
		for (int i = 0; i < this.set.getPlayerNum(); i++) {
			int index = (this.set.getDPos() + i) % this.set.getPlayerNum();
			mPos = this.set.getMJSetPos(index);
			if (null == mPos) {
				continue;
			}
			if (MJFactory.getOpCard(AFMJBuHuaImpl.class).checkOpCard(mPos, FlowerEnum.PRIVATE.ordinal())) {
				isStartSetApplique = true;
				((AFMJSetOp) mPos.getmSetOp()).addOp(mPos, true, OpType.BuHua);//操作链增加补花
			}
		}
		if (isStartSetApplique) {
			// 存在开局补花。
			startSetApplique();
		} else {
			return;
		}
	}

	@Override
	public void checkOpType(int curOpPos, int curCardID, OpType opType) {
		// 清空所有动作类型操作
		this.cleanAllOpType();
		switch (opType) {
			case Out:
				checkOutOpType(curOpPos, curCardID);
				break;
			case Gang:
				checkOpTypeQGH(curOpPos, curCardID);   // 检查抢杠胡
				break;
			case TianHu:
				this.checkAtFirstHu();
				break;
			default:
				break;
		}
	}



	/**
	 * 检查起手胡
	 */
	private void checkAtFirstHu() {
		// 庄家天胡>庄家天听>闲家天听；
		// 检查庄家胡
		checkDPosHu();
	}

	/**
	 * 检查庄家胡牌
	 */
	private void checkDPosHu() {
		AbsMJSetPos mPos = this.set.getMJSetPos(this.set.getDPos());
		// 天胡：庄家起手就胡牌；（天地胡依然需要满足胡牌条件，例“博精”）
		if(((AFMJRoom)set.getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoYiJing)
				|| ((AFMJRoom)set.getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoZhengJingHuoLiangFu)){
			for(MJCard mjCard : mPos.allCards()){
				if(set.getmJinCardInfo().checkJinExist(mjCard.type)){
					return;
				}
			}
		}
		if (mPos.getPosOpRecord().getOpHuList().size() <= 0) {
			if (mPos.checkOpType(0, OpType.Hu)) {
				this.addOpTypeInfo(this.set.getDPos(), OpType.Hu);
				mPos.setmHuOpType(MJHuOpType.ZiMo); // 设置麻将胡的动作类型:自摸
				mPos.getPosOpRecord().clearOpHuList(); // 天地胡不叠加其他牌型
				mPos.getPosOpRecord().addOpHuList(AFMJOpPoint.TianHu); // 天胡
//				mPos.getPosOpRecord().addOpHuList(AFMJOpPoint.Hu); // 胡
			}
		}
	}


	@Override
	public NextOpType exeCardAction(OpType opType) {
		NextOpType nOpType = null;
		switch (opType) {
			case Out:
			case Gang:
			case TianHu:
				nOpType = opAllMapOutCard();
				break;
			default:
				break;
		}
//		if (null == nOpType)
//			set.setDoOpType(OpType.Not);
		return nOpType;
	}

	public AbsMJSetPos getSetPos(int opPos) {
		if (opPos < 0) {
			return null;
		}
		AbsMJSetPos setPos = this.set.getMJSetPos(opPos);
		return setPos;
	}

	public void checkOpTypeQGH(int curOpPos, int curCardID) {
		check_QiangGangHu(curOpPos, curCardID);
	}

	/**
	 * 检测可以抢杠胡的操作者
	 *
	 * @param curOpPos  操作者位置
	 * @param curCardID 操作牌
	 */
	public void check_QiangGangHu(int curOpPos, int curCardID) {
//		if (!this.set.getRoom().RoomCfg()){
//			return;
//		}
		AFMJSetPos setPos = null;
		for (int i = 1; i < this.set.getRoom().getPlayerNum(); i++) {
			int nextPos = (curOpPos + i) % this.set.getRoom().getPlayerNum();
			setPos = (AFMJSetPos) this.set.getMJSetPos(nextPos);
			setPos.getPosOpRecord().clearOpHuList();
			setPos.clearMCardInit();// 加上抽上来的牌
			if (setPos.sizeHuCardTypes() >= MJSpecialEnum.TING.value()) {
				continue;
			}

			if (setPos.checkOpType(curCardID, OpType.QiangGangHu)) {
				this.addOpTypeInfo(nextPos, OpType.QiangGangHu);
			}
		}
	}


	public void checkOutOpType(int curOpPos, int curCardID) {

//		// 赖子打出，任何玩家都不能吃/碰/胡这张赖子，（打出去后算杠、两番）
//		if(this.set.getmJinCardInfo().checkJinExist(curCardID)){
//			return;
//		}

		// 从同一张牌产生游戏事件的优先级来说：胡牌>杠牌>碰牌>吃牌。

//		check_otherPingHu(curOpPos, curCardID);

		if (!checkDiHu(curOpPos, curCardID)) {
			check_otherPingHu(curOpPos, curCardID);
		}



		// 勾选“精不能吃碰”，不可吃、碰、杠别人打出来的精牌；
		if(((AFMJRoom)set.getRoom()).RoomCfg(AFMJKeXuanWanFa.JinGBuNengChiPeng)
				&& this.set.getmJinCardInfo().checkJinExist(curCardID)){
			return;
		}


		// 检查玩家接杠
		check_otherJieGang(curOpPos, curCardID);
		// 检查玩家碰
		check_otherPeng(curOpPos, curCardID);
		// 检查玩家能否吃牌
		check_LowerChi(curOpPos, curCardID);


	}

	/**
	 * 检查下家 吃
	 *
	 * @param curOpPos  当前操作位置ID
	 * @param curCardID 当前操作牌ID
	 */
	protected void check_LowerChi(int curOpPos, int curCardID) {
		// 检查是否存在吃牌
		if (!this.checkExistChi()) {
			return;
		}

		int nextPos = (curOpPos + 1) % this.set.getRoom().getPlayerNum();
		AbsMJSetPos setPos = this.set.getMJSetPos(nextPos);
		if (setPos.checkOpType(curCardID, OpType.Chi)) {
			// 添加动作信息
			this.addOpTypeInfo(nextPos, OpType.Chi);
			return;
		}
	}

	public void check_otherPingHu(int curOpPos, int curCardID) {
		// 如果别人打的是金 则不能吃炮
		// 精当本身用的时候，可以胡别人打出的精；
		/*if (this.set.getmJinCardInfo().checkJinExist(curCardID / 100)) {
			return;
		}*/
		List<Integer> list = new ArrayList<>();
		for (int i = 1; i < this.set.getRoom().getPlayerNum(); i++) {
			int nextPos = (curOpPos + i) % this.set.getRoom().getPlayerNum();
			AbsMJSetPos setPos = this.set.getMJSetPos(nextPos);
			OpType oType = setPos.checkPingHu(nextPos, curCardID);
			if (!OpType.Not.equals(oType)) {
				list.add(nextPos);
//				break; // 一炮多响：无，多家能同时炮胡的时候，点炮下家优先胡；
//				this.addOpTypeInfo(nextPos, OpType.JiePao);
			}
		}
		// 添加胡位置列表
		this.addHuPosList(list, OpType.JiePao);

	}

	/**
	 * 添加胡位置列表
	 * @param list
	 * @param opType 动作
	 */
	private void addHuPosList (List<Integer> list,OpType opType) {
		this.isExistYPDX = false;
		// 没有人胡
		if (list.size() <= 0)  {
			return;
		}
//		// 检查胡
////		if(list.size() >= 3) {
//		if(list.size() >= 2) {
//			// 一炮多响模式
//			this.isExistYPDX = true;
//			set.setYiPaoDuoXiangPosCount(list.size());
//		}
		// 添加动作顺序
		for (Integer posId : list) {
			// 添加动作信息
			this.addOpTypeInfo(posId, opType, isExistYPDX);
		}
	}

	/**
	 * 添加动作信息
	 * @param posId 位置
	 * @param opType 动作类型
	 */
	protected void addOpTypeInfo(Integer posId,OpType opType,boolean isExistYPDX) {
		if (null == this.opTypeInfoList) {
			this.opTypeInfoList = new ArrayList<>();
		}
		int count = this.opTypeInfoList.size();
		// 检查是否存在一炮多响
		if(isExistYPDX) {
			// 存在一炮多响
			if (!HuType.NotHu.equals(MJCEnum.OpHuType(opType))) {
				count = 0;
				this.huPosList.add(posId);
			}
		}
		// 添加动作信息
		this.opTypeInfoList.add(new OpTypeInfo(count+1, posId, opType));
	}



	@Override
	protected boolean checkExistPingHu() {
		return true;
	}

	public void check_otherPeng(int curOpPos, int curCardID) {
		for (int i = 1; i < this.set.getRoom().getPlayerNum(); i++) {
			int nextPos = (curOpPos + i) % this.set.getRoom().getPlayerNum();
			AFMJSetPos setPos = (AFMJSetPos) this.set.getMJSetPos(nextPos);

			// 检查碰动作
			if (setPos.checkOpType(curCardID, OpType.Peng)) {
				int pengCard = curCardID / 100;
				if (!setPos.getPosOpRecord().isOpCardType(pengCard)) { // 检测漏碰
					setPos.getPosOpRecord().setOpCardType(pengCard);
					this.addOpTypeInfo(nextPos, OpType.Peng);
				}
				return;
			}
		}
	}

	// 检测其他人是否可以接杠
	public void check_otherJieGang(int curOpPos, int curCardID) {

		if (this.set.isLastCard()) return; // 牌堆没有可摸的牌时，不可以开杠；

		for (int i = 1; i < this.set.getRoom().getPlayerNum(); i++) {
			int nextPos = (curOpPos + i) % this.set.getRoom().getPlayerNum();
			AbsMJSetPos setPos = this.set.getMJSetPos(nextPos);
			if (setPos.checkOpType(curCardID, OpType.JieGang)) {
				this.addOpTypeInfo(nextPos, OpType.JieGang);
				return;
			}
		}
	}

	@Override
	protected boolean checkExistJGBG() {
		return true;
	}

	@Override
	protected boolean checkExistChi() {
		return true;
	}

	/**
	 * 检查是否存在一炮多响
	 * @return  true:存在一炮多响 , false:不存在一炮多响
	 */
	@Override
	protected boolean checkExistYPDX() {
		return false;
	}

	/**
	 * 三家都有杠则流局
	 */
	public void checkAllPosGang() {
		// 统计所有玩家共杠次数
		int gangCount = 0;
		for (int i = 0; i < this.set.getRoom().getPlayerNum(); i++) {
			AbsMJSetPos setPos = this.set.getMJSetPos(i);
			if (null != setPos) {
				// 统计所有玩家共杠次数
				gangCount += gangCount(setPos);
			}
		}
		if (gangCount >= 3) {
			this.set.endSet();
		}
	}

	/**
	 * 统计玩家杠次数
	 */
	public int gangCount(AbsMJSetPos mSetPos) {
		List<List<Integer>> publicCardList = new ArrayList<>();
		publicCardList.addAll(mSetPos.getPublicCardList());
		int count = 0;
		for (List<Integer> publicCards : publicCardList) {
			int type = publicCards.get(0);
			if (type == OpType.JieGang.value() || type == OpType.AnGang.value() || type == OpType.Gang.value()) {
				return 1;
			}
		}
		return count;
	}

	/**
	 * 检查操作胡
	 */
	private boolean checkOpHu (OpTypeInfo oInfo) {
		// 不是胡跳过
		if (HuType.NotHu.equals(MJCEnum.OpHuType(oInfo.getOpType()))) {
			return false;
		}
		// 没操作胡
		return true;
	}

	/**
	 * 执行动作类型信息
	 */
	public OpTypeInfo exeOpTypeInfo(Integer opPos, OpType opType) {
		OpTypeInfo oInfo = this.exeOpTypeInfoNew(opPos, opType);
		// 操作胡检测一炮多响
		if (this.isExistYPDX) {
			// 是否存在动作信息
			if (null == oInfo) {
				return oInfo;
			}
			Map<Boolean, List<OpTypeInfo>> partitioned = this.opTypeInfoList.stream().collect(Collectors.partitioningBy(e -> checkOpHu(e)));
			// 检查分组数据是否存在
			if (null == partitioned || partitioned.size() <= 0) {
				return oInfo;
			}
			// 有操作胡 并且 并且操作胡人数 >=3
			if (partitioned.containsKey(Boolean.TRUE)) {
				List<OpTypeInfo> oInfos = partitioned.get(Boolean.TRUE);
				// 操作相应动作
				int op = 0;
				// 过
				int pass = 0;
				// 没操作
				int not = 0;
				for (OpTypeInfo opTypeInfo: oInfos) {
					if (opTypeInfo.getType() == 2) {
						op++;
					} else if (opTypeInfo.getType() == 1) {
						pass++;
					} else {
						not++;
					}
				}
				// 如果存在没有操作
				if (not > 0) {
					return new OpTypeInfo(-1, OpType.Not);
				} else {
					// 都操作了
					if (pass >= 3) {
						// 都操作了过
						return new OpTypeInfo(opPos, OpType.Pass);
					} else if (op >= 3) {
						// 都操作了胡
						this.set.endSet();
						return null;
					} else {
						for(int i = 1;i<this.set.getPlayerNum();i++) {
							int posID = (i+this.set.getLastOpInfo().getLastOpPos()) % this.set.getPlayerNum();
							for (OpTypeInfo opTypeInfo: oInfos) {
								if (opTypeInfo.getType() != 2) {
									continue;
								}
								if (opTypeInfo.getPosId() == posID) {
									return opTypeInfo;
								}
							}
						}

						return oInfo;
					}
				}
			} else {
				return oInfo;
			}
		} else {
			return oInfo;
		}
	}


	/**
	 * 执行动作类型信息
	 */
	public OpTypeInfo exeOpTypeInfoNew(Integer opPos,OpType opType) {
		// 检查是否有动作列表
		if (null == this.opTypeInfoList || this.opTypeInfoList.size() <= 0) {
			return null;
		}
		this.opTypeInfoList.sort((OpTypeInfo itme1, OpTypeInfo itme2) -> {
			if (itme1.getId() - itme2.getId() == 0) {
				// 排名一样，通过一样的位置排序(当前操作位置最前面)
				if (itme1.getPosId() == opPos) {
					return -1;
				}
			}
			// 排名排序(123...456)
			return itme1.getId() - itme2.getId();
		});
		// 移除对应的胡位置列表
		this.huPosList.remove(opPos);
		boolean isPass = false;
		// 动作信息
		OpTypeInfo oInfo = null;
		for (int i = 0,size = this.opTypeInfoList.size();i<size;i++) {
			oInfo = this.opTypeInfoList.get(i);
			if (null == oInfo) {
				continue;
			}
			// 是否当前操作玩家
			if (oInfo.getPosId() == opPos) {
				// 如果是操作指定动作
				if (opType.equals(oInfo.getOpType())) {
					// 设置类型
					oInfo.setType(2);
					this.opTypeInfoList.set(i,oInfo);
				} else {
					// 否则,过动作
					// 设置类型
					oInfo.setType(1);
					this.opTypeInfoList.set(i,oInfo);
				}
			}

			if (!isPass) {
				if (oInfo.getType() == 0) {
					// 第一个没操作
					isPass = true;
				} else if (oInfo.getType() == 2) {
					// 操作信息
					return oInfo;
				}
			}
		}
		if (!isPass) {
			// 全部玩家操作完-并且都点了过。
			return new OpTypeInfo(opPos, OpType.Pass);
		} else {
			// 排名前的玩家存在没操作的。
			return new OpTypeInfo(-1, OpType.Not);
		}
	}


	/**
	 * 检查地胡
	 */
	public boolean checkDiHu(int curOpPos, int curCardID) {
		// 当前操作者是否状态
		if (curOpPos != set.getDPos())
			return false;

		AbsMJSetPos setPos = this.set.getMJSetPos(set.getDPos());
		// 检查位置信息
		if (null == setPos) {
			return false;
		}
		// 检查庄家打出牌的数
		if (setPos.sizeOutCardIDs() != 1) {
			return false;
		}
		this.set.getLastOpInfo().setLastOpOutPos(curOpPos);
		List<Integer> list = new ArrayList<>();
		// 检查是否可以胡庄家的牌
		for (int i = 1; i < this.set.getRoom().getPlayerNum(); i++) {
			int nextPos = (curOpPos + i) % this.set.getRoom().getPlayerNum();
			setPos = this.set.getMJSetPos(nextPos);
			OpType oType = setPos.checkPingHu(curOpPos, curCardID);
			if (!OpType.Not.equals(oType)) {
				// 地胡
				// 如果胡的是平胡类型：（天地胡也需要满足该规则）
				// 手里有精只能自摸；
				// 其他牌型无限制；
				if(!set.isDaHu()){ // 平胡
					boolean isPresent = setPos.allCards().stream().anyMatch(k -> set.getmJinCardInfo().getJinKeys().contains(k.type));
					if(isPresent)return false;
				}
				list.add(nextPos);
				this.addOpTypeInfo(nextPos, OpType.JiePao);
				setPos.getPosOpRecord().removeOpHuList(AFMJOpPoint.Hu);
				setPos.getPosOpRecord().clearOpHuList(); // 天地胡不叠加其他牌型
				setPos.getPosOpRecord().addOpHuList(AFMJOpPoint.DiHu);
			}
		}

		if (list.size() > 0) {
			return true;
		}
		return false;
	}


	/**
	 * 添加动作信息
	 *
	 * @param posId  位置
	 * @param opType 动作类型
	 */
	protected void addOpTypeInfo(Integer posId, OpType opType) {
		if (null == this.opTypeInfoList) {
			this.opTypeInfoList = Collections.synchronizedList(new ArrayList<>());;
		}
		int count = this.opTypeInfoList.size();
		// 检查是否存在一炮多响
		if (this.checkExistYPDX()) {
			// 存在一炮多响
			if (!HuType.NotHu.equals(MJCEnum.OpHuType(opType))) {
				count = 0;
				this.huPosList.add(posId);
			}
		}
		// 添加动作信息
		this.opTypeInfoList.add(new OpTypeInfo(count + 1, posId, opType));
	}



}
