package business.a3pk.c2s.iclass;	
	
import business.a3pk.c2s.cclass.A3PKOutInfo;	
import com.ddm.server.common.utils.Lists;	
import jsproto.c2s.cclass.BaseSendMsg;	
	
import java.util.List;	
	
	
@SuppressWarnings("serial")	
public class SA3PK_OpCardEX extends BaseSendMsg {	
	
    public long roomID;	
    public List<A3PKOutInfo> outList = Lists.newArrayList();	
	
    public static SA3PK_OpCardEX make(long roomID, List<A3PKOutInfo> outList) {	
        SA3PK_OpCardEX ret = new SA3PK_OpCardEX();	
        ret.roomID = roomID;	
        ret.outList = outList;	
        return ret;	
    }	
}	
