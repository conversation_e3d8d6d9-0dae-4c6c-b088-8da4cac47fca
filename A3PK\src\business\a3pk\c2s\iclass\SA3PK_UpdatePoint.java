package business.a3pk.c2s.iclass;	
	
import jsproto.c2s.cclass.BaseSendMsg;	
	
import java.util.List;	
	
public class SA3PK_UpdatePoint<T> extends BaseSendMsg {	
    /**	
     * 房间id	
     */	
    public long roomID;	
    public List<T> setPosList;	
    /**	
     * 是否顺便清空510K 公共牌	
     */	
    public boolean isClear;	
	
    /**	
     * 获取510K分数的位置	
     */	
    public int k510PosId;	
	
    public static <T> SA3PK_UpdatePoint<T> make(long roomID,  List<T> setPosList,boolean isClear,int k510PosId) {	
        SA3PK_UpdatePoint ret = new SA3PK_UpdatePoint();	
        ret.setRoomID(roomID);	
        ret.setSetPosList(setPosList);	
        ret.setClear(isClear);	
        ret.setK510PosId(k510PosId);	
        return ret;	
	
    }	
	
    public int getK510PosId() {	
        return k510PosId;	
    }	
	
    public void setK510PosId(int k510PosId) {	
        this.k510PosId = k510PosId;	
    }	
	
    public long getRoomID() {	
        return roomID;	
    }	
	
    public void setRoomID(long roomID) {	
        this.roomID = roomID;	
    }	
	
    public List<T> getSetPosList() {	
        return setPosList;	
    }	
	
    public void setSetPosList(List<T> setPosList) {	
        this.setPosList = setPosList;	
    }	
	
    public boolean isClear() {	
        return isClear;	
    }	
	
    public void setClear(boolean clear) {	
        isClear = clear;	
    }	
}	
	
