package business.global.pk.wxls;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerData;
import business.global.room.base.AbsBaseRoom;
import business.global.room.base.AbsRoomPos;	
import business.wxls.c2s.cclass.WXLSRoomPosInfo;	
import business.wxls.c2s.iclass.CWXLS_Ranked;	
import business.wxls.c2s.iclass.SWXLS_CardReadyChg;	
import cenum.room.RoomState;
import com.ddm.server.common.CommLogD;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;	
import core.db.entity.clarkGame.ClubMemberBO;	
	

public class WXLSRoomPos extends AbsRoomPos {	
	
	// 十三水	
	private boolean isCardReady = false; // 是否已经准备好，全部准备好，才能开始进行第一轮游戏
	private int tuoGuanSetCount = 0; // 连续托管局数
	public WXLSRoomPos(int posID, AbsBaseRoom room) {	
		super(posID, room);	
	}	
	
		
	/**	
	 * 玩家的牌准备	
	 * 	
	 * @param request	
	 * @param isReady	
	 * @param pid	
	 */	
	public void playerCardReady(WebSocketRequest request, boolean isReady, long pid, CWXLS_Ranked cRanked) {	
		if (this.getRoom().getRoomState() != RoomState.Playing) {	
			request.error(ErrorCode.NotAllow, "cur state：" + this.getRoom().getRoomState().toString());	
			return;	
		}	
		setCardReady(isReady,cRanked);
		request.response();
	}	
	public boolean seatPos(long pid, int initPoint, boolean isReady, ClubMemberBO clubMemberBO) {	
		return this.doSeat(pid, initPoint, false, isReady, clubMemberBO);	
	}	
		
	/**	
	 * 十三水 设置准备	
	 * 	
	 * @param isReady	
	 */	
	public void setCardReady(boolean isReady, CWXLS_Ranked cRanked) {	
		if (this.isCardReady)	
			return;	
		this.isCardReady = isReady;
		CommLogD.error(" playerCardReady  true first:{},second,:{},third:{},pid：{},roomID：{},setId:{},isTuoGuan:{},isReady:{}",cRanked.dunPos.first,cRanked.dunPos.second,cRanked.dunPos.third,this.getPid(),getRoom().getRoomID(),getRoom().getCurSetID(),isTrusteeship(),isReady);
		this.setLatelyOutCardTime(0L);
		for(AbsRoomPos roomPos:this.getRoom().getRoomPosMgr().getPosList()){	
			WXLSRoomPos WXLSRoomPos =(WXLSRoomPos)roomPos;	
			if(WXLSRoomPos.getPid()<=0L) continue;	
			CWXLS_Ranked ranked=new CWXLS_Ranked();	
			if (WXLSRoomPos.getPosID()== this.getPosID()){	
				ranked=cRanked;	
			}	
			this.getRoom().getRoomPosMgr().notify2Pos(WXLSRoomPos.getPosID(),(SWXLS_CardReadyChg.make(this.getRoom().getRoomID(), this.getPosID(), isReady,ranked,cRanked.isSpecial)));	
		}	
	}	
	
	/**	
	 * 十三水 清除牌序准备状态	
	 */	
	public void clearCardReady() {	
		this.isCardReady = false;	
	}	
	
	/**	
	 * 十三水 获取牌序状态	
	 * 	
	 * @return	
	 */	
	public boolean isCardReady() {	
		return this.isCardReady;	
	}	
	
	@Override	
	public WXLSRoomPosInfo getNotify_PosInfo() {	
		WXLSRoomPosInfo tmPos = (WXLSRoomPosInfo) this.getRoomPosInfo();	
		tmPos.setTrusteeship(this.isTrusteeship());// 托管状态	
		tmPos.isCardReady = isCardReady;	
		return tmPos;	
	}	
	/**	
	 * 重新选择位置	
	 */	
	public void resetSelectPos() {	
		this.clear();	
		this.getRoom().getRoomTyepImpl().roomPlayerChange(this);	
	}	
	/**	
	 * 新房间位置信息	
	 * @return	
	 */	
	@Override	
	public WXLSRoomPosInfo newRoomPosInfo() {	
		return new WXLSRoomPosInfo();	
	}
	public int getTuoGuanSetCount() {
		return tuoGuanSetCount;
	}

	public void setTuoGuanSetCount(int tuoGuanSetCount) {
		this.tuoGuanSetCount = tuoGuanSetCount;
	}
	/**
	 * 增加托管局数
	 */
	public void addTuoGuanSetCount(){
		tuoGuanSetCount += 1;
	}

	/**
	 * 连续托管局数清零
	 */
	public void clearTuoGuanSetCount(){
		tuoGuanSetCount = 0;
	}
	/**
	 * 设置托管状态
	 *
	 * @param isTrusteeship 托管状态
	 * @param isOwn         是否屏蔽自己
	 */
	@Override
	public void setTrusteeship(boolean isTrusteeship, boolean isOwn) {
		if (this.isTrusteeship() == isTrusteeship) {
			return;
		}
		// 托管2小局解散：连续2局托管
		if(!isTrusteeship){ // 玩家取消托管，连续托管局数清零
			this.clearTuoGuanSetCount();
		}
		this.setTrusteeship(isTrusteeship);
		if (isOwn) {
			this.getRoom().getRoomPosMgr().notify2ExcludePosID(this.getPosID(), this.getRoom().Trusteeship(this.getRoom().getRoomID(), this.getPid(), this.getPosID(), this.isTrusteeship()));
		} else {
			this.getRoom().getRoomPosMgr().notify2All(this.getRoom().Trusteeship(this.getRoom().getRoomID(), this.getPid(), this.getPosID(), this.isTrusteeship()));
		}
	}
}	
