package core.server.afmj;

import core.config.server.GameTypeMgr;
import core.server.GameServer;
import jsproto.c2s.cclass.GameType;

/**
 * 单游戏启动项
 *
 */
public class AFMJAPP {

	public final static int gameTypeId = 399;  // 江西吉安安福麻将

	/**
	 * 游戏类型
	 */
	public static GameType GameType() {
		return GameTypeMgr.getInstance().gameType(gameTypeId);
	}

	public static void main(String[] args) throws Exception {
		GameServer app = new GameServer();
		app.init(args);
	}
}
