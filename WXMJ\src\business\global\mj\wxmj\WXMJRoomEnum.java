package business.global.mj.wxmj;

/**
 * 宁德麻将配置
 *
 * <AUTHOR>
 */
public class WXMJRoomEnum {


    /**
     * 玩法
     *
     * <AUTHOR>
     */
    public enum WXMJJieSuan {
        anhuasuan,
        sanhuashengji,
        sihuashengji,
        ;
    }


    /**
     * 自选茶台 查士泰
     *
     * <AUTHOR>
     */
    public enum WXMJPiaoFenGuiZe {
        no, otf, ftf,
        ;
    }

    /**
     * 自选茶台 查士泰
     *
     * <AUTHOR>
     */
    public enum WXMJPiaoFenLeiXing {
        gudingpiao, jujupiao,
        ;
    }

    /**
     * 自选茶台 查士泰
     *
     * <AUTHOR>
     */
    public enum WXMJCfg {
        youhubihu, lianzhuangbusuanbashu;
    }

    public enum WXMJPiaoFen {
        NOT_OP(-1), // 没有操作		
        NOT_PIAO(0), PIAO1(1), // 飘1分		
        PIAO2(2), // 飘2分		
        PIAO5(5), // 飘5分
        PIAO10(10), // 飘10分
        PIAO15(15), // 飘15分
        ;

        private int value;

        private WXMJPiaoFen(int value) {
            this.value = value;
        }

        public int value() {
            return value;
        }

        public static WXMJPiaoFen valueOf(int value) {
            for (WXMJPiaoFen flow : WXMJPiaoFen.values()) {
                if (flow.value == value) {
                    return flow;
                }
            }
            return WXMJPiaoFen.NOT_OP;
        }
    }

    /**
     * 动作分数
     *
     * <AUTHOR>
     */
    public enum WXMJOpPoint {
        Not(0), PH(2), // 平胡		
        DDH(1), // 對對胡
        DDC(1), // 大吊车
        GSH(1),//杠上花
        PF(1), //飘分
        HS(1), // 花数
        ZM(1),//自摸
        QGH(1),//抢杠胡
        ;

        private int value;

        private WXMJOpPoint(int value) {
            this.value = value;
        }

        public int value() {
            return this.value;
        }

        public void setValue(int value) {
            this.value = value;
        }
    }
}		
