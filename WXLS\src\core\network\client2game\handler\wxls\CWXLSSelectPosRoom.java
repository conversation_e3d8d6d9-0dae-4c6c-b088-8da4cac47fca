package core.network.client2game.handler.wxls;	
	
import business.global.pk.wxls.WXLSRoom;	
import business.global.room.RoomMgr;	
import business.player.Player;	
import business.wxls.c2s.iclass.CWXLS_SelectPosRoom;	
import com.ddm.server.websocket.def.ErrorCode;	
import com.ddm.server.websocket.handler.requset.WebSocketRequest;	
import com.google.gson.Gson;	
import core.network.client2game.handler.PlayerHandler;	
	
import java.io.IOException;	
/**	
 * 换位置	
 * <AUTHOR>	
 *	
 */	
public class CWXLSSelectPosRoom extends PlayerHandler {	
	
	
    @SuppressWarnings("rawtypes")	
    @Override	
    public void handle(Player player, WebSocketRequest request, String message) throws IOException {	
        final CWXLS_SelectPosRoom req = new Gson().fromJson(message, CWXLS_SelectPosRoom.class);	
        long roomID = req.roomID;	
	
	
        WXLSRoom room = (WXLSRoom) RoomMgr.getInstance().getRoom(roomID);	
        if (null == room){	
            request.error(ErrorCode.NotAllow, "CWXLSSelectPosRoom not find room:"+roomID);	
            return;	
        }	
	
        room.selectPosRoom(request, player.getId(),req.posID);	
    }	
}	
