package business.a3pk.c2s.iclass;	
	
import jsproto.c2s.cclass.BaseSendMsg;	
import jsproto.c2s.iclass.room.SBase_PosLeave;	
	
/**	
 * 位置离开通知	
 * 	
 * <AUTHOR>	
 *	
 */	
@SuppressWarnings("serial")	
public class SA3PK_PosLeave extends SBase_PosLeave {	
	
	public static SA3PK_PosLeave make(SBase_PosLeave posLeave) {	
		SA3PK_PosLeave ret = new SA3PK_PosLeave();	
		ret.setRoomID(posLeave.getRoomID());	
		ret.setPos(posLeave.getPos());	
		ret.setBeKick(posLeave.isBeKick());	
		ret.setOwnerID(posLeave.getOwnerID());	
		ret.setKickOutTYpe(posLeave.getKickOutTYpe());	
		ret.setMsg(posLeave.getMsg());	
		return ret;	
	}	
}	
