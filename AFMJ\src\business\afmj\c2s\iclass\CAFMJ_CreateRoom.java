package business.afmj.c2s.iclass;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.ddm.server.common.CommLogD;

import jsproto.c2s.cclass.room.BaseCreateRoom;

/**
 * 创建房间
 */
@SuppressWarnings("serial")
public class CAFMJ_CreateRoom extends BaseCreateRoom implements Cloneable, Serializable {

	private int fengding = -1;  // 封顶 : 150封顶,200封顶,300封顶,500封顶,不封顶

	private int piaojing = -1; // 飘精 : 不飘,博精,必博一精,必博正精或两副

	public int getFengding() {
		return fengding;
	}

	public void setFengding(int fengding) {
		this.fengding = fengding;
	}

	public int getPiaojing() {
		return piaojing;
	}

	public void setPiaojing(int piaojing) {
		this.piaojing = piaojing;
	}

	/**
	 * 对象之间的浅克隆【只负责copy对象本身，不负责深度copy其内嵌的成员对象】
	 * 
	 * @return
	 */
	@Override
	public CAFMJ_CreateRoom clone() {
		return (CAFMJ_CreateRoom) super.clone();
	}

	/**
	 * 实现对象间的深度克隆【从外形到内在细胞，完完全全深度copy】
	 * 
	 * @return
	 */
	public CAFMJ_CreateRoom deepClone() {
		// Anything 都是可以用字节流进行表示，记住是任何！
		CAFMJ_CreateRoom cookBook = null;
		try {

			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			ObjectOutputStream oos = new ObjectOutputStream(baos);
			// 将当前的对象写入baos【输出流 -- 字节数组】里
			oos.writeObject(this);

			// 从输出字节数组缓存区中拿到字节流
			byte[] bytes = baos.toByteArray();

			// 创建一个输入字节数组缓冲区
			ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
			// 创建一个对象输入流
			ObjectInputStream ois = new ObjectInputStream(bais);
			// 下面将反序列化字节流 == 重新开辟一块空间存放反序列化后的对象
			cookBook = (CAFMJ_CreateRoom) ois.readObject();

		} catch (Exception e) {
			CommLogD.error(e.getClass() + ":" + e.getMessage());
		}
		return cookBook;
	}

	@Override
	public int hashCode() {
		return HashCodeBuilder.reflectionHashCode(this);
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}

	@Override
	public boolean equals(Object obj) {
		return EqualsBuilder.reflectionEquals(this, obj);
	}
}