package core.network.client2game.handler.wxls;	
	
import business.global.room.RoomMgr;	
import business.global.room.base.AbsBaseRoom;	
import business.player.Player;	
import business.wxls.c2s.iclass.CWXLS_RoomRecord;	
import com.ddm.server.websocket.def.ErrorCode;	
import com.ddm.server.websocket.handler.requset.WebSocketRequest;	
import com.google.gson.Gson;	
import core.network.client2game.handler.PlayerHandler;	
	
import java.io.IOException;	
	
/**	
 * 房间记录	
 * <AUTHOR>	
 *	
 */	
public class CWXLSRoomRecord extends PlayerHandler {	
		
	
    @SuppressWarnings("rawtypes")	
	@Override	
    public void handle(Player player, WebSocketRequest request, String message) throws IOException {	
    	final CWXLS_RoomRecord req = new Gson().fromJson(message, CWXLS_RoomRecord.class);	
    	long roomID = req.roomID;	
	
		AbsBaseRoom room = RoomMgr.getInstance().getRoom(roomID);	
    	if (null == room){	
    		request.error(ErrorCode.NotAllow, "SSSSStartGame not find room:"+roomID);	
    		return;	
    	}	
	
    	request.response(room.getRecord());	
    }	
}	
