package business.global.pk.wxls.newwxls.comparing;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
	
import java.util.ArrayList;	
import java.util.List;	
import java.util.Map;	
	
/**	
 * 三条的大小比较(直接比较三条)	
 */	
public class WXLSThreeOfTheKindComparingImpl extends WXLSAbstractComparing {	
	
    @Override	
    public int compare(WXLSPlayerDun o1, WXLSPlayerDun o2) {	
        List<WXLSPockerCard> newcards1 = new ArrayList<WXLSPockerCard>();	
        List<WXLSPockerCard> newcards2 = new ArrayList<WXLSPockerCard>();	
        newcards1 = o1.getCards();	
        newcards2 = o2.getCards();	
        Map<Integer, Integer> p1CardMap = getCardsRankCountMap(newcards1);	
        Map<Integer, Integer> p2CardMap = getCardsRankCountMap(newcards2);	
        int ret = this.multiComparing(p1CardMap, p2CardMap, 3);	
        return ret;	
    }	
	
}	
