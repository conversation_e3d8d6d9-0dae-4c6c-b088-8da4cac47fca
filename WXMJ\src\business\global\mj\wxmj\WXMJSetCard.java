package business.global.mj.wxmj;

import business.global.mj.AbsMJSetCard;
import business.global.mj.MJCard;
import business.global.mj.RandomCard;
import cenum.mj.MJCardCfg;

import java.util.ArrayList;
import java.util.List;

/**
 * 麻将 每一局麻将底牌信息 抓牌人是逆时针出手 牌是顺时针被抓
 *
 * <AUTHOR>
 */
public class WXMJSetCard extends AbsMJSetCard {
    public WXMJRoomSet set;

    public WXMJSetCard(WXMJRoomSet set) {
        this.set = set;
        this.room = set.getRoom();
        this.randomCard();
    }

    /**
     * 洗牌
     */
    @Override
    public void randomCard() {
        List<MJCardCfg> mCfgs = new ArrayList<MJCardCfg>();
        mCfgs.add(MJCardCfg.WANG);
        mCfgs.add(MJCardCfg.TIAO);
        mCfgs.add(MJCardCfg.TONG);
        mCfgs.add(MJCardCfg.FENG);
        mCfgs.add(MJCardCfg.JIAN);
        mCfgs.add(MJCardCfg.HUA);
        this.setRandomCard(new RandomCard(mCfgs, this.room.getPlayerNum(), this.room.getXiPaiList().size()));
        this.initDPos(this.set);
    }

    @Override
    protected boolean firstRandomDPos() {
        return false;
    }

    @Override
    public MJCard pop(boolean isNormalMo, int cardType) {
        // 无牌		
        if (this.randomCard.getSize() <= 0) {
            return null;
        }
        MJCard ret = this.getGodCard(cardType);
        ret = null != ret ? ret : this.randomCard.removeLeftCards(0);

        if (isNormalMo) {
            this.randomCard.setNormalMoCnt(this.randomCard.getNormalMoCnt() + 1);
        } else {
            this.randomCard.setGangMoCnt(this.randomCard.getGangMoCnt() + 1);
        }
        return ret;
    }


}			
