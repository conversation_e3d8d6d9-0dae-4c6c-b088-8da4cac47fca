package core.network.client2game.handler.wxmj;			
			
import business.player.Player;			
import business.player.feature.PlayerUnionRoom;			
import business.wxmj.c2s.iclass.CWXMJ_CreateRoom;			
import cenum.PrizeType;			
import com.ddm.server.websocket.handler.requset.WebSocketRequest;			
import com.google.gson.Gson;			
import core.network.client2game.handler.PlayerHandler;			
import core.server.wxmj.WXMJAPP;			
import jsproto.c2s.cclass.room.BaseRoomConfigure;			
			
import java.io.IOException;			
			
/**			
 * 亲友圈房间			
 *			
 * <AUTHOR>			
 */			
public class CWXMJUnionRoom extends PlayerHandler {			
			
    @Override			
    public void handle(Player player, WebSocketRequest request, String message)			
            throws IOException {			
			
        final CWXMJ_CreateRoom clientPack = new Gson().fromJson(message,			
                CWXMJ_CreateRoom.class);			
			
        // 公共房间配置			
        BaseRoomConfigure<CWXMJ_CreateRoom> configure = new BaseRoomConfigure<CWXMJ_CreateRoom>(			
                PrizeType.RoomCard,			
                WXMJAPP.GameType(),			
                clientPack.clone());			
        player.getFeature(PlayerUnionRoom.class).createNoneUnionRoom(request, configure);			
    }			
}			
