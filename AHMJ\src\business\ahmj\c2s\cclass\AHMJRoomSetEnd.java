package business.ahmj.c2s.cclass;	
	
	
import jsproto.c2s.cclass.mj.BaseMJRoom_SetEnd;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**						
 * 房间结算					
 * <AUTHOR>						
 *						
 */			
public class AHMJRoomSetEnd extends BaseMJRoom_SetEnd{
	private int jin;
	private int jin2;
	public int jinJin;
	private List<Integer> zhuaNiaoList = new ArrayList<Integer>();
	public int getJin() {
		return jin;
	}
	public void setJin(int jin) {
		this.jin = jin;
	}
	public void setJin2(int jin2) {
		this.jin2 = jin2;
	}

	public void setJinJin(int jinJin) {
		this.jinJin = jinJin;
	}

	public void setZhuaNiaoList(List<Integer> zhuaNiaoList) {
		this.zhuaNiaoList = zhuaNiaoList;
	}

	public List<Integer> getZhuaNiaoList() {
		return zhuaNiaoList;
	}
}
