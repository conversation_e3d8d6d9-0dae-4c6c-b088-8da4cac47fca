package business.global.pk.a3pk;	
	
import business.a3pk.c2s.cclass.A3PKRoomSetInfo;	
import business.a3pk.c2s.cclass.A3PKRoom_SetEnd;	
import business.a3pk.c2s.iclass.*;	
import business.global.pk.*;	
import business.global.pk.a3pk.optype.A3PKDistributionPartnerImpl;	
import business.global.room.base.AbsRoomPos;	
import business.global.room.base.RoomPlayBack;	
import cenum.PKOpType;	
import cenum.PrizeType;	
import cenum.mj.MJSpecialEnum;	
import cenum.room.RoomDissolutionState;	
import cenum.room.SetState;	
import cenum.room.TrusteeshipState;
import com.ddm.server.common.CommLogD;	
import com.ddm.server.common.utils.CommTime;	
import com.ddm.server.common.utils.Lists;	
import com.ddm.server.websocket.def.ErrorCode;	
import core.network.http.proto.SData_Result;	
import jsproto.c2s.cclass.BaseSendMsg;	
import jsproto.c2s.cclass.pk.base.BasePKRoom_PosEnd;	
import jsproto.c2s.cclass.pk.base.BasePKSet_Pos;	
import jsproto.c2s.cclass.room.RoomSetInfo;	
import lombok.Data;	
import org.apache.commons.collections.CollectionUtils;	
	
import java.util.*;	
import java.util.stream.Collectors;	
	
/**	
 * 长汀510K 一局游戏逻辑	
 *	
 * <AUTHOR>	
 */	
@Data	
public class A3PKRoomSet extends AbsPKSetRoom {	
    /**	
     * 独打的位置	
     */	
    private int challengePos = -1;	
	
    /**	
     * 打牌排名(从1开始)	
     */	
    private List<Integer> rankingPosList = Lists.newArrayList();	
    /**	
     * 余干510k房间	
     */	
    private A3PKRoom a3pkRoom;	
	
    /**	
     * 是否存在大小王	
     * T: 有大小王,F:没有大小王	
     */	
    private boolean isKing;	
	
    /**	
     * 是否摆牌	
     */	
    private boolean isBaiPai = false;	
	
    /**	
     * 先出牌的位置Id	
     */	
    private int firstOutPos = -1;	
    /**	
     * 首出必须带方块4	
     */	
    protected boolean isAtFirstHu = true;	
	
    /**	
     * 挑战者是否操作	
     */	
    private PKOpType challengeOp = PKOpType.Not;	
	
    /**	
     * 打出的队友牌	
     */	
    private List<Integer> showCardList = Lists.newArrayList();	
	
    public A3PKRoomSet(int setID, PKRoom room, int dPos) {	
        super(setID, room, dPos);	
        this.a3pkRoom = (A3PKRoom) room;	
        // 回放记录添加游戏配置	
        this.addGameConfig();	
        // 初始当局信息	
        this.initRoomSet();	
        // 开始发牌	
        this.startSet();	
    }	
	
	
    /**	
     * 初始当局信息	
     */	
    private void initRoomSet() {	
        // 玩法：A3独食（不带大小王）,32牛鬼（带大小王）；	
        this.setKing(this.getA3pkRoom().getWanfa() == A3PKRoomEnum.A3PKWanfaEnum.NiuGui32.ordinal());	
    }	
	
    /**	
     * 是否看别人的牌	
     *	
     * @param pid 玩家pid	
     * @return	
     */	
    private int getSeePosId(long pid) {	
        AbsRoomPos roomPos = this.getRoom().getRoomPosMgr().getPosByPid(pid);	
        if (Objects.nonNull(roomPos)) {	
            A3PKSetPos a3PKSetPos = (A3PKSetPos) this.getPKSetPos(roomPos.getPosID());	
            if (Objects.nonNull(a3PKSetPos) && !A3PKRoomEnum.A3PK_SET_POS_END_TYPE.NOT.equals(a3PKSetPos.getEndType())) {	
                return a3PKSetPos.getSeePosId();	
            }	
        }	
        return -1;	
    }	
	
    /**	
     * 看下家对应的牌	
     *	
     * @param ranksType 队友	
     * @param opPos     位置	
     */	
    public void seeNextRanksCard(int ranksType, A3PKSetPos opPos) {	
        if (CollectionUtils.isEmpty(this.getShowCardList()) || this.getShowCardList().size() < 2) {	
            // 没有打出队友牌或者打出的队友牌不够	
            return;	
        }	
        A3PKSetPos yPos = null;	
        for (int i = 1; i < this.getRoom().getPlayerNum(); i++) {	
            int nextOpPos = (i + opPos.getPosID()) % this.getRoom().getPlayerNum();	
            yPos = (A3PKSetPos) this.getPosDict().get(nextOpPos);	
            // 获取玩家位置信息	
            if (null == yPos || yPos.getRanksType() != ranksType || opPos.getSeePosId() == yPos.getPosID()) {	
                continue;	
            }	
            if (A3PKRoomEnum.A3PK_SET_POS_END_TYPE.NOT.equals(yPos.getEndType())) {	
                opPos.setSeePosId(yPos.getPosID());	
                yPos.setWhoIsSeeMePid(opPos.getPid());	
                this.getRoom().getRoomPosMgr().notify2Pos(opPos.getPosID(), SA3PK_SeeCard.make(this.getRoom().getRoomID(), yPos.getPosID(), yPos.getPrivateCards()));	
                return;	
            }	
        }	
    }	
	
	
    /**	
     * 获取独打位置	
     *	
     * @return	
     */	
    public int getChallengePos() {	
        return this.challengePos;	
    }	
	
	
    public void setChallengeOp(PKOpType challengeOp) {	
        if (!PKOpType.Not.equals(this.challengeOp)) {	
            return;	
        }	
        this.challengeOp = challengeOp;	
    }	
	
    /**	
     * 计算下局庄家位置	
     *	
     * @return	
     */	
    public int calcNextDPos() {	
//        if (this.getChallengePos() >= 0) {	
//            return this.getChallengePos();	
//        }	
        if (CollectionUtils.isEmpty(this.getRankingPosList())) {	
            return this.getDPos();	
        }	
        return this.getRankingPosList().get(0);	
    }	
	
    /**	
     * 开始发牌	
     */	
    public void startSet() {	
        CommLogD.info("startSet id:{}", getSetID());	
        // 洗底牌	
        this.absPKSetCard();	
        // 初始化本局位置管理器	
        this.setSetPosMgr(this.absPKSetPosMgr());	
        // 初始化玩家手上的牌	
        this.initSetPosCard();	
        // 分组	
        PKFactory.getCardType(A3PKDistributionPartnerImpl.class).resultType(this.getPKSetPos(this.getDPos()), null);	
        // 通知本局开始	
        this.notify2SetStart();	
        this.getRoom().getTrusteeship().setTrusteeshipState(TrusteeshipState.Normal);	
    }	
	
    /**	
     * 初始化神牌玩家身上的牌	
     */	
    @Override	
    protected void initGodPosCard() {	
        // 神牌模式	
        this.getPosDict().values().stream().forEach(k -> this.getGodInfo().isGodCard(k, k.getPosID()));	
        // 神牌模式下补牌	
        this.godCardPrivate();	
    }	
	
    /**	
     * 神牌模式下补牌	
     */	
    public void godCardPrivate() {	
        if (this.getGodInfo().getPConfigMgr().getGodCard() == MJSpecialEnum.GOD_CARD.value()) {	
            for (int idx = 0; idx < this.getRoom().getPlayerNum(); idx++) {	
                AbsPKSetPos mSetPos = this.getPKSetPos(idx);	
                // 54张牌：第一局房主与房主下家随机14张，其他玩家随机13张；	
                int value = isDPosOrDPosNext(idx) ? 1 : 0;	
                if (mSetPos.sizePrivateCard() == (this.cardSize() + value) || !((PKRoom) mSetPos.getRoom()).isPlaying(idx)) {	
                    continue;	
                }	
                int podIdx = (this.cardSize() + value) - mSetPos.sizePrivateCard();	
                List<Integer> privateList = this.getPKSetCard().popList(podIdx);	
                this.getPKSetPos(idx).forcePopCard(privateList);	
            }	
        }	
    }	
	
    /**	
     * 初始玩家身上的牌	
     */	
    @Override	
    protected void initPosCard() {	
        // 玩家初始发牌	
        for (int i = 0; i < this.getRoom().getPlayerNum(); i++) {	
            // 54张牌：第一局房主与房主下家随机14张，其他玩家随机13张；	
            int value = isDPosOrDPosNext(i) ? 1 : 0;	
            this.getPosDict().get(i).init(this.getSetCard().popList(this.cardSize() + value));	
        }	
    }	
	
    /**	
     * 是否房主或房主下家	
     * @return	
     */	
    private boolean isDPosOrDPosNext(int dPos) {	
        if (this.isKing()) {	
            // 带大小王玩法	
            if (this.getDPos() == dPos) {	
                // 房主本身	
                return true;	
            }	
            int dPosNext = (this.getDPos() + 1) % this.getRoom().getPlayerNum();	
            // 是否房主下家	
            return dPosNext == dPos;	
        } else {	
            return false;	
        }	
    }	
	
	
    @Override	
    protected void calcCurSetPosPoint() {	
        if (RoomDissolutionState.Dissolution.equals(this.getRoom().getRoomDissolutionState())) {	
        } else {	
            if (this.getChallengePos() >= 0 && PKOpType.Surrender.equals(this.getChallengeOp())) {	
                // 投降	
                // 当A选择投降时，A输3分，BCD各得1分；.	
                ((A3PKSetPosMgr) this.getSetPosMgr()).calc1V3Lose(this.getChallengePos(), 1);	
            } else {	
                // 打出的牌按打出的牌型算分；	
                if (CollectionUtils.isEmpty(this.getRankingPosList())) {	
                    // 没有排名	
                    return;	
                }	
                A3PKSetPos oneSetPos = (A3PKSetPos) this.getPKSetPos(this.getRankingPosList().get(0));	
                if (this.getChallengePos() >= 0) {	
                    boolean isDouble = PKOpType.Challenge.equals(this.getChallengeOp());	
                    //1vs3:示例A为独食，BCD为同队；	
                    for(int i = 0; i < this.getRankingPosList().size();i++) {	
                        int value = this.getRankingPosList().get(i);	
                        if (value == this.getChallengePos()) {	
                            if(i == 0) {	
                                //A头游（赢全红）：A得6分，BCD各输2分；	
                                ((A3PKSetPosMgr) this.getSetPosMgr()).calc3V1CompleteWin(value, isDouble ?4:2);	
                                return;	
                            } else if (i == 1 || i == 2) {	
                                if (this.getA3pkRoom().RoomCfg(A3PKRoomEnum.A3PKCfgEnum.DuShiTeShuSuanFen)) {	
                                    // 当勾选“独食特殊算分”，双方分值不变；	
                                    return;	
                                } else {	
                                    //A二游（赢一半）：A得3分，BCD各输1分；	
                                    //A三游（输一半）：A输3分，BCD各得1分；	
                                    if (i==1) {	
                                        ((A3PKSetPosMgr) this.getSetPosMgr()).calc3V1Win(value, isDouble ? 2 : 1);	
                                    } else if (i == 2) {	
                                        ((A3PKSetPosMgr) this.getSetPosMgr()).calc1V3Lose(value, isDouble ? 2 : 1);	
                                    }	
                                    return;	
                                }	
                            }	
                        }	
                    }	
                    //A四游（输全红）：A输6分，BCD各得2分；	
                    ((A3PKSetPosMgr) this.getSetPosMgr()).calc1V3CompleteLose(this.getChallengePos(), isDouble ?4:2);	
                } else  {	
                    // 2vs2：示例AB同队，CD同队；	
                    A3PKSetPos partnerPosInfo = (A3PKSetPos) this.getPosDict().values().stream().filter(k->k.getPosID() != oneSetPos.getPosID() && ((A3PKSetPos) k).getRanksType() == oneSetPos.getRanksType()).findAny().orElse(null);	
                    if (Objects.isNull(partnerPosInfo)) {	
                        Map<Integer, Long> map = this.getPosDict().values().stream().collect(Collectors.groupingBy(k ->  ((A3PKSetPos) k).getRanksType(), Collectors.counting()));	
                        CommLogD.error("partnerPosInfo error roomId:{},setId:{},map:{},ChallengePos:{}",this.getRoom().getRoomID(),this.getSetID(),map.toString(),this.getChallengePos());	
                        return;	
                    }	
                    for(int i = 0; i < this.getRankingPosList().size();i++) {	
                        int value = this.getRankingPosList().get(i);	
                        if (value == partnerPosInfo.getPosID()) {	
                            if (i == 1 ) {	
                                //A和B分别为头游和二游（赢全红）：AB各得2分，CD各输2分；	
                                ((A3PKSetPosMgr) this.getSetPosMgr()).calc2V2CompleteWin(oneSetPos.getPosID(),value, 2);	
                            } else if (i == 2) {	
                                //A和B分别为头游和三游（赢一半）：AB各得1分，CD各输1分；	
                                ((A3PKSetPosMgr) this.getSetPosMgr()).calc2V2Win(oneSetPos.getPosID(),value,1);	
                            }	
                        }	
                    }	
                }	
            }	
        }	
	
    }	
	
	
    /**	
     * 设置每个操作位列表	
     *	
     * @param pid 玩家Pid	
     * @return	
     */	
    public List<BasePKSet_Pos> setPosList(long pid) {	
        List<BasePKSet_Pos> pos = new ArrayList<>();	
        for (int i = 0; i < this.getRoom().getPlayerNum(); i++) {	
            pos.add(this.getPosDict().get(i).getNotify(pid));	
        }	
        return pos;	
    }	
	
	
    /**	
     * 获取下一位操作者位置	
     *	
     * @param opPos	
     * @return	
     */	
    public int nextOpPos(int opPos) {	
        A3PKSetPos yPos = null;	
        int nextOpPos = 0;	
        int nPos = -1;	
        for (int i = opPos; i < this.getRoom().getPlayerNum(); i++) {	
            nextOpPos = (i + 1) % this.getRoom().getPlayerNum();	
            yPos = (A3PKSetPos) this.getPosDict().get(nextOpPos);	
            // 获取玩家位置信息	
            if (null == yPos) {	
                continue;	
            }	
            // 检查玩家是否过(出完牌)	
            nPos = yPos.isPass();	
            if (nPos >= 0) {	
                // 玩家出完牌，没有接手，由伙伴出。	
                return nPos;	
            }	
            // 下家出牌	
            return nextOpPos;	
        }	
        return nextOpPos;	
    }	
	
    /**	
     * 记录打牌排名	
     *	
     * @return	
     */	
    public int addRankingPos(int posId) {	
        this.rankingPosList.add(posId);	
        return this.rankingPosList.size();	
    }	
	
	
	
	
    /**	
     * 扑克当局结算	
     *	
     * @return	
     */	
    @Override	
    protected A3PKRoom_SetEnd newPKRoomSetEnd() {	
        return new A3PKRoom_SetEnd();	
    }	
    /**	
     * 获取房间当局结束数据	
     *	
     * @return	
     */	
    @Override	
    protected A3PKRoom_SetEnd mRoomSetEnd() {	
        if (Objects.nonNull(this.getSetEnd())) {	
            return (A3PKRoom_SetEnd) this.getSetEnd();	
        }	
        this.setSetEnd(this.newPKRoomSetEnd());	
        A3PKRoom_SetEnd setEnd = (A3PKRoom_SetEnd) this.getSetEnd();	
        // 庄家位置	
        setEnd.setDPos(this.getDPos());	
        setEnd.setEndTime(CommTime.nowSecond());	
        // 设置独打位置	
        setEnd.setChallengePos(this.getChallengePos());	
        if (this.checkExistPrizeType(PrizeType.RoomCard)) {	
            setEnd.setPlayBackCode(getPlayBackDateTimeInfo().getPlayBackCode());	
            setEnd.setRoomDissolutionState(this.getRoom().getRoomDissolutionState());	
        }	
        setEnd.setSetId(this.getSetID());	
        setEnd.setPosResultList(	
                this.getPosDict().values().stream()	
                        .map(k -> k.calcPosEnd())	
                        .sorted(Comparator.comparing(BasePKRoom_PosEnd::getPos))	
                        .collect(Collectors.toList()));	
        return setEnd;	
    }	
	
	
    @Override	
    public RoomSetInfo getNotify_set(long pid) {	
        A3PKRoomSetInfo ret = new A3PKRoomSetInfo();	
        // 庄家位置	
        ret.setdPos(this.getDPos());	
        // 第几局	
        ret.setSetID(this.getSetID());	
        // 当前操作时间	
        ret.setSetCurrentTime(CommTime.nowSecond());	
        // 当前出牌信息	
        ret.setCurOutCardInfo(this.getCurOutCard());	
        // 玩家独打的位置	
        ret.setChallengePos(PKOpType.Challenge.equals(this.getChallengeOp()) ? this.challengePos: -1);	
        // 看谁的牌	
        ret.setSeePosId(this.getSeePosId(pid));	
        // 状态	
        ret.setState(this.getState());	
        // 初始手牌	
        ret.setSetPosList(setPosList(pid));	
        // 如果是等待状态： waiting；	
        if (SetState.Playing.equals(this.getState()) && Objects.nonNull(getCurRound())) {	
            // 当前等待信息	
            ret.setSetRound(this.getCurRound().getNotify_RoundInfo(this.getRoom().getRoomPosMgr().getPosByPid(pid).getPosID()));	
        }	
        // 结束状态	
        if (SetState.End.equals(this.getState())) {	
            ret.setSetEnd(this.getNotify_setEnd());	
        } else {	
            ret.setSetEnd(this.newPKRoomSetEnd());	
        }	
        return ret;	
    }	
	
    @Override	
    public void endSet() {	
        CommLogD.info("endSet id:{}", getSetID());	
        //正常计算	
        if (SetState.End.equals(this.getState())) {	
            return;	
        }	
        this.setState(SetState.End);	
        this.setEnd(true);	
        // 结算算分	
        this.calcPoint();	
        // 广播	
        this.getRoomPlayBack().playBack2All(this.setEnd(getRoom().getRoomID(), this.getNotify_setEnd()));	
        // 记录回放码	
        this.roomPlayBack();
    }	
	
	
    @Override	
    public boolean update(int sec) {	
        boolean isClose = false;	
        if (SetState.Init.equals(this.getState())) {	
            if (CommTime.nowMS() > this.getStartMS() + this.getInitTime()) {	
                this.setState(SetState.Playing);	
                if (!this.startNewRound()) {	
                    this.endSet();	
                }	
            }	
        } else if (SetState.Playing.equals(this.getState())) {	
            boolean isRoundClosed = this.getCurRound().update(sec);	
            if (isRoundClosed) {	
                if (getCurRound().isSetEnd() || !this.startNewRound()) {	
                    this.endSet();	
                }	
            }	
        } else if (SetState.End.equals(this.getState())) {	
            this.clearEndSetRoom();	
            isClose = true;	
	
        } else if (SetState.Waiting.equals(this.getState())) {	
        }	
        return isClose;	
    }	
	
    @Override	
    public int cardSize() {	
        // 每个玩家随机发13张；	
        return 13;	
    }	
	
    @Override	
    protected <T> BaseSendMsg setEnd(long roomID, T setEnd) {	
        return SA3PK_SetEnd.make(roomID, setEnd);	
    }	
	
    @Override	
    protected AbsPKSetPos absPKSetPos(int posID) {	
        return new A3PKSetPos(posID, this.getRoom().getRoomPosMgr().getPosByPosID(posID), this);	
    }	
	
    @Override	
    protected void absPKSetCard() {	
        // 设置当局牌	
        this.setSetCard(new A3PKSetCard(this));	
    }	
	
    @Override	
    protected AbsPKSetPosMgr absPKSetPosMgr() {	
        return new A3PKSetPosMgr(this);	
    }	
	
    @Override	
    protected <T> BaseSendMsg setStart(long roomID, T setInfo) {	
        return SA3PK_SetStart.make(roomID, setInfo);	
    }	
	
    @Override	
    protected AbsPKSetRound nextSetRound(int roundID) {	
        return new A3PKSetRound(this, roundID);	
    }	
	
    @Override	
    public boolean checkExistPrizeType(PrizeType prizeType) {	
        return prizeType.equals(this.room.getBaseRoomConfigure().getPrizeType());	
    }	
	
	
    /**	
     * 配置文件是否需要游戏名	
     *	
     * @return T:需要,F:不需要	
     */	
    @Override	
    public boolean isConfigName() {	
        return true;	
    }	
	
    @Override	
    public RoomPlayBack newRoomPlayBackImpl() {	
        return new A3PKRoomPlayBackImpl(this.getRoom());	
    }	
	
    @Override	
    public void addDissolveRoom(BaseSendMsg baseSendMsg) {	
        if (SetState.End.equals(this.getState()) || null == this.getSetPosMgr()) {	
            return;	
        }	
        this.getRoomPlayBack().addPlaybackList(baseSendMsg, this.getSetPosMgr().getPKAllPlayBackNotify());	
    }	
	
    @Override	
    public void addGameConfig() {	
        this.getRoomPlayBack().addPlaybackList(SA3PK_Config.make(this.getRoom().getCfg(), this.getRoom().getRoomTyepImpl().getRoomTypeEnum()), null);	
    }	
	
    @Override	
    public int getTabId() {	
        return this.getRoom().getTabId();	
    }	
	
    @Override	
    public void clearBo() {	
        this.setBo(null);	
    }	
	
    /**	
     * 理牌操作	
     *	
     * @param posId  操作位置	
     * @param liPais 理牌列表	
     */	
    public SData_Result opLiPai(int posId, List<List<Integer>> liPais) {	
        if (SetState.End.equals(this.getState())) {	
            return SData_Result.make(ErrorCode.NotAllow, "opLiPai curSet state:{%s}", getState());	
        }	
        A3PKSetPos cSetPos = (A3PKSetPos) this.getPosDict().get(posId);	
        if (Objects.isNull(cSetPos)) {	
            return SData_Result.make(ErrorCode.NotAllow, "opLiPai cSetPos posId:{%d}", posId);	
        }	
        // 检查选中的理牌是否手上的牌	
        if (!cSetPos.checkLiPai(liPais)) {	
            return SData_Result.make(ErrorCode.NotAllow, "opLiPai checkLiPai liPais:{%s}", liPais);	
        }	
        return SData_Result.make(ErrorCode.Success);	
    }	
	
	
    /**	
     * 打独操作	
     *	
     * @param posId  操作位置	
     * @param opType 操作类型	
     */	
    public SData_Result opChallengeOp(int posId, int opType) {	
        if (SetState.End.equals(this.getState())) {	
            return SData_Result.make(ErrorCode.NotAllow, "opChallengeOp curSet state:{%s}", getState());	
        }	
        if(!PKOpType.Not.equals(this.getChallengeOp())) {	
            return SData_Result.make(ErrorCode.NotAllow, "opChallengeOp curSet ChallengeOp:{%s}", this.getChallengeOp());	
        }	
        A3PKSetPos cSetPos = (A3PKSetPos) this.getPosDict().get(posId);	
        if (Objects.isNull(cSetPos)) {	
            return SData_Result.make(ErrorCode.NotAllow, "opChallengeOp cSetPos posId:{%d}", posId);	
        }	
        PKOpType newOpType =  PKOpType.valueOf(opType);	
        if (cSetPos.doOpType(null, newOpType)){	
            this.setChallengeOp(newOpType);	
            if(PKOpType.Surrender.equals(this.getChallengeOp())) {	
                this.endSet();	
            }	
            return SData_Result.make(ErrorCode.Success);	
        } else {	
            return SData_Result.make(ErrorCode.NotAllow, "opChallengeOp curSet newOpType:{%s}", newOpType);	
        }	
    }	
	
}	
