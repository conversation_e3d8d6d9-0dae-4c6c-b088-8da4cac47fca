package business.global.mj.ahhbmj.ting;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCardInit;
import business.global.mj.ahhbmj.AHHBMJNormalHuCardImpl;
import business.global.mj.manage.MJFactory;
import business.global.mj.ting.AbsTing;

public class AHHBMJTingImpl extends AbsTing {


    @Override
    public boolean tingHu(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
        return MJFactory.getHuCard(AHHBMJNormalHuCardImpl.class).checkHuCard(mSetPos, mCardInit);

    }

}											
