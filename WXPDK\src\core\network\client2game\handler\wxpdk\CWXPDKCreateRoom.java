package core.network.client2game.handler.wxpdk;

import business.player.Player;
import business.player.feature.PlayerRoom;
import business.wxpdk.c2s.iclass.CWXPDK_CreateRoom;
import cenum.PrizeType;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;
import core.network.client2game.handler.PlayerHandler;
import core.network.http.proto.SData_Result;
import core.server.wxpdk.WXPDKAPP;
import jsproto.c2s.cclass.room.BaseRoomConfigure;

import java.io.IOException;

/**
 * 创建房间
 * 
 * <AUTHOR>
 *
 */
public class CWXPDKCreateRoom extends PlayerHandler {

	@SuppressWarnings("rawtypes")
	@Override
	public void handle(Player player, WebSocketRequest request, String message)
			throws IOException {

		final CWXPDK_CreateRoom clientPack = new Gson().from<PERSON><PERSON>(message,
				CWXPDK_CreateRoom.class);
		// 公共房间配置
		BaseRoomConfigure<CWXPDK_CreateRoom> configure = new BaseRoomConfigure<CWXPDK_CreateRoom>(
				PrizeType.RoomCard,
				WXPDKAPP.GameType(),
				clientPack.clone());
		SData_Result resule = player.getFeature(PlayerRoom.class).createRoomAndConsumeCard(configure);
		if (ErrorCode.Success.equals(resule.getCode())) {
			request.response(resule.getData());
		} else {
			request.error(resule.getCode(),resule.getMsg());
		}
	}
}
