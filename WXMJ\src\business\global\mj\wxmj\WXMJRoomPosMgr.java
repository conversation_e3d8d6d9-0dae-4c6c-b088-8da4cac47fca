package business.global.mj.wxmj;

import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;

import business.global.mj.wxmj.WXMJRoomEnum.WXMJPiaoFen;
import business.global.room.base.AbsBaseRoom;
import business.global.room.mj.MJRoomPosMgr;
import com.ddm.server.common.utils.CommTime;

/**
 * 红中麻将 房间内每个位置信息
 *
 * <AUTHOR>
 */

public class WXMJRoomPosMgr extends MJRoomPosMgr {

    public WXMJRoomPosMgr(AbsBaseRoom room) {
        super(room);
    }

    @Override
    protected void initPosList() {
        // 初始化房间位置
        for (int posID = 0; posID < this.getPlayerNum(); posID++) {
            this.posList.add(new WXMJRoomPos(posID, room));
        }
    }

    /**
     * 是否所有玩家继续下一局
     *
     * @return
     */
    public boolean isAllContinue() {
        if (null == this.getPosList() || this.getPosList().size() <= 1) {
            // 玩家信息列表没数据	
            return false;
        }
        if (((WXMJRoom) getRoom()).getRoomCfg().getFangjian().contains(3)) {
//        //超时继续，萍乡	
            this.getPosList().stream().forEach(k -> {
                if (k.getPid() > 0 && !k.isGameReady() && k.getTimeSec() > 0 && CommTime.nowSecond() - k.getTimeSec() >= 5) {
                    getRoom().continueGame(k.getPid());
                }
            });
        }
        // 玩家在游戏中并且没有准备。	
        return this.getPosList().stream().allMatch(k -> k.getPid() > 0L && k.isGameReady());
    }

    /**
     * 获取飘分列表
     */
    public ArrayList<Integer> getPiaoFenList() {
        ArrayList<Integer> piaofenList = new ArrayList<>();
        piaofenList.addAll(this.getPosList().stream().map(roomPos -> ((WXMJRoomPos) roomPos).getPiaoFenEnum().value())
                .collect(Collectors.toList()));
        return piaofenList;
    }

    /**
     * 飘分是否都有操作
     */
    public boolean checkAllOpPiaoFen() {
        if (((WXMJRoom) this.getRoom()).getTaiMap().size() == this.getPlayerNum() && !((WXMJRoom) this.getRoom()).isRealFirst()) {
            for (Map.Entry<Integer, Integer> ccc : ((WXMJRoom) this.getRoom()).getTaiMap().entrySet()) {
                if (ccc.getValue() < 0) {
                    return false;
                }
            }
            return true;
        }
        return !this.getPosList().stream()
                .filter(roomPos -> ((WXMJRoomPos) roomPos).getPiaoFenEnum().equals(WXMJPiaoFen.NOT_OP)).findAny()
                .isPresent();
    }

}			
