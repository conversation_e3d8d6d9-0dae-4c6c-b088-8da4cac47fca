package business.wxmj.c2s.iclass;			
			
import cenum.RoomTypeEnum;			
import jsproto.c2s.cclass.room.BaseCreateRoom;			
import jsproto.c2s.iclass.room.SBase_Config;			
			
@SuppressWarnings("serial")			
public class SWXMJ_Config extends SBase_Config {			
	public static SWXMJ_Config make(BaseCreateRoom cfg, RoomTypeEnum roomTypeEnum) {			
		SWXMJ_Config ret = new SWXMJ_Config();			
		ret.setCfg(cfg);			
		ret.setRoomType(roomTypeEnum);			
		return ret;			
	}			
}			
