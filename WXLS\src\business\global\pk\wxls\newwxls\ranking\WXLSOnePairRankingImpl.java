package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.Iterator;	
import java.util.Map;	
	
/**	
 * Class {@code OnePairRankingImpl} 解析玩家手中的牌是不是一对(2+1+1+1)	
 */	
public class WXLSOnePairRankingImpl extends WXLSAbstractRanking {	
	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        WXLSRankingResult result = null;	
        Map<Integer, Integer> rankCount = player.getCardsRankCountMap();	
        boolean hasOne = false;	
	
        if (player.getCardSize() == 5) {	
            if (rankCount.size() == 4) {	
                Iterator<Map.Entry<Integer, Integer>> it = rankCount.entrySet().iterator();	
                while (it.hasNext()) {	
                    Map.Entry<Integer, Integer> next = it.next();	
                    if (next.getValue() == 2 || next.getValue() == 1) {	
                        hasOne = true;	
                        break;	
                    }	
                }	
            }	
        } else if (player.getCardSize() == 3) {	
            if (rankCount.size() == 2) {	
                Iterator<Map.Entry<Integer, Integer>> it = rankCount.entrySet().iterator();	
                while (it.hasNext()) {	
                    Map.Entry<Integer, Integer> next = it.next();	
                    if (next.getValue() == 2 || next.getValue() == 1) {	
                        hasOne = true;	
                        break;	
                    }	
                }	
            }	
        }	
	
        if (hasOne) {	
            result = new WXLSRankingResult();	
            result.setRankingEnum(WXLSRankingEnum.ONE_PAIR);	
        }	
	
        return result;	
    }	
	
}	
	
