package business.global.pk.wxpdk;

import business.wxpdk.c2s.cclass.WXPDK_define;
import business.wxpdk.c2s.cclass.WXPDK_define.WXPDK_WANFA;
import cenum.RoomTypeEnum;
import cenum.room.RoomDissolutionState;
import jsproto.c2s.cclass.pk.Victory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.function.Consumer;

public class WXPDKGameResult {
    public WXPDKRoom room; //房间


    public WXPDKGameResult(WXPDKRoom room) {
        this.room = room;
    }

    /**
     * 结算
     */
    public void resultCalc() {
        WXPDKRoomSet set = (WXPDKRoomSet) this.room.getCurSet();
        int winPos = -1;
        if (RoomDissolutionState.Dissolution.equals(room.getRoomRealDissolutionState())) {
            return;
        }
        for (int i = 0; i < this.room.getPlayerNum(); i++) {
            if (set.resultCalcList.get(i)) {
                continue;
            }
            WXPDKRoomPos roomPos = (WXPDKRoomPos) this.room.getRoomPosMgr().getPosByPosID(i);
            if (roomPos.privateCards.size() <= 0) {
                winPos = i;
                break;
            }
        }

        if (winPos == -1) {
            return;
        }
        set.resultCalcList.set(winPos, true);
        this.onJiPaiFenCalc(winPos);
        this.calPiaoHu(winPos);
    }


    /**
     * 记牌分
     */
    public void onJiPaiFenCalc(int pos) {
        WXPDKRoomSet set = (WXPDKRoomSet) this.room.getCurSet();
        int cardNum = this.room.getConfigMgr().getHandleCard().get(this.room.getRoomCfg().shoupai);
        int robCloseAddDouble = this.room.getConfigMgr().getRobCloseAddDouble();
        int winTimer = Math.max(1, set.getAddDoubleNum(pos));

        //是否需要算分
        List<Boolean> needCalPoint = new ArrayList<>(Collections.nCopies(this.room.getPlayerNum(), true));
        if (set.room.isOnlyWinRightNowPoint() && RoomTypeEnum.UNION.equals(set.room.getRoomTypeEnum())) {
            for (int i = 0; i < this.room.getPlayerNum(); i++) {
                WXPDKRoomPos posByPosID = (WXPDKRoomPos) this.room.getRoomPosMgr().getPosByPosID(i);
                if (posByPosID.getGameBeginSportsPoint() <= 0) {
                    needCalPoint.set(i, false);
                } else {
                    needCalPoint.set(i, true);
                }
            }
        }

        for (int i = 0; i < this.room.getPlayerNum(); i++) {
            if (i == pos) {
                continue;
            }
            if (set.resultCalcList.get(i)) {
                continue;
            }
            //如果你当局带的身上竞技点小于0不能算赢的牌型分等
            if (needCalPoint.get(pos)) {
                WXPDKRoomPos roomPos = (WXPDKRoomPos) this.room.getRoomPosMgr().getPosByPosID(i);
                int num = Math.max(1, set.getAddDoubleNum(i)) * winTimer;

                if (!set.isRobCloseCalc()) {
				/*if (i == set.getFirstOpPos() && set.surplusCardList.get(i) == set.getFirstOpNum()) {
					num *= robCloseAddDouble;
				} else */
                    if (set.surplusCardList.get(i) == cardNum) {
                        num *= robCloseAddDouble;
                    }
                }
                int size = roomPos.privateCards.size();
                if (this.room.getRoomCfg().maxAddDouble > 0) {
                    num = Math.min(this.room.getConfigMgr().getMaxAddDoubleList().get(this.room.getRoomCfg().maxAddDouble), num);
                }
                if (this.room.isWanFaByType(WXPDK_WANFA.WXPDK_WANFA_WEIZHANGSUANFEN)) {
                    num *= size <= 0 ? 0 : size;//尾张算分
                } else {
                    num *= (size <= 0 || size == 1) ? 0 : size;//尾张不算分
                }
                boolean isHongTaoShiZhaNiao = set.zhaNiao.get(i) || set.zhaNiao.get(pos);
                num *= (isHongTaoShiZhaNiao ? 2 : 1);
                // 双倍得分
                if (WXPDK_define.BombScore.DOUBLE_.has(room.getRoomCfg().zhadanfenshu)) {
                    num *= Math.max(1, getBombScore(pos, false));
                    // 加10分
                } else if (WXPDK_define.BombScore.ADD_TEN.has(room.getRoomCfg().zhadanfenshu)) {
                    num += getBombScore(pos, true);
                }

                if (room.is1415JiaKou7() && cardNum == 16) {
                    if (size == 14 || size == 15) {
                        num += 7;
                    } else if (size == 13 || size == 12) {
                        num += 6;
                    } else if (size == 11 || size == 10) {
                        num += 5;
                    }
                }

                set.pointList.set(pos, set.pointList.get(pos) + num);
                set.pointList.set(i, set.pointList.get(i) - num);
            }

            //输家身上的炸弹算分
            if (WXPDK_define.BombScore.ADD_TEN.has(room.getRoomCfg().zhadanfenshu)) {
                //如果你当局带的身上竞技点小于0不能算赢的炸弹分
                if (!needCalPoint.get(i)) {
                    continue;
                }
                otherCostScore(i, getBombScore(i, true));
            }
        }
    }

    /**
     * 其他人扣分，自己加分
     *
     * @param pos
     * @param score
     */
    private void otherCostScore(int pos, int score) {
        if (room.getCurSet() != null) {
            WXPDKRoomSet_FJ set = ((WXPDKRoomSet_FJ) room.getCurSet());
            for (int i = 0; i < this.room.getPlayerNum(); i++) {
                if (pos == i) continue;
                set.pointList.set(pos, set.pointList.get(pos) + score);
                set.pointList.set(i, set.pointList.get(i) - score);
            }
        }
    }

    /**
     * 获取炸弹得分
     *
     * @param winnerPos
     * @param needSelfCal 是否算的是自己的分
     * @return
     */
    private int getBombScore(int winnerPos, boolean needSelfCal) {
        WXPDKRoomSet_FJ roomSet = (WXPDKRoomSet_FJ) this.room.getCurSet();
        Victory multiple = new Victory();

        Consumer<Integer> bombScoreHandler = (num) -> {
            if (num <= 0) {
                return;
            }
            switch (WXPDK_define.BombScore.valueOf(room.getRoomCfg().zhadanfenshu)) {
                case DOUBLE_:
                    for (Integer i = 0; i < num; i++) {
                        multiple.setNum(Math.max(1, multiple.getNum()) * 2);
                    }
                    break;
                case ADD_TEN:
                    multiple.setNum(multiple.getNum() + (num * 10));
                    break;
            }
        };

        switch (WXPDK_define.BombAlgorithm.valueOf(room.getRoomCfg().zhadansuanfa)) {
            case ALWAYS:
                if (needSelfCal) {
                    bombScoreHandler.accept(roomSet.getNumByList(roomSet.roomDouble, winnerPos));
                } else {
                    roomSet.roomDouble.parallelStream()
                            .map(Victory::getNum)
                            .forEach(bombScoreHandler);
                }
                break;
            case WINNER:
                if (needSelfCal) {
                    bombScoreHandler.accept(roomSet.getNumByList(roomSet.roomDouble, winnerPos));
                } else {
                    roomSet.roomDouble.parallelStream()
                            .map(Victory::getNum)
                            .forEach(bombScoreHandler);
                }
                break;
            case GETROUNDALLBOMB:
                if (needSelfCal) {
                    bombScoreHandler.accept(roomSet.getNumByList(roomSet.roomDouble, winnerPos));
                } else {
                    roomSet.roomDouble.parallelStream()
                            .map(Victory::getNum)
                            .forEach(bombScoreHandler);
                }
                break;
        }
        return multiple.getNum();
    }

    /**
     * 固定分
     */
    public void onGuDingFenCalc(int pos) {
        WXPDKRoomSet set = (WXPDKRoomSet) this.room.getCurSet();
        int cardNum = this.room.getConfigMgr().getHandleCard().get(this.room.getRoomCfg().shoupai);
        int robCloseAddDouble = this.room.getConfigMgr().getRobCloseAddDouble();
        ArrayList<Integer> doubleList = this.getLoseList(pos);
        for (int i = 0; i < this.room.getPlayerNum(); i++) {
            if (i == pos) {
                continue;
            }
            int num = Math.max(1, set.getAddDoubleNum(pos));
            int addDoubleI = Math.max(1, set.getAddDoubleNum(i));
            num *= addDoubleI * Math.max(1, set.getRoomDouble(pos));

            if (!set.isRobCloseCalc()) {
				/*if (i == set.getFirstOpPos() && set.surplusCardList.get(i) == set.getFirstOpNum()) {
					num *= robCloseAddDouble;
				} else*/
                if (set.surplusCardList.get(i) == cardNum) {
                    num *= robCloseAddDouble;
                }
            }

            if (this.room.getRoomCfg().maxAddDouble > 0) {
                num = Math.min(this.room.getConfigMgr().getMaxAddDoubleList().get(this.room.getRoomCfg().maxAddDouble), num);
            }
            num *= doubleList.get(i);

            set.pointList.set(pos, set.pointList.get(pos) + num);
            set.pointList.set(i, set.pointList.get(i) - num);
        }
    }

    /**
     * 牌多通输
     */
    public void onPaiDuoTongShuCalc(int pos) {
        this.onJiPaiFenCalc(pos);

        if (this.room.isWanFaByType(WXPDK_WANFA.WXPDK_WANFA_XUEZHANDAODI)) {
            return;
        }

        WXPDKRoomSet set = (WXPDKRoomSet) this.room.getCurSet();
        for (int i = 0; i < this.room.getPlayerNum(); i++) {
            if (i == pos) {
                continue;
            }
            if (set.resultCalcList.get(i)) {
                continue;
            }
            WXPDKRoomPos roomPosI = (WXPDKRoomPos) this.room.getRoomPosMgr().getPosByPosID(i);
            for (int j = i; j < this.room.getPlayerNum(); j++) {
                if (i == j) {
                    continue;
                }
                if (j == pos) {
                    continue;
                }
                if (set.resultCalcList.get(j)) {
                    continue;
                }
                int num = Math.max(1, set.getAddDoubleNum(i));
                WXPDKRoomPos roomPosJ = (WXPDKRoomPos) this.room.getRoomPosMgr().getPosByPosID(j);
                boolean isWin = roomPosI.privateCards.size() - roomPosJ.privateCards.size() >= 0 ? false : true;
                num *= Math.max(1, set.getAddDoubleNum(j)) * Math.max(1, set.getRoomDouble(pos));

                if (this.room.getRoomCfg().maxAddDouble > 0) {
                    num = Math.min(this.room.getConfigMgr().getMaxAddDoubleList().get(this.room.getRoomCfg().maxAddDouble), num);
                }
                num *= Math.abs(roomPosI.privateCards.size() - roomPosJ.privateCards.size());

                set.pointList.set(j, set.pointList.get(j) + num * (isWin ? -1 : 1));
                set.pointList.set(i, set.pointList.get(i) + num * (isWin ? 1 : -1));
            }
        }
    }


    /*
     * 获取输家排序
     * **/
    public ArrayList<Integer> getLoseList(int winPos) {
        int playerNum = this.room.getPlayerNum();
        ArrayList<Integer> list = new ArrayList<Integer>(Collections.nCopies(playerNum, 0));
        ArrayList<Victory> cardList = new ArrayList<Victory>();
        for (int i = 0; i < this.room.getPlayerNum(); i++) {
            if (i == winPos) {
                continue;
            }
            WXPDKRoomPos roomPos = (WXPDKRoomPos) this.room.getRoomPosMgr().getPosByPosID(i);
            cardList.add(new Victory(i, roomPos.privateCards.size()));
        }
        cardList.sort(sorter);
        for (int i = 1; i <= cardList.size(); ) {
            Victory victory = cardList.get(i - 1);
            if (null != victory) {
                int count = this.getSameCardNum(cardList, victory.getNum());
                for (int j = 0; j < count; j++) {
                    Victory vic = cardList.get(i - 1 + j);
                    if (null != vic) {
                        list.set(vic.getPos(), this.getTimesByLoseNum(i + count - 1));
                    }
                }
                i += count;
            } else {
                ++i;
            }
        }
        return list;
    }

    /*
     * 排序 大到小
     */
    public Comparator<Victory> sorter = (left, right) -> {
        return left.getNum() - right.getNum();
    };

    /*
     * 获取剩余牌相同玩家个数
     * **/
    public int getSameCardNum(ArrayList<Victory> cardList, int sameCardNum) {
        int count = 0;
        for (Victory victory : cardList) {
            if (null != victory) {
                if (sameCardNum == victory.getNum()) {
                    count++;
                }
            }
        }
        return count;
    }

    /*
     * 计算分数
     * **/
//	public int calcPoint(int pos, int size){
//		int point = 0;
//		WXPDKRoomSet set = (WXPDKRoomSet) this.room.getCurSet();
//		int num = Math.max(1, set.getAddDoubleNum(pos));
//		//point = size * this.getSurplusNum(size) * num * set.getRoomDouble();
//		point = size  * num * set.getRoomDouble();
//		return point;
//	}

//	/*
//	 *根据牌数计算倍数 
//	 * **/
//	public int getSurplusNum(int size){
//		int num = 1;
//		int cardNum = this.room.getConfigMgr().getHandleCard().get(this.room.getRoomCfg().cardNum);
//		if (size == cardNum) {
//			num = 4;
//		} else if(size < cardNum &&  size >= cardNum*3/4){
//			num = 3;
//		}else if(size < cardNum*3/4 &&  size >= cardNum/2){
//			num = 2;
//		}
//		return num;
//	}

    /*
     * 根据输家排位获取倍数
     * */
    public int getTimesByLoseNum(int num) {
        int time = 1;
        switch (num) {
            case 1:
                time = 1;
                break;
            case 2:
                time = 2;
                break;
            case 3:
                time = 3;
                break;
            default:
                time = 1;
                break;
        }
        return time;
    }

    private void calPiaoHu(int winPos) {
        WXPDKRoomPos winner = (WXPDKRoomPos) this.room.getRoomPosMgr().getPosByPosID(winPos);
        int selfPiao = winner.getPiaoHuaEnum().value();
        WXPDKRoomSet set = (WXPDKRoomSet) this.room.getCurSet();
        for (int i = 0; i < room.getPlayerNum(); i++) {
            if (i == winPos) {
                continue;
            } else {
                WXPDKRoomPos roomPos = (WXPDKRoomPos) this.room.getRoomPosMgr().getPosByPosID(i);
                int piao = roomPos.getPiaoHuaEnum().value();
                int addPiao = (piao > 0 ? piao : 0) + (selfPiao > 0 ? selfPiao : 0);
                set.pointList.set(winPos, set.pointList.get(winPos) + addPiao);
                set.pointList.set(i, set.pointList.get(i) - addPiao);
            }
        }
    }
}
