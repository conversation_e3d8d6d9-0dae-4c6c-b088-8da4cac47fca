package business.a3pk.c2s.iclass;	
	
import jsproto.c2s.cclass.BaseSendMsg;	
	
public class SA3PK_RandomPartner extends BaseSendMsg {	
    /**	
     * 房间id	
     */	
    public long roomID;	
    /**	
     * 随机到的伙伴牌	
     */	
    public int randomPartnerCard;	
	
    public static SA3PK_RandomPartner make(long roomID, int randomPartnerCard) {	
        SA3PK_RandomPartner ret = new SA3PK_RandomPartner();	
        ret.setRoomID(roomID);	
        ret.setRandomPartnerCard(randomPartnerCard);	
        return ret;	
	
    }	
	
    public long getRoomID() {	
        return roomID;	
    }	
	
    public void setRoomID(long roomID) {	
        this.roomID = roomID;	
    }	
	
    public int getRandomPartnerCard() {	
        return randomPartnerCard;	
    }	
	
    public void setRandomPartnerCard(int randomPartnerCard) {	
        this.randomPartnerCard = randomPartnerCard;	
    }	
}	
