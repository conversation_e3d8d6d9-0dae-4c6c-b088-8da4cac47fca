package business.global.mj.ahhbmj;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCardInit;
import business.global.mj.ahhbmj.hutype.AHHBMJ_QXSSLImpl;
import business.global.mj.ahhbmj.hutype.AHHBMJ_SSLImpl;
import business.global.mj.hu.BaseHuCard;
import business.global.mj.hu.DDHuCardImpl;
import business.global.mj.hu.MJTemplateSSBKRandom14;
import business.global.mj.hu.SSYHuImpl;
import business.global.mj.manage.MJFactory;
import business.global.mj.util.HuUtil;
import cenum.mj.HuType;
import cenum.mj.OpPointEnum;

import java.util.*;
import java.util.stream.Collectors;

public class AHHBMJNormalHuCardImpl extends BaseHuCard {


    @Override
    public boolean checkHuCard(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
        if (Objects.isNull(mCardInit)) {
            return false;
        }
        if (Objects.isNull(mSetPos)) {
            return false;
        }

        if (MJFactory.getHuCard(DDHuCardImpl.class).checkHuCard(mSetPos, mCardInit)) {
            return true;
        }
        if (mSetPos.getRoom().RoomCfg(AHHBMJRoomEnum.KeXuanWanFa.SSY)) {
            if (MJFactory.getHuCard(SSYHuImpl.class).checkHuCard(mSetPos, mCardInit)) {
                return true;
            }
        }
        if (MJFactory.getHuCard(AHHBMJ_SSLImpl.class).checkHuCard(mSetPos, mCardInit)) {
            return true;
        }
        return HuUtil.getInstance().checkHu(mCardInit.getAllCardInts(), mCardInit.sizeJin());
    }

    /**
     * @param mSetPos
     * @return
     */
    @Override
    public boolean checkHuCard(AbsMJSetPos mSetPos) {

        if (Objects.isNull(mSetPos)) {
            return false;
        }
        MJCardInit cardInit = mSetPos.mjCardInit(true);
        if (mSetPos.getHandCard() != null) {
            Integer type = mSetPos.getHandCard().type;
            if (mSetPos.getSet().getmJinCardInfo().checkJinExist(type)) {
                cardInit.getJins().remove(0);
            } else {
                cardInit.getAllCardInts().remove(type);
            }
        }
        if (cardInit.sizeJin() < 1) {
            return false;
        }
        cardInit.getJins().remove(0);
        cardInit.getAllCardInts().add(50);
        cardInit.getAllCardInts().add(50);
        return checkHuCard(mSetPos, cardInit);
    }

    @Override
    public <T> Object checkHuCardReturn(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
        //检查是不是爆头	
        AHHBMJSetPos setPos = (AHHBMJSetPos) mSetPos;
        AHHBMJRoomSet set = (AHHBMJRoomSet) mSetPos.getSet();
        mSetPos.getPosOpRecord().clearOpHuList();

        if (setPos.isGSKH()) {
            addOpPointEnum(OpPointEnum.GSKH, mSetPos);
        }
        if (set.isGSP()) {
            addOpPointEnum(OpPointEnum.GSP, mSetPos);
        }
        if (mSetPos.getHuType().equals(HuType.QGH)) {
            addOpPointEnum(OpPointEnum.QGH, mSetPos);
        }
        if (checkTianHu(mSetPos)) {
            addOpPointEnum(OpPointEnum.TianHu, mSetPos);
        } else if (checkDiHu(mSetPos)) {
            addOpPointEnum(OpPointEnum.DiHu, mSetPos);
        }
        if (mSetPos.getRoom().RoomCfg(AHHBMJRoomEnum.KeXuanWanFa.SSY)) {
            addOpPointEnum((OpPointEnum) MJFactory.getHuCard(SSYHuImpl.class).checkHuCardReturn(mSetPos, mCardInit), mSetPos);
        }
        if (set.isQXSSL_X4() && MJFactory.getHuCard(AHHBMJ_QXSSLImpl.class).checkHuCard(mSetPos, mCardInit)) {
            addOpPointEnum(OpPointEnum.QXSSL, mSetPos);
        } else if (set.isSSL_X2() && MJFactory.getHuCard(AHHBMJ_SSLImpl.class).checkHuCard(mSetPos, mCardInit)) {
            addOpPointEnum(OpPointEnum.SSL, mSetPos);
        }
        if (MJFactory.getHuCard(DDHuCardImpl.class).checkHuCard(mSetPos, mCardInit)) {
            addOpPointEnum(OpPointEnum.QDHu, mSetPos);
        }
        if (mSetPos.getPosOpRecord().getOpHuList().isEmpty()) {
            addOpPointEnum(OpPointEnum.PingHu, mSetPos);

        }
        return false;
    }

    @Override
    public <T> Object checkHuCardReturn(AbsMJSetPos mSetPos, MJCardInit mCardInit, int type) {
        //检查是不是爆头
        AHHBMJSetPos setPos = (AHHBMJSetPos) mSetPos;
        AHHBMJRoomSet set = (AHHBMJRoomSet) mSetPos.getSet();
        mSetPos.getPosOpRecord().clearOpHuList();
        if (set.isJIA_ZI() && matchShunMid(mCardInit, mSetPos.getHandCard().type)) {
            addOpPointEnum(OpPointEnum.BianJiaDiao, mSetPos);
        }
        if (setPos.isTing() && set.isBAO_HU()) {
            addOpPointEnum(OpPointEnum.DanTing, mSetPos);
        }
        if (set.isQUE_YI() && checkQueMen(mSetPos, mCardInit)) {
            addOpPointEnum(OpPointEnum.QueYiSe, mSetPos);
        }
        if (setPos.isZimo()) {
            addOpPointEnum(OpPointEnum.ZiMo, mSetPos);
        } else {
            if (((AHHBMJRoom) mSetPos.getRoom()).getCfg().getPaohufen() == AHHBMJRoomEnum.PaoHu.PaoHu1Fen.ordinal()) {
                addOpPointEnum(OpPointEnum.JiePao, mSetPos);
            }

        }
        return false;
    }

    /**
     * 检查左顺子
     *
     * @param mjCardInit 牌
     * @param type       类型
     * @return
     */
    public boolean matchShunMid(MJCardInit mjCardInit, Integer type) {
        if (type > 40) {
            return false;
        }
        if (type % 10 + 1 > 9 || type % 10 - 1 < 1)
            return false;
        Integer right1 = type + 1;
        Integer left1 = type - 1;
        MJCardInit newMJCardInit = new MJCardInit(mjCardInit.getAllCardInts(), mjCardInit.getJins(), 0);
        List<Integer> removes = Arrays.asList(type, right1, left1);
        if (!newMJCardInit.getAllCardInts().containsAll(removes)) {
            return false;
        }
        for (Integer card : removes) {
            newMJCardInit.getAllCardInts().remove(card);
        }
        return HuUtil.getInstance().checkHu(newMJCardInit.getAllCardInts(), newMJCardInit.sizeJin());
    }

    private boolean checkDiHu(AbsMJSetPos mSetPos) {

        if (mSetPos.getHuType() != HuType.JiePao) {
            return false;
        }
        int dPos = mSetPos.getSet().getDPos();
        AbsMJSetPos dSetPos = mSetPos.getMJSetPos(dPos);
        if (mSetPos.getSet().getLastOpInfo().getLastOpPos() != dPos) {
            return false;
        }
        if (dSetPos.sizeOutCardIDs() - dSetPos.getPosOpRecord().sizeHua() != 1) {
            return false;
        }

        return true;
    }

    private boolean checkTianHu(AbsMJSetPos mSetPos) {
        if (mSetPos.sizePublicCardList() > 0) {
            return false;
        }
        if (mSetPos.sizeOutCardIDs() - mSetPos.getPosOpRecord().sizeHua() > 0) {
            return false;
        }
        if (mSetPos.getPosID() != mSetPos.getSet().getDPos()) {
            return false;
        }
        return true;
    }

    public void addOpPointEnum(OpPointEnum opPointEnum, AbsMJSetPos mjSetPos) {
        if (opPointEnum == OpPointEnum.Not) {
            return;
        }
        if (mjSetPos.getPosOpRecord().getOpHuList().contains(opPointEnum)) {
            return;
        }
        mjSetPos.getPosOpRecord().addOpHuList(opPointEnum);

    }

    /**
     * @param mSetPos
     * @param mCardInit
     * @return
     */
    private boolean checkQueMen(AbsMJSetPos mSetPos, MJCardInit mCardInit) {

        List<Integer> allInt = new ArrayList<>();
        // 获取牌列表
        allInt.addAll(mCardInit.getAllCardInts());
        // 获取顺子，刻子，杠组成的胡牌。
        allInt.addAll(mSetPos.publicCardTypeList());
        List<Integer> collect = allInt.stream().filter(p -> (p >= 1000 ? (p / 100) : p) < 40).collect(Collectors.toList());
        Map<Integer, Long> map = collect.stream().collect(Collectors.groupingBy(p -> p >= 1000 ? (p / 1000) : (p / 10), Collectors.counting()));
        return map.size() < 3;

    }

}
