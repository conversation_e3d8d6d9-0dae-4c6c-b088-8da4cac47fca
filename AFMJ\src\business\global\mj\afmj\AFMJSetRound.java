package business.global.mj.afmj;

import business.global.mj.*;
import business.global.mj.afmj.optype.AFMJXiaJingKaiJinImpl;
import business.global.mj.afmj.optype.KaiJinJJ_AFMJImpl;
import business.global.mj.manage.MJFactory;
import business.global.mj.set.MJOpCard;
import business.afmj.c2s.cclass.AFMJRoom_RoundPos;
import business.afmj.c2s.iclass.SAFMJ_PosOpCard;
import business.afmj.c2s.iclass.SAFMJ_StartRound;
import cenum.mj.MJOpCardError;
import cenum.mj.OpType;
import com.ddm.server.common.utils.CommTime;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.mj.BaseMJRoom_SetRound;
import jsproto.c2s.cclass.mj.BaseMJSet_Pos;
import jsproto.c2s.cclass.mj.NextOpType;
import business.global.mj.afmj.AFMJRoomEnum.*;


import java.util.List;

/**
 * 回合逻辑 每一次等待操作，都是一个round
 */

public class AFMJSetRound extends AbsMJSetRound {
	
	private final AFMJRoomSet jset;

	public AFMJSetRound(AbsMJSetRoom set, int roundID) {
		super(set, roundID);
		this.jset = (AFMJRoomSet) set;
	}

	@Override
	public synchronized int opCard(WebSocketRequest request, int opPos, OpType opType, MJOpCard mOpCard) {
		if (this.getEndTime() > 0){ // 回合并发
			request.error(ErrorCode.NotAllow, "end Time opPos has no round power");
			return MJOpCardError.ERROR_OP_TYPE.value();
		}
		AbsMJRoundPos pos = this.roundPosDict.get(opPos);//获得当前操作的人的信息
		if (null == pos) {
			request.error(ErrorCode.NotAllow, "endTime opPos has no round power");
			return MJOpCardError.ROUND_POS_ERROR.value();
		}
		int opCardRet = pos.op(request, opType, mOpCard);//检测是否能打出这张牌 　
		if (opCardRet >= 0) {//大于1表示可以打出，操作的位置
			// 位置操作牌
			if (pos.getPos().sizePrivateCard() <= 2) {//特殊功能
				pos.getPos().getPosOpNotice().clearBuNengChuList();
			}
			if (OpType.Peng.equals(this.getOpType()) || OpType.JieGang.equals(this.getOpType())) {
				pos.getPos().getPosOpNotice().clearBuNengChuList();
			}
			this.posOpCardRet(opCardRet, false);
		}
		return opCardRet;
	}

	public synchronized int opCard(WebSocketRequest request, int opPos, OpType opType, MJOpCard mOpCard, boolean isFlash) {
		if (this.getEndTime() > 0){ // 回合并发
			request.error(ErrorCode.NotAllow, "end Time opPos has no round power");
			return MJOpCardError.ERROR_OP_TYPE.value();
		}
		AbsMJRoundPos pos = this.roundPosDict.get(opPos);
		if (null == pos) {
			request.error(ErrorCode.NotAllow, "opPos has no round power");
			return MJOpCardError.ROUND_POS_ERROR.value();
		}
		int opCardRet = pos.op(request, opType, mOpCard);
		if (opCardRet >= 0) {
			// 位置操作牌
			this.posOpCardRet(opCardRet, isFlash);
		}
		return opCardRet;
	}

	@Override
	public boolean startWithGetCard(int pos, boolean isNormalMo) {

		// 抓牌
		if (null == this.set.getMJSetPos(pos).getHandCard()) { // 作弊情况下，已经有手牌
			if (null == this.set.getCard(pos, isNormalMo))
				return false;
		}

		if (set.isAtFirstHu()) {
//			set.getSetPosMgr().startSetApplique(); 	// 开局补花
			// 开精的方式：分为 上下翻精、翻上精、推精 三种；
			// 推精：首局翻上精，后续如果不是流局，就继承上一局的正副精；
			// 如果是流局，重新根据摇骰子翻上精；
			if(((AFMJRoom)jset.getRoom()).RoomCfgWanFa(AFMJWanFa.TuiJing)){ // 推精
				if(jset.getRoom().getCurSetID() == 1){
					// 第一局开金
					MJFactory.getOpCard(KaiJinJJ_AFMJImpl.class).checkOpCard(set.getMJSetPos(set.getDPos()), 0); // 开局开金，金不能是花牌
					set.sendSetPosCard(); // 开金后手牌排序，金在最左边
				}else if(((AFMJRoom)jset.getRoom()).isShangJuLiuJu()){
					// 如果是流局，重新根据摇骰子翻上精；
					if(!jset.isPlayJingAnimation()) jset.setPlayJingAnimation(true); // 播放翻精动画
					MJFactory.getOpCard(KaiJinJJ_AFMJImpl.class).checkOpCard(set.getMJSetPos(set.getDPos()), 0); // 开局开金，金不能是花牌
					set.sendSetPosCard(); // 开金后手牌排序，金在最左边
				}else { // 首局翻上精,后续如果不是流局，就继承上一局的正副精；
					if(jset.isPlayJingAnimation()) jset.setPlayJingAnimation(false); // 不播放翻精动画
					MJCard ZhengJing = ((AFMJRoom)jset.getRoom()).getTuiJingZhengJing(); // 正精
					MJCard FuJing = ((AFMJRoom)jset.getRoom()).getTuiJingFuJing(); // 下副精
					jset.getmJinCardInfo().addJinCard(FuJing); // 添加副金
					jset.getmJinCardInfo().addJinCard(ZhengJing); // 添加正金
					jset.setZhengJing(ZhengJing); // 设置正精
					jset.setFuJing(FuJing); // 设置副精
					jset.kaiJinNotify(ZhengJing , FuJing); // ZhengJing正精 , FuJing：副精
					set.sendSetPosCard(); // 开金后手牌排序，金在最左边
				}
			}else if(((AFMJRoom)jset.getRoom()).RoomCfgWanFa(AFMJWanFa.FanShangJing)){ // 翻上精
				MJFactory.getOpCard(KaiJinJJ_AFMJImpl.class).checkOpCard(set.getMJSetPos(set.getDPos()), 0); // 开局开金，金不能是花牌
				set.sendSetPosCard(); // 开金后手牌排序，金在最左边
			}else if(((AFMJRoom)jset.getRoom()).RoomCfgWanFa(AFMJWanFa.ShangXiaFanJing)){ // 上下翻精
				if(jset.isPlayJingAnimation()) jset.setPlayJingAnimation(false); // 不播放翻精动画
				MJFactory.getOpCard(KaiJinJJ_AFMJImpl.class).checkOpCard(set.getMJSetPos(set.getDPos()), 0); // 开局开金，金不能是花牌
				MJFactory.getOpCard(AFMJXiaJingKaiJinImpl.class).checkOpCard(set.getMJSetPos(set.getDPos()), 0); // 下金开金，金不能是花牌
				set.sendSetPosCard(); // 开金后手牌排序，金在最左边
			}

			// 检测天胡
			set.getSetPosMgr().checkOpType(set.getDPos(), 0, OpType.TianHu);
			NextOpType nOpType = set.getSetPosMgr().exeCardAction(OpType.TianHu);
			if (null != nOpType) {
				for (int posID : nOpType.getPosOpTypeListMap().keySet()) {
					AbsMJRoundPos nextPos = this.nextRoundPos(posID);
					if (nOpType.getPosOpTypeListMap().containsKey(posID)) {
						nextPos.addOpType(nOpType.getPosOpTypeListMap().get(posID));
					}
					nextPos.addOpType(OpType.Pass);
					roundPosDict.put(nextPos.getOpPos(), nextPos);
				}
				set.setAtFirstHu(false);
				return true;
			}

			set.setAtFirstHu(false); // 天胡只检测一次
		}

		return MJRoundPos(pos);

	}


	public boolean update(int sec) {
		// 已经结束
		if (this.endTime != 0) {
			return this.endTime >= this.startTime;
		}
		// 自动打牌
		return this.autoOutCard(sec);
	}


	/**
	 * 下回合操作者的位置
	 */
	protected AbsMJRoundPos nextRoundPos (int pos) {
		return new AFMJRoundPos(this, pos);
	}

	/**
	 *  位置操作牌
	 * @param roomID  房间ID
	 * @param pos     位置
	 * @param set_Pos 位置信息
	 * @param opType  动作类型
	 * @param opCard  操作牌
	 * @param isFlash 是否动画
	 * @return 通知客户端的位置操作牌消息
	 */
	@Override
	protected <T> BaseSendMsg posOpCard(long roomID, int pos, T set_Pos, OpType opType, int opCard, boolean isFlash) {
		return SAFMJ_PosOpCard.make(roomID, pos, set_Pos, opType, opCard, isFlash);
	}
	
	/**
	 * 开始当前回合通知
	 */
	@Override
	protected <T> BaseSendMsg startRound(long roomID, T room_SetWait) {
		return SAFMJ_StartRound.make(roomID, room_SetWait);
	}


	/**
	 * 尝试开始其他回合 如果 没有其他特殊回合 默认返回 false 否则 对其他特殊操作类型进行操作检查
	 */
	@Override
	protected boolean tryStartRoundOther(AbsMJSetRound preRound) {
		// 没有其他特殊操作
		return false;
	}


	/**
	 * 下位置操作类型
	 */
	public AbsMJRoundPos nextPosOpType(AbsMJRoundPos nextPos) {
		if (nextPos.getPos().checkOpType(0, OpType.TingYouJin)) {
			nextPos.addOpType(OpType.TingYouJin);
		} else {
			if (nextPos.getPos().checkOpType(0, OpType.Ting)) {
				nextPos.addOpType(OpType.Ting);
			}
		}
		// 碰完的牌可以马上开杠：补杠
		if (nextPos.getPos().checkOpType(0, OpType.Gang)) {
			nextPos.addOpType(OpType.Gang);
		}
		// 碰完的牌可以马上开杠：暗杠
		if (nextPos.getPos().checkOpType(0, OpType.AnGang)) {
			nextPos.addOpType(OpType.AnGang);
		}
		nextPos.addOpType(OpType.Out);
		return nextPos;
	}
	
	


	
	/**
	 * 位置操作牌
	 * @param opPosRet 操作位置
	 * @param isFlash 是否动画
	 */
	protected void posOpCardRet(int opPosRet, boolean isFlash) {
		int opCardID = this.set.getLastOpInfo().getLastOutCard();
		AbsMJSetPos sPos = this.set.getMJSetPos(opPosRet);
		sPos.getPosOpNotice().clearTingCardMap();
		// 刷新可胡列表
		this.refreshHuCardTypes(sPos);//牌已经打出去
		// 吃碰杠-清理牌  打完牌之后可能会有新的操作
		if (OpType.Peng.equals(this.getOpType()) || OpType.JieGang.equals(this.getOpType())
				|| OpType.Chi.equals(this.getOpType())) {
			if (OpType.Peng.equals(this.getOpType()) || OpType.JieGang.equals(this.getOpType())) {
				// 主要是跟打清空使用。 清空打牌信息
				this.cleanOutCardInfo();
			}
			this.set.getLastOpInfo().clearLastOutCard();
			if (this.checkExistClearPass()) { // 有些牌法当胡跟碰同时在，选择了碰同样漏胡
				if(sPos.getPosOpRecord().getOpList().contains(OpType.JiePao) || sPos.getPosOpRecord().getOpList().contains(OpType.QiangGangHu)) {
					if(OpType.Chi.equals(this.getOpType()) && sPos.getPosOpRecord().getOpList().contains(OpType.Peng)){
						// 当碰跟吃同时在，选择了吃同样漏碰
					}else{
						sPos.getPosOpRecord().clearOpCardType(); // 清空漏碰类型列表
					}
				} else { // 过手,清除漏胡
					if(OpType.Chi.equals(this.getOpType()) && sPos.getPosOpRecord().getOpList().contains(OpType.Peng)){
						// 当碰跟吃同时在，选择了吃同样漏碰
						sPos.getPosOpRecord().clearHuCardType(); // 清空漏胡类型列表
					}else{
						sPos.clearPass(); // 清除漏胡、漏碰类型列表
					}
				}
			}
			this.set.getSetPosMgr().clearOpTypeInfoList();
		}
		// 补杠、暗杠时候，操作牌ID == 0
		if (OpType.Gang.equals(this.getOpType()) || OpType.AnGang.equals(this.getOpType())) {
			opCardID = 0;
		this.setExeOpPos(opPosRet);

	}
		BaseMJSet_Pos posInfoOther = sPos.getNotify(false);
		BaseMJSet_Pos posInfoSelf = sPos.getNotify(true);

		//通知客户端当前玩家做了什么操作
		this.set.getRoomPlayBack().playBack2Pos(opPosRet,
				this.posOpCard(this.room.getRoomID(), opPosRet, posInfoSelf,this.getOpType(), opCardID, isFlash),
				set.getSetPosMgr().getAllPlayBackNotify());
		for (int i = 0; i < this.room.getPlayerNum(); i++) {//i等于自己不作操作 上文操作过了
			if (i == opPosRet) {
				continue;
			}
			this.room.getRoomPosMgr().notify2Pos(i,
					this.posOpCard(this.room.getRoomID(), opPosRet, posInfoOther, this.getOpType(), opCardID, isFlash));
		}

		// 清除飘精动画
		((AFMJSetPos)sPos).clearPiaoJing();


	}


	/**
	 * 自动打牌
	 */
	protected boolean autoOutCard(int sec) {
		if (sec - this.startTime < 1) {
			return false;
		}
		AFMJRoundPos roundPos;
		AFMJSetPos sPos;
		for (int posID = 0; posID < this.room.getPlayerNum(); posID++) {
			roundPos = (AFMJRoundPos) this.roundPosDict.get(posID);
			if (null == roundPos) {
				continue;
			}
			sPos = (AFMJSetPos) roundPos.getPos();
			if (null == sPos) {
				continue;
			}
			List<OpType> opList = roundPos.getRecieveOpTypes();
			if (null == opList || opList.size() <= 0) {
				continue;
			}
		}
		return false;
	}

	/**
	 * 本回合的操作玩
	 *
	 * @param pos 位置ID
	 */
	public boolean MJRoundPos(int pos) {
		AbsMJRoundPos tmPos = this.nextRoundPos(pos);
		tmPos.addOpType(tmPos.getPos().recieveOpTypes());
		this.roundPosDict.put(tmPos.getOpPos(), tmPos);
		return true;
	}

	@Override
	protected boolean checkExistClearPass() {
		return true;
	}

	/**
	 * 获取本轮信息
	 * @param pos 位置
	 */
	@Override
	public BaseMJRoom_SetRound getNotify_RoundInfo(int pos) {
		ret = new BaseMJRoom_SetRound();
		ret.setWaitID(this.roundID);
		ret.setStartWaitSec(this.startTime);
		ret.setRunWaitSec(CommTime.nowSecond() - this.startTime);
		for (AbsMJRoundPos roundPos : this.roundPosDict.values()) {
			if (roundPos.getOpType() != null) {
				continue;
			}
			// 自己 或 公开
			if (pos == roundPos.getOpPos() || roundPos.isPublicWait()) {
				AFMJRoom_RoundPos data = new AFMJRoom_RoundPos();
				boolean isSelf = pos == roundPos.getOpPos();
				data.setOpList(roundPos.getRecieveOpTypes());
				data.setChiList(roundPos.getPos().getPosOpNotice().getChiList());
				data.setLastOpCard(roundPos.getLastOutCard());
				if (data.getOpList().contains(OpType.QiangGangHu)) {
					// 记录抢杠胡，被抢的牌
					data.setLastOpCard(roundPos.getLastOpCard());
				}
				data.setWaitOpPos(roundPos.getOpPos());
				data.setTingCardMap(isSelf ? roundPos.getPos().getPosOpNotice().getTingCardMap() : null);
				data.setGangList(isSelf ? ((AFMJSetPos) roundPos.getPos()).getGangList() : null); // 飞听时可以暗杠的牌
				if (this.isBuChiFuDaFu()) {
					data.setBuChuList(isSelf ? roundPos.getPos().getPosOpNotice().getBuNengChuList() : null);
				}
				ret.addOpPosList(data);
				// 设置动作列表
				roundPos.getPos().getPosOpRecord().setOpList(data.getOpList());
				if(room.isConnectClearTrusteeship()){
					// 重新记录打牌时间
					roundPos.getPos().getRoomPos().setLatelyOutCardTime(CommTime.nowMS());
				}
				// 设置最后操作时间
				this.set.getLastOpInfo().setLastShotTime(CommTime.nowSecond());

			}
		}
		return ret;
	}

	/**
	 * 机器人操作
	 *
	 * @param posID 玩家位置
	 */
	public void RobothandCrad(int posID) {
		if (this.getEndTime() > 0) {
			return;
		}
		if (this.getRoundPosDict().containsKey(posID)) {
			new AFMJRobotOpCard(this).RobothandCrad(posID);
		}
	}


	// 尝试开始回合, 如果失败，则set结束
	@Override
	public boolean tryStartRound() {

		AbsMJSetRound preRound = getPreRound(); // 前一个可参考的操作round
		// 第一次，庄家作为操作者，抓牌，等待出牌
		if (null == preRound) {
			int opPos = this.set.getDPos();
			if (!startWithGetCard(opPos, true)) {
				return false;
			}
			this.notifyStart();
			return true;
		}

		// 上轮出牌
		if (preRound.getOpType() == OpType.Out) {
			return tryStartRoundOut(preRound);
		}
		// 上一轮接牌， 本轮继续出牌
		if (preRound.getOpType() == OpType.Peng) {
			return tryStartRoundPeng(preRound);
		}

		// 上一轮明杠，等抢胡，或者继续抓牌
		if (preRound.getOpType() == OpType.Gang || preRound.getOpType() == OpType.JieGang) {
			return tryStartRoundGang(preRound);
		}

		// 上一轮暗杠，本轮继续抓牌
		if (preRound.getOpType() == OpType.AnGang || preRound.getOpType() == OpType.TianGang) {
			return tryStartRoundAnGang(preRound);
		}

		// 上一轮接牌， 本轮继续出牌
		if (preRound.getOpType() == OpType.Chi) {
			return tryStartRoundChi(preRound);
		}

		// 上一轮放弃接牌
		if (preRound.getOpType() == OpType.Pass) {
			return tryStartRoundPass(preRound);
		}

		// 上一轮报听， 本轮继续出牌
		if (preRound.getOpType() == OpType.BaoTing) {
			return tryStartRoundBaoTing(preRound);
		}

		// 尝试开始其他回合
		return tryStartRoundOther(preRound);
	}

	/**
	 * 报听
	 *
	 * @param preRound 上回合
	 */
	protected boolean tryStartRoundBaoTing(AbsMJSetRound preRound) {
		AbsMJRoundPos nextPos = this.nextRoundPos(preRound.getExeOpPos());
		nextPos = nextPosOpType(nextPos);
		this.roundPosDict.put(nextPos.getOpPos(), nextPos);
		notifyStart();
		return true;
	}


}
