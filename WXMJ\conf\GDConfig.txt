#				
#	public static Integer PockerList_TWOEnd[] = {			
#			
#			0x02,	0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D,  0x0E, //方块2~a			
#			0x12,	0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D,  0x1E, //梅花2~a			
#			0x22,	0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B, 0x2C, 0x2D,  0x2E, //红桃2~a			
#			0x32,	0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C, 0x3D,  0x3E, //黑桃2~a			
#			
#						
#			0x52,	0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5A, 0x5B, 0x5C, 0x5D,  0x5E, //方块2~a			
#			0x62,	0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6A, 0x6B, 0x6C, 0x6D,  0x6E, //梅花2~a			
#			0x72,	0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7A, 0x7B, 0x7C, 0x7D,  0x7E, //红桃2~a			
#			0x82,	0x83, 0x84, 0x85, 0x86, 0x87, 0x48, 0x89, 0x8A, 0x8B, 0x8C, 0x8D,  0x8E, //黑桃2~a			
#	};			
#			
#	//扑克牌(一副)			
#			
#	public static Integer Trump_PockerList[] = {			
#			
#			0x41, 0x42 //大小王			
#			0x91, 0x92 //大小王			
#			
#	};			
#			
#手牌			
handleCard=27;			
#神牌模式(0:关,1:开)			
God_Card = 1;			
#玩家一			
Private_Card1 = [0x5E,	0x2E, 0x0E, 0x02, 0x12, 0x32, 0x23];			
#玩家二			
Private_Card2 = [0x55,	0x25, 0x05, 0x06, 0x16, 0x36];			
#玩家三			
Private_Card3 = [0x58,	0x28, 0x08, 0x09, 0x19, 0x39];			
#玩家四			
Private_Card4 = [0x41];			
#设置级数			
#红方级数   在神牌模式下有效			
redSteps=3;			
#蓝方级数    在神牌模式下有效			
blueSteps=3;			
