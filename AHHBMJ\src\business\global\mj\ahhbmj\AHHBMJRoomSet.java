package business.global.mj.ahhbmj;

import business.ahhbmj.c2s.cclass.AHHBMJRoomSetEnd;
import business.ahhbmj.c2s.cclass.AHHBMJRoomSetInfo;
import business.ahhbmj.c2s.iclass.*;
import business.global.mj.*;
import business.global.mj.manage.MJFactory;
import business.global.mj.op.BuHuaImpl;
import business.global.room.mj.MahjongRoom;
import cenum.mj.FlowerEnum;
import cenum.mj.MJSpecialEnum;
import cenum.mj.OpType;
import cenum.room.SetState;
import com.ddm.server.common.CommLogD;
import com.ddm.server.common.utils.CommTime;
import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.mj.BaseMJRoom_SetEnd;
import jsproto.c2s.cclass.mj.BaseMJSet_Pos;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * 青岛 一局游戏逻辑
 *
 * <AUTHOR>
 */
@SuppressWarnings("ALL")
@Data
public class AHHBMJRoomSet extends AbsMJSetRoom {
    private List<Integer> maList = new ArrayList<>();
    private List<Integer> caiPiaoCanNotOpPosList = new ArrayList<>();
    /**
     * 刚上开花
     */
    private boolean isGSP;


    /**
     * WA
     *
     * @param setID
     * @param room  room
     * @param dPos
     */

    @SuppressWarnings("rawtypes")
    public AHHBMJRoomSet(int setID, MahjongRoom room, int dPos) {
        super(setID, room, dPos);
        this.startMS = CommTime.nowMS();
        this.addGameConfig();
        this.startSet();
    }

    /**
     * 开始发牌
     */
    public void startSet() {
        CommLogD.info("startSet id:{}", getSetID());
        // 洗底牌
        this.absMJSetCard();
        // 初始化本局位置管理器
        this.setSetPosMgr(this.absMJSetPosMgr());
        // 初始化玩家手上的牌
        this.initSetPos();
        // 通知本局开始
        this.notify2SetStart();
        // 一些基本数据初始，无需理会。
        exeStartSet();
    }

    /**
     * 检查小局托管自动解散
     */

    public boolean checkSetEndTrusteeshipAutoDissolution() {
        return this.getRoom().getBaseRoomConfigure().getBaseCreateRoom().getFangjian()
                .contains(AHHBMJRoomEnum.AHHBMJGameRoomConfigEnum.TuoGuanJieSan.ordinal());
    }

    /**
     * 小局托管自动解散回放记录
     * 注意：需要自己重写
     *
     * @param roomId  房间id
     * @param pidList 托管玩家Pid
     * @param sec     记录时间
     * @return
     */
    @Override
    public BaseSendMsg DissolveTrusteeship(long roomId, List<Long> pidList, int sec) {
        return SAHHBMJ_DissolveTrusteeship.make(roomId, pidList, sec);
    }

    /**
     * 开癞子个数
     *
     * @return
     */
    @Override
    public int kaiJinNum() {
        return 0;
    }

    @Override
    public boolean isBaiBanTiJin() {
        return false;
    }

    /**
     * 一局结束的信息
     */
    @Override
    public BaseMJRoom_SetEnd getNotify_setEnd() {
        AHHBMJRoomSetEnd setEndInfo = (AHHBMJRoomSetEnd) this.mRoomSetEnd();
        return setEndInfo;
    }

    @Override
    public void addGameConfig() {
        this.getRoomPlayBack().addPlaybackList(SAHHBMJ_Config.make(this.room.getBaseRoomConfigure().getBaseCreateRoom(), this.room.getBaseRoomConfigure().getBaseCreateRoom().getClubId() > 0), null);
    }

    @Override
    protected <T> BaseSendMsg posGetCard(long roomID, int pos, int normalMoCnt, int gangMoCnt, T set_Pos) {
        return SAHHBMJ_PosGetCard.make(roomID, pos, normalMoCnt, gangMoCnt, set_Pos, this.setCard.getRandomCard().getSize());
    }

    /**
     * 创建新的当局麻将信息
     */
    @Override
    protected AHHBMJRoomSetInfo newMJRoomSetInfo() {
        return new AHHBMJRoomSetInfo();
    }

    /**
     * 获取通知当局信息
     */
    @Override
    public AHHBMJRoomSetInfo getNotify_set(long pid) {
        AHHBMJRoomSetInfo ret = (AHHBMJRoomSetInfo) this.getMJRoomSetInfo(pid);
        return ret;
    }

    /**
     * 麻将当局结算
     *
     * @return
     */
    @Override
    protected AHHBMJRoomSetEnd newMJRoomSetEnd() {
        return new AHHBMJRoomSetEnd();
    }

    /**
     * 牌数
     */
    @Override
    public int cardSize() {
        return MJSpecialEnum.SIZE_13.value();
    }

    /**
     * @return
     */
    @Override
    public int calcNextDPos() {
        if (this.getMHuInfo().getHuPosList().size() > 1) {
            if (getLastOpInfo().getLastOpPos() == dPos) {
                this.room.setEvenDpos(this.room.getEvenDpos() + 1);
            } else {
                this.room.setEvenDpos(1);
            }
            return getLastOpInfo().getLastOpPos();
        }
        if (this.getMHuInfo().getHuPos() != dPos && this.getMHuInfo().getHuPos() != -1) {
            this.room.setEvenDpos(1);
            return (this.getDPos() + 1) % getPlayerNum();
        } else {
            this.room.setEvenDpos(this.room.getEvenDpos() + 1);
            // ，由上局庄家继续做庄						
            return this.dPos;
        }
    }

    /**
     * 小局结算消息
     */
    @Override
    protected <T> BaseSendMsg setEnd(long roomID, T setEnd) {
        return SAHHBMJ_SetEnd.make(roomID, setEnd, isRoomEnd());
    }

    @Override
    public void kaiJinNotify(MJCard jinCard, MJCard jinCard2) {
        getRoomPlayBack().playBack2All(SAHHBMJ_Jin.make(getRoom().getRoomID(), jinCard.getCardID(), 0,
                jinCard2.getCardID(), getMJSetCard().getRandomCard().getNormalMoCnt(),
                getMJSetCard().getRandomCard().getGangMoCnt()));
        return;
    }

    public void tanPai(int pos, long pid) {
        getRoomPlayBack().playBack2Pos(pos, (SAHHBMJ_GetRoomInfo) ((AHHBMJRoom) room).getRoomInfo(pid), null);
        return;
    }

    @Override
    protected AbsMJSetPos absMJSetPos(int posID) {

        return new AHHBMJSetPos(posID, (AHHBMJRoomPos) this.room.getRoomPosMgr().getPosByPosID(posID), this);
    }

    @Override
    protected void absMJSetCard() {
        this.setSetCard(new AHHBMJSetCard(this));
    }

    @Override
    protected AbsMJSetPosMgr absMJSetPosMgr() {
        return new AHHBMJSetPosMgr(this);
    }

    @Override
    protected <T> BaseSendMsg setStart(long roomID, T setInfo) {
        return SAHHBMJ_SetStart.make(roomID, setInfo);
    }

    @Override
    protected AbsMJSetRound nextSetRound(int roundID) {
        return new AHHBMJSetRound(this, roundID);
    }

    @Override
    public boolean isConfigName() {
        return true;
    }

    @Override
    public void sendSetPosCard() {
        for (int i = 0; i < room.getPlayerNum(); i++) {
            AbsMJSetPos setPos = posDict.get(i);
            setPos.sortCards();
        }
        for (int i = 0; i < room.getPlayerNum(); i++) {
            long pid = this.room.getRoomPosMgr().getPosByPosID(i).getPid();
            this.room.getRoomPosMgr().notify2Pos(i,
                    SAHHBMJ_SetPosCard.make(this.room.getRoomID(), this.setPosCard(pid)));
        }
    }

    @Override
    public void MJApplique(int pos) {
        AbsMJSetPos setPos = posDict.get(pos);// 获得玩家					
        BaseMJSet_Pos posInfoOther = setPos.getNotify(false);
        BaseMJSet_Pos posInfoSelf = setPos.getNotify(true);
        getRoomPlayBack().playBack2Pos(pos,
                SAHHBMJ_Applique.make(getRoom().getRoomID(), pos, OpType.Out, 0, false, posInfoSelf, 0), null);
        for (int i = 0; i < getRoom().getPlayerNum(); i++) {
            if (i == pos)
                continue;
            getRoom().getRoomPosMgr().notify2Pos(i,
                    SAHHBMJ_Applique.make(getRoom().getRoomID(), pos, OpType.Out, 0, false, posInfoOther, 0));
        }
    }


    /**
     * 设置setstate
     */
    @Override
    public void setState(SetState setState) {
        this.state = setState;
        this.startMS = CommTime.nowMS();
        AHHBMJRoomPosMgr roomPosMgr = (AHHBMJRoomPosMgr) this.room.getRoomPosMgr();
        this.room.getRoomPosMgr().notify2All(
                SAHHBMJ_ChangeStatus.make(this.getRoom().getRoomID(), this.getRoom().getCurSetID(), setState, roomPosMgr.getPiaoFenList()));
    }

    /**
     * 计算当局每个pos位置的分数。
     */
    @Override
    protected void calcCurSetPosPoint() {
        // 计算位置小局分数	
        this.getPosDict().values().forEach(k -> k.calcPosPoint());
        // 其他特殊结算 连庄记录	
        this.calcOtherPoint();
    }


    // 结算积分
    @Override
    public void calcPoint() {
        AHHBMJRoom room = (AHHBMJRoom) this.getRoom();
        if (this.getMHuInfo().isHuEmpty()) {
            room.huangZhuangCount += 1;
        }
        super.calcPoint();
    }


    @Override
    public boolean equals(Object o) {
        if (o instanceof AHHBMJRoomSet) {
            if (getSetID() == ((AHHBMJRoomSet) o).getSetID()) {
                return true;
            }
        }
        return false;
    }


    public boolean isGSP() {
        return isGSP;
    }

    public void setGSP(boolean GSP) {
        isGSP = GSP;
    }

    public boolean isQU_WAN() {
        return ((CAHHBMJ_CreateRoom) getRoom().getCfg()).getPaishu().contains(AHHBMJRoomEnum.PaiShu.QU_WAN.ordinal());
    }

    public boolean isQU_ZI() {
        return ((CAHHBMJ_CreateRoom) getRoom().getCfg()).getPaishu().contains(AHHBMJRoomEnum.PaiShu.QU_ZI.ordinal());
    }

    public boolean isQU_FENG() {
        return ((CAHHBMJ_CreateRoom) getRoom().getCfg()).getPaishu().contains(AHHBMJRoomEnum.PaiShu.QU_FENG.ordinal());
    }

    public boolean isGANG_SUI_HU_ZOU() {
        return ((CAHHBMJ_CreateRoom) getRoom().getCfg()).getGangpaisuanfen() == AHHBMJRoomEnum.GangPai.GANG_SUI_HU_ZOU.ordinal();
    }

    public boolean isQUE_YI() {
        return ((CAHHBMJ_CreateRoom) getRoom().getCfg()).getJiafan().contains(AHHBMJRoomEnum.JiaFan.QUE_YI.ordinal());
    }

    public boolean isBAO_HU() {
        return ((CAHHBMJ_CreateRoom) getRoom().getCfg()).getJiafan().contains(AHHBMJRoomEnum.JiaFan.BAO_HU.ordinal());
    }

    public boolean isJIA_ZI() {
        return ((CAHHBMJ_CreateRoom) getRoom().getCfg()).getJiafan().contains(AHHBMJRoomEnum.JiaFan.JIA_ZI.ordinal());
    }

    public boolean isQIDUI_X2() {
        return ((CAHHBMJ_CreateRoom) getRoom().getCfg()).getFanbei().contains(AHHBMJRoomEnum.FanBei.QIDUI_X2.ordinal());
    }

    public boolean isGANGHU_X2() {
        return ((CAHHBMJ_CreateRoom) getRoom().getCfg()).getFanbei().contains(AHHBMJRoomEnum.FanBei.GANGHU_X2.ordinal());
    }

    public boolean isSSL_X2() {
        return ((CAHHBMJ_CreateRoom) getRoom().getCfg()).getFanbei().contains(AHHBMJRoomEnum.FanBei.SSL_X2.ordinal());
    }


    public boolean isQXSSL_X4() {
        return ((CAHHBMJ_CreateRoom) getRoom().getCfg()).getFanbei().contains(AHHBMJRoomEnum.FanBei.QXSSL_X4.ordinal());
    }

    /**
     * 摸牌
     *
     * @param opPos
     * @param isNormalMo
     * @return
     */
    public MJCard getCard(int opPos, boolean isNormalMo) {
        AHHBMJSetPos setPos = (AHHBMJSetPos) this.posDict.get(opPos);
        // 随机摸牌
        MJCard card = this.setCard.pop(isNormalMo, this.getGodInfo().godHandCard(setPos));
        if (Objects.isNull(card)) {
            // 黄庄位置
            this.getMHuInfo().setHuangPos(opPos);
            return null;
        }
        // 设置牌
        setPos.getCard(card);
        // 通知房间内的所有玩家，指定玩家摸牌了。
        this.notify2GetCard(setPos);
        // 通知房间内的所有玩家，指定玩家摸牌了。
        if (card != null) {
            if (card.type >= 50) {
                MJFactory.getOpCard(BuHuaImpl.class).checkOpCard(setPos, FlowerEnum.HAND_CARD.ordinal());
            }
        }
        return card;
    }

    public final void initSetPosCard1() {
        if (getGodInfo().isGodCardMode()) {
            // 神牌模式下：（只允许内测时开启）
            this.initGodPosCard();
        } else {
            // 正常模式下：上线模式
            // 初始玩家身上的牌
            this.initPosCard();
        }
        sendSetPosCard();
    }
}
