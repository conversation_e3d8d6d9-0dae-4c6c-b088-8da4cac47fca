package business.global.pk.wxls.utlis;
			
public class WXLSRoomEnum {

	public enum WXLSXianShiConfigEnum {

		NOT_LIMIT(0),

		// 90S
		Second90(91000),
		// 180S
		Second180(181000),
		// 45S
		Second45(46000),
		// 60S
		Second60(61000),
		// 30S
		Second30(31000),
		;
		// 不限制;
		private int value;
		WXLSXianShiConfigEnum(int value) {
			this.value = value;
		}
		public int getValue() {
			return value;
		}
		public void setValue(int value) {
			this.value = value;
		}

		public static WXLSXianShiConfigEnum valueOf(int value) {
			for (WXLSXianShiConfigEnum flow : WXLSXianShiConfigEnum.values()) {
				if (flow.ordinal() == value) {
					return flow;
				}
			}
			return WXLSXianShiConfigEnum.NOT_LIMIT;
		}
	}
	/**
	 * A2345为最小顺子，允许跳过比牌；,加一色
	 */
	public enum WXLSKeXuanWanFa {
		A12345Min(0),
		ALLOWPASS(1),
		JiaYiSe(2),


		;
		private int value;
		WXLSKeXuanWanFa(int value) {this.value = value;}
		public int value(){return this.value;}
	}

	/**
	 * 计分+1，计分*2，无打枪
	 */
	public enum WXLSDaQiang {
		PlsuOne(0),
		MulitiTwo(1),
		Nope(2),
		;
		private int value;
		WXLSDaQiang(int value) {this.value = value;}
		public int value(){return this.value;}
		public static WXLSDaQiang valueOf(int value) {
			for (WXLSDaQiang huPai : WXLSDaQiang.values()) {
				if (huPai.ordinal() == value) {
					return huPai;
				}
			}
			return WXLSDaQiang.Nope;
		}
	}
	/**
	 * 特殊牌不互比，特殊牌互比
	 */
	public enum WXLSSpecial {
		Nope(0),
		Compare(1),
		;
		private int value;
		WXLSSpecial(int value) {this.value = value;}
		public int value(){return this.value;}
		public static WXLSSpecial valueOf(int value) {
			for (WXLSSpecial huPai : WXLSSpecial.values()) {
				if (huPai.ordinal() == value) {
					return huPai;
				}
			}
			return WXLSSpecial.Nope;
		}
	}

	/**
	 * 自动准备,小局托管解散,解散次数不超过5次,托管2小局解散,解散次数不超过3次
	 */
	public enum WXLSGameRoomConfigEnum {

		/**
		 * 自动准备
		 */
		ZiDongZhunBei,
		/**
		 * 小局托管自动解散
		 */
		AuToEnd,
		/**
		 * 解散次数不超过5次
		 */
		JieSanCiShu5,
		/**
		 *  // 托管2小局解散:连续3小局托管
		 */
		TuoGuan2XiaoJuJieSan,
		/**
		 * 解散次数不超过3次
		 */
		JieSanCiShu3,

	}

}									
