package business.global.mj.afmj.optype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCard;
import business.global.mj.MJCardInit;
import business.global.mj.afmj.AFMJRoom;
import business.global.mj.afmj.AFMJRoomSet;
import business.global.mj.afmj.AFMJSetPos;
import business.global.mj.afmj.hutype.*;
import business.global.mj.manage.MJFactory;
import business.global.mj.ting.AbsTing;
import business.global.mj.afmj.AFMJRoomEnum.AFMJOpPoint;
import business.global.mj.util.HuUtil;
import cenum.mj.MJSpecialEnum;
import cenum.mj.OpType;
import business.global.mj.afmj.AFMJRoomEnum.*;


import java.util.ArrayList;
import java.util.List;


public class AFMJTingImpl extends AbsTing {

	@Override
	public boolean tingHu(AbsMJSetPos mSetPos, MJCardInit mCardInit ) {

		// 没打精的情况下系统不显示听牌，没打精就不能胡。
		// “必博一精”或“必博正精或两副”玩法下，如果玩家手牌没有精，且没有满足博精条件，则不能炮胡、抢杠胡，只能自摸。
		// 不是随精玩法
		if(((AFMJRoom)mSetPos.getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoYiJing)){
			// “必博一精”玩法
			if(((AFMJSetPos)mSetPos).existJingByAllCards() && !((AFMJSetPos)mSetPos).existBo1Jin()){
				// 手牌有精牌且没有博一精
				return false;
			}
		}else if(((AFMJRoom)mSetPos.getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoZhengJingHuoLiangFu)) {
			// “必博正精或两副”玩法
			if(((AFMJSetPos)mSetPos).existJingByAllCards() && !((AFMJSetPos)mSetPos).existBo1ZhengJinOrBo2FuJing()){
				// 手牌有精牌且没有博一正精且没有博两副精
				return false;
			}
		}

		// 检测七对
		// 七小对：七个对子；（可以有四张一样的牌）
		if(MJFactory.getHuCard(AFMJDDHuImpl.class).checkHuCard(mSetPos, mCardInit)){
			return true;
		}

		// 碰碰胡
		if (MJFactory.getHuCard(AFMJPPImpl.class).checkHuCard(mSetPos, mCardInit)) {
			return true;
		}

		// 检测十三不靠
		if(!AFMJOpPoint.Not.equals((AFMJOpPoint) MJFactory.getHuCard(AFMJSSBKQingImpl.class).checkHuCardReturn(mSetPos, mCardInit))){
			return true;
		}else if(!AFMJOpPoint.Not.equals((AFMJOpPoint) MJFactory.getHuCard(AFMJSSBKImpl.class).checkHuCardReturn(mSetPos, mCardInit))){
			return true;
		}

		// 检测德国
		// 德国：胡牌的时候手中没有精或精都是当本身用；
		if(!AFMJOpPoint.Not.equals((AFMJOpPoint) MJFactory.getHuCard(AFMJDeGuoImpl.class).checkHuCardReturn(mSetPos, mCardInit))){
			if(AFMJOpPoint.PiHu.equals((AFMJOpPoint) MJFactory.getHuCard(AFMJDeGuoImpl.class).checkHuCardReturn(mSetPos, mCardInit))
					||
					AFMJOpPoint.NotJin.equals((AFMJOpPoint) MJFactory.getHuCard(AFMJDeGuoImpl.class).checkHuCardReturn(mSetPos, mCardInit))){
			}
		}

		// 屁胡：基本的胡牌牌型，可胡字牌
		if (MJFactory.getHuCard(AFMJHuImpl.class).checkHuCard(mSetPos, mCardInit)) {
			return true;
		}

		return false;
	}

	/**
	 * 检查操作
	 */
	private boolean checkOp(AFMJSetPos sPos, MJCardInit mCardInit, OpType opType) {
		return sPos.checkPosTing(mCardInit, opType);
	}

	public boolean checkHuCardReturn(AbsMJSetPos mSetPos, Object object) {
		if (AFMJOpPoint.Not.equals(object)) {
			return false;
		}
		return true;
	}

	/**
	 * 检查听到的牌
	 */
	@Override
	public List<Integer> absCheckTingCard(AbsMJSetPos mSetPos,List<MJCard> allCardList) {
		List<Integer> ret = new ArrayList<>();
		// 遍历其他牌
		MJCardInit mInit = mSetPos.mjCardInit(allCardList, true);
		if (null == mInit) {
			return ret;
		}
		boolean isHu = true;
		// 添加一张任意牌，进行测试是否能胡
		isHu = tingHu(mSetPos,
				this.newMJCardInit(mInit.getAllCardInts(), mInit.getJins(), MJSpecialEnum.NOT_JIN.value()));
		if (!isHu) {
			return ret;
		}
		ret.addAll(mSetPos.getSet().getmJinCardInfo().getJinKeys());
		// 遍历其他牌
		for (int type : HuUtil.CheckTypes) {
			isHu = tingHu(mSetPos, this.newMJCardInit(mInit.getAllCardInts(), mInit.getJins(), type));
			if (isHu) {
				if (mSetPos.getSet().getmJinCardInfo().getJinKeys().contains(type)) {
					continue;
				} else {
					if (!ret.contains(type))
						ret.add(type);
				}

			}

		}
		mInit = null;
		allCardList = null;
		return ret;
	}

	/**
	 * 检查听到的牌
	 */
	public boolean checkTingCardList(MJCardInit mInit) {
		List<Integer> ret = new ArrayList<>();
		boolean isHu = true;
		// 遍历其他牌
		for (int type : HuUtil.CheckTypes) {
			isHu = checkTingHu(new MJCardInit(mInit.getAllCardInts(), type));
			if (isHu) {
				if (!ret.contains(type)) {
					return true;
				}
			}
		}
		mInit = null;
		return false;
	}

	/**
	 * 检查杠后是否可以听胡
	 *
	 * @param mCardInit
	 * @return
	 */
	public boolean checkTingHu(MJCardInit mCardInit) {
		return MJFactory.getHuCard(AFMJHuImpl.class).checkHuCard(null, mCardInit);
	}


	/**
	 * 听列表
	 *
	 * @param lists
	 *            列表
	 * @param idx
	 *            下标
	 * @return
	 */
	public List<Integer> tingList(AbsMJSetPos mSetPos, List<Integer> lists, int idx) {
		AFMJRoomSet jSet = (AFMJRoomSet)mSetPos.getSet();
		// 获取所有牌
		List<MJCard> allCards = mSetPos.allCards();
		// 如果牌的下标 == 所有牌 -1
		if (allCards.size() == idx) {
			return lists;
		}
		// 获取牌ID
		int cardId = allCards.get(idx).cardID;
		// 移除一张牌
		allCards.remove(idx);
		// 听牌
		List<Integer> tingList = absCheckTingCard(mSetPos, allCards);
		idx++;
		// 判断听牌数
		if(!tingJingPai(mSetPos)){ // 只能听手牌中的精牌
			if(!mSetPos.getSet().getmJinCardInfo().getJinKeys().contains(cardId / 100)){ // 不是正副精牌
				return tingList(mSetPos, lists, idx);
			}
		}
		if (tingList.size() > 0) {
			mSetPos.getPosOpNotice().addTingCardList(cardId / 100, tingList);
			lists.add(cardId);
			return tingList(mSetPos, lists, idx);
		}
		return tingList(mSetPos, lists, idx);
	}


	/**
	 *
	 * @param mSetPos 玩家位置信息
	 * @return 是否可以听牌，true:已经博精，可以听不是精牌的听牌，false：只能听精牌
	 */
	public Boolean tingJingPai(AbsMJSetPos mSetPos){

		AFMJRoomSet jset = (AFMJRoomSet)mSetPos.getSet();
		boolean isBoJing= false; // 是否博精:打出精牌，true:已经博精，可以听不是精牌的听牌，false：只能听精牌
		if(((AFMJRoom)mSetPos.getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoYiJing)){ // 必博一精

			// 打出去区域
			List<Integer> OutCardIDs = mSetPos.getOutCardIDs();
			for(Integer cardId : OutCardIDs){
				if(jset.getmJinCardInfo().getJinKeys().contains(cardId / 100)){
					isBoJing = true;
					break;
				}
			}

			if(!isBoJing){ // 没有打出精牌，检测手牌是否有精牌
				if(mSetPos.allCards().stream().noneMatch(k -> jset.getmJinCardInfo().getJinKeys().contains(k.type))){ // 手牌没有精牌
					isBoJing = true;
				}
			}

		}else if(((AFMJRoom)mSetPos.getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoZhengJingHuoLiangFu)){ // 必博正精或两副
			// 打出去区域
			int outfuJingCount = 0; // 打出去的副精牌数
			List<Integer> OutCardIDs = mSetPos.getOutCardIDs();
			for(Integer cardId : OutCardIDs){
				if(jset.getZhengJing().type == cardId / 100){ // 有打出去的正精
					isBoJing = true;
					break;
				}
				if(jset.getFuJing().type == cardId / 100){ // 有打出去的副精
					outfuJingCount += 1;
				}
				if(outfuJingCount == 2){ // 打出去2张副精
					isBoJing = true;
					break;
				}
			}

			if(!isBoJing){ // 没有打出精牌，检测手牌是否有精牌
				if(mSetPos.allCards().stream().noneMatch(k -> jset.getmJinCardInfo().getJinKeys().contains(k.type))){ // 手牌没有精牌
					isBoJing = true;
				}
			}
		}else{ // 不是必博一精，可以听不是精牌的听牌
			isBoJing = true;
		}

		return  isBoJing;

	}




}
