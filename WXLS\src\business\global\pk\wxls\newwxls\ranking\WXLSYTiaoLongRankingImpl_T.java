package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.Map;	
	
/**	
 *一条龙	
 */	
public class WXLSYTiaoLongRankingImpl_T extends WXLSAbstractRanking {	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        WXLSRankingResult result = null;	
        if (player.getCardSize() == 13) {	
            Map<Integer, Integer> rankCount = player.getCardsRankCountMap();	
            if (rankCount.size() == 13) {	
                result = new WXLSRankingResult();	
                result.setPockerCards(player.getCards());	
                result.setRankingEnum(WXLSRankingEnum.YTiaoLong);	
            }	
        }	
        return result;	
    }	
}	
