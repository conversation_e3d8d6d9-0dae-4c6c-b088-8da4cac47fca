package business.wxls.c2s.iclass;	
	
import jsproto.c2s.cclass.BaseSendMsg;	
	
public class SWXLS_LostConnect extends BaseSendMsg {	
    	
    public long roomID;	
    public long pid;	
    public boolean isLostConnect;	
    public boolean isShowLeave;	
	
    public static SWXLS_LostConnect make(long roomID, long pid, boolean isLostConnect, boolean isShowLeave) {	
    	SWXLS_LostConnect ret = new SWXLS_LostConnect();	
        ret.roomID = roomID;	
        ret.pid = pid;	
        ret.isLostConnect = isLostConnect;	
        ret.isShowLeave = isShowLeave;	
	
        return ret;	
    	
	
    }	
}	
