package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.Iterator;	
import java.util.Map;	
	
/**	
 * 铁支	
 */	
public class WXLSFourOfTheKindRankingImpl extends WXLSAbstractRanking {	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        WXLSRankingResult result = null;	
        Map<Integer, Integer> rankCount = player.getCardsRankCountMap();	
        boolean hasFour = false;	
	
        if (player.getCardSize() == 5) {	
            Iterator<Map.Entry<Integer, Integer>> it = rankCount.entrySet().iterator();	
            while (it.hasNext()) {	
                int count = it.next().getValue();	
                if (count >= 4 ) {
                    hasFour = true;	
                    break;	
                }	
            }	
        }	
	
        if (hasFour) {	
            result = new WXLSRankingResult();	
            result.setRankingEnum(WXLSRankingEnum.FOUR_OF_THE_KIND);	
        }	
	
        return result;	
    }	
	
}	
