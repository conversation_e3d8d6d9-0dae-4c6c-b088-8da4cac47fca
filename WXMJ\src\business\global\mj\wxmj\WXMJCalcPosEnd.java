package business.global.mj.wxmj;

import business.global.mj.AbsCalcPosEnd;
import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCard;
import business.global.mj.MJCardInit;
import business.global.mj.wxmj.WXMJRoomEnum.WXMJOpPoint;
import business.global.mj.util.HuUtil;
import cenum.mj.HuType;
import cenum.mj.MJEndType;
import cenum.mj.OpType;

import java.util.*;
import java.util.stream.Collectors;

public class WXMJCalcPosEnd extends AbsCalcPosEnd {
    private WXMJRoomSet set;
    private Map<WXMJOpPoint, Integer> newHuTypeMap = new HashMap<>();
    private int hua = 0;

    public WXMJCalcPosEnd(AbsMJSetPos mSetPos) {
        super(mSetPos);
        this.set = (WXMJRoomSet) mSetPos.getSet();
    }

    @Override
    public int calcPoint(boolean isZhuang, Object... params) {
        return 0;
    }

    @Override
    public void calcPosEnd(AbsMJSetPos mSetPos) {
        mSetPos.setEndPoint(mSetPos.getEndPoint() + mSetPos.getDeductPoint());
    }

    /**
     * 胡牌，赢家:庄家 ，(底分+水数)*胡牌倍数+(插码数*胡牌倍数) 输家:水数 其他输入付钱
     *
     * @param mSetPos 玩家信息
     */
    @Override
    public void calcPosPoint(AbsMJSetPos mSetPos) {
        calcChaTai();
        // 臭庄则当局不计分。		
        if (this.set.getMHuInfo().isHuEmpty()) {
            return;
        }

        if (mSetPos.getHuType() == HuType.NotHu || mSetPos.getHuType() == HuType.DianPao) {
            return;
        }
        if (mSetPos.getHuType() == HuType.JiePao) {
            this.setDianPao();
        }
        calcGang();
        // 计算胡牌		
        this.calcHu();
    }

    private void calcChaTai() {
        if (((WXMJRoom) set.getRoom()).getTaiMap().get(this.getMSetPos().getPosID()) != null && ((WXMJRoom) set.getRoom()).getTaiMap().get(this.getMSetPos().getPosID()) != 0) {
            newHuTypeMap.put(WXMJOpPoint.PF, ((WXMJRoom) set.getRoom()).getTaiMap().get(this.getMSetPos().getPosID()));
        }

    }


    public void calcPosPoint() {
        this.getMSetPos().setEndPoint(this.getMSetPos().getEndPoint() + this.getMSetPos().getDeductPoint());
    }

    /**
     * 飘分
     */
    public int getPiaoFenNum(AbsMJSetPos mSetPos) {
        WXMJRoomPos roomPos = (WXMJRoomPos) mSetPos.getRoomPos();
        return Math.max(roomPos.getPiaoFenEnum().value(), 0);
    }

    public void calcGang() {
        WXMJSetPos setPos = (WXMJSetPos) this.getMSetPos();
        for (List<Integer> lll : setPos.getPublicCardList()) {
            if (lll.get(0) == OpType.Gang.value() || lll.get(0) == OpType.JieGang.value()) {
                if (lll.get(2) / 100 > 40) {
                    hua += 3;
                } else {
                    hua += 1;
                }
            } else if (lll.get(0) == OpType.AnGang.value()) {
                if (lll.get(2) / 100 > 40) {
                    hua += 4;
                } else {
                    hua += 2;
                }
            } else if (lll.get(0) == OpType.Peng.value()) {
                if (lll.get(2) / 100 > 40) {
                    hua += 1;
                }
            }
        }

        Map<Integer, List<MJCard>> cardTypeList = setPos.allCards().stream().collect(Collectors.groupingBy(MJCard::getType, Collectors.toList()));
        for (Map.Entry<Integer, List<MJCard>> ccc : cardTypeList.entrySet()) {
            if (cardTypeList.size() >= 8) {
                System.out.println();
            }
            if (ccc.getKey() > 40 && ccc.getValue().size() >= 4) {
                hua += 2;
            } else if (ccc.getKey() == setPos.getHandCard().getType() && ccc.getValue().size() == 3 && ccc.getKey() > 40) {
                if (this.getMSetPos().getHuType() == HuType.ZiMo) {
                    hua += 2;
                } else if (this.getMSetPos().getHuType() == HuType.JiePao) {
                    hua += 1;
                }

            }
        }

    }

    /**
     * 计算胡
     */
    private void calcHu() {
        WXMJSetPos setPos = (WXMJSetPos) this.getMSetPos();
        hua += setPos.getPosOpRecord().sizeHua();

        int beishu = 1;
        int ewai = 0;
        if (((WXMJRoom) set.getRoom()).getRoomCfg().getJiesuan() == WXMJRoomEnum.WXMJJieSuan.sihuashengji.ordinal()) {
            ewai += 1;
        }

        if (((WXMJRoom) set.getRoom()).getRoomCfg().getJiesuan() == WXMJRoomEnum.WXMJJieSuan.anhuasuan.ordinal()) {
            for (Object ooo : setPos.getPosOpRecord().getOpHuList()) {
                newHuTypeMap.put((WXMJOpPoint) ooo, huPaiFen((WXMJOpPoint) ooo));
            }
            if (hua > 0) {
                newHuTypeMap.put(WXMJOpPoint.HS, hua);
            }
            if (setPos.getHuType() == HuType.ZiMo) {
                newHuTypeMap.put(WXMJOpPoint.ZM, 2);
                beishu *= 2;
                for (Object ooo : setPos.getPosOpRecord().getOpHuList()) {
                    beishu *= huPaiFen((WXMJOpPoint) ooo);
                    newHuTypeMap.put((WXMJOpPoint) ooo, huPaiFen((WXMJOpPoint) ooo));
                }
                int point = (hua + 1) * beishu;
                for (Map.Entry<Integer, AbsMJSetPos> ccc : set.getPosDict().entrySet()) {
                    if (ccc.getValue().getPosID() != setPos.getPosID()) {
                        ccc.getValue().setDeductPoint(ccc.getValue().getDeductPoint() - point - ((WXMJSetPos) ccc.getValue()).getPiaofen() - setPos.getPiaofen());
                        setPos.setDeductPoint(setPos.getDeductPoint() + point + ((WXMJSetPos) ccc.getValue()).getPiaofen() + setPos.getPiaofen());
                    }
                }
            } else if (setPos.getHuType() == HuType.JiePao) {
                for (Object ooo : setPos.getPosOpRecord().getOpHuList()) {
                    beishu *= huPaiFen((WXMJOpPoint) ooo);
                }
                int point = (hua + 1) * beishu;
                set.getMJSetPos(set.getLastOpInfo().getLastOutCardPos()).setDeductPoint(set.getMJSetPos(set.getLastOpInfo().getLastOutCardPos()).getDeductPoint() - point - ((WXMJSetPos) set.getMJSetPos(set.getLastOpInfo().getLastOutCardPos())).getPiaofen() - setPos.getPiaofen());
                setPos.setDeductPoint(setPos.getDeductPoint() + point + ((WXMJSetPos) set.getMJSetPos(set.getLastOpInfo().getLastOutCardPos())).getPiaofen() + setPos.getPiaofen());
            } else if (setPos.getHuType() == HuType.QGH) {
                beishu = 2;
                if (((WXMJRoom) set.getRoom()).getRoomCfg().getQianggang() == 0) {
                    int point = ((hua + 1) * beishu) * (set.getPlayerNum() - 1);
                    for (Map.Entry<Integer, AbsMJSetPos> ccc : set.getPosDict().entrySet()) {
                        if (ccc.getValue().getPosID() == setPos.getPosID()) {
                            point += ((WXMJSetPos) ccc.getValue()).getPiaofen();
                        }
                    }
                    setPos.setDeductPoint(setPos.getDeductPoint() + point);
                    set.getMJSetPos(set.getLastOpInfo().getLastOpPos()).setDeductPoint(set.getMJSetPos(set.getLastOpInfo().getLastOpPos()).getDeductPoint() - point);
                } else {
                    for (Object ooo : setPos.getPosOpRecord().getOpHuList()) {
                        beishu *= huPaiFen((WXMJOpPoint) ooo);
                    }
                    int point = (hua + 1 + 1) * beishu;
                    for (Map.Entry<Integer, AbsMJSetPos> ccc : set.getPosDict().entrySet()) {
                        if (ccc.getValue().getPosID() != setPos.getPosID()) {
                            ccc.getValue().setDeductPoint(ccc.getValue().getDeductPoint() - point - ((WXMJSetPos) ccc.getValue()).getPiaofen() - setPos.getPiaofen());
                            setPos.setDeductPoint(setPos.getDeductPoint() + point + ((WXMJSetPos) ccc.getValue()).getPiaofen() + setPos.getPiaofen());
                        }
                    }

                }
            }
        } else if (((WXMJRoom) set.getRoom()).getRoomCfg().getJiesuan() == WXMJRoomEnum.WXMJJieSuan.sanhuashengji.ordinal() || ((WXMJRoom) set.getRoom()).getRoomCfg().
                getJiesuan() == WXMJRoomEnum.WXMJJieSuan.sihuashengji.ordinal()) {//三花升级
//            for (Object ooo : setPos.getPosOpRecord().getOpHuList()) {
//                newHuTypeMap.put((WXMJOpPoint) ooo, 1);
//            }
            if (setPos.getHuType() == HuType.ZiMo) {
                int point = 2;
                if (setPos.getPosOpRecord().getOpHuList().contains(WXMJOpPoint.GSH)) {
                    point = 4;
                    newHuTypeMap.put(WXMJOpPoint.GSH, 4);
                } else {
                    newHuTypeMap.put(WXMJOpPoint.ZM, 2);
                }
                int i = hua - ewai;
                if (i > 2) {
                    newHuTypeMap.put(WXMJOpPoint.HS, i - 2);
                }
                point += i - 2 < 0 ? 0 : i - 2;
                if (((WXMJRoom) set.getRoom()).getRoomCfg().getJiesuan() == WXMJRoomEnum.WXMJJieSuan.sanhuashengji.ordinal()) {
                    for (Object ooo : setPos.getPosOpRecord().getOpHuList()) {
                        if (LinghuPaiFen((WXMJOpPoint) ooo) != 0) {
                            newHuTypeMap.put((WXMJOpPoint) ooo, 1);
                            point += 1;
                        }
                    }
                }
                for (Map.Entry<Integer, AbsMJSetPos> ccc : set.getPosDict().entrySet()) {
                    if (ccc.getValue().getPosID() != setPos.getPosID()) {
                        ccc.getValue().setDeductPoint(ccc.getValue().getDeductPoint() - point - ((WXMJSetPos) ccc.getValue()).getPiaofen() - setPos.getPiaofen());
                        setPos.setDeductPoint(setPos.getDeductPoint() + point + ((WXMJSetPos) ccc.getValue()).getPiaofen() + setPos.getPiaofen());
                    }
                }
            } else if (setPos.getHuType() == HuType.JiePao) {
                int point = 1;
                if (setPos.getPosOpRecord().getOpHuList().contains(WXMJOpPoint.GSH)) {
                    point = 4;
                    newHuTypeMap.put(WXMJOpPoint.GSH, 4);
                } else {
                    newHuTypeMap.put(WXMJOpPoint.PH, 1);
                }
                int i = hua - ewai;
                if (i > 2) {
                    newHuTypeMap.put(WXMJOpPoint.HS, i - 2);
                }
                point += i - 2 < 0 ? 0 : i - 2;
                for (Object ooo : setPos.getPosOpRecord().getOpHuList()) {
                    if (LinghuPaiFen((WXMJOpPoint) ooo) != 0) {
                        point += 1;
                        newHuTypeMap.put((WXMJOpPoint) ooo, 1);
                    }
                }
                set.getMJSetPos(set.getLastOpInfo().getLastOutCardPos()).setDeductPoint(set.getMJSetPos(set.getLastOpInfo().getLastOutCardPos()).getDeductPoint() - point - ((WXMJSetPos) set.getMJSetPos(set.getLastOpInfo().getLastOutCardPos())).getPiaofen() - setPos.getPiaofen());
                setPos.setDeductPoint(setPos.getDeductPoint() + point + ((WXMJSetPos) set.getMJSetPos(set.getLastOpInfo().getLastOutCardPos())).getPiaofen() + setPos.getPiaofen());
            } else if (setPos.getHuType() == HuType.QGH) {
                if (((WXMJRoom) set.getRoom()).getRoomCfg().getQianggang() == 0) {
                    int point = 2;
                    if (setPos.getPosOpRecord().getOpHuList().contains(WXMJOpPoint.GSH)) {
                        point = 4;
                        newHuTypeMap.put(WXMJOpPoint.GSH, 4);
                    } else {
                        newHuTypeMap.put(WXMJOpPoint.QGH, 2);
                    }
                    int i = hua - ewai + 1;
                    if (i > 2) {
                        newHuTypeMap.put(WXMJOpPoint.HS, i - 2);
                    }
                    point += i - 2 < 0 ? 0 : i - 2;

                    for (Object ooo : setPos.getPosOpRecord().getOpHuList()) {
                        if (LinghuPaiFen((WXMJOpPoint) ooo) != 0) {
                            point += 1;
                            newHuTypeMap.put((WXMJOpPoint) ooo, 1);
                        }
                    }
                    point = point * (set.getPlayerNum() - 1);
                    for (Map.Entry<Integer, AbsMJSetPos> ccc : set.getPosDict().entrySet()) {
                        if (ccc.getValue().getPosID() != setPos.getPosID()) {
                            point += ((WXMJSetPos) ccc.getValue()).getPiaofen();
                        }
                    }
                    setPos.setDeductPoint(setPos.getDeductPoint() + point);
                    set.getMJSetPos(set.getLastOpInfo().getLastOpPos()).setDeductPoint(set.getMJSetPos(set.getLastOpInfo().getLastOpPos()).getDeductPoint() - point);
                } else {
                    int point = 2;
                    if (setPos.getPosOpRecord().getOpHuList().contains(WXMJOpPoint.GSH)) {
                        point = 4;
                        newHuTypeMap.put(WXMJOpPoint.GSH, 4);
                    } else {
                        newHuTypeMap.put(WXMJOpPoint.QGH, 2);
                    }
                    int i = hua - ewai + 1;
                    if (i > 2) {
                        newHuTypeMap.put(WXMJOpPoint.HS, i - 2);
                    }
                    point += i - 2 < 0 ? 0 : i - 2;
                    for (Object ooo : setPos.getPosOpRecord().getOpHuList()) {
                        if (LinghuPaiFen((WXMJOpPoint) ooo) != 0) {
                            point += 1;
                            newHuTypeMap.put((WXMJOpPoint) ooo, 1);
                        }
                    }
                    for (Map.Entry<Integer, AbsMJSetPos> ccc : set.getPosDict().entrySet()) {
                        if (ccc.getValue().getPosID() != setPos.getPosID()) {
                            ccc.getValue().setDeductPoint(ccc.getValue().getDeductPoint() - point - ((WXMJSetPos) ccc.getValue()).getPiaofen() - setPos.getPiaofen());
                            setPos.setDeductPoint(setPos.getDeductPoint() + point + ((WXMJSetPos) ccc.getValue()).getPiaofen() + setPos.getPiaofen());
                        }
                    }
                }
            }
        }
    }

    public int huPaiFen(WXMJOpPoint opPoint) {
        switch (opPoint) {
            case PH:
                return 1;
            case DDC:
            case DDH:
                return 2;
            case GSH:
                return 4;
        }
        return 0;
    }

    public int LinghuPaiFen(WXMJOpPoint opPoint) {
        switch (opPoint) {
            case DDC:
            case DDH:
                return 2;
        }
        return 0;
    }

    /**
     * 判断是不是顺子
     *
     * @return
     */

    private class CalcPosEnd {
        @SuppressWarnings("unused")
        private Map<WXMJOpPoint, Integer> huTypeMap = new HashMap<>();

        public CalcPosEnd(Map<WXMJOpPoint, Integer> huTypeMap) {
            this.huTypeMap = huTypeMap;
        }

    }

    @Override
    public <T> void calcOpPointType(T opType, int count) {
        WXMJOpPoint opPoint = (WXMJOpPoint) opType;
        switch (opPoint) {
            case Not:
                break;
            default:
                this.addhuType(opPoint, count, MJEndType.PLUS);
                break;
        }
    }

    /**
     * 添加胡类型
     *
     * @param opPoint
     * @param point
     */
    protected void addhuType(WXMJOpPoint opPoint, int point, MJEndType bEndType) {
        if (this.newHuTypeMap.containsKey(opPoint)) {
            // 累计
            int calcPoint = point;
            if (MJEndType.PLUS.equals(bEndType)) {
                calcPoint = this.newHuTypeMap.get(opPoint) + point;
            } else if (MJEndType.MULTIPLY.equals(bEndType)) {
                calcPoint = this.newHuTypeMap.get(opPoint) * point;
            }
            this.newHuTypeMap.put(opPoint, calcPoint);
        } else {
            this.newHuTypeMap.put(opPoint, point);
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> T getCalcPosEnd() {
        return (T) new CalcPosEnd(this.newHuTypeMap);
    }

}
