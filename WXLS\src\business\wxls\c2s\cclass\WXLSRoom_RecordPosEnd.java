package business.wxls.c2s.cclass;	
	
	
public class WXLSRoom_RecordPosEnd {	
	public long roomID = 0;	
	public long pid = 0; //玩家ID	
	public int setID = 0;	
//	public List<String> CardList = new ArrayList<>(); //最终胡牌的列表	
//	public int shootNum = 0;//打枪的次数	
//	public int fourbagger = 0;//全垒打	
	public String gameJson ="";//游戏json	
	public int gameType = -1;//游戏类型	
	public int point = 0; // 本局积分变更	
	public long setEnd = 0;	
	
}	
