package business.global.mj.afmj;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import business.global.mj.MJCard;
import business.global.mj.afmj.AFMJRoomEnum.*;
import business.afmj.c2s.cclass.AFMJRoomSetInfo;
import business.afmj.c2s.iclass.*;
import business.global.room.base.AbsRoomPosMgr;
import cenum.room.RoomDissolutionState;
import com.google.gson.Gson;
import business.global.mj.AbsMJSetRoom;
import business.global.room.mj.MahjongRoom;
import cenum.ChatType;
import cenum.ClassType;
import cenum.PrizeType;
import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.RoomEndResult;
import jsproto.c2s.cclass.room.BaseRoomConfigure;
import jsproto.c2s.cclass.room.GetRoomInfo;
import jsproto.c2s.cclass.room.RoomPosInfo;
import jsproto.c2s.iclass.S_GetRoomInfo;
import jsproto.c2s.iclass.room.SBase_Dissolve;
import jsproto.c2s.iclass.room.SBase_PosLeave;

/**
 * 房间操作
 */

public class AFMJRoom extends MahjongRoom {
	private final CAFMJ_CreateRoom roomCfg; // 开房配置
	public List<Integer> kexuanwanfa = new ArrayList<>();
	private int ownerDposCount = 0; // 房主坐庄次数
	private MJCard tuiJingZhengJing = new MJCard(6001); // 推精正精牌
	private MJCard tuiJingFuJing = new MJCard(6002); // 推精副精牌
	private boolean shangJuLiuJu = false; // 上局是否流局

	public MJCard getTuiJingZhengJing() {
		return tuiJingZhengJing;
	}

	public void setTuiJingZhengJing(MJCard tuiJingZhengJing) {
		this.tuiJingZhengJing = tuiJingZhengJing;
	}

	public MJCard getTuiJingFuJing() {
		return tuiJingFuJing;
	}

	public void setTuiJingFuJing(MJCard tuiJingFuJing) {
		this.tuiJingFuJing = tuiJingFuJing;
	}

	public boolean isShangJuLiuJu() {
		return shangJuLiuJu;
	}

	public void setShangJuLiuJu(boolean shangJuLiuJu) {
		this.shangJuLiuJu = shangJuLiuJu;
	}

	public void setOwnerDposCount(int ownerDposCount) {
		this.ownerDposCount = ownerDposCount;
	}

	/**
	 * 麻将公共父类构造函数
	 * @param baseRoomConfigure 公共配置
	 * @param roomKey           房间key
	 */
	protected AFMJRoom(BaseRoomConfigure baseRoomConfigure, String roomKey, long ownerID) {
		super(baseRoomConfigure, roomKey, ownerID);
		initShareBaseCreateRoom(CAFMJ_CreateRoom.class, baseRoomConfigure);
		this.roomCfg = (CAFMJ_CreateRoom) this.getBaseRoomConfigure().getBaseCreateRoom();
		kexuanwanfa();
	}

	/**
	 * 获取房间配置
	 */
	public CAFMJ_CreateRoom getRoomCfg() {
		if (this.roomCfg == null) {
			initShareBaseCreateRoom(CAFMJ_CreateRoom.class, getBaseRoomConfigure());
			return (CAFMJ_CreateRoom) getBaseRoomConfigure().getBaseCreateRoom();
		}
		return this.roomCfg;
	}


	private void kexuanwanfa() {
		if (this.getBaseRoomConfigure().getPrizeType() == PrizeType.RoomCard) {
			this.kexuanwanfa = this.getRoomCfg().getKexuanwanfa();
		} else {
			this.kexuanwanfa = Collections.singletonList(0);
		}
	}

	// 封顶
	public <E> boolean RoomCfgFengDing(E m) {
		AFMJFengDing roomCfgEnum = (AFMJFengDing) m;
		int roomCfgInt = roomCfgEnum.ordinal();
		return this.getRoomCfg().getFengding() == roomCfgInt;
	}


	// 玩法
	public <E> boolean RoomCfgWanFa(E m) {
		AFMJWanFa roomCfgEnum = (AFMJWanFa) m;
		int roomCfgInt = roomCfgEnum.ordinal();
		return this.getRoomCfg().getWanfa() == roomCfgInt;
	}

	// 飘精
	public <E> boolean RoomCfgPiaoJing(E m) {
		AFMJPiaoJing roomCfgEnum = (AFMJPiaoJing) m;
		int roomCfgInt = roomCfgEnum.ordinal();
		return this.getRoomCfg().getPiaojing() == roomCfgInt;
	}

	// 可选玩法
	@Override
	public <E> boolean RoomCfg(E m) {
		AFMJKeXuanWanFa roomCfgEnum = (AFMJKeXuanWanFa) m;
		int roomCfgInt = roomCfgEnum.ordinal();
		return this.getRoomCfg().getKexuanwanfa().contains(roomCfgInt);
	}

	@Override
	public int getWanfa() {
		return this.getRoomCfg().getWanfa();
	}

	@Override
	public String dataJsonCfg() {
		return new Gson().toJson(this.getRoomCfg());
	}



	/**
	 * 开始新一局
	 */
	@Override
	public void startNewSet() {
		// 更新连续托管局数
		for(int i = 0; i < getPlayerNum(); i++){
			AFMJRoomPos AFMJRoomPos = (AFMJRoomPos)getRoomPosMgr().getPosByPosID(i);
			if(AFMJRoomPos.isTrusteeship()){ // 托管
				AFMJRoomPos.addTuoGuanSetCount();
			}
		}
		this.setCurSetID(this.getCurSetID()+1);
		// / 计算庄位
		if (this.getCurSetID() == 1) {
			setDPos(0); // 第一局坐庄由房主为庄家
			this.setOwnerDposCount(1); // 第一局坐庄由房主为庄家
		} else if (this.getCurSet() != null) {
			// 根据上一局计算下一局庄家
			AbsMJSetRoom mRoomSet = (AbsMJSetRoom) this.getCurSet();
			setDPos(mRoomSet.calcNextDPos());
			mRoomSet.clear();
		}

		// 每个位置，清空准备状态
		this.getRoomPosMgr().clearGameReady();
		// 通知局数变化
		this.getRoomTyepImpl().roomSetIDChange();

		this.setCurSet(this.newMJRoomSet(this.getCurSetID(), this, this.getDPos()));
	}

	@Override
	public BaseSendMsg Trusteeship(long roomID, long pid, int pos, boolean trusteeship) {
		return SAFMJ_Trusteeship.make(roomID, pid, pos, trusteeship);
	}

	@Override
	public BaseSendMsg PosLeave(SBase_PosLeave posLeave) {
		return SAFMJ_PosLeave.make(posLeave);
	}

	@Override
	public BaseSendMsg LostConnect(long roomID, long pid, boolean isLostConnect, boolean isShowLeave) {
		return SAFMJ_LostConnect.make(roomID, pid, isLostConnect, isShowLeave);
	}

	@Override
	public BaseSendMsg PosContinueGame(long roomID, int pos) {
		return SAFMJ_PosContinueGame.make(roomID, pos);
	}

	@Override
	public BaseSendMsg PosUpdate(long roomID, int pos, RoomPosInfo posInfo, int custom) {
		return SAFMJ_PosUpdate.make(roomID, pos, posInfo, custom);
	}

	@Override
	public BaseSendMsg PosReadyChg(long roomID, int pos, boolean isReady) {
		return SAFMJ_PosReadyChg.make(roomID, pos, isReady);
	}

	@Override
	public BaseSendMsg Dissolve(SBase_Dissolve dissolve) {
		return SAFMJ_Dissolve.make(dissolve);
	}

	@Override
	public BaseSendMsg StartVoteDissolve(long roomID, int createPos, int endSec) {
		return SAFMJ_StartVoteDissolve.make(roomID, createPos, endSec);
	}

	@Override
	public BaseSendMsg PosDealVote(long roomID, int pos, boolean agreeDissolve, int endSec) {
		return SAFMJ_PosDealVote.make(roomID, pos, agreeDissolve);
	}

	@Override
	public BaseSendMsg Voice(long roomID, int pos, String url) {
		return SAFMJ_Voice.make(roomID, pos, url);
	}

	@Override
	public BaseSendMsg XiPai(long roomID, long pid, ClassType cType) {
		return SAFMJ_XiPai.make(roomID, pid, cType);
	}

	@Override
	public BaseSendMsg ChatMessage(long pid, String name, String content, ChatType type, long toCId, int quickID) {
		return SAFMJ_ChatMessage.make(pid, name, content, type, toCId, quickID);
	}

	@Override
	public <T> BaseSendMsg RoomRecord(List<T> records) {
		return SAFMJ_RoomRecord.make(records);
	}

	@Override
	public BaseSendMsg ChangePlayerNum(long roomID, int createPos, int endSec, int playerNum) {
		return SAFMJ_ChangePlayerNum.make(roomID, createPos, endSec, playerNum);
	}

	@Override
	public BaseSendMsg ChangePlayerNumAgree(long roomID, int pos, boolean agreeChange) {
		return SAFMJ_ChangePlayerNumAgree.make(roomID, pos, agreeChange);
	}

	@Override
	public BaseSendMsg ChangeRoomNum(long roomID, String roomKey, int createType) {
		return SAFMJ_ChangeRoomNum.make(roomID, roomKey, createType);
	}

	@Override
	public GetRoomInfo getRoomInfo(long pid) {
		S_GetRoomInfo ret = new S_GetRoomInfo();
		// 设置房间公共信息
		this.getBaseRoomInfo(ret);
		if (null != this.getCurSet()) {
			ret.setSet(this.getCurSet().getNotify_set(pid));
		} else {
			ret.setSet(new AFMJRoomSetInfo());
		}
		return ret;
	}

	@Override
	public <T> T getCfg() {
		return (T) getRoomCfg();
	}

	@Override
	protected AbsMJSetRoom newMJRoomSet(int curSetID, MahjongRoom room, int dPos) {
		return new AFMJRoomSet(curSetID, room, dPos);
	}

	@Override
	public <T> BaseSendMsg RoomEnd(T record, RoomEndResult<?> sRoomEndResult) {
		return SAFMJ_RoomEnd.make(this.getMJRoomRecordInfo(), this.getRoomEndResult());
	}

	/**
	 * 托管时间值
	 */
	public int trusteeshipTimeValue() {
		return AFMJXianShi.valueOf(getBaseRoomConfigure().getBaseCreateRoom().getXianShi()).value();
	}


	/**
	 * 30秒未准备自动退出
	 */
	@Override
	public boolean is30SencondTimeOut() {
		return getRoomCfg().getGaoji().contains(4);
	}

	@Override
	public boolean isCanChangePlayerNum() {
		return getRoomCfg() != null
				&& getRoomCfg().getFangjian() != null
				&& getRoomCfg().getFangjian().contains(AFMJFangJian.IsCanChangePlayerNum.ordinal());
	}

	/**
	 * 自动准备游戏 玩家加入房间时，自动进行准备。
	 *
	 *
	 *
	 */
	@Override
	public boolean autoReadyGame() {
		return getRoomCfg() != null
				&& getRoomCfg().getFangjian() != null
				&& getRoomCfg().getFangjian().contains(AFMJFangJian.AutoReady.ordinal());
	}

	/**
	 * 是否需要解散次数
	 */
	@Override
	public boolean needDissolveCount(){
		return getRoomCfg().getFangjian().contains(AFMJFangJian.JieSanCiShu5.ordinal())
				|| getRoomCfg().getFangjian().contains(AFMJFangJian.JieSanCiShu3.ordinal());
	}

	/**
	 * 获取解散次数
	 * @return 解散次数
	 */
	@Override
	public int getJieShanShu(){
		if(getRoomCfg().getFangjian().contains(AFMJFangJian.JieSanCiShu5.ordinal())){
			return 5;
		}else if(getRoomCfg().getFangjian().contains(AFMJFangJian.JieSanCiShu3.ordinal())){
			return 3;
		}
		return 0;
	}


//	/**
//	 * 是否需要：小局10秒自动准备
//	 */
//	public boolean needXiaoJu10MiaoAutoReady(){
//		return getRoomCfg().getFangjian().contains(AFMJFangJian.XiaoJu10Miao.ordinal());
//	}

	/**
	 * 解散房间
	 *
	 * @param ownnerForce
	 *            T:房主解散,F:发起解散
	 */
	public void doDissolveRoom(boolean ownnerForce) {
		// 游戏结束标志：解散为false
		if(getCurSet() != null){ // 已经开局
			((AFMJRoomSet)getCurSet()).setEndFlag(false);
		}
		// 房间解散
		this.setRoomDissolutionState(RoomDissolutionState.Dissolution);
		this.exeDissolveRoom();
		// 通知所有人
		this.getRoomPosMgr().notify2All(this.Dissolve(SBase_Dissolve.make(this.getRoomID(), ownnerForce)));
	}

	/**
	 * 房间内每个位置信息 管理器
	 */
	@Override
	public AbsRoomPosMgr initRoomPosMgr() {
		return new AFMJRoomPosMgr(this);
	}

	/**
	 * 初始化托管
	 */
	@Override
	public void initTrusteeship() {
		this.setTrusteeship(new AFMJTrusteeship(this));
	}
	
}
