package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.List;	
	
/**	
 * 6起	
 */	
public class WXLSLiuQiRankingImpl_T extends WXLSAbstractRanking {	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        WXLSRankingResult result = null;	
        if (player.getCardSize() == 13) {	
            List<WXLSPockerCard> cards = player.getCards();	
            boolean flag = true;	
            for (int i = 0; i < cards.size(); i++) {	
                if (cards.get(i).getRank().getNumber() < 6) {	
                    flag = false;	
                }	
            }	
            if (flag) {	
                result = new WXLSRankingResult();	
                result.setPockerCards(player.getCards());	
                result.setRankingEnum(WXLSRankingEnum.LIUQI);	
                return result;	
            }	
        }	
        return result;	
    }	
}	
