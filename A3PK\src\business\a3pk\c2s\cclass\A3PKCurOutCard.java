package business.a3pk.c2s.cclass;	
	
import com.ddm.server.common.utils.Lists;	
	
import java.util.ArrayList;	
	
public class A3PKCurOutCard {	
    /**	
     * 最后打牌的玩家位置	
     */	
    private int outCardPos = -1;	
    /**	
     * 出牌类型	
     */	
    private int outCardType = -1;	
    /**	
     * 当前打出的牌	
     */	
    private ArrayList<Integer> curOutCards = Lists.newArrayList();	
    /**	
     * 比较值	
     */	
    private int compValue = 0;	
    /**	
     * 比较的数量	
     */	
    private int compSize = 0;	
	
    /**	
     * 清空当前打出的牌	
     */	
    public void clearCurOutCard() {	
        this.outCardPos = -1;	
        this.outCardType = -1;	
        this.curOutCards.clear();	
        this.compValue = 0;	
        this.compSize = 0;	
    }	
	
	
    public int getOutCardPos() {	
        return outCardPos;	
    }	
	
    public void setOutCardPos(int outCardPos) {	
        this.outCardPos = outCardPos;	
    }	
	
    public int getOutCardType() {	
        return outCardType;	
    }	
	
    public void setOutCardType(int outCardType) {	
        this.outCardType = outCardType;	
    }	
	
    public ArrayList<Integer> getCurOutCards() {	
        return curOutCards;	
    }	
	
    public void setCurOutCards(ArrayList<Integer> curOutCards) {	
        this.curOutCards = curOutCards;	
    }	
	
    public int getCompValue() {	
        return compValue;	
    }	
	
    public void setCompValue(int compValue) {	
        this.compValue = compValue;	
    }	
	
    public int getCompSize() {	
        return compSize;	
    }	
	
    public void setCompSize(int compSize) {	
        this.compSize = compSize;	
    }	
}	
