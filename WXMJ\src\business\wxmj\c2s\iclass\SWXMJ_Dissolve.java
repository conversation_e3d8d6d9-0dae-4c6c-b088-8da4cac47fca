package business.wxmj.c2s.iclass;			
			
import jsproto.c2s.iclass.room.SBase_Dissolve;			
			
/**			
 * 房间解散通知			
 * 			
 * <AUTHOR>			
 *			
 */			
public class SWXMJ_Dissolve extends SBase_Dissolve {			
			
	/**			
	 * 			
	 */			
	private static final long serialVersionUID = 1L;			
			
	public static SWXMJ_Dissolve make(SBase_Dissolve dissolve) {			
		SWXMJ_Dissolve ret = new SWXMJ_Dissolve();			
		ret.setOwnnerForce(dissolve.isOwnnerForce());			
		ret.setRoomID(dissolve.getRoomID());			
		ret.setDissolveNoticeType(dissolve.getDissolveNoticeType());			
		ret.setMsg(dissolve.getMsg());			
		return ret;			
	}			
}			
