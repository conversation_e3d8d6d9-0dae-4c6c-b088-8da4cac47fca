package business.global.pk.wxls.newwxls.comparing;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
	
import java.util.ArrayList;	
import java.util.List;	
	
/**	
 * 同花大小比较(比较最大牌即可)	
 */	
public class WXLSFlushComparingImpl extends WXLSAbstractComparing {	
	
    @Override	
    public int compare(WXLSPlayerDun o1, WXLSPlayerDun o2) {	
        List<WXLSPockerCard> cards = o1.getCards();	
        List<WXLSPockerCard> newcards = new ArrayList<WXLSPockerCard>();	
            newcards = cards;	
        List<WXLSPockerCard> cards2 = o2.getCards();	
        List<WXLSPockerCard> newcards2 = new ArrayList<WXLSPockerCard>();	
            newcards2 = cards2;	
        int ret = this.seqComparing(newcards, newcards2);
        if(ret==0){
            return  compareZeroByColor(o1,o2);
        }
        return ret;	
	
    }	
	
}	
	
