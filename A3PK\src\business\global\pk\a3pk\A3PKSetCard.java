package business.global.pk.a3pk;	
	
import java.util.ArrayList;	
import java.util.Collections;	
import java.util.Iterator;	
import java.util.List;	
	
import business.global.pk.AbsPKSetCard;	
import com.ddm.server.common.utils.CommMath;	
import com.ddm.server.common.utils.Lists;	
import jsproto.c2s.cclass.pk.BasePocker;	
import jsproto.c2s.cclass.pk.BasePockerLogic;	
import org.apache.commons.collections.CollectionUtils;	
	
/**	
 * 长汀510K	
 * 每一局麻将底牌信息	
 * 抓牌人是逆时针出手	
 * 牌是顺时针被抓	
 *	
 * <AUTHOR>	
 */	
public class A3PKSetCard extends AbsPKSetCard {	
    /**	
     * 当局信息	
     */	
    public A3PKRoomSet set;	
	
    /**	
     * 牌列表	
     */	
    private ArrayList<Integer> leftCards = Lists.newArrayList();	
	
    public A3PKSetCard(A3PKRoomSet set) {	
        this.set = set;	
        // 随机牌	
        this.randomCard();	
    }	
	
    /**	
     * 随机牌	
     */	
    public void randomCard() {	
        // 初始牌组	
        this.leftCards.addAll(BasePockerLogic.getOnlyRandomPockerList(1, set.isKing() ? 1:0, BasePocker.PockerListType.POCKERLISTTYPE_TWOEND));	
        // 打散牌组	
        Collections.shuffle(this.leftCards);	
    }	
	
    @Override	
    public ArrayList<Integer> popList(int cnt) {	
        ArrayList<Integer> ret = new ArrayList<Integer>();	
        if (CollectionUtils.isEmpty(this.leftCards) || this.leftCards.size() < cnt) {	
            return ret;	
        };	
        for (int i = 0; i < cnt; i++) {	
            ret.add(this.leftCards.remove(CommMath.randomInt(this.leftCards.size() -1 )));	
        }	
        return ret;	
    }	
	
	
    /**	
     * 强制发牌（测试用）	
     *	
     * @param forcePop 强制牌列表	
     * @return	
     */	
    @Override	
    public List<Integer> forcePopList(List<Integer> forcePop) {	
        if (null == forcePop) {	
            return Collections.emptyList();	
        }	
        List<Integer> ret = new ArrayList<>();	
        for (int type : forcePop) {	
            int mCard = this.removeLeftCardType(type);	
            if (mCard > 0) {	
                ret.add(mCard);	
            }	
        }	
        return ret;	
    }	
	
    /**	
     * 移除指定类型的牌	
     *	
     * @param cardType 牌类型	
     * @return	
     */	
    public Integer removeLeftCardType(int cardType) {	
        // 创建迭代器	
        Iterator<Integer> it = this.leftCards.iterator();	
        // 循环遍历迭代器	
        while (it.hasNext()) {	
            int mCard = it.next();	
            if (BasePocker.getCardValueEx(mCard) == BasePocker.getCardValueEx(cardType) && BasePocker.getCardColor(mCard) == BasePocker.getCardColor(cardType)) {	
                it.remove();	
                return mCard;	
            }	
        }	
        return 0;	
    }	
	
	
	
	
    @Override	
    public Integer pop() {	
        return null;	
    }	
	
    @Override	
    public Integer appointPopCard(int popCard) {	
        return null;	
    }	
	
    @Override	
    public ArrayList<Integer> popList(int cnt, int i) {	
        return null;	
    }	
	
}	
	
