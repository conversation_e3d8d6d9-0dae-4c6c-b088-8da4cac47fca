package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.Iterator;	
import java.util.Map;	
	
/**	
 * 五对一刻	
 */	
public class WXLSWDuiYiKeRankingImpl_T extends WXLSAbstractRanking {	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        if (player.getCards().size() != 13) {	
            return null;	
        }	
        WXLSRankingResult result = null;	
        Map<Integer, Integer> rankCount = player.getCardsRankCountMap();	
        boolean flag = false;	
        int threecnt = 0;	
        int twocnt = 0;	
        Iterator<Map.Entry<Integer, Integer>> it = rankCount.entrySet().iterator();	
        while (it.hasNext()) {	
            Map.Entry<Integer, Integer> entry= it.next();	
            int value =entry.getValue();	
            if (value == 3) {	
                threecnt++;	
            }	
            if (value == 2) {	
                twocnt++;	
            } else if (value == 4) {	
                twocnt += 2;	
            }	
        }	
	
        if (twocnt == 5 && threecnt == 1) {	
            flag = true;	
        }	
        if (flag) {	
            result = new WXLSRankingResult();	
            result.setPockerCards(player.getCards());	
            result.setRankingEnum(WXLSRankingEnum.WDuiYiKe);	
        }	
	
        return result;	
    }	
	
	
}	
