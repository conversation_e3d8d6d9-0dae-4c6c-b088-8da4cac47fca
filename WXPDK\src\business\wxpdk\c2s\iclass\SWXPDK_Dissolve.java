package business.wxpdk.c2s.iclass;

import jsproto.c2s.iclass.room.SBase_Dissolve;

/**
 * 房间解散通知
 * 
 * <AUTHOR>
 *
 */
public class SWXPDK_Dissolve extends SBase_Dissolve {


	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public static SWXPDK_Dissolve make(SBase_Dissolve dissolve) {
		SWXPDK_Dissolve ret = new SWXPDK_Dissolve();
		ret.setOwnnerForce(dissolve.isOwnnerForce());
		ret.setRoomID(dissolve.getRoomID());
		ret.setDissolveNoticeType(dissolve.getDissolveNoticeType());
		ret.setMsg(dissolve.getMsg());
		return ret;
	}
}
