package business.global.mj.wwmj;									
									
import business.global.room.base.AbsBaseRoom;									
import business.global.room.mj.MJRoomPosMgr;									
									
public class WWMJRoomMgr extends MJRoomPosMgr {									
    public WWMJRoomMgr(AbsBaseRoom room) {									
        super(room);									
    }									
									
    @Override									
    protected void initPosList() {									
        // 初始化房间位置									
        for (int posID = 0; posID < this.getPlayerNum(); posID++) {									
            this.posList.add(new WWMJRoomPos<>(posID, room));									
        }									
    }									
    /**									
     * 检查小局结算托管次数									
     *									
     * @return									
     */									
    public boolean checkSetEndTrusteeship() {									
        this.posList.stream().forEach(k -> ((WWMJRoomPos) k).addSetEndTrusteeshipCount());									
        return this.posList.stream().anyMatch(k -> ((WWMJRoomPos) k).getSetEndTrusteeshipCount() >= 2);									
    }									
}									
