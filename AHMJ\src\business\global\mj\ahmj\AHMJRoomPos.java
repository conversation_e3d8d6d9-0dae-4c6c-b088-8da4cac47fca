package business.global.mj.ahmj;	
	
import business.global.room.base.AbsBaseRoom;	
import business.global.room.mj.MJRoomPos;
import business.global.mj.ahmj.AHMJRoomEnum.AHMJDaNiao;
/**	
 * 房间内每个位置信息	
 *	
 * @param <T>	
 * <AUTHOR>	
 */	
	
public class AHMJRoomPos<T> extends MJRoomPos {	
    private AHMJDaNiao daNiao = AHMJDaNiao.NOT_OP;//打鸟
    private int tuoGuanSetCount = 0; // 连续托管局数

    public AHMJRoomPos(int posID, AbsBaseRoom room) {	
        super(posID, room);	
    }


    /**
     * @return m_shangHuoEnum
     */
    public AHMJDaNiao getDaNiao() {
        return daNiao;
    }

    public void setDaNiao(AHMJDaNiao daNiao) {
        this.daNiao = daNiao;
    }

    public int getTuoGuanSetCount() {
        return tuoGuanSetCount;
    }

    /**
     * 增加托管局数
     */
    public void addTuoGuanSetCount(){
        tuoGuanSetCount += 1;
    }

    /**
     * 连续托管局数清零
     */
    public void clearTuoGuanSetCount(){
        tuoGuanSetCount = 0;
    }

    /**
     * 设置托管状态
     *
     * @param isTrusteeship 托管状态
     * @param isOwn         是否屏蔽自己
     */
    public void setTrusteeship(boolean isTrusteeship, boolean isOwn) {
        if (this.isTrusteeship() == isTrusteeship) {
            return;
        }
        // 托管2小局解散：连续2局托管
        if(!isTrusteeship){ // 玩家取消托管，连续托管局数清零
            this.clearTuoGuanSetCount();
        }else{
            this.addTuoGuanSetCount();
        }
        this.setTrusteeship(isTrusteeship);
        if (isOwn) {
            this.getRoom().getRoomPosMgr().notify2ExcludePosID(this.getPosID(), this.getRoom().Trusteeship(this.getRoom().getRoomID(), this.getPid(), this.getPosID(), this.isTrusteeship()));
        } else {
            this.getRoom().getRoomPosMgr().notify2All(this.getRoom().Trusteeship(this.getRoom().getRoomID(), this.getPid(), this.getPosID(), this.isTrusteeship()));
        }
    }
}
