package business.global.mj.afmj.optype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCard;
import business.global.mj.afmj.AFMJRoomSet;
import business.global.mj.afmj.AFMJSetCard;
import business.global.mj.manage.OpCard;
import cenum.mj.MJSpecialEnum;

import java.util.LinkedHashMap;

/**
 *  开金
 *
 *  	四家抓完牌后开始的第一张牌翻开，翻开的牌加1即为宝牌；（进金）
 *  	注：中发白算一起，如翻开白则中是金；
 *  	东南西北算一起，如翻开北则东是金；
 *  	翻开的那张牌不能抓；
 *  	例如第一张牌是一万，那么二万就是宝。
 *  	此时牌里就剩下3张一万，4张二万；
 *
 *  百搭牌（筋）： 三原四搭原则。
 *  例：翻出的牌是3万，则扣除已翻出的一张3万，剩下三张3万为原子，往下加一，四张4万为百搭。
 *  东南西北为一轮，翻到北，则东为百搭；
 *  中白发为一轮，翻到发，中为百搭。
 *
 *  根据骰子点数，从相应的牌墩翻开上面那张麻将子，为正精；该麻将子加一为副精；
 */


public class KaiJinJJ_AFMJImpl implements OpCard {

	@Override
	public boolean checkOpCard(AbsMJSetPos mSetPos, int cardID) {
		// 操作开金
		this.opKaiJin(mSetPos);
		return false;
	}


	@Override
	public boolean doOpCard(AbsMJSetPos mSetPos, int cardID) {
		// TODO Auto-generated method stub
		return false;
	}

	/**
	 * 开金
	 */
	public void opKaiJin(AbsMJSetPos mSetPos) {
		// 开金的次数 >= 指定的开金数，开金完毕，通知开金成功。
		if (mSetPos.getSet().getmJinCardInfo().sizeJin() >= mSetPos.getSet().getmJinCardInfo().getKaiJinNum()) {
			MJCard jin = mSetPos.getSet().getmJinCardInfo().getJin(1); // 正精
			mSetPos.getSet().getmJinCardInfo().addJinCard(jinJin(jin)); // 添加副精
			((AFMJRoomSet)mSetPos.getSet()).setZhengJing(jin); // 设置正精牌型
			((AFMJRoomSet)mSetPos.getSet()).setFuJing(jinJin(jin)); // 设置副精牌型
			// 开金通知。
//			mSetPos.getSet().kaiJinNotify(jinJin,backJinJin(jinJin)); // jinJin：副精，backJinJin(jinJin)：正精
			mSetPos.getSet().kaiJinNotify(jin,jinJin(jin)); // jinJin：副精，backJinJin(jinJin)：正精
			return;
		}
		// 开金
		this.kaiJinCard(mSetPos);
		// 再操作开金
		this.opKaiJin(mSetPos);
	}


	/**
	 * 开金补花通知
	 * @param mSetPos 玩家信息
	 * @param mCard 开出的牌
	 */
	private void kaiJinApplique (AbsMJSetPos mSetPos,MJCard mCard) {
		// 添加打出的牌
		mSetPos.addOutCardIDs(mCard.getCardID());
		// 添加花
		mSetPos.getPosOpRecord().addHua(mCard.getCardID());
		// 通知补花,补花位置。
		mSetPos.getSet().MJApplique(mSetPos.getPosID());
	}


	/**
	 * 开金
	 * @param mSetPos 玩家信息
	 */
	public void kaiJinCard (AbsMJSetPos mSetPos) {
		// 摸牌开金
//		MJCard card = mSetPos.getSet().getMJSetCard().pop(false,mSetPos.getSet().getGodInfo().godHandCard(mSetPos));
		// 翻开的正精和副精都有四张，且翻开的牌不能是花牌；
		MJCard card = ((AFMJSetCard)mSetPos.getSet().getMJSetCard()).popJinCard(mSetPos.getSet().getGodInfo().godHandCard(mSetPos));
		if(null == card) {
			// 没有牌
			return;
		}
		// 检查摸到牌的类型是否 < 50 , > 50 花
		if (card.getType() < MJSpecialEnum.NOT_HUA.value()) {
			// 添加金牌
//			if (mSetPos.getSet().getmJinCardInfo().addJinCard(jinJin(card))) { // 添加副金
			mSetPos.getSet().getmJinCardInfo().clear();
			mSetPos.getSet().getmJinCardInfo().setJinMap(new LinkedHashMap<>(mSetPos.getSet().getmJinCardInfo().getKaiJinNum()));
			if (mSetPos.getSet().getmJinCardInfo().addJinCard(card)) { // 添加正金
				// 金牌添加成功
				return;
			} else {
				// 添加金失败，重新开金
				kaiJinCard(mSetPos);
			}
		} else {
			// 开金开到花牌，通知补花，并且重新开金
			kaiJinApplique(mSetPos,card);
			// 重新开金
			kaiJinCard(mSetPos);
		}
	}



	/**
	 * 进金
	 */
	public MJCard jinJin (MJCard card) {
		//牌类型
		int type = card.type /10;
		//牌大小
		int size = (card.cardID % 1000)/100;
		int tem = size;
		//如果是箭牌类型
		if (card.type >=45) {
			//牌 >= 7 就是白板
			if (size >= 7) {
				// 进金为 红中
				size = 5;
			}
		} else if (card.type > 40) {
			//如果是风牌
			//牌 >= 4 就是 北风
			if (size >= 4) {
				//进金为 东风
				size = 1;
			}
		} else {
			//如果是 万条筒
			//牌 >= 9 就是 九 万条筒
			if (size >= 9) {
				// 进金为 一 万条筒
				size = 1;
			}
		}
		if (tem == size) {
			size++;
		}
		int cardId = (type * 10+size)*100 + 1;
		return new MJCard(cardId);
	}

	/**
	 * 退金
	 */
	public MJCard backJinJin (MJCard card) {
		//牌类型
		int type = card.type /10;
		//牌大小
		int size = (card.cardID % 1000)/100;
		int tem = size;
		//如果是箭牌类型
		if (card.type >=45) {
			//牌 >= 5 就是 红中
			if (size <= 5) {
				// 退金为 白板
				size = 7;
			}
		} else if (card.type > 40) {
			//如果是风牌
			//牌 >= 1 就是 东风
			if (size <= 1) {
				//退金为 北风
				size = 4;
			}
		} else {
			//如果是 万条筒
			//牌 >= 1 就是 一 万条筒
			if (size <= 1) {
				// 退金为 九 万条筒
				size = 9;
			}
		}
		if (tem == size) {
			size --;
		}


		int cardId = (type * 10+size)*100 + 1;
		return new MJCard(cardId);
	}

}
