package business.wxpdk.c2s.iclass;

import java.util.ArrayList;

import business.wxpdk.c2s.cclass.WXPDK_define;
import jsproto.c2s.cclass.BaseSendMsg;

/**
 * 接收客户端数据
 * 状态改变
 *
 * <AUTHOR>
 */

public class SWXPDK_PiaoChangeStatus extends BaseSendMsg {

    /**
     *
     */
    private static final long serialVersionUID = 1L;
    public long roomID;
    public int setID;//局数
    public int state = WXPDK_define.WXPDK_GameStatus.WXPDK_GAME_STATUS_WaitingEx.value();  //位置
    public ArrayList<Integer> piaoHuaList = new ArrayList<>(); //飘分

    public static SWXPDK_PiaoChangeStatus make(long roomID, int setID, int state, ArrayList<Integer> piaoFenList) {
        SWXPDK_PiaoChangeStatus ret = new SWXPDK_PiaoChangeStatus();
        ret.roomID = roomID;
        ret.setID = setID;
        ret.state = state;
        ret.piaoHuaList = piaoFenList;
        return ret;
    }

}
