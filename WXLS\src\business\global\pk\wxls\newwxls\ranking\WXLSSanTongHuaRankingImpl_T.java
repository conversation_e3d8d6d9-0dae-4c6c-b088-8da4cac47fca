package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSCardSuitEnum;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.ArrayList;	
import java.util.Iterator;	
import java.util.List;	
import java.util.Map;	
	
/**	
 *三同花	
 */	
public class WXLSSanTongHuaRankingImpl_T extends WXLSAbstractRanking {	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        WXLSRankingResult result = null;	
	
        if (player.getCardSize() == 13) {	
//			List<WXLSPockerCard> cards = player.getCards();	
            Map<WXLSCardSuitEnum, List<WXLSPockerCard>> suitCount = player.getCardsSuitCountMap();	
            int count = 0;	
            List<Integer> lst = new ArrayList<Integer>();	
            Iterator<Map.Entry<WXLSCardSuitEnum, List<WXLSPockerCard>>> it = suitCount.entrySet().iterator();	
            while (it.hasNext()) {	
                Map.Entry<WXLSCardSuitEnum, List<WXLSPockerCard>> entry = it.next();	
                count++;	
                lst.add(entry.getValue().size());	
            }	
            if (count == 3) {// 三花色	
                    List<Integer> caselst = new ArrayList<Integer>();	
                    caselst.add(3);	
                    caselst.add(5);	
                    caselst.add(5);	
                    if (lst.containsAll(caselst)) {	
                        result = new WXLSRankingResult();	
                        result.setPockerCards(player.getCards());	
                        result.setRankingEnum(WXLSRankingEnum.STongHua);	
                    }	
            } else if (count == 2) {// 2花色	
                    List<Integer> caselst = new ArrayList<Integer>();	
                    caselst.add(3);	
                    caselst.add(10);	
                    List<Integer> caselst1 = new ArrayList<Integer>();	
                    caselst1.add(8);	
                    caselst1.add(5);	
                    if (lst.containsAll(caselst) || lst.containsAll(caselst1)) {	
                        result = new WXLSRankingResult();	
                        result.setPockerCards(player.getCards());	
                        result.setRankingEnum(WXLSRankingEnum.STongHua);	
                    }	
            } else if (count == 1) {	
                result = new WXLSRankingResult();	
                result.setPockerCards(player.getCards());	
                result.setRankingEnum(WXLSRankingEnum.STongHua);	
            }	
        }	
        return result;	
    }	
	
}	
	
