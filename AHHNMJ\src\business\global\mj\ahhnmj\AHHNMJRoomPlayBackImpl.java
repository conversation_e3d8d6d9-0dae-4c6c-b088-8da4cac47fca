package business.global.mj.ahhnmj;		
		
import business.global.room.base.AbsBaseRoom;		
import business.global.room.base.RoomPlayBackImplAbstract;		
import jsproto.c2s.cclass.BaseSendMsg;		
		
/**		
 * 回放的房间管理		
 * 		
 * <AUTHOR>		
 */		
public class AHHNMJRoomPlayBackImpl extends RoomPlayBackImplAbstract {		
		
	private final static String PosGetCard = "PosGetCard";		
	private final static String PosOpCard = "PosOpCard";		
	private final static String SetStart = "SetStart";		
	private final static String StartVoteDissolve = "StartVoteDissolve";		
	private final static String MingPai = "MingPai";		
	private final static String ChangeStatus = "ChangeStatus";		
	private final static String MaiMa = "MaiMa";		
	private final static String PiaoHua = "PiaoHua";		
		
		
		
	public AHHNMJRoomPlayBackImpl(AbsBaseRoom room) {		
		super(room);		
	}		
		
	@Override		
	public boolean isOpCard(BaseSendMsg msg) {		
		return msg.getOpName().indexOf(PosGetCard) > 0 || msg.getOpName().indexOf(PosOpCard) > 0		
				|| msg.getOpName().indexOf(SetStart) > 0 || msg.getOpName().indexOf(StartVoteDissolve) > 0		
				|| msg.getOpName().indexOf(MingPai) > 0|| msg.getOpName().indexOf(ChangeStatus) > 0|| msg.getOpName().indexOf(MaiMa) > 0		
				|| msg.getOpName().indexOf(PiaoHua) > 0;		
	}		
		
		
}		
