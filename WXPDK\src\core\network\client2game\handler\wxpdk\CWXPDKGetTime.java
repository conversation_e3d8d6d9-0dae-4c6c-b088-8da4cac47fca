package core.network.client2game.handler.wxpdk;

import business.global.pk.wxpdk.WXPDKRoom;
import business.global.pk.wxpdk.WXPDKRoomSet;
import business.global.room.RoomMgr;
import business.wxpdk.c2s.iclass.CWXPDK_AddDouble;
import business.wxpdk.c2s.iclass.SWXPDK_GetTime;
import business.player.Player;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.exception.WSException;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;
import core.network.client2game.handler.PlayerHandler;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022-04-22 10:09
 */
public class CWXPDKGetTime extends PlayerHandler {

    @Override
    public void handle(Player player, WebSocketRequest request, String message) throws WSException, IOException {
        final CWXPDK_AddDouble clientPack = new Gson().fromJson(message, CWXPDK_AddDouble.class);

        WXPDKRoom room = (WXPDKRoom) RoomMgr.getInstance().getRoom(clientPack.roomID);
        if (null == room){
            request.error(ErrorCode.NotAllow, "CWXPDKGetTime not find room:"+clientPack.roomID);
            return;
        }
        WXPDKRoomSet set =  (WXPDKRoomSet) room.getCurSet();
        if(null == set){
            request.error(ErrorCode.NotAllow, "CWXPDKGetTime not set room:"+clientPack.roomID);
            return;
        }

        SWXPDK_GetTime make = SWXPDK_GetTime.make(room.getRoomID(), player.getPid(), clientPack.pos);
        make.secTotal = set.getTime1(clientPack.pos);
        request.response(make);
    }
}
