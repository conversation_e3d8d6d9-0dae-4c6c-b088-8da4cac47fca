package business.global.mj.ahmj.optype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCard;
import business.global.mj.MJCardInit;
import business.global.mj.ahmj.AHMJSetPos;
import business.global.mj.manage.OpCard;
import cenum.mj.MJSpecialEnum;
import cenum.mj.OpType;

import java.util.*;
import java.util.stream.Collectors;

public class AHMJAnGangImpl implements OpCard {
    @Override
    public boolean checkOpCard(AbsMJSetPos mSetPos, int specialCard) {
        AHMJSetPos jPos = (AHMJSetPos) mSetPos;
        if (jPos.isTing()) {
            return false;
        }
        return this.baoTingAnGang(jPos);
    }

    protected boolean checkFilter(AbsMJSetPos mSetPos, int type) {
        return !mSetPos.getSet().getmJinCardInfo().checkJinExist(type) && type < MJSpecialEnum.NOT_HUA.value();
    }

    /**
     * 报听杠检查
     * @param mSetPos
     * @return
     */
    public boolean baoTingAnGang (AHMJSetPos mSetPos) {
        MJCardInit mInit= mSetPos.mjCardInit(true);
        if (Objects.isNull(mInit)) {
            return false;
        }
        Map<Integer, Long> map = mSetPos.allCards().stream()
                // 筛选出所有的牌类型
                .map(MJCard::getType)
                // 检查等于金牌 或者 不是花牌
                .filter(k->this.checkFilter(mSetPos,k))
                // 按牌类型分组
                .collect(Collectors.groupingBy(p -> p, Collectors.counting()));
        boolean isGang = false;
        if (null == map || map.size() <= 0) {
            return false;
        }
        for(Map.Entry<Integer,Long> entry:map.entrySet()){
            if (entry.getValue().intValue() >= 4) {
                // 检查报听-杠
                if (mSetPos.checkBaotingGang(entry.getKey())) {
                    // 添加可以杠的Key
                    mSetPos.getAnGangList().add(entry.getKey());
                    isGang = true;
                }
            }
        }
        return isGang;
    }

    /**
     * 检测暗杠
     *
     * @param mSetPos
     * @return
     */
    public boolean anGang(AHMJSetPos mSetPos) {
        // 获取牌的类型
        Map<Integer, Long> map = mSetPos.allCards().stream().map(k -> k.getType()).collect(Collectors.groupingBy(p -> p, Collectors.counting()));
        if (null == map) {
            return false;
        }
        // 遍历
        for (Map.Entry<Integer, Long> value : map.entrySet()) {
            if (value.getValue() >= 4L) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean doOpCard(AbsMJSetPos mSetPos, int cardID) {
        return false;
    }

    public boolean doOpCard(AbsMJSetPos mSetPos, int cardID, OpType opType) {
        int type = cardID / 100;
        boolean ret = false;
        AHMJSetPos jPos = (AHMJSetPos) mSetPos;
        if (!jPos.getAnGangList().contains(type)) {
            return false;
        }
        List<Integer> publicCard = new ArrayList<>();
        int fromPos = mSetPos.getPosID();
        publicCard.add(opType.value());
        publicCard.add(fromPos);
        publicCard.add(cardID);

        // 搜集牌
        List<MJCard> tmp = new ArrayList<>();
        if (mSetPos.getHandCard().type == type) {
            tmp.add(mSetPos.getHandCard());
        }
        for (int i = 0; i < mSetPos.sizePrivateCard(); i++) {
            if (type == mSetPos.getPrivateCard().get(i).type) {
                tmp.add(mSetPos.getPrivateCard().get(i));
                if (tmp.size() >= 4) {
                    ret = true;
                    break;
                }
            }
        }

        if (ret) {
            // 增加亮牌
            publicCard.add(tmp.get(0).cardID);
            publicCard.add(tmp.get(1).cardID);
            publicCard.add(tmp.get(2).cardID);
            publicCard.add(tmp.get(3).cardID);

            mSetPos.addPublicCard(publicCard);
            mSetPos.removeAllPrivateCard(tmp);
            if (mSetPos.getHandCard().type != type) {
                mSetPos.addPrivateCard(mSetPos.getHandCard());
                mSetPos.sortCards();
            }
            mSetPos.cleanHandCard();
        }

        return ret;
    }
}
