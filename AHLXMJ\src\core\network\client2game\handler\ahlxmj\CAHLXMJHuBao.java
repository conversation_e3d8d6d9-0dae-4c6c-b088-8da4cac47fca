package core.network.client2game.handler.ahlxmj;	
	
import business.global.mj.ahlxmj.AHLXMJRoom;	
import business.global.room.RoomMgr;	
import business.player.Player;	
import com.ddm.server.websocket.def.ErrorCode;	
import com.ddm.server.websocket.handler.requset.WebSocketRequest;	
import com.google.gson.Gson;	
import core.network.client2game.handler.PlayerHandler;	
import core.network.http.proto.SData_Result;	
import jsproto.c2s.iclass.mj.CMJ_OpCard;	
	
import java.io.IOException;	
	
public class CAHLXMJHuBao extends PlayerHandler {	
	
    @SuppressWarnings("rawtypes")	
    @Override	
    public void handle(Player player, WebSocketRequest request, String message) throws IOException {	
        final CMJ_OpCard req = new Gson().fromJson(message, CMJ_OpCard.class);	
        long roomID = req.roomID;	
        AHLXMJRoom room = (AHLXMJRoom) RoomMgr.getInstance().getRoom(roomID);	
        if (null == room) {	
            request.error(ErrorCode.NotAllow, getOpName() + " not find room:" + roomID);	
            return;	
        }	
        SData_Result result = room.huBao(request, player.getPid(), req.setID);	
        if (!ErrorCode.Success.equals(result.getCode())) {	
            request.error(result.getCode(), result.getMsg());	
        }	
    }	
	
}				
