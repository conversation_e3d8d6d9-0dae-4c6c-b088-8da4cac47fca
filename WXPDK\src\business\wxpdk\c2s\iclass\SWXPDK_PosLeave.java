package business.wxpdk.c2s.iclass;

import jsproto.c2s.iclass.room.SBase_PosLeave;

/**
 * 位置离开通知
 * 
 * <AUTHOR>
 *
 */
@SuppressWarnings("serial")
public class SWXPDK_PosLeave extends SBase_PosLeave {

	public static SWXPDK_PosLeave make(SBase_PosLeave posLeave) {
		SWXPDK_PosLeave ret = new SWXPDK_PosLeave();
		ret.setRoomID(posLeave.getRoomID());
		ret.setPos(posLeave.getPos());
		ret.setBeKick(posLeave.isBeKick());
		ret.setOwnerID(posLeave.getOwnerID());
		ret.setKickOutTYpe(posLeave.getKickOutTYpe());
		ret.setMsg(posLeave.getMsg());
		return ret;
	}
}
