package business.global.mj.ahhbmj;

import business.global.mj.AbsMJSetPos;
import business.global.mj.AbsMJSetPosMgr;
import business.global.mj.AbsMJSetRoom;
import business.global.mj.AbsMJSetRound;
import business.global.mj.manage.MJFactory;
import business.global.mj.op.BuHuaImpl;
import business.global.mj.template.MJTemplateRoom;
import business.global.mj.template.optype.MJTemplateBuHuaImpl;
import cenum.mj.FlowerEnum;
import cenum.mj.HuType;
import cenum.mj.MJCEnum;
import cenum.mj.OpType;
import jsproto.c2s.cclass.mj.BaseMJSet_Pos;
import jsproto.c2s.cclass.mj.NextOpType;
import jsproto.c2s.cclass.mj.OpTypeInfo;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 淮北麻将
 *
 * <AUTHOR>
 */
public class AHHBMJSetPosMgr extends AbsMJSetPosMgr {
    public AHHBMJSetPosMgr(AbsMJSetRoom set) {
        super(set);
    }

    @Override
    public void startSetApplique() {
        // 标识是否有开局补花
        List<Integer> list = new ArrayList<Integer>();
        boolean isStartSetApplique = false;
        AbsMJSetPos mPos = null;
        for (int i = 0; i < this.set.getPlayerNum(); i++) {
            int index = (this.set.getDPos() + i) % this.set.getPlayerNum();
            mPos = this.set.getMJSetPos(index);
            if (null == mPos) {
                continue;
            }
            if (MJFactory.getOpCard(BuHuaImpl.class).checkOpCard(mPos, FlowerEnum.PRIVATE.ordinal())) {
                isStartSetApplique = true;

            }
        }
        if (isStartSetApplique) {
            // 存在开局补花。
            startSetApplique();
        }
    }

    @Override
    public List<BaseMJSet_Pos> getAllPlayBackNotify() {
        List<BaseMJSet_Pos> setPosList = new ArrayList<BaseMJSet_Pos>();
        for (int i = 0; i < this.set.getRoom().getPlayerNum(); i++) {
            AbsMJSetPos setPos = this.set.getMJSetPos(i);
            if (null != setPos) {
                setPosList.add(setPos.getPlayBackNotify());
            }
        }
        return setPosList;
    }


    /**
     * 检测可以抢杠胡的操作者
     *
     * @param curOpPos  操作者位置
     * @param curCardID 操作牌
     * @return
     */
    @Override
    public void check_QiangGangHu(int curOpPos, int curCardID) {
        AHHBMJSetPos setPos = null;
        for (int i = 1; i < this.set.getRoom().getPlayerNum(); i++) {
            int nextPos = (curOpPos + i) % this.set.getRoom().getPlayerNum();
            setPos = (AHHBMJSetPos) this.set.getMJSetPos(nextPos);
            if (setPos.checkOpType(curCardID, OpType.Hu)) {
                this.addOpTypeInfo(nextPos, OpType.QiangGangHu);
            }
        }
    }

    @Override
    protected boolean checkExistYPDX() {
        return this.set.getRoom().RoomCfg(AHHBMJRoomEnum.KeXuanWanFa.YPDX);
    }

    @Override
    protected boolean checkExistPingHu() {
        return !this.set.getRoom().RoomCfg(AHHBMJRoomEnum.KeXuanWanFa.BU_KE_DIANPAO);
    }

    /**
     * 检查是否存在接杠补杠 T:接杠时可选择碰后再补杠。
     *
     * @return
     */
    @Override
    protected boolean checkExistJGBG() {
        return true;
    }

    @Override
    protected boolean checkExistChi() {
        return false;
    }

    /**
     * 检查动作类型。
     *
     * @param curOpPos  当前操作位置ID
     * @param curCardID 当前操作牌ID
     * @param opType    动作类型
     */
    @Override
    public void checkOpType(int curOpPos, int curCardID, OpType opType) {
        // 清空所有动作类型操作
        this.cleanAllOpType();
        switch (opType) {
            case Out:
            case BaoTing:
                checkOutOpType(curOpPos, curCardID);
                break;
            case Gang:
                checkOpTypeQGH(curOpPos, curCardID);
                break;
            case Fan:
                this.checkTianTing(curOpPos, opType);
                break;
            case Pao:
                this.checkXiaPao(curOpPos, opType);
                break;
            default:
                break;
        }
    }

    /**
     * 下个动作类型。
     *
     * @param opType
     * @return
     */
    @Override
    public NextOpType exeCardAction(OpType opType) {
        NextOpType nOpType = null;
        switch (opType) {
            case Out:
            case Gang:
            case BaoTing:
            case Fan:
            case Pao:
                nOpType = opAllMapOutCard();
                break;
            default:
                break;
        }
        return nOpType;
    }

    /**
     * 检查天听 庄家打完牌的时候判断
     *
     * @param curOpPos
     * @return
     */
    private void checkTianTing(int curOpPos, OpType opType) {
        for (int i = 1; i < this.set.getRoom().getPlayerNum(); i++) {
            int nextPos = (curOpPos + i) % this.set.getRoom().getPlayerNum();
            this.addOpTypeInfo(nextPos, opType);
        }

    }

    /**
     * 检查天听 庄家打完牌的时候判断
     *
     * @param curOpPos
     * @return
     */
    private void checkXiaPao(int curOpPos, OpType opType) {
        AbsMJSetRound preRound = this.set.getPreRound();
        if (Objects.isNull(preRound)) {
            return;
        }
        if (preRound.getExeOpPos() == set.getDPos() && preRound.getOpType().equals(OpType.Piao_Fen)) {
            if (preRound.getOpCard() < 4) {
                this.addOpTypeInfo(preRound.getExeOpPos(), opType);
            } else {
                for (int i = 0; i < this.set.getRoom().getPlayerNum(); i++) {
                    if (i == set.getDPos()) {
                        continue;
                    }
                    this.addOpTypeInfo(i, OpType.Fan);
                }
            }
        } else {
            for (int i = 0; i < this.set.getRoom().getPlayerNum(); i++) {
                if (i == set.getDPos()) {
                    continue;
                }
                this.addOpTypeInfo(i, opType);
            }
        }

    }

    /**
     * 添加动作信息
     *
     * @param posId  位置
     * @param opType 动作类型
     */
    @Override
    protected void addOpTypeInfo(Integer posId, OpType opType) {
        if (null == this.opTypeInfoList) {
            this.opTypeInfoList = Collections.synchronizedList(new ArrayList<>());
            ;
        }
        int count = this.opTypeInfoList.size();
        // 检查是否存在一炮多响
        if (this.checkExistYPDX()) {
            // 存在一炮多响
            if (!HuType.NotHu.equals(MJCEnum.OpHuType(opType))) {
                count = 0;
                this.huPosList.add(posId);
            }
        }
        // 如果是天听的话
        if (opType.equals(OpType.Fan) || opType.equals(OpType.Pao)) {
            count = 0;
            this.huPosList.add(posId);
        }
        // 添加动作信息
        this.opTypeInfoList.add(new OpTypeInfo(count + 1, posId, opType));
        // 添加动作信息
    }

}			
