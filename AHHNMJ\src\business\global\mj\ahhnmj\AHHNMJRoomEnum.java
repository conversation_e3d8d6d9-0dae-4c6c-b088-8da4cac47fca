package business.global.mj.ahhnmj;

/**
 * 麻将配置
 *
 * <AUTHOR>
 */
public class AHHNMJRoomEnum {

    /**
     * 叫嘴
     *
     * <AUTHOR>
     */
    public enum AHHNMJJiaoZui {
        Ji<PERSON>,    // 叫嘴
        <PERSON><PERSON>_<PERSON><PERSON>,    // 不叫
        ;

    }


    /**
     * 可选玩法
     *
     * <AUTHOR>
     */
    public enum AHHNMJWanFa {
        LZD,//连庄倒
        QGQB,//抢杠全包
        Nao,//闹
    }

    public enum AHHNMJGameRoomConfigEnum {
        /**
         * 房间内切换人数
         */
        FangJianQieHuanRenShu,
        /**
         * 自动准备
         */
        ZiDongZhunBei,
        /**
         * 小局托管解散
         */
        SetAutoJieSan,
        ;
    }


    /**
     * 动作分数
     *
     * <AUTHOR>
     */
    public enum AHHNMJOpPoint {
        Not(0),
        GSKH(2),//杠开
        GSP(2),//杠上炮
        QGH(1),//抢杠胡
        ZiMo(2), //自摸
        Hu(1), //平胡
        AnGang(2),//暗杠
        gang(1),//明杠
        Ji<PERSON>ui(1),//叫嘴
        Nao(1),//闹
        LianZhuang(1),//连庄
        ;

        private int value;

        private AHHNMJOpPoint(int value) {
            this.value = value;
        }

        public int value() {
            return this.value;
        }


    }

}		
