package business.ahmj.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;

import java.util.ArrayList;
import java.util.List;

/**
 * 翻牌
 *
 * <AUTHOR>
 * @date 2020/10/22
 */
public class SAHMJ_FlipCards extends BaseSendMsg {
    public List<Integer> cardList = new ArrayList<>();
    public Integer posId;

    public static SAHMJ_FlipCards make(List<Integer> cardList, Integer posId) {
        SAHMJ_FlipCards ret = new SAHMJ_FlipCards();
        ret.cardList = new ArrayList<>(cardList);
        ret.posId = posId;
        return ret;
    }

}
