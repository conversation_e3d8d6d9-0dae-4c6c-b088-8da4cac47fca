package business.global.mj.ahhbmj;

import business.ahhbmj.c2s.cclass.AHHBMJResults;
import business.ahhbmj.c2s.cclass.AHHBMJRoomSetInfo;
import business.ahhbmj.c2s.iclass.*;
import business.global.mj.AbsMJSetPos;
import business.global.mj.AbsMJSetRoom;
import business.global.room.ContinueRoomInfoMgr;
import business.global.room.base.AbsRoomPosMgr;
import business.global.room.mj.MahjongRoom;
import cenum.ChatType;
import cenum.ClassType;
import cenum.room.DissolveType;
import com.ddm.server.common.CommLogD;
import com.google.gson.Gson;
import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.RoomEndResult;
import jsproto.c2s.cclass.room.BaseRoomConfigure;
import jsproto.c2s.cclass.room.ContinueRoomInfo;
import jsproto.c2s.cclass.room.GetRoomInfo;
import jsproto.c2s.cclass.room.RoomPosInfo;
import jsproto.c2s.iclass.S_GetRoomInfo;
import jsproto.c2s.iclass.room.SBase_Dissolve;
import jsproto.c2s.iclass.room.SBase_PosLeave;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 淮北麻将 房间逻辑
 *
 * @param <T>
 * <AUTHOR>
 */

public class AHHBMJRoom<T> extends MahjongRoom {
    public CAHHBMJ_CreateRoom cfg;// 开房配置												

    /**
     * 荒庄计数
     */
    public int huangZhuangCount;
    public int lastDPos;
    public Map<Integer, List<Integer>> zuoLaPaoList = new HashMap<>();

    /**
     * 房主需要准备
     *
     * @return T:不准备,F:默认准备
     */
    public boolean ownerNeedReady() {
        return true;
    }


    /**
     * 房间内每个位置信息 管理器
     */
    @Override
    public AbsRoomPosMgr initRoomPosMgr() {
        return new AHHBMJRoomPosMgr(this);
    }

    /**
     * @param baseRoomConfigure
     * @param roomKey
     * @param ownerID
     */
    protected AHHBMJRoom(BaseRoomConfigure<CAHHBMJ_CreateRoom> baseRoomConfigure, String roomKey, long ownerID) {
        super(baseRoomConfigure, roomKey, ownerID);
        initShareBaseCreateRoom(CAHHBMJ_CreateRoom.class, baseRoomConfigure);
        this.cfg = (CAHHBMJ_CreateRoom) this.getBaseRoomConfigure().getBaseCreateRoom();

    }

    @Override
    public void clear() {
        super.clear();
        cfg = null;
    }


    @Override
    public boolean isCanChangePlayerNum() {
        return this.getBaseRoomConfigure().getBaseCreateRoom().getFangjian()
                .contains(AHHBMJRoomEnum.AHHBMJGameRoomConfigEnum.FANGJIANNEIQIEHUANRENSHU.ordinal());
    }

    /**
     * 自动准备游戏 玩家加入房间时，自动进行准备。
     */
    @Override
    public boolean autoReadyGame() {
        return this.getBaseRoomConfigure().getBaseCreateRoom().getFangjian()
                .contains(AHHBMJRoomEnum.AHHBMJGameRoomConfigEnum.ZiDongZhunBei.ordinal());
    }

    @Override
    public <E> boolean RoomCfg(E m) {
        AHHBMJRoomEnum.KeXuanWanFa cfgEnum = (AHHBMJRoomEnum.KeXuanWanFa) m;
        int cfgInt = cfgEnum.ordinal();
        if (this.getRoomCfg().getKexuanwanfa().contains(cfgInt)) {
            return true;
        }
        return false;
    }


    @Override
    public int getWanfa() {
        return 0;
    }

    /**
     * 获取解散
     *
     * @return
     */


    @Override
    protected AbsMJSetRoom newMJRoomSet(int curSetID, MahjongRoom room, int dPos) {
        return new AHHBMJRoomSet(curSetID, room, dPos);
    }

    @SuppressWarnings("rawtypes")
    @Override
    public GetRoomInfo getRoomInfo(long pid) {
        S_GetRoomInfo ret = new SAHHBMJ_GetRoomInfo();
        // 设置房间公共信息												
        this.getBaseRoomInfo(ret);
        if (null != this.getCurSet()) {
            ret.setSet(this.getCurSet().getNotify_set(pid));
        } else {
            ret.setSet(new AHHBMJRoomSetInfo());
        }
        return ret;
    }

    @Override
    public <T> BaseSendMsg RoomEnd(T record, RoomEndResult<?> sRoomEndResult) {
        RoomEndResult roomEndResult = this.getRoomEndResult();
        roomEndResult.getResultsList().stream().max(Comparator.comparingInt(AHHBMJResults::getPoint)).ifPresent(n -> {
            ((AHHBMJResults) n).setWinner(true);
        });
        return SAHHBMJ_RoomEnd.make(this.getMJRoomRecordInfo(), roomEndResult);
    }

    @Override
    public BaseSendMsg ChangePlayerNum(long roomID, int createPos, int endSec, int playerNum) {
        return SAHHBMJ_ChangePlayerNum.make(roomID, createPos, endSec, playerNum);
    }

    @Override
    public BaseSendMsg ChangePlayerNumAgree(long roomID, int pos, boolean agreeChange) {
        return SAHHBMJ_ChangePlayerNumAgree.make(roomID, pos, agreeChange);
    }

    @Override
    public BaseSendMsg ChangeRoomNum(long roomID, String roomKey, int createType) {
        return SAHHBMJ_ChangeRoomNum.make(roomID, roomKey, createType);
    }

    @Override
    public BaseSendMsg Trusteeship(long roomID, long pid, int pos, boolean trusteeship) {
        return SAHHBMJ_Trusteeship.make(roomID, pid, pos, trusteeship);
    }

    @Override
    public BaseSendMsg PosLeave(SBase_PosLeave posLeave) {
        return SAHHBMJ_PosLeave.make(posLeave);
    }

    @Override
    public BaseSendMsg LostConnect(long roomID, long pid, boolean isLostConnect, boolean isShowLeave) {
        return SAHHBMJ_LostConnect.make(roomID, pid, isLostConnect, isShowLeave);
    }

    @Override
    public BaseSendMsg PosContinueGame(long roomID, int pos) {
        return SAHHBMJ_PosContinueGame.make(roomID, pos);
    }

    @Override
    public BaseSendMsg PosUpdate(long roomID, int pos, RoomPosInfo posInfo, int custom) {
        return SAHHBMJ_PosUpdate.make(roomID, pos, posInfo, custom);
    }

    @Override
    public BaseSendMsg PosReadyChg(long roomID, int pos, boolean isReady) {
        return SAHHBMJ_PosReadyChg.make(roomID, pos, isReady);
    }

    @Override
    public BaseSendMsg Dissolve(SBase_Dissolve dissolve) {
        return SAHHBMJ_Dissolve.make(dissolve);
    }

    @Override
    public BaseSendMsg StartVoteDissolve(long roomID, int createPos, int endSec) {
        return SAHHBMJ_StartVoteDissolve.make(roomID, createPos, endSec);
    }

    @Override
    public BaseSendMsg PosDealVote(long roomID, int pos, boolean agreeDissolve, int endSec) {
        return SAHHBMJ_PosDealVote.make(roomID, pos, agreeDissolve);
    }

    @Override
    public BaseSendMsg Voice(long roomID, int pos, String url) {
        return SAHHBMJ_Voice.make(roomID, pos, url);
    }

    @Override
    public BaseSendMsg XiPai(long roomID, long pid, ClassType cType) {
        return SAHHBMJ_XiPai.make(roomID, pid, cType);
    }

    @Override
    public BaseSendMsg ChatMessage(long pid, String name, String content, ChatType type, long toCId, int quickID) {
        return SAHHBMJ_ChatMessage.make(pid, name, content, type, toCId, quickID);
    }

    @Override
    public <T> BaseSendMsg RoomRecord(List<T> records) {
        return SAHHBMJ_RoomRecord.make(records);
    }

    @Override
    public String dataJsonCfg() {
        return new Gson().toJson(this.getRoomCfg());
    }

    /**
     * 获取房间配置
     *
     * @return
     */
    public CAHHBMJ_CreateRoom getRoomCfg() {
        if (this.cfg == null) {
            initShareBaseCreateRoom(CAHHBMJ_CreateRoom.class, getBaseRoomConfigure());
            return (CAHHBMJ_CreateRoom) getBaseRoomConfigure().getBaseCreateRoom();
        }
        return this.cfg;
    }

    /**
     * 清除记录。
     */
    @Override
    public void clearEndRoom() {
        super.clear();
        cfg = null;// 开房配置											
    }

    @SuppressWarnings("unchecked")
    @Override
    public CAHHBMJ_CreateRoom getCfg() {
        return (CAHHBMJ_CreateRoom) getRoomCfg();
    }

    /**
     * 检查是否解散房间
     *
     * @return T:解散,F:不解散
     */
    public boolean checkDissolveRoom(int curSec) {
        return checkDissolveRoom(curSec, DissolveType.ALL);
    }


    /**
     * 继续房间功能 如果有需要的话去子游戏那边处理
     */
    @Override
    protected void continueRoom() {
        ContinueRoomInfo continueRoomInfo = new ContinueRoomInfo();
        continueRoomInfo.setRoomID(this.getRoomID());
        continueRoomInfo.setBaseRoomConfigure(this.getBaseRoomConfigure().deepClone());
        continueRoomInfo.setRoomEndTime(this.getGameRoomBO().getEndTime());
        continueRoomInfo.setPlayerIDList(this.getRoomPidAll());
        ContinueRoomInfoMgr.getInstance().putContinueRoomInfo(continueRoomInfo);
    }

    /**
     * 30秒未准备自动退出
     *
     * @return
     */
    @Override
    public boolean is30SencondTimeOut() {
        return getRoomCfg().getGaoji().contains(4);
    }

    /**
     * 新一局
     */
    @Override
    public void startNewSet() {
        this.setCurSetID(this.getCurSetID() + 1);
        // / 计算庄位
        if (this.getCurSetID() == 1) {
            Random random = new Random();
            int firstDpos = random.nextInt(this.getPlayerNum());
            setDPos(firstDpos);
        } else if (this.getCurSet() != null) {
            AbsMJSetRoom mRoomSet = (AbsMJSetRoom) this.getCurSet();
            // 根据上一局计算下一局庄家
            int nextDPos = mRoomSet.calcNextDPos();
            setLastDPos(getDPos());
            mRoomSet.getPosDict().values().forEach(k -> this.zuoLaPaoList.put(k.getPosID(), ((AHHBMJSetPos) k).zuoLaPaoList()));
            setDPos(nextDPos);
            mRoomSet.clear();
        }
        // 每个位置，清空准备状态			
        this.getRoomPosMgr().clearGameReady();
        // 通知局数变化			
        this.getRoomTyepImpl().roomSetIDChange();
        AbsMJSetRoom newMJRoomSet = this.newMJRoomSet(this.getCurSetID(), this, this.getDPos());
        this.setCurSet(newMJRoomSet);
    }

    public int getLastDPos() {
        return lastDPos;
    }

    public void setLastDPos(int lastDPos) {
        this.lastDPos = lastDPos;
    }

    public Map<Integer, List<Integer>> getZuoLaPaoList() {
        return zuoLaPaoList;
    }

    public void setZuoLaPaoList(Map<Integer, List<Integer>> zuoLaPaoList) {
        this.zuoLaPaoList = zuoLaPaoList;
    }
}
