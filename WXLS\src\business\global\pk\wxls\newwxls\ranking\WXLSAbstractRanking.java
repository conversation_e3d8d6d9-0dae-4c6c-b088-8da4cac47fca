package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSCardRankEnum;	
import business.wxls.c2s.cclass.newwxls.WXLSCardSuitEnum;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
	
import java.util.ArrayList;	
import java.util.Iterator;	
import java.util.List;	
	
public abstract class WXLSAbstractRanking implements WXLSIRanking {	
    public static ArrayList<ArrayList<WXLSCardRankEnum>> twoCars = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
    public static ArrayList<ArrayList<WXLSCardRankEnum>> threeCars = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
    public static ArrayList<ArrayList<WXLSCardRankEnum>> threeCarsTwo = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
    public static ArrayList<ArrayList<WXLSCardRankEnum>> fourCars = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
    public static ArrayList<ArrayList<WXLSCardRankEnum>> fiveCars = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
    static {	
        fiveCars = getAllList(5);// 不包含鬼牌	
        twoCars = getAllList(2);// 一张鬼牌	
        threeCars = getAllList(3);// 不包含鬼牌	
        threeCarsTwo = getAllList32();// 3+2 鬼牌	
        fourCars = getAllList(4);// 4+1 鬼牌	
    }	
	
    @Override	
    public WXLSRankingResult resolve(WXLSPlayerDun player) {	
        this.preResolve(player);	
        WXLSRankingResult result = this.doResolve(player);	
        this.postResolve(player, result);	
        return result;	
    }	
	
    protected static ArrayList<ArrayList<WXLSCardRankEnum>> getAllList(int length) {	
        ArrayList<ArrayList<WXLSCardRankEnum>> ret = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
        for (WXLSCardRankEnum rankEnum : WXLSCardRankEnum.values()) {	
                ArrayList<ArrayList<WXLSCardRankEnum>> tmp = getList(rankEnum, length);	
                if (tmp != null && tmp.size() > 0) {	
                    ret.addAll(tmp);	
                }	
        }	
        return ret;	
    }	
	
    @SuppressWarnings("unchecked")	
    protected static ArrayList<ArrayList<WXLSCardRankEnum>> getAllList32() {	
        ArrayList<ArrayList<WXLSCardRankEnum>> ret = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
        for (int i = 0; i < WXLSAbstractRanking.fiveCars.size(); i++) {	
            ArrayList<WXLSCardRankEnum> sublist = (ArrayList<WXLSCardRankEnum>) WXLSAbstractRanking.fiveCars.get(i).clone();	
            for (int j = 0; j < sublist.size(); j++) {	
                for (int k = j + 1; k < sublist.size(); k++) {	
                    ArrayList<WXLSCardRankEnum> tmp = (ArrayList<WXLSCardRankEnum>) WXLSAbstractRanking.fiveCars.get(i).clone();	
                    tmp.remove(sublist.get(j));	
                    tmp.remove(sublist.get(k));	
                    if (!ret.contains(tmp)) {	
                        ret.add(tmp);	
                    }	
                }	
            }	
        }	
	
        return ret;	
    }	
	
    protected static ArrayList<WXLSCardRankEnum> listRemove(ArrayList<WXLSCardRankEnum> alllist, List<WXLSCardRankEnum> rmlist) {	
        Iterator<WXLSCardRankEnum> it = alllist.iterator();	
        while (it.hasNext()) {	
            WXLSCardRankEnum x = it.next();	
            if (rmlist.contains(x)) {	
                rmlist.remove(x);	
                it.remove();	
            }	
        }	
        return alllist;	
    }	
	
    @SuppressWarnings("unchecked")	
    public ArrayList<ArrayList<WXLSCardRankEnum>> check(ArrayList<WXLSCardRankEnum> cards, int firstcnt, int secondcnt,	
                                                    int thirdcnt) {	
        ArrayList<ArrayList<WXLSCardRankEnum>> retlist = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
        ArrayList<ArrayList<WXLSCardRankEnum>> firstlst = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
        ArrayList<ArrayList<WXLSCardRankEnum>> tmplst = getSZListByCards(cards, firstcnt);	
        if (tmplst.size() > 0) {	
            firstlst = tmplst;	
        }	
	
        for (int i = 0; i < firstlst.size(); i++) {	
            ArrayList<WXLSCardRankEnum> cards2 = new ArrayList<WXLSCardRankEnum>();	
            cards2 = (ArrayList<WXLSCardRankEnum>) cards.clone();	
            cards2 = listRemove(cards2, (ArrayList<WXLSCardRankEnum>) firstlst.get(i).clone());	
            ArrayList<ArrayList<WXLSCardRankEnum>> secondlst = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
            ArrayList<ArrayList<WXLSCardRankEnum>> tmplst2 = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
            if (secondcnt == 3) {	
                tmplst2 = getSZListByCards2(cards2);	
            } else {	
                tmplst2 = getSZListByCards(cards2, secondcnt);	
            }	
            if (tmplst2.size() > 0) {	
                secondlst = tmplst2;	
            }	
	
            for (int j = 0; j < secondlst.size(); j++) {	
                ArrayList<WXLSCardRankEnum> cards3 = new ArrayList<WXLSCardRankEnum>();	
                cards3 = (ArrayList<WXLSCardRankEnum>) cards2.clone();	
                cards3 = listRemove(cards3, (ArrayList<WXLSCardRankEnum>) secondlst.get(j).clone());	
                ArrayList<ArrayList<WXLSCardRankEnum>> tmplst3 = getSZListByCards(cards3, thirdcnt);	
	
                if (tmplst3.size() > 0) {	
                    retlist.add(firstlst.get(i));	
                    retlist.add(secondlst.get(j));	
                    retlist.add(tmplst3.get(0));	
                    return retlist;	
                }	
            }	
        }	
        return retlist;	
    }	
	
	
    @SuppressWarnings("unchecked")	
    public ArrayList<ArrayList<WXLSCardRankEnum>> checkAll(ArrayList<WXLSCardRankEnum> cards, int firstcnt, int secondcnt,	
                                                       int thirdcnt) {	
        ArrayList<ArrayList<WXLSCardRankEnum>> retlist = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
        ArrayList<ArrayList<WXLSCardRankEnum>> firstlst = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
        ArrayList<ArrayList<WXLSCardRankEnum>> tmplst = getSZListByCards(cards, firstcnt);	
        if (tmplst.size() > 0) {	
            firstlst = tmplst;	
        }	
	
        for (int i = 0; i < firstlst.size(); i++) {	
            ArrayList<WXLSCardRankEnum> cards2 = new ArrayList<WXLSCardRankEnum>();	
            cards2 = (ArrayList<WXLSCardRankEnum>) cards.clone();	
            cards2 = listRemove(cards2, (ArrayList<WXLSCardRankEnum>) firstlst.get(i).clone());	
            ArrayList<ArrayList<WXLSCardRankEnum>> secondlst = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
            ArrayList<ArrayList<WXLSCardRankEnum>> tmplst2 = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
            if (secondcnt == 3) {	
                tmplst2 = getSZListByCards2(cards2);	
            } else {	
                tmplst2 = getSZListByCards(cards2, secondcnt);	
            }	
            if (tmplst2.size() > 0) {	
                secondlst = tmplst2;	
            }	
	
            for (int j = 0; j < secondlst.size(); j++) {	
                ArrayList<WXLSCardRankEnum> cards3 = new ArrayList<WXLSCardRankEnum>();	
                cards3 = (ArrayList<WXLSCardRankEnum>) cards2.clone();	
                cards3 = listRemove(cards3, (ArrayList<WXLSCardRankEnum>) secondlst.get(j).clone());	
                ArrayList<ArrayList<WXLSCardRankEnum>> tmplst3 = getSZListByCards(cards3, thirdcnt);	
	
                if (tmplst3.size() > 0) {	
                    retlist.add(firstlst.get(i));	
                    retlist.add(secondlst.get(j));	
                    retlist.add(tmplst3.get(0));	
                }	
            }	
        }	
        return retlist;	
    }	
	
	
    /**	
     * 单数字的顺子	
     *	
     * @param rank	
     * @return	
     */	
    @SuppressWarnings("unchecked")	
    protected static ArrayList<ArrayList<WXLSCardRankEnum>> getList(WXLSCardRankEnum rank, int length) {	
        int num = rank.getNumber();	
        // 处理带A的顺子	
        if (rank == WXLSCardRankEnum.CARD_ACE) {	
            num = 1;	
        }	
        int maxnum = num + length - 1;	
        if (maxnum > 14) {	
            return null;	
        }	
	
        // 一张鬼的情况	
        if (4 == length || 2 == length) {	
            ArrayList<WXLSCardRankEnum> ret = new ArrayList<WXLSCardRankEnum>();	
            ArrayList<ArrayList<WXLSCardRankEnum>> ret2 = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
            for (int i = 0; i < length; i++) {	
                int index = num + i;	
                if (index == 1) {	
                    index = 14;	
                }	
                ret.add(WXLSCardRankEnum.valueOf(index));	
            }	
            ret2.add(ret);	
            // 添加间隔顺子	
            if (maxnum < 14) {	
                WXLSCardRankEnum adde = WXLSCardRankEnum.valueOf(maxnum + 1);	
                for (int i = 1; i < ret.size(); i++) {	
                    ArrayList<WXLSCardRankEnum> tmp = (ArrayList<WXLSCardRankEnum>) ret.clone();	
                    tmp.remove(i);	
                    tmp.add(adde);	
                    ret2.add(tmp);	
                }	
            }	
            return ret2;	
        } else {	
            ArrayList<WXLSCardRankEnum> ret = new ArrayList<WXLSCardRankEnum>();	
            for (int i = 0; i < length; i++) {	
                int index = num + i;	
                if (index == 1) {	
                    index = 14;	
                }	
                ret.add(WXLSCardRankEnum.valueOf(index));	
            }	
            ArrayList<ArrayList<WXLSCardRankEnum>> ret2 = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
            ret2.add(ret);	
            return ret2;	
        }	
    }	
	
    /**	
     * 筛选出手牌所包含的顺子	
     */	
    protected ArrayList<ArrayList<WXLSCardRankEnum>> getSZListByCards(ArrayList<WXLSCardRankEnum> cards, int length) {	
        ArrayList<ArrayList<WXLSCardRankEnum>> ret = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
        if (length == 5) {	
            for (int i = 0; i < fiveCars.size(); i++) {	
                if (cards.containsAll(WXLSAbstractRanking.fiveCars.get(i))) {	
                    ret.add(WXLSAbstractRanking.fiveCars.get(i));	
                }	
            }	
        } else if (length == 4) {	
            for (int i = 0; i < WXLSAbstractRanking.fourCars.size(); i++) {	
                if (cards.containsAll(WXLSAbstractRanking.fourCars.get(i))) {	
                    ret.add(WXLSAbstractRanking.fourCars.get(i));	
                }	
            }	
        } else if (length == 3) {	
            for (int i = 0; i < WXLSAbstractRanking.threeCars.size(); i++) {	
                if (cards.containsAll(WXLSAbstractRanking.threeCars.get(i))) {	
                    ret.add(WXLSAbstractRanking.threeCars.get(i));	
                }	
            }	
        } else if (length == 2) {	
            for (int i = 0; i < WXLSAbstractRanking.twoCars.size(); i++) {	
                if (cards.containsAll(WXLSAbstractRanking.twoCars.get(i))) {	
                    ret.add(WXLSAbstractRanking.twoCars.get(i));	
                }	
            }	
        } else if (length == 1) {	
            ret.add(cards);	
        }	
        return ret;	
    }	
	
    protected ArrayList<ArrayList<WXLSCardRankEnum>> getSZListByCards2(ArrayList<WXLSCardRankEnum> cards) {	
        ArrayList<ArrayList<WXLSCardRankEnum>> ret = new ArrayList<ArrayList<WXLSCardRankEnum>>();	
        for (int i = 0; i < threeCarsTwo.size(); i++) {	
            if (cards.containsAll(WXLSAbstractRanking.threeCarsTwo.get(i))) {	
                ret.add(WXLSAbstractRanking.threeCarsTwo.get(i));	
            }	
        }	
        return ret;	
    }	
	
    private void preResolve(WXLSPlayerDun player) {	
    }	
	
    private void postResolve(WXLSPlayerDun player, WXLSRankingResult result) {	
        if (result != null) {	
            result.setHighCard((player.getCards().get(0)));	
        }	
        player.setRankingResult(result);	
    }	
	
    protected abstract WXLSRankingResult doResolve(WXLSPlayerDun player);	
	
    protected boolean isSameSuit(List<WXLSPockerCard> cards) {	
        if (cards == null || cards.size() == 0) {	
            return false;	
        }	
        if (cards.size() == 1) {	
            return true;	
        }	
        if (cards.size() > 1) {	
            WXLSCardSuitEnum suitEnum = WXLSCardSuitEnum.CLUBS;	
            for(int i = 0 ; i <cards.size();i++)	
            {	
                WXLSPockerCard card = cards.get(i);	
                    suitEnum = card.getSuit();	
                    break;	
            }	
            for (int i = 1; i < cards.size(); i++) {	
                    if (suitEnum == null) {	
                        suitEnum = cards.get(i).getSuit();	
                    } else if (suitEnum != cards.get(i).getSuit()) {	
                        return false;	
                    }	
            }	
        }	
        return true;	
    }	
	
    protected int getGuiPaiCount(List<WXLSPockerCard> cards) {	
        return 0;	
    }	
	
    public static void main(String[] args) {	
        // for(int i = 0 ; i < WXLSAbstractRanking.fourCars.size(); i ++)	
        // {	
        //	
        // }	
	
        System.out.printf("twoCars=====size:%d===", WXLSAbstractRanking.twoCars.size());	
        System.out.println(WXLSAbstractRanking.twoCars);	
	
        System.out.printf("threeCars=====size:%d===", WXLSAbstractRanking.threeCars.size());	
        System.out.println(WXLSAbstractRanking.threeCars);	
	
        System.out.printf("threeCarsTwo=====size:%d===", WXLSAbstractRanking.threeCarsTwo.size());	
        System.out.println(WXLSAbstractRanking.threeCarsTwo);	
	
        System.out.printf("fourCars=====size:%d===", WXLSAbstractRanking.fourCars.size());	
        System.out.println(WXLSAbstractRanking.fourCars);	
	
        System.out.printf("fiveCars=====size:%d===", WXLSAbstractRanking.fiveCars.size());	
        System.out.println(WXLSAbstractRanking.fiveCars);	
    }	
}	
