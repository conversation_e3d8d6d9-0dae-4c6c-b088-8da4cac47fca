package business.global.pk.wxls.newwxls;	
	
import BaseCommon.CommLog;	
import business.global.pk.wxls.WXLSRoom;	
import business.global.pk.wxls.WXLSRoomPosMgr;
import business.global.pk.wxls.WXLSRoomSet;
import business.global.pk.wxls.utlis.WXLSRoomEnum;
import business.wxls.c2s.cclass.SWXLS_RankingResult;
import business.wxls.c2s.cclass.entity.WXLSPlayerCardType;	
import business.wxls.c2s.cclass.entity.WXLSPlayerResult;	
import business.wxls.c2s.cclass.entity.WXLSRanking;	
import business.wxls.c2s.cclass.newwxls.WXLSConstants;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
import business.wxls.c2s.iclass.CWXLS_CreateRoom;	
import business.wxls.c2s.iclass.CWXLS_Ranked;	
import com.ddm.server.common.CommLogD;	
	
import java.io.BufferedReader;	
import java.io.File;	
import java.io.FileInputStream;	
import java.io.InputStreamReader;	
import java.util.*;	
	
public class WXLSDealer  {	
    private WXLSPoker poker;	
    public WXLSRoom room = null;	
    // 玩家列表	
    public Map<Long, WXLSPlayerData> PlayerList = new HashMap<Long, WXLSPlayerData>();	
    // 对比所有结果	
    private SWXLS_RankingResult sRankingResult = new SWXLS_RankingResult();	
    // 特殊牌玩家	
    public List<WXLSPlayerData> tesupaiPlayers = new ArrayList<WXLSPlayerData>();	
    // 神牌列表	
    // private static HashMap<Integer, List<WXLSPockerCard>> hMap = new	
    // HashMap<Integer, List<WXLSPockerCard>>();	
	
//    private long zj = -1;	
//    public long winMaxPid = -1;	
//    private int winMax = -1;	
	
    public WXLSDealer() {	
    }	
	
	
    public void clean() {	
        if (null != PlayerList) {	
            this.PlayerList.forEach((key,value)->{	
                if (null != value) {	
                    value.clean();	
                }	
            });	
            this.PlayerList.clear();	
            this.PlayerList = null;	
        }	
	
        if (null != tesupaiPlayers) {	
            this.tesupaiPlayers.forEach(key->{	
                if (null != key) {	
                    key.clean();	
                }	
            });	
            this.tesupaiPlayers.clear();	
            this.tesupaiPlayers = null;	
        }	
        if (null != this.poker) {	
            this.poker.clean();	
            this.poker = null;	
        }	
        if (null != this.sRankingResult) {	
            this.sRankingResult.clean();	
            this.sRankingResult = null;	
        }	
        this.room = null;	
    }	
	
	
	
    /**	
     * 添加玩家	
     *	
     * @param pid	
     * @param player	
     */	
    public void addPlayer(long pid, WXLSPlayerData player) {	
        if (null == player) {	
            CommLogD.error("WXLSDealer addPlayer null == player");	
            return;	
        }	
        this.PlayerList.put(pid, player);	
    }	
	
    public WXLSPlayerData getPlayer(long pid) {	
        return this.PlayerList.get(pid);	
    }	
	
    /**	
     * 对比所有结果	
     *	
     * @return	
     */	
    public SWXLS_RankingResult getSRankingResult() {	
        return this.sRankingResult;	
    }	
	
//    public long getWinMaxPid() {	
//        return this.winMaxPid;	
//    }	
	
    /**	
     * 全垒打结果	
     *	
     * @return	
     */	
    public WXLSPlayerResult getFourbagger() {	
        return this.sRankingResult.fourbagger;	
    }	
	
    /**	
     * 玩家设置出牌顺序, 手动配置三顺子，不会提示三顺子，	
     *	
     * @param cRanked	
     */	
    public boolean setRanked(CWXLS_Ranked cRanked) {	
        WXLSPlayerData playerdata = PlayerList.get(cRanked.pid);	
        if (null != playerdata) {	
            if (cRanked.dunPos.first.size() != 3 || cRanked.dunPos.second.size() != 5	
                    || cRanked.dunPos.third.size() != 5) {	
                return false;	
            }	
            if (playerdata.setCards(cRanked.dunPos,this.room.roomCfg,((WXLSRoomSet)room.getCurSet()).getPublicCard())) {
                return playerdata.checkDun();	
            } else {	
                return false;	
            }	
        } else {	
            return false;	
        }	
    }	
    /**	
     * 设置特殊牌	
     *	
     * @param cRanked	
     */	
    public void setSpecialRanked(CWXLS_Ranked cRanked) {	
        WXLSPlayerData playerdata = PlayerList.get(cRanked.pid);	
        playerdata.first=new WXLSPlayerDun(cRanked.dunPos.first,this.room.roomCfg);	
        playerdata.second=new WXLSPlayerDun(cRanked.dunPos.second,this.room.roomCfg);	
        playerdata.third=new WXLSPlayerDun(cRanked.dunPos.third,this.room.roomCfg);	
        playerdata.setSpecialType(cRanked.special);	
	
	
    }	
	
    /**	
     * 发牌	
     *	
     * @return	
     */	
    public List<WXLSPockerCard> dispatch(WXLSPlayerData player, int playerNum, CWXLS_CreateRoom room_roomCfg) {	
        List<WXLSPockerCard> ret = new ArrayList<WXLSPockerCard>();	
        for (int j = 0; j < WXLSConstants.HAND_CARD_NUMBER; j++) {	
            ret.add(this.poker.dispatch());	
        }	
        player.setPlayerCards(ret,room_roomCfg,playerNum);
        addPlayer(player.pid, player);	
        return ret;	
    }	
    /**	
     * 发牌	
     *	
     * @return	
     */	
    public List<WXLSPockerCard> dispatch(WXLSPlayerData player, int playerNum) {	
        List<WXLSPockerCard> ret = new ArrayList<WXLSPockerCard>();	
        for (int j = 0; j < WXLSConstants.HAND_CARD_NUMBER; j++) {	
            ret.add(this.poker.dispatch());	
        }	
        player.setPlayerCards(ret,this.room.roomCfg,((WXLSRoomSet)room.getCurSet()).playerNum);
        addPlayer(player.pid, player);	
        return ret;	
    }	
	
    public void init(WXLSRoom room) {	
        this.room = room;	
        sRankingResult = new SWXLS_RankingResult();	
        this.PlayerList.clear();	
        this.tesupaiPlayers.clear();	
        this.poker = new WXLSPoker(room.getXiPaiNum(),room.getRoomCfg().getKexuanwanfa().contains(WXLSRoomEnum.WXLSKeXuanWanFa.JiaYiSe.value()));
        room.addXiPaiNum(true);	
    }	
	
    public WXLSPoker getPoker() {	
        return poker;	
    }	
	
    public void setPoker(WXLSPoker poker) {	
        this.poker = poker;	
    }	
	
    /**	
     * 比牌	
     */	
    public void Compare(long zj) {	
//        this.zj = zj;	
        tesupaiPlayers.clear();	
//        if (this.room.isZJModel()) {	
//            ZJCompare();	
//        } else {	
            allPlayerCompare();	
            getResult();	
	
//        }	
    }	
	
    /**	
     * 获取比赛结果	
     */	
    @SuppressWarnings("rawtypes")	
    private void getResult() {	
        // 基础数据	
        Iterator iter = PlayerList.entrySet().iterator();	
        while (iter.hasNext()) {	
            Map.Entry entry = (Map.Entry) iter.next();	
            sRankingResult.rankeds.add(((WXLSPlayerData) entry.getValue()).getRanked());	
            sRankingResult.pCard1.add(((WXLSPlayerData) entry.getValue()).getPlayerCardType(1));	
            sRankingResult.pCard2.add(((WXLSPlayerData) entry.getValue()).getPlayerCardType(2));	
            sRankingResult.pCard3.add(((WXLSPlayerData) entry.getValue()).getPlayerCardType(3));	
            sRankingResult.pCardResult1.add(((WXLSPlayerData) entry.getValue()).getPlayerResult(1));	
            sRankingResult.pCardResult2.add(((WXLSPlayerData) entry.getValue()).getPlayerResult(2));	
            sRankingResult.pCardResult3.add(((WXLSPlayerData) entry.getValue()).getPlayerResult(3));	
            sRankingResult.simPlayerResult.add(((WXLSPlayerData) entry.getValue()).getTotalShuiS(room));	
        }	
	
        // 计算打枪
        sRankingResult.killRankins.forEach(ps -> {
            if (this.room.isDaQiang(WXLSRoomEnum.WXLSDaQiang.PlsuOne)) {
                PlayerList.get(ps.getKeyPid()).addShuiCount((this.getBaseScore()));
                PlayerList.get(ps.getToPid()).addShuiCount(-(this.getBaseScore()));
            } else if(this.room.isDaQiang(WXLSRoomEnum.WXLSDaQiang.MulitiTwo)){
                PlayerList.get(ps.getKeyPid()).addShuiCount(ps.getShui() );
                PlayerList.get(ps.getToPid()).addShuiCount(-ps.getShui() );
            }
        });
	
        Iterator iter2 = PlayerList.entrySet().iterator();
        while (iter2.hasNext()) {
            Map.Entry entry = (Map.Entry) iter2.next();
            WXLSPlayerResult player = ((WXLSPlayerData) entry.getValue()).getTotalShuiS(room);
            sRankingResult.simResults.add(player);
        }
//        if(!(tesupaiPlayers.size()>=3&&(((WXLSRoomPosMgr)this.room.getRoomPosMgr()).getAllPlayerNum()==4))){
            //如果存在特殊牌和人数没有达到4人的时候 不计算通杀
            if(tesupaiPlayers.size()==0&&(((WXLSRoomPosMgr)this.room.getRoomPosMgr()).getAllPlayerNum()==4)){	
                // 计算通杀	
                Map<Long, Integer> tmpCount = new HashMap<Long, Integer>();	
                for (int i = 0; i < sRankingResult.killRankins.size(); i++) {	
                    long pid = sRankingResult.killRankins.get(i).getKeyPid();	
                    if (tmpCount.containsKey(pid)) {	
                        tmpCount.put(pid, tmpCount.get(pid) + 1);	
                    } else {	
                        tmpCount.put(pid, 1);	
                    }	
                }	
	
                Iterator<Map.Entry<Long, Integer>> it = tmpCount.entrySet().iterator();	
                while (it.hasNext()) {	
                    Map.Entry<Long, Integer> next = it.next();	
                    if (next.getValue() >= 2 && next.getValue() == PlayerList.size() - 1) {// 通杀条件	
                        WXLSPlayerData qldplayer = PlayerList.get(next.getKey());	
                        int pos = qldplayer.posIdx;	
                        sRankingResult.fourbagger = new WXLSPlayerResult(next.getKey(), pos, 0);
                        //赢的是包括打枪的分数
                        int cnt = (qldplayer.getFirstTypeShui(false) + qldplayer.getSecondTypeShui(false) + qldplayer.getThirdTypeShui(false))*2;
//                        int cnt = qldplayer.getShui();
                        PlayerList.values().forEach(player -> {
                            if (player.pid != qldplayer.pid) {	
                                if(getTongSha()){	
                                    qldplayer.addShuiCount( cnt );	
                                    player.addShuiCount(- cnt );}	
                            }	
	
                        });	
                        break;	
                    }	
                }	
            }	
            Set<Long> set = PlayerList.keySet();	
            List<Long> list = new ArrayList<Long>(set);	
            // 特殊牌处理	
            for (int i = 0; i < this.tesupaiPlayers.size(); i++) {	
                sRankingResult.specialPockerCard.add(new WXLSPlayerCardType(tesupaiPlayers.get(i).pid,	
                        tesupaiPlayers.get(i).posIdx, tesupaiPlayers.get(i).getSpecialType()));	
                WXLSPlayerData p1 = PlayerList.get(tesupaiPlayers.get(i).pid);	
                for (int j = 0; j < list.size(); j++) {	
                    WXLSPlayerData p2 = PlayerList.get(list.get(j));	
                    if (p1.pid != p2.pid&&!p2.getSpecial()) {	
                            int tmp = p1.getSpecialType();	
                            int shui = SpecialShui(tmp);	
                            p1.addShuiCount(shui);	
                            p1.addSpecialCount(shui);	
                            p2.addShuiCount(-shui);	
                            p2.addSpecialCount(-shui);	
                    }	
                }	
            }	
            if(room.isSpecialComapre()){
                // 特殊牌玩家对比	
                for (int i = 0; i < this.tesupaiPlayers.size(); i++) {	
                    for (int j = i + 1; j < this.tesupaiPlayers.size(); j++) {	
                        WXLSPlayerData p1 = PlayerList.get(tesupaiPlayers.get(i).pid);	
                        WXLSPlayerData p2 = PlayerList.get(tesupaiPlayers.get(j).pid);	
                        int tmp1 = p1.getSpecialType();	
                        int shui1 = SpecialShui(tmp1);	
                        int tmp2 = p2.getSpecialType();	
                        int shui2 = SpecialShui(tmp2);	
                        p1.addShuiCount(shui1);	
                        p1.addSpecialCount(shui1);	
                        p2.addShuiCount(-shui1);	
                        p2.addSpecialCount(-shui1);	
                        p1.addShuiCount(-shui2);	
                        p1.addSpecialCount(-shui2);	
                        p2.addShuiCount(shui2);	
                        p2.addSpecialCount(shui2);	
//                        if (WXLSRankingEnum.valueOf(p1.getSpecialType()).value() >WXLSRankingEnum.valueOf(p2.getSpecialType()).value()) {	
//                            int tmp = p1.getSpecialType();	
//                            int shui = SpecialShui(tmp);	
//                            p1.addShuiCount(shui);	
//                            p1.addSpecialCount(shui);	
//                            p2.addShuiCount(-shui);	
//                            p2.addSpecialCount(-shui);	
//                        } else if (WXLSRankingEnum.valueOf(p1.getSpecialType()).value() <WXLSRankingEnum.valueOf(p2.getSpecialType()).value()) {	
//                            int tmp = p2.getSpecialType();	
//                            int shui = SpecialShui(tmp);	
//                            p1.addShuiCount(-shui);	
//                            p1.addSpecialCount(-shui);	
//                            p2.addShuiCount(shui);	
//                            p2.addSpecialCount(shui);	
//                        }	
                    }	
                }	
            }	
//        }
        // 计算最终最终结果	
        Iterator iter3 = PlayerList.entrySet().iterator();	
        while (iter3.hasNext()) {	
            Map.Entry entry = (Map.Entry) iter3.next();	
            sRankingResult.playerResults.add(((WXLSPlayerData) entry.getValue()).getTotalShuiS(room));	
            sRankingResult.specialResult.add(((WXLSPlayerData) entry.getValue()).getSpecialShuiS());	
        }	
    }	
	
    private int SpecialShui(int tmp) {	
        return WXLSRankingEnum.valueOf(tmp).value() ;	
    }	
	
    /**	
     * 所有玩家比牌	
     */	
    private void allPlayerCompare() {	
        Set<Long> set = PlayerList.keySet();	
        List<Long> list = new ArrayList<Long>(set);	
        for (int i = 0; i < list.size(); i++) {	
            WXLSPlayerData p1 = PlayerList.get(list.get(i));	
            if (p1.getSpecial()) {	
                tesupaiPlayers.add(p1);// 特殊牌玩家	
            }	
        }	
        for (int i = 0; i < list.size(); i++) {	
            for (int j = i + 1; j < list.size(); j++) {	
                WXLSPlayerData p1 = PlayerList.get(list.get(i));	
                WXLSPlayerData p2 = PlayerList.get(list.get(j));	
                if (!p1.getSpecial() && !p2.getSpecial()) {	
                    this.compareAB(p1, p2);	
                }	
            }	
        }	
    }	
	
    /**	
     * 庄家比牌	
     */	
//    private void ZJCompare() {	
//        sRankingResult.zjid = this.zj;	
//        WXLSPlayerData zj = this.PlayerList.get(this.zj);	
//        Set<Long> set = PlayerList.keySet();	
//        List<Long> list = new ArrayList<Long>(set);	
//        // 特殊牌处理	
//        for (int i = 0; i < list.size(); i++) {	
//            WXLSPlayerData p1 = PlayerList.get(list.get(i));	
//            if (p1.getSpecial()) {	
//                tesupaiPlayers.add(p1);	
//            }	
//        }	
//	
//        // 比牌	
//        for (int i = 0; i < list.size(); i++) {	
//            WXLSPlayerData p1 = PlayerList.get(list.get(i));	
//            if (zj.pid != p1.pid) {	
//                this.compareZJ(zj, p1);	
//            }	
//        }	
//	
//        // 基础数据	
//        Iterator iter = PlayerList.entrySet().iterator();	
//        while (iter.hasNext()) {	
//            Map.Entry entry = (Map.Entry) iter.next();	
//            sRankingResult.rankeds.add(((WXLSPlayerData) entry.getValue()).getRanked());	
//            sRankingResult.pCard1.add(((WXLSPlayerData) entry.getValue()).getPlayerCardType(1));	
//            sRankingResult.pCard2.add(((WXLSPlayerData) entry.getValue()).getPlayerCardType(2));	
//            sRankingResult.pCard3.add(((WXLSPlayerData) entry.getValue()).getPlayerCardType(3));	
//            sRankingResult.pCardResult1.add(((WXLSPlayerData) entry.getValue()).getPlayerResult(1));	
//            sRankingResult.pCardResult2.add(((WXLSPlayerData) entry.getValue()).getPlayerResult(2));	
//            sRankingResult.pCardResult3.add(((WXLSPlayerData) entry.getValue()).getPlayerResult(3));	
//            sRankingResult.simPlayerResult.add(((WXLSPlayerData) entry.getValue()).getTotalShuiS());	
//        }	
//	
//        // 计算打枪	
//        sRankingResult.killRankins.forEach(ps -> {	
//            if (this.room.getGunBS() == 1) {	
//                PlayerList.get(ps.getKeyPid()).addShuiCount((this.getBaseScore()));	
//                PlayerList.get(ps.getToPid()).addShuiCount(-(this.getBaseScore()));	
//            } else {	
//                PlayerList.get(ps.getKeyPid()).addShuiCount(ps.getShui() * (this.room.getGunBS() - 1));	
//                PlayerList.get(ps.getToPid()).addShuiCount(-ps.getShui() * (this.room.getGunBS() - 1));	
//            }	
//        });	
//	
//        Iterator iter2 = PlayerList.entrySet().iterator();	
//        while (iter2.hasNext()) {	
//            Map.Entry entry = (Map.Entry) iter2.next();	
//            WXLSPlayerResult player = ((WXLSPlayerData) entry.getValue()).getTotalShuiS();	
//            sRankingResult.simResults.add(player);	
//        }	
//	
//        // 特殊牌数据	
//        for (int i = 0; i < this.tesupaiPlayers.size(); i++) {	
//            sRankingResult.specialPockerCard.add(new WXLSPlayerCardType(tesupaiPlayers.get(i).pid,	
//                    tesupaiPlayers.get(i).posIdx, tesupaiPlayers.get(i).getSpecialType()));	
//        }	
//	
//        // 最终数据	
//        Iterator iter3 = PlayerList.entrySet().iterator();	
//        while (iter3.hasNext()) {	
//            Map.Entry entry = (Map.Entry) iter3.next();	
//            sRankingResult.playerResults.add(((WXLSPlayerData) entry.getValue()).getTotalShuiS());	
//            int count = ((WXLSPlayerData) entry.getValue()).getShui();	
//            if (count > this.winMax) {	
//                this.winMax = count;	
//                this.winMaxPid = ((WXLSPlayerData) entry.getValue()).pid;	
//            }	
//        }	
//	
//    }	
	
    private boolean getMapai(WXLSPlayerData p1, WXLSPlayerData p2) {	
//        if (this.room.mapai != null) {	
//            List<WXLSPockerCard> cards = p1.getCards();	
//            for (int i = 0; i < cards.size(); i++) {	
//                if (cards.get(i).equals(this.room.mapai)) {	
//                    return true;	
//                }	
//            }	
//            List<WXLSPockerCard> cards2 = p2.getCards();	
//            for (int i = 0; i < cards2.size(); i++) {	
//                if (cards2.get(i).equals(this.room.mapai)) {	
//                    return true;	
//                }	
//            }	
//        }	
        return false;	
    }	
    private boolean getTongSha() {	
        if (this.room.roomCfg.getHuludaoshu() == 0) {
            return true;	
        }	
        return false;	
    }	
    private int getBeishu() {	
        int beishu = 1;	
//        if (this.room.cfg.zhuangjiaguize == 4) {	
//            beishu = this.room.getBS();	
//        }	
        return beishu;	
    }	
	
    private int getBaseScore() {	
        return this.room.getBaseScore();	
    }	
	
    private void compareAB(WXLSPlayerData p1, WXLSPlayerData p2) {	
//        int mpbs = getMapai(p1, p2) ? 2 : 1;	
//        int beishu = mpbs * this.getBaseScore();	
	
        if (!p1.checkDun() || !p2.checkDun()) {	
            CommLog.error("compareAB !p1.checkDun() || !p2.checkDun()");	
            return;	
        }	
	    boolean isSheJian=checkSheJian(p1,p2);

        // 第一墩	
        int s1 = p1.first.compareTo(p2.first);
        int p1ShuiNum=0;
        if (s1 == 1) {	
            p1.addBaseShuiCount(-p2.getFirstTypeShui(isSheJian) , 1);
            p1ShuiNum+=(-p2.getFirstTypeShui(isSheJian));
            p2.addBaseShuiCount(p2.getFirstTypeShui(isSheJian) , 1);
        } else if (s1 == -1) {	
            p1.addBaseShuiCount(p1.getFirstTypeShui(isSheJian) , 1);
            p1ShuiNum+=(p1.getFirstTypeShui(isSheJian));
            p2.addBaseShuiCount(-p1.getFirstTypeShui(isSheJian) , 1);
        }	
        // 第二墩	
        int s2 = p1.second.compareTo(p2.second);	
        if (s2 == 1) {	
            p1.addBaseShuiCount(-p2.getSecondTypeShui(isSheJian) , 2);
            p1ShuiNum+=(-p2.getSecondTypeShui(isSheJian));
            p2.addBaseShuiCount(p2.getSecondTypeShui(isSheJian), 2);
        } else if (s2 == -1) {	
            p1.addBaseShuiCount(p1.getSecondTypeShui(isSheJian) , 2);
            p1ShuiNum+=(p1.getSecondTypeShui(isSheJian));
            p2.addBaseShuiCount(-p1.getSecondTypeShui(isSheJian) , 2);
        }	
        // 第三墩	
        int s3 = p1.third.compareTo(p2.third);	
        if (s3 == 1) {	
            p1.addBaseShuiCount(-p2.getThirdTypeShui(isSheJian) , 3);
            p1ShuiNum+=(-p2.getThirdTypeShui(isSheJian));
            p2.addBaseShuiCount(p2.getThirdTypeShui(isSheJian) , 3);
        } else if (s3 == -1) {	
            p1.addBaseShuiCount(p1.getThirdTypeShui(isSheJian) , 3);
            p1ShuiNum+=(p1.getThirdTypeShui(isSheJian));
            p2.addBaseShuiCount(-p1.getThirdTypeShui(isSheJian) , 3);
        }
        if(!room.isDaQiang(WXLSRoomEnum.WXLSDaQiang.Nope)){
            // 打枪
            if (s1 == 1 && s2 == 1 && s3 == 1) {
                int total = (p2.getFirstTypeShui(isSheJian) + p2.getSecondTypeShui(isSheJian) + p2.getThirdTypeShui(isSheJian));
                sRankingResult.killRankins.add(new WXLSRanking(p2.pid, p2.posIdx, total, p1.pid, p1.posIdx));

            }
            if (s1 == -1 && s2 == -1 && s3 == -1) {
                int total = (p1.getFirstTypeShui(isSheJian) + p1.getSecondTypeShui(isSheJian) + p1.getThirdTypeShui(isSheJian)) ;
                sRankingResult.killRankins.add(new WXLSRanking(p1.pid, p1.posIdx, total, p2.pid, p2.posIdx));

            }
        }
        if(isSheJian){
           if(p1ShuiNum>0){
               sRankingResult.sheJianRankins.add(new WXLSRanking(p1.pid, p1.posIdx, 0, p2.pid, p2.posIdx));
           }else {
               sRankingResult.sheJianRankins.add(new WXLSRanking(p2.pid, p2.posIdx, 0, p1.pid, p1.posIdx));
           }
        }



    }

    private boolean checkSheJian(WXLSPlayerData p1, WXLSPlayerData p2) {
        boolean sheJian=false;
        boolean p1Public=false;
        if(((WXLSRoomSet)room.getCurSet()).playerNum==3){
            WXLSPockerCard publicCard=((WXLSRoomSet)room.getCurSet()).getPublicCard();
            boolean oneFlag=p1.getCardsRank().stream().anyMatch(k->k.getSuit().equals(publicCard.getSuit())&&k.getRank().equals(publicCard.getRank()));
            boolean twoFlga=p2.getCardsRank().stream().anyMatch(k->k.getSuit().equals(publicCard.getSuit())&&k.getRank().equals(publicCard.getRank()));
            if((oneFlag&&!twoFlga)){
                p1Public=true;
                sheJian= true;
            }else if(((!oneFlag&&twoFlga))) {
                sheJian= true;
            }
        }
        if(sheJian){
            boolean isSheJian=false;
            // 第一墩
            int s1 = p1.first.compareTo(p2.first);
            int shuiNum=0;
            if (s1 == 1) {
                if(p1Public){
                    shuiNum+=(-p2.getFirstTypeShui(isSheJian));
                }else {
                    shuiNum+=(p2.getFirstTypeShui(isSheJian));
                }

            } else if (s1 == -1) {
                if(p1Public){
                    shuiNum+=(p1.getFirstTypeShui(isSheJian));
                }else {
                    shuiNum+=(-p1.getFirstTypeShui(isSheJian));
                }
            }
            // 第二墩
            int s2 = p1.second.compareTo(p2.second);
            if (s2 == 1) {
                if(p1Public){
                    shuiNum+=(-p2.getSecondTypeShui(isSheJian));
                }else {
                    shuiNum+=(p2.getSecondTypeShui(isSheJian));
                }
            } else if (s2 == -1) {
                if(p1Public){
                    shuiNum+=(p1.getSecondTypeShui(isSheJian));
                }else {
                    shuiNum+=(-p1.getSecondTypeShui(isSheJian));
                }

            }
            // 第三墩
            int s3 = p1.third.compareTo(p2.third);
            if (s3 == 1) {
                if(p1Public){
                    shuiNum+=(-p2.getThirdTypeShui(isSheJian));
                }else {
                    shuiNum+=(p2.getThirdTypeShui(isSheJian));
                }

            } else if (s3 == -1) {
                if(p1Public){
                    shuiNum+=(p1.getThirdTypeShui(isSheJian));
                }else {
                    shuiNum+=(-p1.getThirdTypeShui(isSheJian));
                }

            }
            if(shuiNum<=-1){
                return true;
            }
        }
        return false;
    }

//    private void compareZJ(WXLSPlayerData p1, WXLSPlayerData p2) {	
//        // 庄家倍数	
//        // int zjbs = this.getBeishu() * this.getBaseScore();	
//        int zjbs = this.getBeishu();	
//	
//        if (!p1.getSpecial() && !p2.getSpecial()) {	
//            int beishu = (getMapai(p1, p2) ? 2 : 1) * zjbs * this.getBaseScore();	
//	
//            // 第一墩	
//            int s1 = p1.first.compareTo(p2.first);	
//            if (s1 == 1) {	
//                p1.addBaseShuiCount(-p2.getFirstTypeShui() * beishu, 1);	
//                p2.addBaseShuiCount(p2.getFirstTypeShui() * beishu, 1);	
//            } else if (s1 == -1) {	
//                p1.addBaseShuiCount(p1.getFirstTypeShui() * beishu, 1);	
//                p2.addBaseShuiCount(-p1.getFirstTypeShui() * beishu, 1);	
//            }	
//            // 第二墩	
//            int s2 = p1.second.compareTo(p2.second);	
//            if (s2 == 1) {	
//                p1.addBaseShuiCount(-p2.getSecondTypeShui() * beishu, 2);	
//                p2.addBaseShuiCount(p2.getSecondTypeShui() * beishu, 2);	
//            } else if (s2 == -1) {	
//                p1.addBaseShuiCount(p1.getSecondTypeShui() * beishu, 2);	
//                p2.addBaseShuiCount(-p1.getSecondTypeShui() * beishu, 2);	
//            }	
//            // 第三墩	
//            int s3 = p1.third.compareTo(p2.third);	
//            if (s3 == 1) {	
//                p1.addBaseShuiCount(-p2.getThirdTypeShui() * beishu, 3);	
//                p2.addBaseShuiCount(p2.getThirdTypeShui() * beishu, 3);	
//            } else if (s3 == -1) {	
//                p1.addBaseShuiCount(p1.getThirdTypeShui() * beishu, 3);	
//                p2.addBaseShuiCount(-p1.getThirdTypeShui() * beishu, 3);	
//            }	
//            // 打枪	
//            if (s1 == 1 && s2 == 1 && s3 == 1) {	
//                int total = (p2.getFirstTypeShui() + p2.getSecondTypeShui() + p2.getThirdTypeShui()) * beishu;	
//                sRankingResult.killRankins.add(new WXLSRanking(p2.pid, p2.posIdx, total, p1.pid, p1.posIdx));	
//            }	
//            if (s1 == -1 && s2 == -1 && s3 == -1) {	
//                int total = (p1.getFirstTypeShui() + p1.getSecondTypeShui() + p1.getThirdTypeShui()) * beishu;	
//                sRankingResult.killRankins.add(new WXLSRanking(p1.pid, p1.posIdx, total, p2.pid, p2.posIdx));	
//            }	
//        } else {	
//            if (p1.getSpecial() && p2.getSpecial()) {	
//                // 特殊牌玩家对比	
//                if (p1.getSpecialType() > p2.getSpecialType()) {	
//                    if (p1.getSpecialType() > p2.getSpecialType()) {	
//                        int tmp = p1.getSpecialType();	
//                        int shui = SpecialShui(tmp) * zjbs;	
//                        p1.addShuiCount(shui);	
//                        p2.addShuiCount(-shui);	
//                    } else if (p1.getSpecialType() < p2.getSpecialType()) {	
//                        int tmp = p2.getSpecialType();	
//                        int shui = SpecialShui(tmp) * zjbs;	
//                        p1.addShuiCount(-shui);	
//                        p2.addShuiCount(shui);	
//                    }	
//                } else if (p1.getSpecial() && !p2.getSpecial()) {	
//                    // 和普通玩家比	
//                    int tmp = p1.getSpecialType();	
//                    int shui = SpecialShui(tmp) * zjbs;	
//                    p1.addShuiCount(shui);	
//                    p2.addShuiCount(-shui);	
//                } else if (!p1.getSpecial() && p2.getSpecial()) {	
//                    // 和普通玩家比	
//                    int tmp = p2.getSpecialType();	
//                    int shui = SpecialShui(tmp) * zjbs;	
//                    p1.addShuiCount(-shui);	
//                    p2.addShuiCount(shui);	
//                }	
//            }	
//	
//        }	
//    }	
        /**	
         * 检查所有用户的牌	
         */	
        public boolean checkAllPlayerCard() {	
            Set<Long> set = PlayerList.keySet();	
            List<Long> list = new ArrayList<Long>(set);	
            if (list.size() <= 0)	
                return false;	
	
            for (int i = 0; i < list.size(); i++) {	
                WXLSPlayerData p1 = PlayerList.get(list.get(i));	
                if (null == p1) {	
                    return false;	
                }	
                if (!p1.getSpecial()) {	
                    if (!p1.checkDun()) {	
                        return false;	
                    }	
                }	
            }	
            return true;	
        }	
	
    /**	
     * 设置神牌	
     */	
    private static List<WXLSPockerCard> godCard(int index) {	
        // List<WXLSPockerCard> cards1 = new ArrayList<WXLSPockerCard>();	
        // cards1.add(new WXLSPockerCard(CardSuitEnum.HEARTS,	
        // CardRankEnum.CARD_QUEUE));	
        // cards1.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_QUEUE));	
        // cards1.add(new WXLSPockerCard(CardSuitEnum.HEARTS,	
        // CardRankEnum.CARD_JACK));	
        //	
        // cards1.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_JACK));	
        // cards1.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_EIGHT));	
        // cards1.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_EIGHT));	
        // cards1.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_SEVEN));	
        // cards1.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_SEVEN));	
        //	
        // cards1.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_SEVEN));	
        // cards1.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_SIX));	
        // cards1.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_SIX));	
        // cards1.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_THREE));	
        // cards1.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_THREE));	
        //	
        // List<WXLSPockerCard> cards2 = new ArrayList<WXLSPockerCard>();	
        // cards2.add(new WXLSPockerCard(CardSuitEnum.HEARTS,	
        // CardRankEnum.CARD_FIVE));	
        // cards2.add(new WXLSPockerCard(CardSuitEnum.HEARTS,	
        // CardRankEnum.CARD_FOUR));	
        // cards2.add(new WXLSPockerCard(CardSuitEnum.HEARTS,	
        // CardRankEnum.CARD_TWO));	
        //	
        // cards2.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_ACE));	
        // cards2.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_NINE));	
        // cards2.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_KING));	
        // cards2.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_QUEUE));	
        // cards2.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_JACK));	
        //	
        // cards2.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_TEN));	
        // cards2.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_NINE));	
        // cards2.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_SIX));	
        // cards2.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_FOUR));	
        // cards2.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_TWO));	
        //	
        // List<WXLSPockerCard> cards3 = new ArrayList<WXLSPockerCard>();	
        // cards3.add(new WXLSPockerCard(CardSuitEnum.HEARTS,	
        // CardRankEnum.CARD_FIVE));	
        // cards3.add(new WXLSPockerCard(CardSuitEnum.HEARTS,	
        // CardRankEnum.CARD_FOUR));	
        // cards3.add(new WXLSPockerCard(CardSuitEnum.HEARTS,	
        // CardRankEnum.CARD_TWO));	
        //	
        // cards3.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_NINE));	
        // cards3.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_ACE));	
        // cards3.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_KING));	
        // cards3.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_TEN));	
        // cards3.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_JACK));	
        //	
        // cards3.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_TEN));	
        // cards3.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_NINE));	
        // cards3.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_SIX));	
        // cards3.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_FOUR));	
        // cards3.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_TWO));	
        //	
        // List<WXLSPockerCard> cards4 = new ArrayList<WXLSPockerCard>();	
        // cards4.add(new WXLSPockerCard(CardSuitEnum.HEARTS,	
        // CardRankEnum.CARD_FIVE));	
        // cards4.add(new WXLSPockerCard(CardSuitEnum.HEARTS,	
        // CardRankEnum.CARD_FOUR));	
        // cards4.add(new WXLSPockerCard(CardSuitEnum.HEARTS,	
        // CardRankEnum.CARD_TWO));	
        //	
        // cards4.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_NINE));	
        // cards4.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_ACE));	
        // cards4.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_KING));	
        // cards4.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_TEN));	
        // cards4.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_JACK));	
        //	
        // cards4.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_TEN));	
        // cards4.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_NINE));	
        // cards4.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_SIX));	
        // cards4.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_FOUR));	
        // cards4.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_TWO));	
        //	
        // List<WXLSPockerCard> cards5 = new ArrayList<WXLSPockerCard>();	
        // cards5.add(new WXLSPockerCard(CardSuitEnum.HEARTS,	
        // CardRankEnum.CARD_FIVE));	
        // cards5.add(new WXLSPockerCard(CardSuitEnum.HEARTS,	
        // CardRankEnum.CARD_FOUR));	
        // cards5.add(new WXLSPockerCard(CardSuitEnum.HEARTS,	
        // CardRankEnum.CARD_TWO));	
        //	
        // cards5.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_ACE));	
        // cards5.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_NINE));	
        // cards5.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_KING));	
        // cards5.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_TEN));	
        // cards5.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_JACK));	
        //	
        // cards5.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_TEN));	
        // cards5.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_NINE));	
        // cards5.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_SIX));	
        // cards5.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_FOUR));	
        // cards5.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_TWO));	
        //	
        // List<WXLSPockerCard> cards6 = new ArrayList<WXLSPockerCard>();	
        // cards6.add(new WXLSPockerCard(CardSuitEnum.HEARTS,	
        // CardRankEnum.CARD_FIVE));	
        // cards6.add(new WXLSPockerCard(CardSuitEnum.HEARTS,	
        // CardRankEnum.CARD_FOUR));	
        // cards6.add(new WXLSPockerCard(CardSuitEnum.HEARTS,	
        // CardRankEnum.CARD_TWO));	
        //	
        // cards6.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_NINE));	
        // cards6.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_ACE));	
        // cards6.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_KING));	
        // cards6.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_TEN));	
        // cards6.add(new WXLSPockerCard(CardSuitEnum.SPADES,	
        // CardRankEnum.CARD_JACK));	
        //	
        // cards6.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_TEN));	
        // cards6.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_NINE));	
        // cards6.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_SIX));	
        // cards6.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_FOUR));	
        // cards6.add(new WXLSPockerCard(CardSuitEnum.DIAMONDS,	
        // CardRankEnum.CARD_TWO));	
        //	
        // hMap.put(0, cards1);	
        // hMap.put(1, cards2);	
        // hMap.put(2, cards3);	
        // hMap.put(3, cards4);	
        // hMap.put(4, cards5);	
        // hMap.put(5, cards6);	
        // return hMap.get(index);	
	
        List<List<WXLSPockerCard>> list = loadCardsList("shenpai.txt");	
        return list.get(index);	
	
    }	
	
    private static List<List<WXLSPockerCard>> loadCardsList(String filePath) {	
        List<List<WXLSPockerCard>> ret = new ArrayList<List<WXLSPockerCard>>();	
        try {	
	
            String encoding = "GBK";	
            File file = new File(filePath);	
            if (file.isFile() && file.exists()) { // 判断文件是否存在	
                InputStreamReader read = new InputStreamReader(new FileInputStream(file), encoding);// 考虑到编码格式	
                BufferedReader bufferedReader = new BufferedReader(read);	
                String lineTxt = null;	
                // int num = 69001000;	
	
                while ((lineTxt = bufferedReader.readLine()) != null) {	
                    String[] aa = lineTxt.split(",");	
                    List<WXLSPockerCard> cardslst = new ArrayList<WXLSPockerCard>();	
                    for (int i = 0; i < aa.length; i++) {	
                        cardslst.add(new WXLSPockerCard(aa[i]));	
                    }	
                    ret.add(cardslst);	
                }	
                read.close();	
                bufferedReader.close();	
                file.exists();	
            } else {	
                System.out.println("找不到神牌配置文件");	
            }	
        } catch (Exception e) {	
            System.out.println("神牌配置文件读取出错");	
            e.printStackTrace();	
        }	
        return ret;	
    }	
	
    public static void main(String[] args) {	
    }	
}	
