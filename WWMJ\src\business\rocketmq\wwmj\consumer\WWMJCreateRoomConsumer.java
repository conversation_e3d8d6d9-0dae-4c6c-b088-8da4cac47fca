package business.rocketmq.wwmj.consumer;														
														
import BaseCommon.CommLog;														
import business.global.sharegm.ShareNodeServerMgr;														
import business.wwmj.c2s.iclass.CWWMJ_CreateRoom;														
import business.rocketmq.bo.MqAbsRequestBo;														
import business.rocketmq.constant.MqTopic;														
import business.rocketmq.consumer.BaseCreateRoomConsumer;														
import cenum.PrizeType;														
import com.ddm.server.annotation.Consumer;														
import com.ddm.server.common.rocketmq.MqConsumerHandler;														
import com.google.gson.Gson;														
import core.server.wwmj.WWMJAPP;														
import jsproto.c2s.cclass.room.BaseRoomConfigure;														
														
/**														
 * <AUTHOR> xush<PERSON><PERSON>														
 * create at:  2020-08-19  11:17														
 * @description: 模版麻将创建房间														
 */														
@Consumer(topic = MqTopic.BASE_CREATE_ROOM, id = WWMJAPP.gameTypeId)														
public class WWMJCreateRoomConsumer extends BaseCreateRoomConsumer implements MqConsumerHandler {														
														
														
    @Override														
    public void action(Object body) throws ClassNotFoundException {														
        MqAbsRequestBo mqAbsRequestBo = (MqAbsRequestBo) body;														
        //判断游戏和请求创建节点一致														
        if (mqAbsRequestBo.getGameTypeId() == WWMJAPP.GameType().getId() && ShareNodeServerMgr.getInstance().checkCurrentNode(mqAbsRequestBo.getShareNode().getIp(), mqAbsRequestBo.getShareNode().getPort())) {														
//            CommLog.info("创建房间[{}]", mqAbsRequestBo.getGameTypeName());														
            final CWWMJ_CreateRoom clientPack = new Gson().fromJson(mqAbsRequestBo.getBody(),														
                    CWWMJ_CreateRoom.class);														
            // 公共房间配置														
            BaseRoomConfigure<CWWMJ_CreateRoom> configure = new BaseRoomConfigure<>(														
                    PrizeType.RoomCard,														
                    WWMJAPP.GameType(),														
                    clientPack.clone());														
            super.action(body, WWMJAPP.GameType().getId(), configure);														
        }														
    }														
}														
