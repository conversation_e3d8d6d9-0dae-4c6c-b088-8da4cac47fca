package business.ahhbmj.c2s.iclass;						
						
import jsproto.c2s.cclass.BaseSendMsg;						
						
/**						
 * 扣或者碰到声音						
 */						
public class SAHHBMJ_OpSound extends BaseSendMsg {						
    //房间id												
    public long roomID;						
    //操作位置												
    public int pos;						
    //类型												
    public int type;						
    //扣牌												
    public int kouCard;						
						
    public static SAHHBMJ_OpSound make(long roomID, int pos, int type, int kouCard) {						
        SAHHBMJ_OpSound ret = new SAHHBMJ_OpSound();						
        ret.roomID = roomID;						
        ret.pos = pos;						
        ret.type = type;						
        ret.kouCard = kouCard;						
        return ret;						
    }						
}												
