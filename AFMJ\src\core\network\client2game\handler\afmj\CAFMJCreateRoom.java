package core.network.client2game.handler.afmj;

import java.io.IOException;

import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.google.gson.Gson;

import business.player.Player;
import business.player.feature.PlayerRoom;
import business.afmj.c2s.iclass.CAFMJ_CreateRoom;
import cenum.PrizeType;
import core.network.client2game.handler.PlayerHandler;
import core.network.http.proto.SData_Result;
import core.server.afmj.AFMJAPP;
import jsproto.c2s.cclass.room.BaseRoomConfigure;

/**
 * 创建房间
 * 
 * <AUTHOR>
 *
 */
public class CAFMJCreateRoom extends PlayerHandler {

	@SuppressWarnings("rawtypes")
	@Override
	public void handle(Player player, WebSocketRequest request, String message)
			throws IOException {

		final CAFMJ_CreateRoom clientPack = new Gson().fromJson(message,
				CAFMJ_CreateRoom.class);
		// 公共房间配置
		BaseRoomConfigure<CAFMJ_CreateRoom> configure = new BaseRoomConfigure<CAFMJ_CreateRoom>(
				PrizeType.RoomCard,
				AFMJAPP.GameType(),
				clientPack.clone());
		SData_Result resule = player.getFeature(PlayerRoom.class).createRoomAndConsumeCard(configure);
		if (ErrorCode.Success.equals(resule.getCode())) {
			request.response(resule.getData());
		} else {
			request.error(resule.getCode(),resule.getMsg());
		}
	}
}
