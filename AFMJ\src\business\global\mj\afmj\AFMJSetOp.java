package business.global.mj.afmj;

import java.util.ArrayList;
import java.util.List;

import business.global.mj.*;
import business.global.mj.hu.QiangGangHuCardImpl;
import business.global.mj.afmj.hutype.*;
import business.global.mj.afmj.AFMJRoomEnum.*;
import business.global.mj.afmj.optype.*;
import business.global.mj.manage.MJFactory;
import cenum.mj.*;
import com.ddm.server.common.CommLogD;


public class AFMJSetOp extends AbsMJSetOp {


	// 操作
	private final List<OpType> opTypes = new ArrayList<>();
	// 玩家信息
	private final AFMJSetPos mSetPos;


	public AFMJSetOp(AFMJSetPos mSetPos) {
		super();
		this.mSetPos = mSetPos;
	}

	@Override
	public boolean doOpType(int cardID, OpType opType) {
		// 冲一包三
		switch (opType) {
			case Chi:
			case Peng:
			case JieGang:
			case JiePao:
			case Hu:
				if (cardID > 0) {
					if (mSetPos.getSet().getmJinCardInfo().checkJinExist(cardID / 100)) {
						mSetPos.getBaoChongJingCardList().add(cardID);
					}
				}
		}
		boolean doOpType = false;
		switch (opType) {
			case Chi:
//			doOpType = MJFactory.getOpCard(ChiCardNormalImpl.class).doOpCard(mSetPos, cardID);
				doOpType = MJFactory.getOpCard(AFMJChiCardNormalImpl.class).doOpCard(mSetPos, cardID);
				// this.addOp(mSetPos, doOpType, OpType.Chi);
				break;
			case Peng:
//			doOpType = MJFactory.getOpCard(PengCardImpl.class).doOpCard(mSetPos, cardID);
				doOpType = MJFactory.getOpCard(AFMJPengCardImpl.class).doOpCard(mSetPos, cardID);
				break;
			case JieGang:
//			doOpType = MJFactory.getOpCard(JieGangCardImpl.class).doOpCard(mSetPos, cardID);
				doOpType = MJFactory.getOpCard(AFMJJieGangCardImpl.class).doOpCard(mSetPos, cardID);
				this.addOp(mSetPos, doOpType, OpType.JieGang);
				break;
			case Gang:
//			doOpType = MJFactory.getOpCard(GangCardImpl.class).doOpCard(mSetPos, cardID);
				doOpType = MJFactory.getOpCard(AFMJGangCardImpl.class).doOpCard(mSetPos, cardID);
				this.addOp(mSetPos, doOpType, OpType.Gang);
				break;
			case AnGang:
//			doOpType = MJFactory.getOpCard(AnGangCardImpl.class).doOpCard(mSetPos, cardID);
				doOpType = MJFactory.getOpCard(AFMJAnGangCardImpl.class).doOpCard(mSetPos, cardID);
				this.addOp(mSetPos, doOpType, OpType.AnGang);
				break;
			case JiePao:
			case Hu:
			case TianHu:
				doOpType = doPingHu(mSetPos);
				break;
			case QiangGangHu:
				doOpType = MJFactory.getHuCard(QiangGangHuCardImpl.class).checkHuCard(mSetPos.getMJSetPos());
				if (doOpType)
					mSetPos.setmHuOpType(MJHuOpType.QGHu);
				break;
			default:
				break;
		}

		return doOpType;
	}

	@Override
	public boolean checkOpType(int cardID, OpType opType) {
		int cardType = cardID / 100;
		boolean isOpType = false;
		switch (opType) {
			case Chi:
//			isOpType = MJFactory.getOpCard(ChiCardNormalImpl.class).checkOpCard(mSetPos, cardID);
				isOpType = MJFactory.getOpCard(AFMJChiCardNormalImpl.class).checkOpCard(mSetPos, cardID);
				break;
			case Peng:
//			isOpType = MJFactory.getOpCard(PengCardImpl.class).checkOpCard(mSetPos, cardID);
				isOpType = MJFactory.getOpCard(AFMJPengCardImpl.class).checkOpCard(mSetPos, cardID);
				break;
			case JieGang:
				if(((AFMJRoomSet)mSetPos.getSet()).isLastCard())return false; // 牌堆没有可摸的牌时，不可以开杠；
//			isOpType = MJFactory.getOpCard(JieGangCardImpl.class).checkOpCard(mSetPos, cardID);
				isOpType = MJFactory.getOpCard(AFMJJieGangCardImpl.class).checkOpCard(mSetPos, cardID);
				break;
			case Gang:
				if(((AFMJRoomSet)mSetPos.getSet()).isLastCard())return false; // 牌堆没有可摸的牌时，不可以开杠；
//			isOpType = MJFactory.getOpCard(GangCardImpl.class).checkOpCard(mSetPos, cardID);
				isOpType = MJFactory.getOpCard(AFMJGangCardImpl.class).checkOpCard(mSetPos, cardID);
				break;
			case AnGang:
				if(((AFMJRoomSet)mSetPos.getSet()).isLastCard())return false; // 牌堆没有可摸的牌时，不可以开杠；
//			isOpType = MJFactory.getOpCard(AnGangCardImpl.class).checkOpCard(mSetPos, MJSpecialEnum.NOT_HUA.value());
				isOpType = MJFactory.getOpCard(AFMJAnGangCardImpl.class).checkOpCard(mSetPos, MJSpecialEnum.NOT_HUA.value());
				break;
			case Hu:
			case JiePao:
			case TianHu:
			case QiangGangHu:

				// 冰冻功能：勾选后，玩家手牌（碰杠的精不计）里有精只能自摸；未勾选，无该限制。
				if(mSetPos.getRoom().RoomCfg(AFMJKeXuanWanFa.BingDongGongNeng)) {
					if(mSetPos.existJing() && cardType > 0)return false;
				}

				((AFMJRoomSet)mSetPos.getSet()).setDaHu(false); // 设置不是大胡

				// 检测七对
				// 七小对：七个对子；（可以有四张一样的牌）
				if(MJFactory.getHuCard(AFMJDDHuImpl.class).checkHuCard(mSetPos, mCardInitQGH(mSetPos.mCardInit(cardType, true), opType, cardType))){
					mSetPos.getPosOpRecord().addOpHuList(AFMJOpPoint.QiDui);
				}

				// 检测对对胡
				// 大七对（即碰碰胡）：胡牌的时候手中全是碰牌刻子或杠，加一组对子；
				if(MJFactory.getHuCard(AFMJPPImpl.class).checkHuCard(mSetPos, mCardInitQGH(mSetPos.mCardInit(cardType, true), opType, cardType))){
					mSetPos.getPosOpRecord().addOpHuList(AFMJOpPoint.PPHu);
				}

				// 检测十三不靠

				// 七星十三烂：十三烂的基础上，且有七个字都有；
				// 不叠加十三烂算分；
				if(!AFMJOpPoint.Not.equals
						(MJFactory.getHuCard(AFMJSSBKQingImpl.class)
								.checkHuCardReturn(mSetPos, mCardInitQGH(mSetPos.mCardInit(cardType, true), opType, cardType)))
				){
					mSetPos.getPosOpRecord().addOpHuList(AFMJOpPoint.QiXingShiSanLan);
				}else if(!AFMJOpPoint.Not.equals
						(MJFactory.getHuCard(AFMJSSBKImpl.class)
								.checkHuCardReturn(mSetPos, mCardInitQGH(mSetPos.mCardInit(cardType, true), opType, cardType)))
				){
					// 十三烂：十四张牌满足同色序数牌点数相减＞2且不重复，字牌也没有重复的；
					// 字牌至少5张；
					// 万条筒三花色都要有；例：147万、147条、258筒
					mSetPos.getPosOpRecord().addOpHuList(AFMJOpPoint.ShiSanLan);
				}

				// 检测德国
				// 德国：胡牌的时候手中没有精或精都是当本身用；
				if(!AFMJOpPoint.Not.equals
						(MJFactory.getHuCard(AFMJDeGuoImpl.class)
								.checkHuCardReturn(mSetPos, mCardInitQGH(mSetPos.mCardInit(cardType, true), opType, cardType),handCard(mSetPos,cardType)))
				){
					if(AFMJOpPoint.PiHu.equals
							(MJFactory.getHuCard(AFMJDeGuoImpl.class)
									.checkHuCardReturn(mSetPos, mCardInitQGH(mSetPos.mCardInit(cardType, true), opType, cardType),handCard(mSetPos,cardType)))
							||
							AFMJOpPoint.NotJin.equals
									(MJFactory.getHuCard(AFMJDeGuoImpl.class)
											.checkHuCardReturn(mSetPos, mCardInitQGH(mSetPos.mCardInit(cardType, true), opType, cardType),handCard(mSetPos,cardType)))
					){
						mSetPos.getPosOpRecord().addOpHuList(AFMJOpPoint.DeGuo);
					}

				}

				// 精吊：听任意牌的胡；
				// 将牌中至少有一张精，或者七对中有一组对子里至少有一张精；
				// 必须自摸；
				if(mSetPos.getHuCardTypes().size() >= 34){
					if (cardType > 0) { // 点炮
					}else { // 自摸
						mSetPos.getPosOpRecord().addOpHuList(AFMJOpPoint.JingDiao);
					}
				}

				// 检测大胡
				if(mSetPos.getPosOpRecord().getOpHuList().size() > 0){
					((AFMJRoomSet)mSetPos.getSet()).setDaHu(true);
				}

				// 检测胡
				if(MJFactory.getHuCard(AFMJHuImpl.class).checkHuCard(mSetPos, mCardInitQGH(mSetPos.mCardInit(cardType, true), opType, cardType))){
					mSetPos.getPosOpRecord().addOpHuList(AFMJOpPoint.Hu);
				}

				// “必博一精”或“必博正精或两副”玩法下，如果玩家手牌没有精，且没有满足博精条件，则不能炮胡、抢杠胡，只能自摸。（随精玩法不用限制）
				if(((AFMJRoom)mSetPos.getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoYiJing)){
					// “必博一精”：该玩法同上，但是手里有精牌时，必须打出去一张精（正副都行），才能胡牌；
					// 手里有精：吃碰杠的精不算手里的精；
					if((cardID > 0 || opType.equals(OpType.QiangGangHu))){
						// 炮胡、抢杠胡
						if(!mSetPos.existBo1Jin()){
							// 没有博一精
							return false;
						}
					}else {
						// 自摸
						if(mSetPos.existJingByAllCards() && !mSetPos.existBo1Jin()){
							// 手牌有精牌且没有博一精
							return false;
						}
					}
				}else if(((AFMJRoom)mSetPos.getRoom()).RoomCfgPiaoJing(AFMJPiaoJing.BiBoZhengJingHuoLiangFu)) {
					// “必博正精或两副”：该玩法同博精，但是手里有精时，必须打出去一张正精或两张副精，才能胡牌；
					// 手里有精：吃碰杠的精不算手里的精；
					if((cardID > 0 || opType.equals(OpType.QiangGangHu))){
						// 炮胡、抢杠胡
						if(!mSetPos.existBo1ZhengJinOrBo2FuJing()){
							// 没有博一正精且没有博两副精
							return false;
						}
					}else {
						// 自摸
						if(mSetPos.existJingByAllCards() && !mSetPos.existBo1ZhengJinOrBo2FuJing()){
							// 手牌有精牌且没有博一正精且没有博两副精
							return false;
						}
					}
				}

				return mSetPos.getPosOpRecord().getOpHuList().size() > 0;

			case Ting:
				isOpType = MJFactory.getTingCard(AFMJTingImpl.class).checkTingList(mSetPos);
				break;
			default:
				break;
		}
		return isOpType;
	}

	public boolean doPingHu(AbsMJSetPos mSetPos) {
		if (MJHuOpType.JiePao.equals(mSetPos.getmHuOpType())) {
			int lastOutCard = mSetPos.getSet().getLastOpInfo().getLastOutCard();
			if (lastOutCard > 0) {
				mSetPos.setHandCard(new MJCard(lastOutCard));
				// 更新接炮的牌的ID
				((AFMJRoomSet)mSetPos.getSet()).setJiePaoCardId(lastOutCard);
			}
		}
		return true;
	}

	/**
	 * 清除所有动作
	 */
	public void cleanOp() {
		this.opTypes.clear();
	}

	/**
	 * 添加动作
	 *
	 * @param doOpType 是否操作成功
	 * @param opType   动作类型
	 */
	public void addOp(AbsMJSetPos mSetPos, boolean doOpType, OpType opType) {
		if (doOpType) {
			// 检查不是补花操作,刷新听牌
			if (!OpType.BuHua.equals(opType)) {
				mSetPos.calcHuFan();
			}
			this.opTypes.add(opType);
		}
	}

	/**
	 * 检查动作
	 */
	public boolean isOpContains(OpType opType) {
		return this.opTypes.contains(opType);
	}

	/**
	 * 检查动作
	 */
	public int isOpSize() {
		return this.opTypes.size();
	}

	@Override
	public void clear() {
	}

	/**
	 * 清除所有动作
	 */
	public void clearOp() {
		this.opTypes.clear();
	}

	/**
	 * 首牌
	 *
	 * @param mSetPos 玩家信息位置操作
	 * @param cardType 牌型
	 * @return 首牌牌型
	 */
	public int handCard (MJSetPos mSetPos, int cardType) {
		//检查牌类型 > 0
		if (cardType > 0) { // 点炮
			return cardType;
		}  else { // 自摸
			// 获取首牌
			return mSetPos.getHandCard().type;
		}
	}

	/**
	 * 检测抢杠胡的麻将牌的初始信息
	 * 操作：正副精都可以打出，可以当本身进行吃、碰、杠，且精当本身用的时候，可以胡别人打出的精；
	 *
	 * @param mjCardInit 麻将牌的初始信息
	 * @param opType 操作类型
	 * @param cardType 抢杠胡抢的杠牌牌型
	 * @return 抢杠胡的麻将牌的初始信息
	 */
	public MJCardInit mCardInitQGH(MJCardInit mjCardInit, OpType opType, int cardType){
		// 不是抢杠胡
		if(!opType.equals(OpType.QiangGangHu))return mjCardInit;
		// 抢杠胡抢的杠牌牌型不是金
		if(!mSetPos.getSet().getmJinCardInfo().checkJinExist(cardType))return mjCardInit;
		// 金牌做自己
		if(mjCardInit.getJins().contains(cardType)){
			mjCardInit.getJins().remove(0);
		}
		mjCardInit.addCardInts(cardType);
		return mjCardInit;
	}

}
