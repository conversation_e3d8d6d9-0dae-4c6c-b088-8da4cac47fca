package business.a3pk.c2s.iclass;	
	
import jsproto.c2s.cclass.BaseSendMsg;	
	
@SuppressWarnings("serial")	
public class SA3PK_SendGift extends BaseSendMsg {	
	public long roomID;	
	public int  sendPos; //发送者	
	public int 	recivePos; //接受者	
	public long productId;	
	
    public static SA3PK_SendGift make(long roomID, int sendPos, int recivePos, long productId) {	
    	SA3PK_SendGift ret = new SA3PK_SendGift();	
        ret.roomID = roomID;	
        ret.sendPos = sendPos;	
        ret.recivePos = recivePos;	
        ret.productId = productId;	
        return ret;	
    }	
}	
