package core.network.client2game.handler.wwmj;											
											
import java.io.IOException;											
											
import com.ddm.server.websocket.handler.requset.WebSocketRequest;											
import com.google.gson.Gson;											
											
import business.wwmj.c2s.iclass.CWWMJ_CreateRoom;											
import business.player.Player;											
import business.player.feature.PlayerClubRoom;											
import cenum.PrizeType;											
import core.network.client2game.handler.PlayerHandler;											
import core.server.wwmj.WWMJAPP;											
import jsproto.c2s.cclass.room.BaseRoomConfigure;											
											
/**											
 * 亲友圈房间											
 * 											
 * <AUTHOR>											
 *											
 */											
public class CWWMJClubRoom extends PlayerHandler {											
											
	@Override											
	public void handle(Player player, WebSocketRequest request, String message)											
			throws IOException {											
											
		final CWWMJ_CreateRoom clientPack = new Gson().fromJson(message,											
				CWWMJ_CreateRoom.class);													
											
		// 公共房间配置											
		BaseRoomConfigure<CWWMJ_CreateRoom> configure = new BaseRoomConfigure<CWWMJ_CreateRoom>(											
				PrizeType.RoomCard,											
				WWMJAPP.GameType(),											
				clientPack.clone());											
		player.getFeature(PlayerClubRoom.class).createNoneClubRoom(request,configure);											
	}											
}											
