#手牌
handleCard=[12,15,16,24];
#去掉的牌
deleteCard={[0x0E, 0x0F, 0x1F, 0x2F],,[0x0E, 0x0F, 0x1F, 0x2F, 0x1E, 0x2E, 0x0D],,[0x0E, 0x0F, 0x1F, 0x2F],,[0x0E, 0x0F, 0x1F, 0x2F]};
#结算时索引值对应的结算方式
#记牌分
jiPaiFen = 0;
#牌多通输   
paiDuoTongShu = 1; 
#固定分
guDingFen = 2;	
#机器人明牌概率
aiOpenCard = 5
#机器人翻倍概率
aiAddDouble = 10
#加倍倍数
addDoubleList = [1,2,5];
#抢关门分数2*16
robClosePointByNotGuDingFen = 32;
#抢关门分数2 固定分
robClosePointByGuDingFen = 2;
#关门*2倍
robCloseAddDouble = 2;
#房间加倍限制
maxRoomAddDouble = 3;
#上庄分数
backerPointList = [0, 100, 150, 200];
#翻倍上限
maxAddDoubleList = [0, 5, 10, 20];