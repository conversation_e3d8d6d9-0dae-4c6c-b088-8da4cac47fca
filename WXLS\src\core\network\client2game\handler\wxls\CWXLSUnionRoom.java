package core.network.client2game.handler.wxls;	
	
import business.player.Player;	
import business.player.feature.PlayerUnionRoom;	
import business.wxls.c2s.iclass.CWXLS_CreateRoom;	
import cenum.PrizeType;	
import com.ddm.server.websocket.handler.requset.WebSocketRequest;	
import com.google.gson.Gson;	
import core.network.client2game.handler.PlayerHandler;	
import core.server.wxls.WXLSAPP;	
import jsproto.c2s.cclass.room.BaseRoomConfigure;	
	
import java.io.IOException;	
	
public class CWXLSUnionRoom extends PlayerHandler {	
	
    @Override	
    public void handle(Player player, WebSocketRequest request, String message)	
            throws IOException {	
	
        final CWXLS_CreateRoom clientPack = new Gson().fromJson(message,	
                CWXLS_CreateRoom.class);	
	
        // 公共房间配置	
        BaseRoomConfigure<CWXLS_CreateRoom> configure = new BaseRoomConfigure<CWXLS_CreateRoom>(	
                PrizeType.RoomCard,	
                WXLSAPP.GameType(),	
                clientPack.clone());	
        player.getFeature(PlayerUnionRoom.class).createNoneUnionRoom(request,configure);	
    }	
}	
	
