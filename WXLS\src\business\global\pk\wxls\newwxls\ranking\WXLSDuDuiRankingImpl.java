package business.global.pk.wxls.newwxls.ranking;

import business.global.pk.wxls.newwxls.WXLSPlayerDun;
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;

import java.util.List;
import java.util.Map;

/**
 * 独对
 */
public class WXLSDuDuiRankingImpl extends WXLSAbstractRanking {
    @Override
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {
        WXLSRankingResult result = null;
        if (player.getCardSize() == 13) {
            List<WXLSPockerCard> cards = player.getCards();
            Map<Integer, Integer> rankCount = player.getCardsRankCountMap();
            if (rankCount.size()  == 12) {
                result = new WXLSRankingResult();
                result.setPockerCards(cards);
                result.setRankingEnum(WXLSRankingEnum.DuDui);
            }
        }
        return result;
    }
}	
