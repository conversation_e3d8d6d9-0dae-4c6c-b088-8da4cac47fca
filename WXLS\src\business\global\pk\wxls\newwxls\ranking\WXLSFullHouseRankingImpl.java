package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.Iterator;	
import java.util.Map;	
	
/**	
 * 解析玩家手中的牌是不是葫芦	
 */	
public class WXLSFullHouseRankingImpl extends WXLSAbstractRanking {	
	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        WXLSRankingResult result = null;	
	
        boolean isFullHouse = false;	
        if (player.getCardSize() == 5) {	
            Map<Integer, Integer> rankCount = player.getCardsRankCountMap();	
            if (rankCount.size() == 2) {	
                Iterator<Map.Entry<Integer, Integer>> it = rankCount.entrySet().iterator();	
                while (it.hasNext()) {	
                    Map.Entry<Integer, Integer> next = it.next();	
                    if (next.getValue() == 2 || next.getValue() == 3) {	
                        isFullHouse = true;	
                        break;	
                    }	
                }	
            }	
        }	
        if (isFullHouse) {	
            result = new WXLSRankingResult();	
            result.setRankingEnum(WXLSRankingEnum.FULL_HOUSE);	
        }	
	
        return result;	
    }	
	
}	
