package business.wxls.c2s.cclass;	
	
import cenum.room.SetState;	
	
import java.util.ArrayList;	
import java.util.List;	
	
/**	
 * 十三水 当前局游戏信息	
 * <AUTHOR>	
 *	
 */	
public class WXLSRoom_Set {	
		
	public long roomID = 0; // 房间ID	
	public int setID = 0; // 游戏局ID	
	public SetState state = SetState.Init; // 游戏状态	
	public long setStartTime = 0;	
	public long setCurrentTime = 0;	
	public String mapai = "";	
	public long backerPos =0;	
	public boolean isXiPai = false;	
	public boolean isPlaying = false;	
	public List<WXLSRoomSet_Pos> posInfo = new ArrayList<WXLSRoomSet_Pos>(); // 一局玩家列表	
	public List<WXLSRoomSet_End> posEndInfo = new ArrayList<>(); // 一局玩家列表	
	public List<WXLSSet_Pos> setPosList = new ArrayList<>();	
	public WXLSRoomSet_End setEnd = new WXLSRoomSet_End();	
		
	
		
}	
