package business.wxls.c2s.cclass;	
	
	
	
	
import business.wxls.c2s.cclass.entity.WXLSPlayerResult;	
import business.wxls.c2s.iclass.CWXLS_Ranked;	
	
import java.util.List;	
	
/**	
 *	
 * <AUTHOR>	
 *	
 */	
public class WXLSSetEndResult {	
	private List<CWXLS_Ranked> rankeds;	
	private List<WXLSPlayerResult> posResultList;	
	private long zjid = -1;	
	private int beishu = 1;
	public String publicCard;

	
	public WXLSSetEndResult(List<CWXLS_Ranked> rankeds, List<WXLSPlayerResult> WXLSPlayerResults, long zjid, int beishu,String publicCard) {
		super();	
		this.rankeds = rankeds;	
		this.posResultList = WXLSPlayerResults;	
		this.zjid = zjid;	
		this.beishu = beishu;
		this.publicCard = publicCard;
	}	
	
	public WXLSSetEndResult(List<CWXLS_Ranked> rankeds, List<WXLSPlayerResult> WXLSPlayerResults,String publicCard) {
		super();	
		this.rankeds = rankeds;	
		this.posResultList = WXLSPlayerResults;
		this.publicCard = publicCard;
	}	
	public List<CWXLS_Ranked> getRankeds() {	
		return rankeds;	
	}	
	
	public void setRankeds(List<CWXLS_Ranked> rankeds) {	
		this.rankeds = rankeds;	
	}	
	
	public List<WXLSPlayerResult> getWXLSPlayerResults() {	
		return posResultList;	
	}	
	
	public void setWXLSPlayerResults(List<WXLSPlayerResult> WXLSPlayerResults) {	
		this.posResultList = WXLSPlayerResults;	
	}	
	
}	
