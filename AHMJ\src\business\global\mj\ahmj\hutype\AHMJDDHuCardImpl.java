package business.global.mj.ahmj.hutype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCardInit;
import business.global.mj.hu.BaseHuCard;
import business.global.mj.ahmj.AHMJRoomEnum.AHMJOpPoint;
import business.global.mj.util.HuDuiUtil;
import business.ahmj.c2s.cclass.AHMJPointItem;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * 小七对：七个对子；（可以有四张一样的牌）
 *
 * <AUTHOR>
 */
public class AHMJDDHuCardImpl extends BaseHuCard {
    @Override
    public boolean checkHuCard(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
        if (null == mCardInit) {
            return false;
        }
        //检查是否有碰杠吃
        if (mSetPos.sizePublicCardList() > 0) {
            return false;
        }
        return HuDuiUtil.getInstance().checkDuiHu(mCardInit.getAllCardInts(), mCardInit.sizeJin());

    }

    @Override
    public <T> Object checkHuCardReturn(AbsMJSetPos mSetPos, MJCardInit mCardInit, int cardId) {
        AHMJPointItem item;
        if (null == mCardInit) {
            return null;
        }
        //检查是否有碰杠吃
        if (mSetPos.sizePublicCardList() > 0) {
            return null;
        }
        if (!HuDuiUtil.getInstance().checkDuiHu(mCardInit.getAllCardInts(), mCardInit.sizeJin())) {
            return null;
        }
        // 分组统计手上的相同类型的牌
        Map<Integer, Long> groupingByMap = mCardInit.getAllCardInts().stream()
                .collect(Collectors.groupingBy(p -> p, Collectors.counting()));
        if (null == groupingByMap || groupingByMap.size() <= 0) {
            return null;
        }
        item = new AHMJPointItem();
        item.addAHMJOpPoint(AHMJOpPoint.QDHu, AHMJOpPoint.QDHu.value());
        return item;

    }

    @Override
    public <T> Object checkHuCardReturn(AbsMJSetPos mSetPos, MJCardInit mCardInit) {
        return this.checkHuCardReturn(mSetPos, mCardInit, 0);


    }
}		
