package business.global.pk.wxls.newwxls.ranking;	
	
import business.global.pk.wxls.newwxls.WXLSPlayerDun;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
import business.wxls.c2s.cclass.newwxls.WXLSRankingEnum;	
	
import java.util.List;	
	
/**	
 *全小	
 */	
public class WXLSQXiaoRankingImpl_T extends WXLSAbstractRanking {	
    @Override	
    protected WXLSRankingResult doResolve(WXLSPlayerDun player) {	
        WXLSRankingResult result = null;	
        if (player.getCardSize() == 13&&player.getPlayerNum()!=4) {
            List<WXLSPockerCard> cards = player.getCards();	
            boolean flag = true;	
            for (int i = 0; i < cards.size(); i++) {	
                if (cards.get(i).getRank().getNumber() > 10) {	
                    flag = false;	
                }	
            }	
            if (flag) {	
                result = new WXLSRankingResult();	
                result.setPockerCards(player.getCards());	
                result.setRankingEnum(WXLSRankingEnum.QXiao);	
                return result;	
            }	
        }	
        return result;	
    }	
}	
