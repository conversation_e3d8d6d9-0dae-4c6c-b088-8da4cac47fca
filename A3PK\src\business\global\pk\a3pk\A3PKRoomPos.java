package business.global.pk.a3pk;	
	
import business.global.room.base.AbsBaseRoom;	
import business.global.room.base.AbsRoomPos;	
import lombok.Data;	
	
@Data	
public class A3PKRoomPos extends AbsRoomPos {	
    /**	
     * 余干510K房间	
     */	
    private A3PKRoom a3pkRoom;	
    /**	
     * 构造函数	
     *	
     * @param posID 位置	
     * @param room  房间信息	
     */	
    public A3PKRoomPos(int posID, AbsBaseRoom room) {	
        super(posID, room);	
        this.a3pkRoom = (A3PKRoom) room;	
        this.setPlayTheGame(true);	
    }	
}	
