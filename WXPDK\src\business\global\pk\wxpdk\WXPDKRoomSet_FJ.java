package business.global.pk.wxpdk;

import business.wxpdk.c2s.cclass.WXPDK_define;
import business.wxpdk.c2s.cclass.WXPDK_define.WXPDK_WANFA;
import jsproto.c2s.cclass.pk.Victory;

import java.util.ArrayList;
import java.util.List;

/**
 * 跑得快一局游戏逻辑
 * <AUTHOR>
 */

public  class WXPDKRoomSet_FJ extends WXPDKRoomSet{

	public ArrayList<Victory> roomDouble ;		//房间倍数

	@SuppressWarnings("rawtypes")
	public WXPDKRoomSet_FJ( WXPDKRoom room) {
		super(room);
		this.roomDouble 				= new ArrayList<Victory>();
	}

	/**
	 * @return m_RoomDouble
	 */
	@Override
	public int getRoomDouble(int pos) {
		return Math.max(1,  this.getNumByList(this.roomDouble, pos));
	}


	/**
	 */
	@Override
	public void addRoomDouble(int pos, int roomAddDouble) {
		if (WXPDK_define.BombAlgorithm.PASS.has(this.room.getRoomCfg().zhadansuanfa)) {
			return;
		}
		if(this.room.isWanFaByType(WXPDK_WANFA.WXPDK_WANFA_MAXZHADAN)  ){
			if (this.m_AddRoomDoubleCount >= this.room.getConfigMgr().getMaxRoomAddDouble()) {
				return;
			} else {
				this.m_AddRoomDoubleCount++;
			}
		}
		this.addNumByList(this.roomDouble, pos, roomAddDouble);
	}

	@Override
	public void addNumByList(ArrayList<Victory> list, int pos, int num) {
		boolean flag = false;
		for (Victory victory : list) {
			if (victory == null) {
                return;
            }
			if (victory.getPos() == pos) {
				int count = 0;
				if(num != 0) {
					count = victory.getNum() != 0 ? victory.getNum() : 1;
				}
				num = num != 0 ? num : 1;
				victory.setNum(num + count);
				flag = true;
			}
		}
		if (!flag) {
			list.add(new Victory(pos, num));
		}
	}

	@Override
	protected List<Victory> getRoomDoubleList() {
		return roomDouble;
	}
}
