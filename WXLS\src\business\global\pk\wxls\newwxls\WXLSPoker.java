package business.global.pk.wxls.newwxls;	
	
import business.wxls.c2s.cclass.newwxls.WXLSCardRankEnum;	
import business.wxls.c2s.cclass.newwxls.WXLSCardSuitEnum;	
import business.wxls.c2s.cclass.newwxls.WXLSPockerCard;	
	
import java.util.ArrayList;	
import java.util.Collections;	
import java.util.List;	
import java.util.Random;	
	
public class WXLSPoker {	
	
    private List<WXLSPockerCard> cards;	
    private Random random;	
	
    public void clean () {	
        if (null != this.cards) {	
            this.cards.clear();	
            this.cards = null;	
        }	
        this.random = null;	
    }	
	
	
    public WXLSPoker(int count,boolean jiaYiSe) {
        this.random = new Random();	
        this.init(count,jiaYiSe);
    }	
	
    private void init(int count,boolean jiaYiSe) {
        this.cards = new ArrayList<WXLSPockerCard>();	
        for (WXLSCardSuitEnum suitEnum : WXLSCardSuitEnum.values()) {	
                addYSCards(suitEnum, "");	
        }	
	    if(jiaYiSe){
            WXLSCardSuitEnum suitEnum=WXLSCardSuitEnum.SPADES;
            for (WXLSCardRankEnum rankEnum : WXLSCardRankEnum.values()) {
                if (rankEnum.getNumber() < 15) {
                    cards.add(new WXLSPockerCard(suitEnum, rankEnum,true));
                }
            }
        }
        Collections.shuffle(this.cards);
        for (int i = 0; i<count;i++) {	
            Collections.shuffle(this.cards);	
        }	
    }	
	
    private void addYSCards(WXLSCardSuitEnum suit, String flag) {	
        for (WXLSCardRankEnum rankEnum : WXLSCardRankEnum.values()) {	
            if (rankEnum.getNumber() < 15) {	
                if (flag.equals("")) {	
                    cards.add(new WXLSPockerCard(suit, rankEnum));	
                } else {	
                    cards.add(new WXLSPockerCard(suit, rankEnum, flag));	
                }	
            }	
        }	
    }	
	
    public int getSize() {	
        return this.cards.size();	
    }	
	
    public List<WXLSPockerCard> getCards() {	
        return cards;	
    }	
	
    public void setCards(List<WXLSPockerCard> cards) {	
        this.cards = cards;	
    }	
	
    public WXLSPockerCard dispatch() {	
        return cards.remove(random.nextInt(cards.size()));	
    }	
    public List<WXLSPockerCard> dispatch(int num) {	
        List<WXLSPockerCard> card=new ArrayList<>();	
        if(num<=0){	
            return card;	
        }	
        for(int i=0;i<num;i++){	
            card.add(cards.remove(random.nextInt(cards.size())));	
        }	
        return card;	
    }	
	
}	
