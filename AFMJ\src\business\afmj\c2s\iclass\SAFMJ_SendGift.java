package business.afmj.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;

@SuppressWarnings("serial")
public class SAFMJ_SendGift extends BaseSendMsg {
	public long roomID;
	public int  sendPos; //发送者
	public int 	recivePos; //接受者
	public long productId;

    public static SAFMJ_SendGift make(long roomID, int sendPos, int recivePos, long productId) {
    	SAFMJ_SendGift ret = new SAFMJ_SendGift();
        ret.roomID = roomID;
        ret.sendPos = sendPos;
        ret.recivePos = recivePos;
        ret.productId = productId;
        return ret;
    }
}