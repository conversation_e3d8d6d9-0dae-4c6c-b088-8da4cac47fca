package business.afmj.c2s.iclass;

import jsproto.c2s.cclass.BaseSendMsg;


public class SAFMJ_ChangePlayerNumAgree extends BaseSendMsg {
    
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	public long roomID;
    public int pos;
    public boolean agreeChange;
    public static SAFMJ_ChangePlayerNumAgree make(long roomID, int pos, boolean agreeChange) {
    	SAFMJ_ChangePlayerNumAgree ret = new SAFMJ_ChangePlayerNumAgree();
        ret.roomID = roomID;
        ret.pos = pos;
        ret.agreeChange = agreeChange;
        return ret;
    

    }
}