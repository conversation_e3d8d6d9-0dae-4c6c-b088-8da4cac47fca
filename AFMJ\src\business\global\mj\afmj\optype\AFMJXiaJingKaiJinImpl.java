package business.global.mj.afmj.optype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCard;
import business.global.mj.afmj.AFMJRoomSet;
import business.global.mj.afmj.AFMJSetCard;
import business.global.mj.manage.OpCard;
import cenum.mj.MJSpecialEnum;

/**
 *  下精开金
 *
 *  上下翻精：翻开以上的上精，然后翻开的麻将子牌墩下面那张牌为“下精”；（默认玩法）
 * 下精：下精也分为正副精，加一为副精；
 * 下精的正副精无其他意义，仅用于发牌时计算精分加分用；
 * 流程为，翻开上精后，同时出现翻下精的动画，同时每个玩家手牌对应下精的麻将子亮出，同时统计下精的精分；
 * 下精分统计：同玩家手牌比较，运用计分规则；
 *
 *  	四家抓完牌后开始的第一张牌翻开，翻开的牌加1即为宝牌；（进金）
 *  	注：中发白算一起，如翻开白则中是金；
 *  	东南西北算一起，如翻开北则东是金；
 *  	翻开的那张牌不能抓；
 *  	例如第一张牌是一万，那么二万就是宝。
 *  	此时牌里就剩下3张一万，4张二万；
 *
 *  百搭牌（筋）： 三原四搭原则。
 *  例：翻出的牌是3万，则扣除已翻出的一张3万，剩下三张3万为原子，往下加一，四张4万为百搭。
 *  东南西北为一轮，翻到北，则东为百搭；
 *  中白发为一轮，翻到发，中为百搭。
 *
 */


public class AFMJXiaJingKaiJinImpl implements OpCard {

	@Override
	public boolean checkOpCard(AbsMJSetPos mSetPos, int cardID) {
		// 操作开金
		this.opKaiJin(mSetPos);
		return false;
	}


	@Override
	public boolean doOpCard(AbsMJSetPos mSetPos, int cardID) {
		// TODO Auto-generated method stub
		return false;
	}

	/**
	 * 下精开金
	 */
	public void opKaiJin(AbsMJSetPos mSetPos) {
		// 开金的次数 >= 指定的开金数，开金完毕，通知开金成功。
		if (((AFMJRoomSet)mSetPos.getSet()).getZhengJing() != null
			&& ((AFMJRoomSet)mSetPos.getSet()).getFuJing() != null
			&& ((AFMJRoomSet)mSetPos.getSet()).getXiaZhengJingCard() != null
			&& ((AFMJRoomSet)mSetPos.getSet()).getXiaFuJingCard() != null){
			int jin = ((AFMJRoomSet)mSetPos.getSet()).getZhengJing().cardID; // 上正精
			int jin2 = ((AFMJRoomSet)mSetPos.getSet()).getFuJing().cardID; // 上副精
			MJCard xiaZhengJing = ((AFMJRoomSet)mSetPos.getSet()).getXiaZhengJingCard(); // 下正精
			MJCard xiaFuJing = ((AFMJRoomSet)mSetPos.getSet()).getXiaFuJingCard(); // 下副精
			// 结算下精分
			((AFMJRoomSet)mSetPos.getSet()).calcXiaJing();
			// 开金通知
			((AFMJRoomSet)mSetPos.getSet()).xiaJingNotify(
					jin,
					jin2,
					xiaZhengJing,
					xiaFuJing,
					((AFMJRoomSet)mSetPos.getSet()).getXiaJingFenMap(),
					((AFMJRoomSet)mSetPos.getSet()).getXiaBaWangJingPos());
			return;
		}
		// 下精开金
		this.kaiJinCard(mSetPos);
		// 再操作下精开金
		this.opKaiJin(mSetPos);
	}


	/**
	 * 开金补花通知
	 * @param mSetPos 玩家信息
	 * @param mCard 开出的牌
	 */
	private void kaiJinApplique (AbsMJSetPos mSetPos,MJCard mCard) {
		// 添加打出的牌
		mSetPos.addOutCardIDs(mCard.getCardID());
		// 添加花
		mSetPos.getPosOpRecord().addHua(mCard.getCardID());
		// 通知补花,补花位置。
		mSetPos.getSet().MJApplique(mSetPos.getPosID());
	}


	/**
	 * 下精开金
	 * @param mSetPos 玩家信息
	 */
	public void kaiJinCard (AbsMJSetPos mSetPos) {
		// 摸牌开金
//		MJCard card = mSetPos.getSet().getMJSetCard().pop(false,mSetPos.getSet().getGodInfo().godHandCard(mSetPos));
		// 翻开的正精和副精都有四张，且翻开的牌不能是花牌；
		MJCard card = ((AFMJSetCard)mSetPos.getSet().getMJSetCard()).popJinCard(mSetPos.getSet().getGodInfo().godHandCard(mSetPos));
		if(null == card) {
			// 没有牌
			return;
		}
		// 检查摸到牌的类型是否 < 50 , > 50 花
		if (card.getType() < MJSpecialEnum.NOT_HUA.value()) {
			((AFMJRoomSet)mSetPos.getSet()).setXiaZhengJingCard(card); // 设置下正精牌
			((AFMJRoomSet)mSetPos.getSet()).setXiaFuJingCard(jinJin(card)); // 设置下副精牌
		} else {
			// 开金开到花牌，通知补花，并且重新开金
			kaiJinApplique(mSetPos,card);
			// 重新开金
			kaiJinCard(mSetPos);
		}
	}



	/**
	 * 进金
	 */
	public MJCard jinJin (MJCard card) {
		//牌类型
		int type = card.type /10;
		//牌大小
		int size = (card.cardID % 1000)/100;
		int tem = size;
		//如果是箭牌类型
		if (card.type >=45) {
			//牌 >= 7 就是白板
			if (size >= 7) {
				// 进金为 红中
				size = 5;
			}
		} else if (card.type > 40) {
			//如果是风牌
			//牌 >= 4 就是 北风
			if (size >= 4) {
				//进金为 东风
				size = 1;
			}
		} else {
			//如果是 万条筒
			//牌 >= 9 就是 九 万条筒
			if (size >= 9) {
				// 进金为 一 万条筒
				size = 1;
			}
		}
		if (tem == size) {
			size++;
		}
		int cardId = (type * 10+size)*100 + 1;
		return new MJCard(cardId);
	}

	/**
	 * 退金
	 */
	public MJCard backJinJin (MJCard card) {
		//牌类型
		int type = card.type /10;
		//牌大小
		int size = (card.cardID % 1000)/100;
		int tem = size;
		//如果是箭牌类型
		if (card.type >=45) {
			//牌 >= 5 就是 红中
			if (size <= 5) {
				// 退金为 白板
				size = 7;
			}
		} else if (card.type > 40) {
			//如果是风牌
			//牌 >= 1 就是 东风
			if (size <= 1) {
				//退金为 北风
				size = 4;
			}
		} else {
			//如果是 万条筒
			//牌 >= 1 就是 一 万条筒
			if (size <= 1) {
				// 退金为 九 万条筒
				size = 9;
			}
		}
		if (tem == size) {
			size --;
		}


		int cardId = (type * 10+size)*100 + 1;
		return new MJCard(cardId);
	}

}
