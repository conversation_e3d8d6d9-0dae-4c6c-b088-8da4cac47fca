package business.ahhnmj.c2s.iclass;		
				
import cenum.ClassType;				
import jsproto.c2s.cclass.BaseSendMsg;				
				
				
public class SAHHNMJ_Xi<PERSON>ai extends BaseSendMsg {		
    /**				
	 * 				
	 */				
	private static final long serialVersionUID = 1L;				
	public long roomID;				
    public long pid;				
    public ClassType cType;				
    public static SAHHNMJ_Xi<PERSON>ai make(long roomID, long pid, ClassType cType) {		
    	SAHHNMJ_XiPai ret = new SAHHNMJ_XiPai();		
        ret.roomID = roomID;				
        ret.pid = pid;				
        ret.cType = cType;				
        return ret;				
    				
				
    }				
}				
