package business.global.pk.a3pk.optype;	
	
import business.global.pk.AbsPKSetPos;	
import business.global.pk.PKCurOutCardInfo;	
import business.global.pk.PKOpCard;	
import business.global.pk.a3pk.A3PKRoomEnum;	
import com.ddm.server.common.CommLogD;	
import jsproto.c2s.cclass.pk.BasePocker;	
	
import org.apache.commons.collections.CollectionUtils;	
import org.apache.commons.collections.MapUtils;	
	
import java.util.*;	
import java.util.stream.Collectors;	
	
/**	
 * 检查对子牌型	
 */	
public class A3PKDuiZiImpl<T> extends A3PKBaseCardType<T> {	
	
    @Override	
    public boolean resultType(AbsPKSetPos mSetPos, List<Integer> privateCardList, PKOpCard opCard) {	
        PKCurOutCardInfo curOutCard = mSetPos.getSet().getCurOutCard();	
        if (opCard.getCardList().size() != 2) {	
            return false;	
        }	
        // 是否有重复的牌	
        Map<Integer, Long> map = opCard.getCardList().stream().collect(Collectors.groupingBy(k -> BasePocker.getCardValue(k), Collectors.counting()));	
        if (MapUtils.isEmpty(map) || map.size() != 1) {	
            CommLogD.error("A3PKDuiZiImpl map:{}",map.toString() );	
            return false;	
        }	
        int compCardId = opCard.getCardList().stream().max(Comparator.comparing(Integer::intValue)).orElse(0);	
        if (checkNotCurOutCard(A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_DUIZI, curOutCard, mSetPos.getPosID(), opCard.getCardList()) && (A3PKRoomEnum.compLeCardId(compCardId, curOutCard.getCompValue()) || opCard.getCardList().size() != curOutCard.getCurOutCards().size())) {	
            // 不符合出牌规则	
            return false;	
        }	
        return  curOutCard.setCurOutCards(mSetPos.getPosID(), A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_DUIZI.value(), opCard.getCardList(), compCardId);	
    }	
	
    @Override	
    public PKOpCard robotResultType(AbsPKSetPos mSetPos, PKCurOutCardInfo curOutCard, T item) {	
        Map<Integer, List<Integer>> map = (Map<Integer, List<Integer>>) item;	
        List<Integer> cardList = map.entrySet().stream().filter(k ->A3PKRoomEnum.compGtCardId(k.getValue().get(0),curOutCard.getCompValue()) && k.getValue().size() == 2).map(k -> k.getValue()).findFirst().orElse(Collections.emptyList());	
        return CollectionUtils.isNotEmpty(cardList) ? PKOpCard.OpCard(A3PKRoomEnum.A3PK_CARD_TYPE.A3PK_CARD_TYPE_DUIZI.value(), cardList) : null;	
    }	
    	
    	
}	
