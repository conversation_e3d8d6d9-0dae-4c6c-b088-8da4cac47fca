package business.wxpdk.c2s.iclass;
import business.wxpdk.c2s.cclass.WXPDKRoomSetInfo;
import jsproto.c2s.cclass.BaseSendMsg;

/**
 * 一局游戏开始
 * <AUTHOR>
 * */
@SuppressWarnings("serial")
public class SWXPDK_SetStart extends BaseSendMsg {

    public long roomID;
    public WXPDKRoomSetInfo setInfo;

    public static SWXPDK_SetStart make(long roomID, WXPDKRoomSetInfo setInfo) {
        SWXPDK_SetStart ret = new SWXPDK_SetStart();
        ret.roomID = roomID;
        ret.setInfo = setInfo;
        return ret;
    }
}
