package business.ahmj.c2s.iclass;	
	
import cenum.room.SetState;	
import jsproto.c2s.cclass.BaseSendMsg;	
	
import java.util.ArrayList;	
	
/**						
 * 接收客户端数据						
 * 状态改变						
 * <AUTHOR>						
 *						
 */						
						
@SuppressWarnings("serial")						
public class SAHMJ_ChangeStatus extends BaseSendMsg {						
						
	public long roomID;						
	public int  setID;//局数						
    public SetState state;  //位置	
    private ArrayList<Integer> daNiaoList = new ArrayList<>(); // 打鸟列表
    public static SAHMJ_ChangeStatus make(long roomID, int  setID, SetState state,ArrayList<Integer> daNiaoList) {
    	SAHMJ_ChangeStatus ret = new SAHMJ_ChangeStatus();						
        ret.roomID = roomID;						
        ret.setID = setID;						
        ret.state = state;						
        ret.daNiaoList = daNiaoList;
        return ret;	
    }						
}						
