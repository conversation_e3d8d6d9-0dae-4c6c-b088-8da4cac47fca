package business.global.mj.wxmj;

import business.global.mj.*;
import business.global.mj.hu.NormalHuCardImpl;
import business.global.mj.manage.MJFactory;
import business.global.mj.set.MJOpCard;
import business.wxmj.c2s.iclass.SWXMJ_PosOpCard;
import business.wxmj.c2s.iclass.SWXMJ_StartRound;
import cenum.mj.MJOpCardError;
import cenum.mj.OpType;
import com.ddm.server.common.utils.CommTime;
import com.ddm.server.websocket.def.ErrorCode;
import com.ddm.server.websocket.handler.requset.WebSocketRequest;
import com.ddm.server.websocket.handler.requset.WebSocketRequestDelegate;
import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.mj.BaseMJRoom_SetRound;
import jsproto.c2s.cclass.mj.BaseMJSet_Pos;

import java.util.List;
import java.util.Map;

/**
 * 麻将 回合逻辑 每一次等待操作，都是一个round
 *
 * <AUTHOR>
 */

public class WXMJSetRound extends AbsMJSetRound {

    public WXMJSetRound(AbsMJSetRoom set, int roundID) {
        super(set, roundID);
    }

    /**
     * 机器人操作
     *
     * @param posID
     */
    @Override
    public void RobothandCrad(int posID) {
        if (this.getEndTime() > 0) {
            return;
        }
        if (this.getRoundPosDict().containsKey(posID)) {
            new WXMJRobotOpCard(this).RobothandCrad(posID);
        }
    }

    @Override
    public synchronized int opCard(WebSocketRequest request, int opPos, OpType opType, MJOpCard mOpCard) {
        if (this.getEndTime() > 0) {
            request.error(ErrorCode.NotAllow, "end Time opPos has no round power");
            return MJOpCardError.ERROR_OP_TYPE.value();
        }
        AbsMJRoundPos pos = this.roundPosDict.get(opPos);
        if (null == pos) {
            request.error(ErrorCode.NotAllow, "opPos has no round power");
            return MJOpCardError.ROUND_POS_ERROR.value();
        }
        int opCardRet = pos.op(request, opType, mOpCard);
        if (opCardRet >= 0) {
            // 位置操作牌		
            this.posOpCardRet(opCardRet, false);
        }
        return opCardRet;
    }

    /**
     * 位置操作牌
     *
     * @param opPosRet 操作位置
     * @param isFlash  是否动画
     */
    @Override
    protected void posOpCardRet(int opPosRet, boolean isFlash) {
        int opCardID = this.set.getLastOpInfo().getLastOutCard();
        AbsMJSetPos sPos = this.set.getMJSetPos(opPosRet);
        sPos.getPosOpNotice().clearTingCardMap();
        // 刷新可胡列表
        this.refreshHuCardTypes(sPos);
        // 吃碰杠-清理牌
        if (OpType.Peng.equals(this.getOpType()) || OpType.JieGang.equals(this.getOpType())
                || OpType.Chi.equals(this.getOpType())) {
            if (OpType.Peng.equals(this.getOpType()) || OpType.JieGang.equals(this.getOpType())) {
                // 主要是跟打清空使用。 清空打牌信息
                this.cleanOutCardInfo();
            }
            this.set.getLastOpInfo().clearLastOutCard();

            if (this.checkExistClearPass()) {
                // 过手
                sPos.clearPass();
                // 漏过的玩家
                this.passLeak(opCardID, sPos.getPosID());
            }
            this.set.getSetPosMgr().clearOpTypeInfoList();
        }
        // 补杠、暗杠时候，操作牌ID == 0
        if (OpType.Gang.equals(this.getOpType()) || OpType.AnGang.equals(this.getOpType())) {
            opCardID = 0;
        }
        this.setExeOpPos(opPosRet);
        BaseMJSet_Pos posInfoOther = sPos.getNotify(false);
        BaseMJSet_Pos posInfoSelf = sPos.getNotify(true);
        this.set.getRoomPlayBack().playBack2Pos(opPosRet, this.posOpCardTing(this.room.getRoomID(), opPosRet, posInfoSelf, this.getOpType(), opCardID, isFlash, ((WXMJRoomSet) set).isShifoutianting()), set.getSetPosMgr().getAllPlayBackNotify());
        this.set.getRoom().getRoomPosMgr().notify2ExcludePosID(opPosRet, this.posOpCardTing(this.room.getRoomID(), opPosRet, posInfoOther, this.getOpType(), opCardID, isFlash, ((WXMJRoomSet) set).isShifoutianting()));
        ((WXMJRoomSet) set).setShifoutianting(false);
    }

    /**
     * 碰
     *
     * @param preRound
     * @return
     */
    @Override
    protected boolean tryStartRoundPeng(AbsMJSetRound preRound) {
        AbsMJRoundPos nextPos = this.nextRoundPos(preRound.getExeOpPos());
        nextPos = nextPosOpTypePeng(nextPos);
        this.roundPosDict.put(nextPos.getOpPos(), nextPos);
        notifyStart();
        return true;
    }

    /**
     * 下位置操作类型
     *
     * @param nextPos
     * @return
     */
    public AbsMJRoundPos nextPosOpTypePeng(AbsMJRoundPos nextPos) {
        if (nextPos.getPos().checkOpType(0, OpType.TingYouJin)) {
            nextPos.addOpType(OpType.TingYouJin);
        } else {
            if (nextPos.getPos().checkOpType(0, OpType.Ting)) {
                nextPos.addOpType(OpType.Ting);
            }
        }
        nextPos.addOpType(OpType.Out);
        return nextPos;
    }

    /**
     * 开始本回合,并摸牌
     *
     * @param pos
     * @param isNormalMo
     * @return
     */
    @Override
    public boolean startWithGetCard(int pos, boolean isNormalMo) {
        // 抓牌		
        // 作弊情况下，已经有手牌		
        if (null == this.set.getMJSetPos(pos).getHandCard()) {
            if (null == this.set.getCard(pos, isNormalMo)) {
                return false;
            }
        }
        if (this.set.isAtFirstHu()) {// 第一轮
            this.set.setAtFirstHu(false);
            this.set.getSetPosMgr().startSetApplique();// 补花
            this.set.sendSetPosCard();
        }
        return MJRoundPos(pos);
    }

    /**
     * 位置操作牌
     */
    @Override
    public <T> BaseSendMsg posOpCard(long roomID, int pos, T set_Pos, OpType opType, int opCard, boolean isFlash) {
        return SWXMJ_PosOpCard.make(roomID, pos, set_Pos, opType, opCard, isFlash, false);
    }

    public <T> BaseSendMsg posOpCardTing(long roomID, int pos, T set_Pos, OpType opType, int opCard, boolean isFlash,
                                         boolean map) {
        return SWXMJ_PosOpCard.make(roomID, pos, set_Pos, opType, opCard, isFlash, map);
    }

    @Override
    protected <T> BaseSendMsg startRound(long roomID, T room_SetWait) {
        return SWXMJ_StartRound.make(roomID, room_SetWait);
    }

    @Override
    protected boolean autoOutCard(int sec) {
        if (sec - this.startTime < 1) {
            return false;
        }
        WXMJRoundPos roundPos;
        WXMJSetPos sPos;
        for (int posID = 0; posID < this.room.getPlayerNum(); posID++) {
            roundPos = (WXMJRoundPos) this.roundPosDict.get(posID);
            if (null == roundPos) {
                continue;
            }
            sPos = (WXMJSetPos) roundPos.getPos();
            if (null == sPos || sPos.getRoomPos().isTrusteeship()) {
                continue;
            }

            List<OpType> opList = roundPos.getRecieveOpTypes();
            if (null == opList || opList.size() <= 0) {
                continue;
            }
            if (set.getRoom().RoomCfg(WXMJRoomEnum.WXMJCfg.youhubihu)) {
                int cardID = 0;
                if (opList.contains(OpType.Hu)) {
                    this.opCard(new WebSocketRequestDelegate(), roundPos.getOpPos(), OpType.Hu,
                            MJOpCard.OpCard(cardID));
                } else if (opList.contains(OpType.JiePao)) {
                    this.opCard(new WebSocketRequestDelegate(), roundPos.getOpPos(), OpType.JiePao,
                            MJOpCard.OpCard(cardID));
                }
            }
            if (sPos.isTing) {
                int cardID = 0;
                if (opList.contains(OpType.Hu)) {
                    AbsMJRoundPos nextPos = this.nextRoundPos(posID);
                    nextPos.addOpType(OpType.Pass);
                    nextPos.addOpType(OpType.Hu);
                    sPos.setTing(false);
                    sPos.setBeiYongTing(true);
                    this.roundPosDict.put(nextPos.getOpPos(), nextPos);
                    notifyStart();
                } else if (opList.contains(OpType.JiePao)) {
                    AbsMJRoundPos nextPos = this.nextRoundPos(posID);
                    nextPos.addOpType(OpType.Pass);
                    nextPos.addOpType(OpType.JiePao);
                    sPos.setTing(false);
                    sPos.setBeiYongTing(true);
                    this.roundPosDict.put(nextPos.getOpPos(), nextPos);
                    notifyStart();
                } else if (opList.contains(OpType.Out)) {
                    if (null != sPos.getHandCard()) {
                        cardID = sPos.getHandCard().cardID;
                    } else {
                        continue;
                    }
                    this.opCard(new WebSocketRequestDelegate(), roundPos.getOpPos(), OpType.Out,
                            MJOpCard.OpCard(cardID));
                }
            }
        }
        return false;
    }

    @Override
    protected AbsMJRoundPos nextRoundPos(int pos) {
        return new WXMJRoundPos(this, pos);
    }

    @Override
    protected boolean tryStartRoundOther(AbsMJSetRound preRound) {
        return false;
    }


    /**
     * 过 天听的时候点过进入 此时没有人有手牌 由庄家的下一家继续摸牌打牌
     *
     * @param preRound
     * @return
     */
    protected boolean tryStartRoundPass(AbsMJSetRound preRound) {
        WXMJSetPos setPos = (WXMJSetPos) this.set.getMJSetPos(preRound.getExeOpPos());
        WXMJRoomSet roomSet = (WXMJRoomSet) this.getSet();
        if (preRound.getWaitDealRound() != null) {
            preRound = preRound.getWaitDealRound();
        }
        // 上次的出牌，需要继续处理		
        if (preRound.checkExistNextRoundOp()) {
            // 检查下回合操作位置		
            return this.checkNextRoundOpPos(preRound);
        } else {
            // 其他操作		
            if (passOther()) {
                return true;
            }
            // 判断是否进入过天听 没有进入的话 进入天听		
            if (this.checkPass()) {
                return true;
            } else if (preRound.getOpType() == OpType.Out) {
                // 无法再处理了，下家抓牌		
                // 获取用户位置ID		
                return checkQtherPing();
            } else if (preRound.getOpType() == OpType.Gang) {
                return checkQtherQiang();
            }
            return true;
        }
    }


    /**
     * T：同一圈内，没过手（过手是摸牌，吃，碰杠都算），就不能碰漏掉的那张牌；(过圈)。
     */
    @Override
    protected boolean checkExistClearPass() {
        return true;
    }

    /**
     * 检查是否直接过
     *
     * @return
     */
    protected boolean checkPass() {
        for (int i = 0; i < this.room.getPlayerNum(); i++) {
            if (set.getMJSetPos(i).getHandCard() != null) {
                WXMJSetPos setPos = (WXMJSetPos) set.getMJSetPos(i);
                AbsMJRoundPos nextPos = this.nextRoundPos(i);
                if (setPos.checkOpType(0, OpType.AnGang)) {
                    nextPos.addOpType(OpType.AnGang);
                }
                nextPos.addOpType(OpType.Out);
                this.roundPosDict.put(nextPos.getOpPos(), nextPos);
                notifyStart();
                return true;
            }
        }
        return false;
    }

    /**
     * 是否可以吃幅打幅
     *
     * @return T:不能吃幅打幅，F:可以
     */
    protected boolean isBuChiFuDaFu() {
        return true;
    }


    /**
     * 获取本轮信息
     *
     * @param pos 位置
     * @return
     */

    public BaseMJRoom_SetRound getNotify_RoundInfo(int pos) {
        ret = new BaseMJRoom_SetRound();
        ret.setWaitID(this.roundID);
        ret.setStartWaitSec(this.startTime);
        for (AbsMJRoundPos roundPos : this.roundPosDict.values()) {
            if (roundPos.getOpType() != null) {
                continue;
            }
            // 自己 或 公开		
            if (pos == roundPos.getOpPos() || roundPos.isPublicWait()) {
                WXMJRoom_RoundPos data = new WXMJRoom_RoundPos();
                boolean isSelf = pos == roundPos.getOpPos();
                data.setOpList(roundPos.getRecieveOpTypes());
                data.setChiList(roundPos.getPos().getPosOpNotice().getChiList());
                data.setLastOpCard(roundPos.getLastOutCard());
                data.setWaitOpPos(roundPos.getOpPos());
                data.setTingCardMap(isSelf ? roundPos.getPos().getPosOpNotice().getTingCardMap() : null);
                if (this.isBuChiFuDaFu()) {
                    data.setBuChuList(isSelf ? roundPos.getPos().getPosOpNotice().getBuNengChuList() : null);
                }
                data.setTing(((WXMJSetPos) roundPos.getPos()).isTing);
                ret.addOpPosList(data);
                // 设置动作列表		
                roundPos.getPos().getPosOpRecord().setOpList(data.getOpList());
                // 重新记录打牌时间		
                roundPos.getPos().getRoomPos().setLatelyOutCardTime(CommTime.nowMS());
                // 设置最后操作时间		
                this.set.getLastOpInfo().setLastShotTime(CommTime.nowSecond());

            }
        }
        return ret;
    }
}			
