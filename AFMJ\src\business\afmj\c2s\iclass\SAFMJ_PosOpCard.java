package business.afmj.c2s.iclass;
import cenum.mj.OpType;
import jsproto.c2s.cclass.BaseSendMsg;
import jsproto.c2s.cclass.mj.BaseMJSet_Pos;

import java.util.List;


@SuppressWarnings("serial")
public class SAFMJ_PosOpCard<T> extends BaseSendMsg {
    
    public long roomID;
    public int pos;
    public T set_Pos;
    public OpType opType;
    public int opCard;
    public boolean isFlash;
    /*// 每个玩家的牌面
    public List<BaseMJSet_Pos> setPosList;*/


    //public static <T> SAFMJ_PosOpCard<T> make(long roomID, int pos, T set_Pos, List<BaseMJSet_Pos> setPosList, OpType opType, int opCard, boolean isFlash) {
    public static <T> SAFMJ_PosOpCard<T> make(long roomID, int pos, T set_Pos,  OpType opType, int opCard, boolean isFlash) {
    	SAFMJ_PosOpCard<T> ret = new SAFMJ_PosOpCard<T>();
        ret.roomID = roomID;
        ret.pos = pos;
        ret.set_Pos = set_Pos;
        ret.opType = opType;
        ret.opCard = opCard;
        ret.isFlash = isFlash;
    //    ret.setPosList = setPosList;

        return ret;
    

    }
}