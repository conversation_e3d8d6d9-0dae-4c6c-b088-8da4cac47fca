package business.global.mj.ahmj.optype;

import business.global.mj.AbsMJSetPos;
import business.global.mj.MJCard;
import business.global.mj.MJCardInit;
import business.global.mj.ahmj.AHMJSetPos;
import business.global.mj.manage.OpCard;
import cenum.mj.OpType;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 接杠
 */
public class AHMJJieGangCardImpl implements OpCard {

    @Override
    public boolean checkOpCard(AbsMJSetPos mSetPos, int cardID) {
        AHMJSetPos jPos = (AHMJSetPos) mSetPos;
        if (jPos.isTing()) {
            return false;
        }
        return this.jieGang(jPos, cardID);
    }


    public boolean jieGang(AHMJSetPos mSetPos, int cardID) {
        MJCardInit mInit = mSetPos.mjCardInit(true);
        if (null == mInit) {
            return false;
        }
        // 获取牌的类型
        int type = cardID / 100;
        // 检查是否有金
        if (mSetPos.getSet().getmJinCardInfo() != null && mSetPos.getSet().getmJinCardInfo().checkJinExist(type)) {
            return false;
        }

        Map<Integer, Long> map = mInit.getAllCardInts().stream()
                .collect(Collectors.groupingBy(p -> p, Collectors.counting()));
        if (null == map) {
            return false;
        }
        if (map.containsKey(type)) {
            if (map.get(type) >= 3) {
                if(mSetPos.checkBaotingGang(cardID/100)){
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public boolean doOpCard(AbsMJSetPos mSetPos, int cardID) {
        int lastOutCard = mSetPos.getSet().getLastOpInfo().getLastOutCard();
        int fromPos = mSetPos.getMJSetCard().getCardByID(lastOutCard).ownnerPos;

        AHMJSetPos jPos = (AHMJSetPos) mSetPos;
        boolean ret = false;
        List<Integer> publicCard = new ArrayList<>();
        publicCard.add(OpType.JieGang.value());
        publicCard.add(fromPos);
        publicCard.add(lastOutCard);

        // 搜集牌
        int type = lastOutCard / 100;
        List<MJCard> tmp = new ArrayList<>();
        for (int i = 0; i < mSetPos.sizePrivateCard(); i++) {
            if (type == mSetPos.getPrivateCard().get(i).type) {
                tmp.add(mSetPos.getPrivateCard().get(i));
                if (tmp.size() >= 3) {
                    ret = true;
                    break;
                }
            }
        }

        if (ret) {
            publicCard.add(tmp.get(0).cardID);
            publicCard.add(lastOutCard);
            publicCard.add(tmp.get(1).cardID);
            publicCard.add(tmp.get(2).cardID);
            mSetPos.addPublicCard(publicCard);
            mSetPos.removeAllPrivateCard(tmp);

        }
        return ret;

    }

}
