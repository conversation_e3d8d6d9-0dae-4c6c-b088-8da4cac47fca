package core.network.client2game.handler.wwmj;				
				
import business.global.mj.wwmj.WWMJRoom;	
import business.global.room.RoomMgr;	
import business.player.Player;	
import com.ddm.server.websocket.def.ErrorCode;	
import com.ddm.server.websocket.handler.requset.WebSocketRequest;	
import com.google.gson.Gson;	
import core.network.client2game.handler.PlayerHandler;	
import jsproto.c2s.iclass.mj.CMJ_OpPass;	
	
import java.io.IOException;	
					
public class CWWMJDoOptype extends PlayerHandler {				
					
    @Override					
    public void handle(Player player, WebSocketRequest request, String message) throws IOException {					
        final CMJ_OpPass req = new Gson().fromJson(message, CMJ_OpPass.class);					
        long roomID = req.roomID;					
        WWMJRoom room = (WWMJRoom) RoomMgr.getInstance().getRoom(roomID);					
        if (null == room) {					
            request.error(ErrorCode.NotAllow, "CNAMJOpCard not find room:" + roomID);	
            return;	
        }	
	
        room.opPass(request, player.getPid(), req);	
    }					
}					
