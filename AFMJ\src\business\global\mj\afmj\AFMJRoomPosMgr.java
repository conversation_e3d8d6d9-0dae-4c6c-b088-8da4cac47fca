package business.global.mj.afmj;

import business.global.room.base.AbsBaseRoom;
import business.global.room.base.AbsRoomPos;
import business.global.room.mj.MJRoomPosMgr;
import business.player.Robot.RobotMgr;
import com.ddm.server.common.utils.CommTime;

import java.util.Objects;

/**
 * 房间内每个位置信息
 *
 */

public class AFMJRoomPosMgr extends MJRoomPosMgr {

    public AFMJRoomPosMgr(AbsBaseRoom room) {
        super(room);
    }

    /**
     * 初始化poslist
     */
    @Override
    public void initPosList() {
        for (int i = 0; i < getPlayerNum(); i++) {
            posList.add(new AFMJRoomPos(i, room));
        }
    }


//    /**
//     * 是否所有玩家继续下一局
//     * 房间：小局10秒自动准备
//     */
//    public boolean isAllContinue() {
//        if (null == this.getPosList() || this.getPosList().size() <= 1) {
//            // 玩家信息列表没数据
//            return false;
//        }
//       //超时继续，小局10秒自动准备
//        if(((AFMJRoom)this.getRoom()).needXiaoJu10MiaoAutoReady()){
//            this.getPosList().stream().forEach(k -> {
//                if (k.getPid() > 0 && !k.isGameReady() && k.getTimeSec() > 0 && CommTime.nowSecond()- k.getTimeSec() >= 10) {
//                    getRoom().continueGame(k.getPid());
//                }
//            });
//        }
//        // 玩家在游戏中并且没有准备。
//        return this.getPosList().stream().allMatch(k -> k.getPid() > 0L && k.isGameReady());
//    }

    /**
     * 检查用户超时
     */
    @Override
    public void checkOverTime(int ServerTime) {
        if (ServerTime == 0) {
            return;
        }
        for (AbsRoomPos pos : this.getPosList()) {
            if (Objects.isNull(pos) || pos.getPid() <= 0L) {
                continue;
            }
            if (pos.getLatelyOutCardTime() <= 0) {
                continue;
            }
            if (pos.isTrusteeship()) {
                continue;
            }

            if (pos.isRobot() && CommTime.nowMS() > pos.getLatelyOutCardTime() + RobotMgr.getInstance().getThinkTime()) {
                this.getRoom().RobotDeal(pos.getPosID());
                continue;
            }
            if (CommTime.nowMS() > pos.getLatelyOutCardTime() + ServerTime) {
                pos.setLatelyOutCardTime(CommTime.nowMS());
                if (Objects.nonNull(this.getRoom())) {
                    // 启动定时器
                    this.getRoom().startTrusteeShipTime();
                }
                // 增加托管局数
                ((AFMJRoomPos)pos).addTuoGuanSetCount();
                pos.setTrusteeship(true, false);
                if (Objects.nonNull(this.getRoom())) {
                    if(room.needAtOnceOpCard()){
                        room.roomTrusteeship(pos.getPosID());
                    }
                }

            }
        }
    }

}
