package business.wxls.c2s.iclass;	
	
import jsproto.c2s.cclass.BaseSendMsg;	
	
public class SWXLS_SendGift extends BaseSendMsg {	
    public long roomID;	
    public int  sendPos; //发送者	
    public int 	recivePos; //接受者	
    public long productId;	
	
    public static SWXLS_SendGift make(long roomID, int sendPos, int recivePos, long productId) {	
        SWXLS_SendGift ret = new SWXLS_SendGift();	
        ret.roomID = roomID;	
        ret.sendPos = sendPos;	
        ret.recivePos = recivePos;	
        ret.productId = productId;	
        return ret;	
    }	
}	
