package business.global.mj.wwmj;


import business.global.mj.AbsMJSetPos;
import business.global.mj.AbsMJSetPosMgr;
import business.global.mj.AbsMJSetRound;
import business.global.mj.template.MJTemplateRoomSet;
import business.global.room.mj.MJRoomPos;
import lombok.Getter;
import lombok.Setter;
	
	
/**	
 * 模板麻将一局游戏逻辑	
 *	
 * <AUTHOR>	
 */	
@Getter	
@Setter	
public class WWMJRoomSet extends MJTemplateRoomSet {	
	
    public WWMJRoomSet(int setID, WWMJRoom room, int dPos) {	
        super(setID, room, dPos);	
	
    }	
	
    /**	
     * 检查小局托管自动解散	
     */	
    @Override	
    public boolean checkSetEndTrusteeshipAutoDissolution() {	
        if (this.getRoom().getBaseRoomConfigure().getBaseCreateRoom().getFangjian().contains(WWMJRoomEnum.WWMJGameRoomConfigEnum.TuoGuanJieSan2.ordinal())) {	
            return ((WWMJRoomMgr) this.getRoom().getRoomPosMgr()).checkSetEndTrusteeship();	
        }	
        return this.getRoom().getBaseRoomConfigure().getBaseCreateRoom().getFangjian().contains(WWMJRoomEnum.WWMJGameRoomConfigEnum.TuoGuanJieSan.ordinal());	
    }	
	
    @Override	
    public boolean equals(Object o) {	
        if (o instanceof WWMJRoomSet) {	
            if (getSetID() == ((WWMJRoomSet) o).getSetID()) {	
                return true;	
            }	
        }	
        return false;	
    }	
	
	
    /**	
     * 下回合操作位置	
     */	
    @Override	
    protected AbsMJSetRound nextSetRound(int roundID) {	
        return new WWMJSetRound(this, roundID);	
    }	
	
    /**	
     * 玩家位置信息	
     */	
    @Override	
    protected AbsMJSetPos absMJSetPos(int posID) {	
        return new WWMJSetPos(posID, (MJRoomPos) this.room.getRoomPosMgr().getPosByPosID(posID),	
                this);	
	
    }	
	
    /**	
     * 本局玩家操作管理	
     */	
    @Override	
    protected AbsMJSetPosMgr absMJSetPosMgr() {	
        return new WWMJSetPosMgr(this);	
    }	
	
    /**	
     * 计算圈	
     */	
    public void calcCurSetQuan() {	
    }	
	
	
    /**	
     * 是否白板替金	
     */	
    @Override	
    public boolean isBaiBanTiJin() {	
        return false;	
    }	
	
    @Override	
    public int cardSize() {	
        return 13;
    }	
	
    /**	
     * 本局牌管理	
     */	
    @Override	
    protected void absMJSetCard() {	
        // 设置当局牌										
        this.setSetCard(new WWMJSetCard(this));	
    }	


    @Override	
    public WWMJRoom getRoom() {	
        return (WWMJRoom) super.getRoom();	
    }
    /**
     * 计算当局每个pos位置的分数。
     */
    @Override
    protected void calcCurSetPosPoint() {
        // 计算位置小局分数
        this.getPosDict().values().forEach(k -> k.calcPosPoint());
        //只能赢当前身上分
        this.onlyWinRightNowPoint();
        //计算不低于0
        this.calYiKaoPoint();
        this.calcOtherPoint();
    }

}										
